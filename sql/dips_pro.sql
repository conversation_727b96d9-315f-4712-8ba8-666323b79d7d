-- ------------------------------------------------------------
-- 会话
-- ------------------------------------------------------------
-- Drop table if it exists (for re-runnability)
DROP TABLE IF EXISTS conversations;

-- Create conversations table
CREATE TABLE conversations (
    id UUID PRIMARY KEY NOT NULL,       -- 主键，UUID 类型
    label VARCHAR(255),                 -- 会话标签/名称，可为空
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(), -- 创建时间
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()  -- 最后更新时间
    -- Add user_id column here if/when multi-user support is added
    -- user_id VARCHAR(32) NOT NULL
);

-- Add comments
COMMENT ON TABLE conversations IS '存储会话的基本信息';
COMMENT ON COLUMN conversations.id IS '会话的唯一标识符 (UUID)';
COMMENT ON COLUMN conversations.label IS '用户自定义的会话标签或自动生成的摘要';
COMMENT ON COLUMN conversations.created_at IS '会话创建时间';
COMMENT ON COLUMN conversations.updated_at IS '会话最后更新时间';

-- ------------------------------------------------------------
-- 会话表结构更新：添加用户ID字段支持多用户会话隔离
-- 执行时间：2024-12-21
-- 说明：实现会话消息按当前登录用户过滤功能
-- ------------------------------------------------------------

-- 给conversations表添加user_id字段
ALTER TABLE conversations ADD COLUMN user_id BIGINT;

-- 添加字段注释
COMMENT ON COLUMN conversations.user_id IS '会话所属用户ID，关联sys_user.id';

-- 创建索引提升查询性能
CREATE INDEX idx_conversations_user_id ON conversations (user_id);
CREATE INDEX idx_conversations_user_created ON conversations (user_id, created_at);

-- 为现有数据设置默认用户ID（可选，用于开发测试）
-- 如果有现有数据且需要分配给特定用户，取消下面注释并修改用户ID
-- UPDATE conversations SET user_id = 1 WHERE user_id IS NULL;

-- ------------------------------------------------------------
-- 会话消息
-- ------------------------------------------------------------
-- Drop table if it exists
DROP TABLE IF EXISTS chat_messages;

-- Create chat_messages table
CREATE TABLE chat_messages (
    id UUID PRIMARY KEY NOT NULL,                -- 主键，UUID 类型
    conversation_id UUID NOT NULL,             -- 会话 ID，UUID 类型，关联 conversations.id
    user_id VARCHAR(32),                       -- 用户 ID，字符串类型，最大长度 32，可为空
    role VARCHAR(10) NOT NULL,                 -- 角色 ('user' or 'assistant')，字符串类型，最大长度 10
    content TEXT NOT NULL,                     -- 消息内容，文本类型
    intermediate_steps_json JSONB NULL,        -- 思维链步骤，JSONB 类型
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(), -- 创建时间，带时区的时间戳，默认为当前时间
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()  -- 最后更新时间，带时区的时间戳，默认为当前时间
);

ALTER TABLE chat_messages ADD COLUMN round_sequence BIGINT;
ALTER TABLE chat_messages ADD COLUMN message_order INTEGER;

-- Add comments to the table and columns
COMMENT ON TABLE chat_messages IS '存储聊天消息记录';
COMMENT ON COLUMN chat_messages.id IS '消息的唯一标识符 (UUID)';
COMMENT ON COLUMN chat_messages.conversation_id IS '所属会话的唯一标识符 (UUID)';
COMMENT ON COLUMN chat_messages.user_id IS '发送消息的用户标识符 (可选)';
COMMENT ON COLUMN chat_messages.role IS '消息发送者的角色 (''user'' 或 ''assistant'')';
COMMENT ON COLUMN chat_messages.content IS '消息的具体内容 (文本)';
COMMENT ON COLUMN chat_messages.intermediate_steps_json IS '助手生成响应时的中间步骤 (JSONB 格式)';
COMMENT ON COLUMN chat_messages.created_at IS '消息创建时间 (带时区)';
COMMENT ON COLUMN chat_messages.updated_at IS '消息最后更新时间 (带时区)';
COMMENT ON COLUMN chat_messages.round_sequence IS '轮次序号：一个用户问题及其对应的所有助手回复构成一个轮次，从1开始递增';
COMMENT ON COLUMN chat_messages.message_order IS '轮次内消息顺序：在同一轮次内，用户消息为1，助手消息按出现顺序递增';

-- Create index on conversation_id for faster lookups
CREATE INDEX idx_conversation_id ON chat_messages (conversation_id);

-- Create index for round sequence
CREATE INDEX idx_chat_messages_conversation_round ON chat_messages(conversation_id, round_sequence);

-- Create index for round sequence and message order
CREATE INDEX idx_chat_messages_conversation_round_order ON chat_messages(conversation_id, round_sequence, message_order);

-- ------------------------------------------------------------
-- 会话模板
-- ------------------------------------------------------------
-- Drop table if it exists (optional, for easier development iteration)
DROP TABLE IF EXISTS prompt_templates;

-- Create the prompt_templates table
CREATE TABLE prompt_templates
(
    id               UUID PRIMARY KEY      DEFAULT gen_random_uuid(),
    name             VARCHAR(255) NOT NULL,
    description      TEXT,
    agent_type       VARCHAR(100),          -- Can be used to categorize templates by the agent they target
    template_content TEXT,                  -- Optional: The template string with placeholders like {{slot_name}}
    slot_definitions JSONB        NOT NULL, -- Stores the array of SlotDefinition objects as JSON
    created_at       TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    updated_at       TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);

-- Add comments to the table and columns
COMMENT ON TABLE prompt_templates IS '存储可重用的提示词模板及其槽位定义';
COMMENT ON COLUMN prompt_templates.id IS '提示词模板的唯一标识符 (UUID)';
COMMENT ON COLUMN prompt_templates.name IS '用户友好的模板名称';
COMMENT ON COLUMN prompt_templates.description IS '模板用途的详细描述';
COMMENT ON COLUMN prompt_templates.agent_type IS '此模板适用的 Agent 或 Agent 类型标识符';
COMMENT ON COLUMN prompt_templates.template_content IS '带有占位符的原始模板文本 (可选，主要用于显示/参考)';
COMMENT ON COLUMN prompt_templates.slot_definitions IS '包含每个槽位定义的 JSONB 数组 (名称、标签、类型、是否必需、选项等)';
COMMENT ON COLUMN prompt_templates.created_at IS '模板创建的时间戳 (带时区)';
COMMENT ON COLUMN prompt_templates.updated_at IS '模板最后更新的时间戳 (带时区)';

-- Create indexes for faster querying
CREATE INDEX idx_prompt_templates_agent_type ON prompt_templates (agent_type);

-- Trigger function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
RETURN NEW;
END;
$$ language 'plpgsql';

-- Drop trigger if it exists before creating
DROP TRIGGER IF EXISTS update_prompt_templates_updated_at ON prompt_templates;
-- Trigger to call the function before any update on prompt_templates
CREATE TRIGGER update_prompt_templates_updated_at
    BEFORE UPDATE ON prompt_templates
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Drop trigger if it exists before creating
DROP TRIGGER IF EXISTS update_conversations_updated_at ON conversations;
-- Trigger to call the function before any update on conversations
CREATE TRIGGER update_conversations_updated_at
    BEFORE UPDATE ON conversations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Drop trigger if it exists before creating
DROP TRIGGER IF EXISTS update_chat_messages_updated_at ON chat_messages;
-- Trigger to call the function before any update on chat_messages
CREATE TRIGGER update_chat_messages_updated_at
    BEFORE UPDATE ON chat_messages
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ------------------------------------------------------------
-- 提示词模板关联关系
-- ------------------------------------------------------------
-- Drop table if it exists
DROP TABLE IF EXISTS prompt_template_relations;

-- Create the prompt_template_relations table
CREATE TABLE prompt_template_relations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    source_template_id UUID NOT NULL REFERENCES prompt_templates(id) ON DELETE CASCADE,
    target_template_id UUID NOT NULL REFERENCES prompt_templates(id) ON DELETE CASCADE,
    priority INTEGER NOT NULL DEFAULT 0, -- 优先级，数字越小优先级越高
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(source_template_id, target_template_id) -- 防止重复关联
);

-- Add comments to the table and columns
COMMENT ON TABLE prompt_template_relations IS '存储提示词模板之间的关联关系';
COMMENT ON COLUMN prompt_template_relations.id IS '关联关系的唯一标识符 (UUID)';
COMMENT ON COLUMN prompt_template_relations.source_template_id IS '源模板的唯一标识符 (UUID)';
COMMENT ON COLUMN prompt_template_relations.target_template_id IS '目标模板的唯一标识符 (UUID)';
COMMENT ON COLUMN prompt_template_relations.priority IS '关联关系的优先级，数字越小优先级越高';
COMMENT ON COLUMN prompt_template_relations.created_at IS '关联关系创建时间 (带时区)';
COMMENT ON COLUMN prompt_template_relations.updated_at IS '关联关系最后更新时间 (带时区)';

-- Create indexes for faster querying
CREATE INDEX idx_prompt_template_relations_source ON prompt_template_relations (source_template_id);
CREATE INDEX idx_prompt_template_relations_target ON prompt_template_relations (target_template_id);
CREATE INDEX idx_prompt_template_relations_priority ON prompt_template_relations (source_template_id, priority);

-- Add trigger for updated_at
DROP TRIGGER IF EXISTS update_prompt_template_relations_updated_at ON prompt_template_relations;
CREATE TRIGGER update_prompt_template_relations_updated_at
    BEFORE UPDATE ON prompt_template_relations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ------------------------------------------------------------
-- 计费模块表结构
-- ------------------------------------------------------------

-- ------------------------------------------------------------
-- 计费套餐配置表
-- ------------------------------------------------------------
-- Drop table if it exists
DROP TABLE IF EXISTS b_billing_packages CASCADE;

-- Create billing packages table
CREATE TABLE b_billing_packages (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL, -- 套餐名称
    description TEXT, -- 套餐描述
    input_token_price DECIMAL(10,6) NOT NULL, -- 输入Token单价（每千Token价格）
    output_token_price DECIMAL(10,6) NOT NULL, -- 输出Token单价（每千Token价格）
    free_tokens BIGINT DEFAULT 0, -- 新用户免费Token数量
    max_tokens_per_request BIGINT DEFAULT 0, -- 单次请求最大Token限制，0表示无限制
    daily_token_limit BIGINT DEFAULT 0, -- 每日Token使用限制，0表示无限制
    is_active BOOLEAN DEFAULT true, -- 是否激活
    sort_order INTEGER DEFAULT 0, -- 排序权重
    is_default BOOLEAN DEFAULT false, -- 是否为默认套餐
    features TEXT, -- 套餐特性描述
    limitations TEXT, -- 套餐限制说明
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX idx_b_billing_packages_active_sort ON b_billing_packages (is_active, sort_order);
CREATE INDEX idx_b_billing_packages_created_at ON b_billing_packages (created_at);

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS update_b_billing_packages_updated_at ON b_billing_packages;
CREATE TRIGGER update_b_billing_packages_updated_at
    BEFORE UPDATE ON b_billing_packages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add table comments
COMMENT ON TABLE b_billing_packages IS '计费套餐配置表';
COMMENT ON COLUMN b_billing_packages.id IS '主键ID';
COMMENT ON COLUMN b_billing_packages.name IS '套餐名称';
COMMENT ON COLUMN b_billing_packages.description IS '套餐描述';
COMMENT ON COLUMN b_billing_packages.input_token_price IS '输入Token单价（每千Token价格）';
COMMENT ON COLUMN b_billing_packages.output_token_price IS '输出Token单价（每千Token价格）';
COMMENT ON COLUMN b_billing_packages.free_tokens IS '新用户免费Token数量';
COMMENT ON COLUMN b_billing_packages.max_tokens_per_request IS '单次请求最大Token限制，0表示无限制';
COMMENT ON COLUMN b_billing_packages.daily_token_limit IS '每日Token使用限制，0表示无限制';
COMMENT ON COLUMN b_billing_packages.is_active IS '是否激活';
COMMENT ON COLUMN b_billing_packages.sort_order IS '排序权重';
COMMENT ON COLUMN b_billing_packages.is_default IS '是否为默认套餐';
COMMENT ON COLUMN b_billing_packages.features IS '套餐特性描述';
COMMENT ON COLUMN b_billing_packages.limitations IS '套餐限制说明';
COMMENT ON COLUMN b_billing_packages.created_at IS '创建时间';
COMMENT ON COLUMN b_billing_packages.updated_at IS '更新时间';

-- 可视化系数
ALTER TABLE b_billing_packages ADD COLUMN visual_discount_factor DECIMAL(4,3) DEFAULT 1.000 NOT NULL;
COMMENT ON COLUMN b_billing_packages.visual_discount_factor IS '可视化Agent输出Token折扣系数，1.00表示无折扣，0.10表示90%折扣';

-- ------------------------------------------------------------
-- 用户余额表
-- ------------------------------------------------------------
-- Drop table if it exists
DROP TABLE IF EXISTS b_user_balances CASCADE;

-- Create user balances table
CREATE TABLE b_user_balances (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL, -- 用户ID，关联users.id
    package_id BIGINT, -- 当前套餐ID，关联b_billing_packages.id
    recharged_balance DECIMAL(15,2) DEFAULT 0, -- 充值余额（人民币）
    gift_balance DECIMAL(15,2) DEFAULT 0, -- 赠送余额（人民币）
    free_tokens BIGINT DEFAULT 0, -- 免费Token余额
    used_tokens_today BIGINT DEFAULT 0, -- 今日已使用Token数
    last_reset_date DATE, -- 上次重置日期
    total_recharged DECIMAL(15,2) DEFAULT 0, -- 累计充值金额
    total_consumed DECIMAL(15,2) DEFAULT 0, -- 累计消费金额
    frozen_balance DECIMAL(15,2) DEFAULT 0, -- 冻结余额
    last_transaction_at TIMESTAMP WITH TIME ZONE, -- 最后交易时间
    is_frozen BOOLEAN DEFAULT false, -- 账户是否被冻结
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);



-- Create unique constraint and indexes
ALTER TABLE b_user_balances ADD CONSTRAINT uk_b_user_balances_user_id UNIQUE (user_id);
CREATE INDEX idx_b_user_balances_package_id ON b_user_balances (package_id);
CREATE INDEX idx_b_user_balances_updated_at ON b_user_balances (updated_at);

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS update_b_user_balances_updated_at ON b_user_balances;
CREATE TRIGGER update_b_user_balances_updated_at
    BEFORE UPDATE ON b_user_balances
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add table comments
COMMENT ON TABLE b_user_balances IS '用户余额表';
COMMENT ON COLUMN b_user_balances.id IS '主键ID';
COMMENT ON COLUMN b_user_balances.user_id IS '用户ID，关联users.id';
COMMENT ON COLUMN b_user_balances.package_id IS '当前套餐ID，关联b_billing_packages.id';
COMMENT ON COLUMN b_user_balances.recharged_balance IS '充值余额（人民币）';
COMMENT ON COLUMN b_user_balances.gift_balance IS '赠送余额（人民币）';
COMMENT ON COLUMN b_user_balances.free_tokens IS '免费Token余额';
COMMENT ON COLUMN b_user_balances.used_tokens_today IS '今日已使用Token数';
COMMENT ON COLUMN b_user_balances.last_reset_date IS '上次重置日期';
COMMENT ON COLUMN b_user_balances.total_recharged IS '累计充值金额';
COMMENT ON COLUMN b_user_balances.total_consumed IS '累计消费金额';
COMMENT ON COLUMN b_user_balances.frozen_balance IS '冻结余额';
COMMENT ON COLUMN b_user_balances.last_transaction_at IS '最后交易时间';
COMMENT ON COLUMN b_user_balances.is_frozen IS '账户是否被冻结';
COMMENT ON COLUMN b_user_balances.created_at IS '创建时间';
COMMENT ON COLUMN b_user_balances.updated_at IS '更新时间';

-- 用户月消费统计表将在文件末尾重新定义为PostgreSQL风格

-- ------------------------------------------------------------
-- 计费使用记录表
-- ------------------------------------------------------------
-- Drop table if it exists
DROP TABLE IF EXISTS b_billing_usage_records CASCADE;

-- Create billing usage records table
CREATE TABLE b_billing_usage_records (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL, -- 用户ID，关联users.id
    message_id UUID NOT NULL, -- 消息ID，关联chat_messages.id
    conversation_id UUID, -- 对话ID
    message_role VARCHAR(20), -- 消息角色：USER, ASSISTANT
    model_name VARCHAR(50) NOT NULL, -- 使用的模型名称

    -- Token统计
    input_tokens BIGINT NOT NULL DEFAULT 0, -- 输入Token数量
    output_tokens BIGINT NOT NULL DEFAULT 0, -- 输出Token数量
    thought_chain_tokens BIGINT DEFAULT 0, -- 思维链Token数量
    total_tokens BIGINT NOT NULL DEFAULT 0, -- 总Token数量

    -- 费用计算
    input_cost DECIMAL(10,6) DEFAULT 0, -- 输入费用
    output_cost DECIMAL(10,6) DEFAULT 0, -- 输出费用
    total_cost DECIMAL(10,6) NOT NULL DEFAULT 0, -- 总费用

    -- 状态管理
    status VARCHAR(20) DEFAULT 'SUCCESS', -- 状态：SUCCESS, FAILED, PENDING
    billing_status VARCHAR(20) DEFAULT 'BILLED', -- 计费状态：BILLED, UNBILLED, REFUNDED

    -- 套餐信息
    package_id BIGINT, -- 使用的套餐ID，关联b_billing_packages.id
    input_token_price DECIMAL(10,6), -- 计费时的输入Token单价
    output_token_price DECIMAL(10,6), -- 计费时的输出Token单价

    -- 申诉相关
    is_appealed BOOLEAN DEFAULT false, -- 是否已申诉
    appeal_status VARCHAR(20), -- 申诉状态：PENDING, APPROVED, REJECTED

    -- 时间字段
    request_time TIMESTAMP WITH TIME ZONE, -- 请求时间
    response_time TIMESTAMP WITH TIME ZONE, -- 响应时间
    duration_ms BIGINT, -- 处理时长（毫秒）
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX idx_b_billing_usage_records_user_created ON b_billing_usage_records (user_id, created_at);
CREATE INDEX idx_b_billing_usage_records_message_id ON b_billing_usage_records (message_id);
CREATE INDEX idx_b_billing_usage_records_conversation_id ON b_billing_usage_records (conversation_id);
CREATE INDEX idx_b_billing_usage_records_message_role ON b_billing_usage_records (message_role);
CREATE INDEX idx_b_billing_usage_records_status ON b_billing_usage_records (status);
CREATE INDEX idx_b_billing_usage_records_billing_status ON b_billing_usage_records (billing_status);
CREATE INDEX idx_b_billing_usage_records_appeal ON b_billing_usage_records (is_appealed, appeal_status);
CREATE INDEX idx_b_billing_usage_records_package_id ON b_billing_usage_records (package_id);
CREATE INDEX idx_b_billing_usage_records_created_at ON b_billing_usage_records (created_at);

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS update_b_billing_usage_records_updated_at ON b_billing_usage_records;
CREATE TRIGGER update_b_billing_usage_records_updated_at
    BEFORE UPDATE ON b_billing_usage_records
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ------------------------------------------------------------
-- 计费使用记录表字段补充：添加 billing_type 字段
-- 执行时间：2025-06-16 16:51:05
-- 说明：修复 Java 实体类与数据库表结构不一致的问题
-- 错误：ERROR: column bur1_0.billing_type does not exist
-- ------------------------------------------------------------

-- 添加 billing_type 字段
ALTER TABLE b_billing_usage_records ADD COLUMN billing_type VARCHAR(20) DEFAULT 'TOKEN_BASED';

-- 添加字段注释
COMMENT ON COLUMN b_billing_usage_records.billing_type IS '计费类型：TOKEN_BASED, FAILED, MANUAL';

-- 创建索引提升查询性能
CREATE INDEX idx_b_billing_usage_records_billing_type ON b_billing_usage_records (billing_type);
CREATE INDEX idx_b_billing_usage_records_user_billing_type ON b_billing_usage_records (user_id, billing_type);

-- Add table comments
COMMENT ON TABLE b_billing_usage_records IS '计费使用记录表（以消息为单位）- 已移除模型依赖';
COMMENT ON COLUMN b_billing_usage_records.id IS '主键ID';
COMMENT ON COLUMN b_billing_usage_records.user_id IS '用户ID，关联users.id';
COMMENT ON COLUMN b_billing_usage_records.message_id IS '消息ID，关联chat_messages.id（消息内容通过此ID关联获取）';
COMMENT ON COLUMN b_billing_usage_records.conversation_id IS '对话ID';
COMMENT ON COLUMN b_billing_usage_records.message_role IS '消息角色：USER, ASSISTANT';
COMMENT ON COLUMN b_billing_usage_records.input_tokens IS '输入Token数量';
COMMENT ON COLUMN b_billing_usage_records.output_tokens IS '输出Token数量';
COMMENT ON COLUMN b_billing_usage_records.thought_chain_tokens IS '思维链Token数量';
COMMENT ON COLUMN b_billing_usage_records.total_tokens IS '总Token数量';
COMMENT ON COLUMN b_billing_usage_records.input_cost IS '输入费用';
COMMENT ON COLUMN b_billing_usage_records.output_cost IS '输出费用';
COMMENT ON COLUMN b_billing_usage_records.total_cost IS '总费用';
COMMENT ON COLUMN b_billing_usage_records.status IS '状态：SUCCESS, FAILED, PENDING';
COMMENT ON COLUMN b_billing_usage_records.billing_status IS '计费状态：BILLED, UNBILLED, REFUNDED';
COMMENT ON COLUMN b_billing_usage_records.package_id IS '使用的套餐ID，关联b_billing_packages.id';
COMMENT ON COLUMN b_billing_usage_records.input_token_price IS '计费时的输入Token单价';
COMMENT ON COLUMN b_billing_usage_records.output_token_price IS '计费时的输出Token单价';
COMMENT ON COLUMN b_billing_usage_records.is_appealed IS '是否已申诉';
COMMENT ON COLUMN b_billing_usage_records.appeal_status IS '申诉状态：PENDING, APPROVED, REJECTED';
COMMENT ON COLUMN b_billing_usage_records.request_time IS '请求时间';
COMMENT ON COLUMN b_billing_usage_records.response_time IS '响应时间';
COMMENT ON COLUMN b_billing_usage_records.duration_ms IS '处理时长（毫秒）';
COMMENT ON COLUMN b_billing_usage_records.created_at IS '创建时间';
COMMENT ON COLUMN b_billing_usage_records.updated_at IS '更新时间';

-- 添加可视化折扣系数字段
ALTER TABLE b_billing_usage_records ADD COLUMN visual_discount_factor DECIMAL(5,3) DEFAULT 1.000 NOT NULL;

-- 添加字段注释
COMMENT ON COLUMN b_billing_usage_records.visual_discount_factor IS '本次计费使用的可视化折扣系数，1.000表示无折扣，0.100表示90%折扣';

-- 创建索引（可选，用于查询优化）
CREATE INDEX idx_b_billing_usage_records_visual_discount ON b_billing_usage_records (visual_discount_factor);

-- ------------------------------------------------------------
-- 计费系统优化：删除模型相关信息
-- 执行时间：2025-01-16 17:22:00
-- 说明：删除计费时的模型相关信息，计费不再依赖模型类型
-- ------------------------------------------------------------

-- 删除 model_name 字段的非空约束和字段本身
ALTER TABLE b_billing_usage_records DROP CONSTRAINT IF EXISTS b_billing_usage_records_model_name_check;
ALTER TABLE b_billing_usage_records ALTER COLUMN model_name DROP NOT NULL;
ALTER TABLE b_billing_usage_records DROP COLUMN IF EXISTS model_name;

-- 更新表注释
COMMENT ON TABLE b_billing_usage_records IS '计费使用记录表（以消息为单位）';

ALTER TABLE b_user_balances
ALTER COLUMN recharged_balance TYPE DECIMAL(15, 6);
ALTER TABLE b_user_balances
ALTER COLUMN gift_balance TYPE DECIMAL(15, 6);
-- 乐观锁
ALTER TABLE b_user_balances ADD COLUMN version BIGINT NOT NULL DEFAULT 0;

-- 修复计费相关表的精度问题
-- 将 amount 字段从 DECIMAL(15,2) 改为 DECIMAL(15,6) 以支持更精确的Token费用计算
-- 修改 b_billing_transactions 表的 amount 字段精度
ALTER TABLE b_billing_transactions
ALTER COLUMN amount TYPE DECIMAL(15, 6);

-- 修改 b_billing_transactions 表的余额快照字段精度
ALTER TABLE b_billing_transactions
ALTER COLUMN balance_before TYPE DECIMAL(15, 6);

ALTER TABLE b_billing_transactions
ALTER COLUMN balance_after TYPE DECIMAL(15, 6);

-- 修改 b_billing_usage_records 表的费用相关字段精度
ALTER TABLE b_billing_usage_records
ALTER COLUMN input_cost TYPE DECIMAL(10, 6);

ALTER TABLE b_billing_usage_records
ALTER COLUMN output_cost TYPE DECIMAL(10, 6);

ALTER TABLE b_billing_usage_records
ALTER COLUMN total_cost TYPE DECIMAL(10, 6);

-- 修改 b_billing_appeals 表的退费金额字段精度
ALTER TABLE b_billing_appeals
ALTER COLUMN refund_amount TYPE DECIMAL(10, 6);



-- ------------------------------------------------------------
-- 计费交易记录表
-- ------------------------------------------------------------
-- Drop table if it exists
DROP TABLE IF EXISTS b_billing_transactions CASCADE;

-- Create billing transactions table
CREATE TABLE b_billing_transactions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL, -- 用户ID，关联users.id
    transaction_no VARCHAR(100) NOT NULL, -- 交易流水号
    external_transaction_id VARCHAR(100), -- 外部交易ID
    type VARCHAR(20) NOT NULL, -- 交易类型：RECHARGE, DEDUCT, REFUND, GIFT
    amount DECIMAL(15,2) NOT NULL, -- 交易金额（正数为收入，负数为支出）
    balance_type VARCHAR(20) NOT NULL, -- 余额类型：RECHARGED, GIFT, FREE_TOKENS
    status VARCHAR(20) DEFAULT 'SUCCESS', -- 交易状态：PENDING, SUCCESS, FAILED, CANCELLED
    related_id BIGINT, -- 关联记录ID（通用）
    related_type VARCHAR(50), -- 关联记录类型
    payment_method VARCHAR(50), -- 支付方式

    -- 关联记录
    usage_record_id BIGINT, -- 关联的使用记录ID，关联b_billing_usage_records.id
    payment_record_id BIGINT, -- 关联的支付记录ID，关联b_payment_records.id
    appeal_id BIGINT, -- 关联的申诉记录ID，关联b_billing_appeals.id

    -- 余额快照
    balance_before DECIMAL(15,2), -- 交易前余额
    balance_after DECIMAL(15,2), -- 交易后余额

    description TEXT, -- 交易描述
    operator_id BIGINT, -- 操作员ID（管理员操作时），关联users.id

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create unique constraint and indexes
ALTER TABLE b_billing_transactions ADD CONSTRAINT uk_b_billing_transactions_transaction_no UNIQUE (transaction_no);
CREATE INDEX idx_b_billing_transactions_user_created ON b_billing_transactions (user_id, created_at);
CREATE INDEX idx_b_billing_transactions_type ON b_billing_transactions (type);
CREATE INDEX idx_b_billing_transactions_usage_record ON b_billing_transactions (usage_record_id);
CREATE INDEX idx_b_billing_transactions_payment_record ON b_billing_transactions (payment_record_id);
CREATE INDEX idx_b_billing_transactions_appeal_id ON b_billing_transactions (appeal_id);
CREATE INDEX idx_b_billing_transactions_operator_id ON b_billing_transactions (operator_id);
CREATE INDEX idx_b_billing_transactions_created_at ON b_billing_transactions (created_at);

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS update_b_billing_transactions_updated_at ON b_billing_transactions;
CREATE TRIGGER update_b_billing_transactions_updated_at
    BEFORE UPDATE ON b_billing_transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add table comments
COMMENT ON TABLE b_billing_transactions IS '计费交易记录表';
COMMENT ON COLUMN b_billing_transactions.id IS '主键ID';
COMMENT ON COLUMN b_billing_transactions.user_id IS '用户ID，关联users.id';
COMMENT ON COLUMN b_billing_transactions.transaction_no IS '交易流水号';
COMMENT ON COLUMN b_billing_transactions.external_transaction_id IS '外部交易ID';
COMMENT ON COLUMN b_billing_transactions.type IS '交易类型：RECHARGE, DEDUCT, REFUND, GIFT';
COMMENT ON COLUMN b_billing_transactions.amount IS '交易金额（正数为收入，负数为支出）';
COMMENT ON COLUMN b_billing_transactions.balance_type IS '余额类型：RECHARGED, GIFT, FREE_TOKENS';
COMMENT ON COLUMN b_billing_transactions.status IS '交易状态：PENDING, SUCCESS, FAILED, CANCELLED';
COMMENT ON COLUMN b_billing_transactions.related_id IS '关联记录ID（通用）';
COMMENT ON COLUMN b_billing_transactions.related_type IS '关联记录类型';
COMMENT ON COLUMN b_billing_transactions.payment_method IS '支付方式';
COMMENT ON COLUMN b_billing_transactions.usage_record_id IS '关联的使用记录ID，关联b_billing_usage_records.id';
COMMENT ON COLUMN b_billing_transactions.payment_record_id IS '关联的支付记录ID，关联b_payment_records.id';
COMMENT ON COLUMN b_billing_transactions.appeal_id IS '关联的申诉记录ID，关联b_billing_appeals.id';
COMMENT ON COLUMN b_billing_transactions.balance_before IS '交易前余额';
COMMENT ON COLUMN b_billing_transactions.balance_after IS '交易后余额';
COMMENT ON COLUMN b_billing_transactions.description IS '交易描述';
COMMENT ON COLUMN b_billing_transactions.operator_id IS '操作员ID（管理员操作时），关联users.id';
COMMENT ON COLUMN b_billing_transactions.created_at IS '创建时间';
COMMENT ON COLUMN b_billing_transactions.updated_at IS '更新时间';

-- ------------------------------------------------------------
-- 计费申诉记录表
-- ------------------------------------------------------------
-- Drop table if it exists
DROP TABLE IF EXISTS b_billing_appeals CASCADE;

-- Create billing appeals table
CREATE TABLE b_billing_appeals (
    id BIGSERIAL PRIMARY KEY,
    appeal_no VARCHAR(100) NOT NULL, -- 申诉单号
    user_id BIGINT NOT NULL, -- 申诉用户ID，关联users.id
    usage_record_id BIGINT NOT NULL, -- 申诉的使用记录ID，关联b_billing_usage_records.id

    -- 申诉内容
    reason TEXT NOT NULL, -- 申诉原因
    user_description TEXT, -- 用户详细描述
    evidence_urls TEXT, -- 证据文件URLs（JSON数组）

    -- 处理信息
    admin_id BIGINT, -- 处理管理员ID，关联users.id
    status VARCHAR(20) DEFAULT 'PENDING', -- 申诉状态：PENDING, APPROVED, REJECTED, CANCELLED
    admin_comment TEXT, -- 管理员处理意见

    -- 退费信息
    refund_amount DECIMAL(10,6), -- 退费金额
    refund_transaction_id BIGINT, -- 退费交易记录ID，关联b_billing_transactions.id

    -- 时间字段
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 提交时间
    processed_at TIMESTAMP WITH TIME ZONE, -- 处理时间
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create unique constraints and indexes
ALTER TABLE b_billing_appeals ADD CONSTRAINT uk_b_billing_appeals_appeal_no UNIQUE (appeal_no);
ALTER TABLE b_billing_appeals ADD CONSTRAINT uk_b_billing_appeals_usage_record UNIQUE (usage_record_id);
CREATE INDEX idx_b_billing_appeals_user_status ON b_billing_appeals (user_id, status);
CREATE INDEX idx_b_billing_appeals_admin_status ON b_billing_appeals (admin_id, status);
CREATE INDEX idx_b_billing_appeals_status_submitted ON b_billing_appeals (status, submitted_at);
CREATE INDEX idx_b_billing_appeals_refund_transaction ON b_billing_appeals (refund_transaction_id);
CREATE INDEX idx_b_billing_appeals_created_at ON b_billing_appeals (created_at);

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS update_b_billing_appeals_updated_at ON b_billing_appeals;
CREATE TRIGGER update_b_billing_appeals_updated_at
    BEFORE UPDATE ON b_billing_appeals
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add table comments
COMMENT ON TABLE b_billing_appeals IS '计费申诉记录表';
COMMENT ON COLUMN b_billing_appeals.id IS '主键ID';
COMMENT ON COLUMN b_billing_appeals.appeal_no IS '申诉单号';
COMMENT ON COLUMN b_billing_appeals.user_id IS '申诉用户ID，关联users.id';
COMMENT ON COLUMN b_billing_appeals.usage_record_id IS '申诉的使用记录ID，关联b_billing_usage_records.id';
COMMENT ON COLUMN b_billing_appeals.reason IS '申诉原因';
COMMENT ON COLUMN b_billing_appeals.user_description IS '用户详细描述';
COMMENT ON COLUMN b_billing_appeals.evidence_urls IS '证据文件URLs（JSON数组）';
COMMENT ON COLUMN b_billing_appeals.admin_id IS '处理管理员ID，关联users.id';
COMMENT ON COLUMN b_billing_appeals.status IS '申诉状态：PENDING, APPROVED, REJECTED, CANCELLED';
COMMENT ON COLUMN b_billing_appeals.admin_comment IS '管理员处理意见';
COMMENT ON COLUMN b_billing_appeals.refund_amount IS '退费金额';
COMMENT ON COLUMN b_billing_appeals.refund_transaction_id IS '退费交易记录ID，关联b_billing_transactions.id';
COMMENT ON COLUMN b_billing_appeals.submitted_at IS '提交时间';
COMMENT ON COLUMN b_billing_appeals.processed_at IS '处理时间';
COMMENT ON COLUMN b_billing_appeals.created_at IS '创建时间';
COMMENT ON COLUMN b_billing_appeals.updated_at IS '更新时间';

-- ------------------------------------------------------------
-- 支付记录表
-- ------------------------------------------------------------
-- Drop table if it exists
DROP TABLE IF EXISTS b_payment_records CASCADE;

-- Create payment records table
CREATE TABLE b_payment_records (
    id BIGSERIAL PRIMARY KEY,
    payment_no VARCHAR(100) NOT NULL, -- 支付单号
    user_id BIGINT NOT NULL, -- 用户ID，关联users.id

    -- 支付信息
    payment_method VARCHAR(20) NOT NULL, -- 支付方式：WECHAT, ALIPAY, ADMIN
    payment_platform VARCHAR(20), -- 支付平台：WECHAT_PAY, ALIPAY
    platform_order_no VARCHAR(100), -- 第三方平台订单号
    platform_transaction_no VARCHAR(100), -- 第三方平台交易号

    -- 金额信息
    amount DECIMAL(15,2) NOT NULL, -- 支付金额
    currency VARCHAR(3) DEFAULT 'CNY', -- 货币代码

    -- 状态信息
    status VARCHAR(20) DEFAULT 'PENDING', -- 支付状态：PENDING, SUCCESS, FAILED, CANCELLED, REFUNDED
    failure_reason TEXT, -- 失败原因
    cancel_reason TEXT, -- 取消原因
    refund_amount DECIMAL(15,2), -- 退款金额
    refund_time TIMESTAMP WITH TIME ZONE, -- 退款时间
    refund_reason TEXT, -- 退款原因

    -- 回调数据
    callback_data TEXT, -- 支付平台回调数据
    callback_time TIMESTAMP WITH TIME ZONE, -- 回调时间

    -- 客户端信息
    client_ip VARCHAR(45), -- 客户端IP（使用字符串存储以兼容IPv4和IPv6）
    user_agent TEXT, -- 用户代理

    -- 时间字段
    expired_at TIMESTAMP WITH TIME ZONE, -- 支付过期时间
    paid_at TIMESTAMP WITH TIME ZONE, -- 支付完成时间
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create unique constraints and indexes
ALTER TABLE b_payment_records ADD CONSTRAINT uk_b_payment_records_payment_no UNIQUE (payment_no);
ALTER TABLE b_payment_records ADD CONSTRAINT uk_b_payment_records_platform_order_no UNIQUE (platform_order_no);
CREATE INDEX idx_b_payment_records_user_status ON b_payment_records (user_id, status);
CREATE INDEX idx_b_payment_records_status_created ON b_payment_records (status, created_at);
CREATE INDEX idx_b_payment_records_platform_order ON b_payment_records (platform_order_no);
CREATE INDEX idx_b_payment_records_platform_transaction ON b_payment_records (platform_transaction_no);
CREATE INDEX idx_b_payment_records_created_at ON b_payment_records (created_at);

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS update_b_payment_records_updated_at ON b_payment_records;
CREATE TRIGGER update_b_payment_records_updated_at
    BEFORE UPDATE ON b_payment_records
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add table comments
COMMENT ON TABLE b_payment_records IS '支付记录表';
COMMENT ON COLUMN b_payment_records.id IS '主键ID';
COMMENT ON COLUMN b_payment_records.payment_no IS '支付单号';
COMMENT ON COLUMN b_payment_records.user_id IS '用户ID，关联users.id';
COMMENT ON COLUMN b_payment_records.payment_method IS '支付方式：WECHAT, ALIPAY, ADMIN';
COMMENT ON COLUMN b_payment_records.payment_platform IS '支付平台：WECHAT_PAY, ALIPAY';
COMMENT ON COLUMN b_payment_records.platform_order_no IS '第三方平台订单号';
COMMENT ON COLUMN b_payment_records.platform_transaction_no IS '第三方平台交易号';
COMMENT ON COLUMN b_payment_records.amount IS '支付金额';
COMMENT ON COLUMN b_payment_records.currency IS '货币代码';
COMMENT ON COLUMN b_payment_records.status IS '支付状态：PENDING, SUCCESS, FAILED, CANCELLED, REFUNDED';
COMMENT ON COLUMN b_payment_records.failure_reason IS '失败原因';
COMMENT ON COLUMN b_payment_records.cancel_reason IS '取消原因';
COMMENT ON COLUMN b_payment_records.refund_amount IS '退款金额';
COMMENT ON COLUMN b_payment_records.refund_time IS '退款时间';
COMMENT ON COLUMN b_payment_records.refund_reason IS '退款原因';
COMMENT ON COLUMN b_payment_records.callback_data IS '支付平台回调数据';
COMMENT ON COLUMN b_payment_records.callback_time IS '回调时间';
COMMENT ON COLUMN b_payment_records.client_ip IS '客户端IP（使用字符串存储以兼容IPv4和IPv6）';
COMMENT ON COLUMN b_payment_records.user_agent IS '用户代理';
COMMENT ON COLUMN b_payment_records.expired_at IS '支付过期时间';
COMMENT ON COLUMN b_payment_records.paid_at IS '支付完成时间';
COMMENT ON COLUMN b_payment_records.created_at IS '创建时间';
COMMENT ON COLUMN b_payment_records.updated_at IS '更新时间';

-- ------------------------------------------------------------
-- 外键约束
-- ------------------------------------------------------------
-- 用户余额表外键
ALTER TABLE b_user_balances ADD CONSTRAINT fk_b_user_balances_package_id 
    FOREIGN KEY (package_id) REFERENCES b_billing_packages(id);

-- 使用记录表外键
ALTER TABLE b_billing_usage_records ADD CONSTRAINT fk_b_billing_usage_records_message_id 
    FOREIGN KEY (message_id) REFERENCES chat_messages(id);
ALTER TABLE b_billing_usage_records ADD CONSTRAINT fk_b_billing_usage_records_package_id 
    FOREIGN KEY (package_id) REFERENCES b_billing_packages(id);

-- 交易记录表外键
ALTER TABLE b_billing_transactions ADD CONSTRAINT fk_b_billing_transactions_usage_record_id 
    FOREIGN KEY (usage_record_id) REFERENCES b_billing_usage_records(id);
ALTER TABLE b_billing_transactions ADD CONSTRAINT fk_b_billing_transactions_payment_record_id 
    FOREIGN KEY (payment_record_id) REFERENCES b_payment_records(id);
ALTER TABLE b_billing_transactions ADD CONSTRAINT fk_b_billing_transactions_appeal_id 
    FOREIGN KEY (appeal_id) REFERENCES b_billing_appeals(id);

-- 申诉记录表外键
ALTER TABLE b_billing_appeals ADD CONSTRAINT fk_b_billing_appeals_usage_record_id 
    FOREIGN KEY (usage_record_id) REFERENCES b_billing_usage_records(id);
ALTER TABLE b_billing_appeals ADD CONSTRAINT fk_b_billing_appeals_refund_transaction_id 
    FOREIGN KEY (refund_transaction_id) REFERENCES b_billing_transactions(id);

-- ------------------------------------------------------------
-- 提示词模板向量
-- ------------------------------------------------------------
-- Drop table if it exists
DROP TABLE IF EXISTS prompt_template_vectors;
-- Create the prompt_template_vectors table
CREATE TABLE prompt_template_vectors (
    _id             SERIAL UNIQUE NOT NULL, -- _id INT GENERATED BY DEFAULT AS IDENTITY UNIQUE NOT NULL
    id              UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    template_id     UUID,
    typical_description VARCHAR(255),
    embedding       VECTOR(768),
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by      VARCHAR(64),
    updated_by      VARCHAR(64)
);

-- Add comments to the table and columns
COMMENT ON TABLE prompt_template_vectors IS '存储提示词模板的向量';
COMMENT ON COLUMN prompt_template_vectors._id IS '自增数字ID用于分批、排序';
COMMENT ON COLUMN prompt_template_vectors.id IS '向量的唯一标识符 (UUID)';
COMMENT ON COLUMN prompt_template_vectors.template_id IS '关联的模板 ID';
COMMENT ON COLUMN prompt_template_vectors.typical_description IS '模板的典型描述';
COMMENT ON COLUMN prompt_template_vectors.embedding IS '模板的向量表示';
COMMENT ON COLUMN prompt_template_vectors.created_at IS '向量创建时间';
COMMENT ON COLUMN prompt_template_vectors.updated_at IS '向量最后更新时间';
COMMENT ON COLUMN prompt_template_vectors.created_by IS '创建向量的用户';
COMMENT ON COLUMN prompt_template_vectors.updated_by IS '最后更新向量的用户';

-- ------------------------------------------------------------
-- 预付费阶梯定价扩展
-- 执行时间：2025-01-16 17:30:00
-- 说明：实现预付费阶梯定价功能，支持不同套餐不同月消费要求和Token单价
-- ------------------------------------------------------------

-- 为 b_billing_packages 表添加月消费相关字段
ALTER TABLE b_billing_packages ADD COLUMN monthly_minimum_spend DECIMAL(10,2) DEFAULT 0;
ALTER TABLE b_billing_packages ADD COLUMN package_level VARCHAR(20) DEFAULT 'BASIC';

-- 添加字段注释
COMMENT ON COLUMN b_billing_packages.monthly_minimum_spend IS '最低月消费要求（人民币）';
COMMENT ON COLUMN b_billing_packages.package_level IS '套餐等级：BASIC/STANDARD/PREMIUM';

-- ------------------------------------------------------------
-- 用户月消费统计表（PostgreSQL版本）
-- ------------------------------------------------------------
-- 删除现有的MySQL风格表定义，重新创建PostgreSQL风格
DROP TABLE IF EXISTS b_user_monthly_spending CASCADE;

CREATE TABLE b_user_monthly_spending (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    package_id BIGINT NOT NULL,
    month_year VARCHAR(7) NOT NULL, -- 格式: 2024-01
    total_spend DECIMAL(10,2) DEFAULT 0,
    minimum_required DECIMAL(10,2) DEFAULT 0,
    qualification_status VARCHAR(20) DEFAULT 'UNQUALIFIED', -- 'QUALIFIED' 或 'UNQUALIFIED'
    package_switched_at TIMESTAMP WITH TIME ZONE NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT uk_user_month UNIQUE (user_id, month_year)
);

-- 创建索引
CREATE INDEX idx_b_user_monthly_spending_package_month ON b_user_monthly_spending (package_id, month_year);
CREATE INDEX idx_b_user_monthly_spending_user_created ON b_user_monthly_spending (user_id, created_at);
CREATE INDEX idx_b_user_monthly_spending_qualification ON b_user_monthly_spending (qualification_status);
CREATE INDEX idx_b_user_monthly_spending_month_year ON b_user_monthly_spending (month_year);

-- 添加触发器
DROP TRIGGER IF EXISTS update_b_user_monthly_spending_updated_at ON b_user_monthly_spending;
CREATE TRIGGER update_b_user_monthly_spending_updated_at
    BEFORE UPDATE ON b_user_monthly_spending
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 添加外键约束
ALTER TABLE b_user_monthly_spending ADD CONSTRAINT fk_b_user_monthly_spending_package_id 
    FOREIGN KEY (package_id) REFERENCES b_billing_packages(id);

-- 添加表注释
COMMENT ON TABLE b_user_monthly_spending IS '用户月消费统计表';
COMMENT ON COLUMN b_user_monthly_spending.id IS '主键ID';
COMMENT ON COLUMN b_user_monthly_spending.user_id IS '用户ID，关联users.id';
COMMENT ON COLUMN b_user_monthly_spending.package_id IS '套餐ID，关联b_billing_packages.id';
COMMENT ON COLUMN b_user_monthly_spending.month_year IS '月份，格式: 2024-01';
COMMENT ON COLUMN b_user_monthly_spending.total_spend IS '当月总消费金额';
COMMENT ON COLUMN b_user_monthly_spending.minimum_required IS '当月最低消费要求';
COMMENT ON COLUMN b_user_monthly_spending.qualification_status IS '达标状态：QUALIFIED（已达标）, UNQUALIFIED（未达标）';
COMMENT ON COLUMN b_user_monthly_spending.package_switched_at IS '本月套餐切换时间';
COMMENT ON COLUMN b_user_monthly_spending.created_at IS '创建时间';
COMMENT ON COLUMN b_user_monthly_spending.updated_at IS '更新时间';

-- ------------------------------------------------------------
-- 预设套餐配置数据（阶梯定价）
-- ------------------------------------------------------------
-- 更新现有套餐为阶梯定价模式，删除现有数据
DELETE FROM b_billing_packages;

-- 插入预设的阶梯定价套餐
INSERT INTO b_billing_packages (name, description, input_token_price, output_token_price, monthly_minimum_spend, package_level, free_tokens, max_tokens_per_request, daily_token_limit, is_active, sort_order, is_default) VALUES
('基础套餐', '无最低月消费要求，按标准价格计费，适合轻度使用用户', 0.008, 0.012, 0, 'BASIC', 10000, 8000, 50000, true, 1, true),
('标准套餐', '月消费满200元享受优惠价格，适合中度使用用户', 0.005, 0.008, 200, 'STANDARD', 20000, 16000, 100000, true, 2, false),
('高级套餐', '月消费满1000元享受最优价格，适合重度使用用户', 0.003, 0.005, 1000, 'PREMIUM', 40000, 0, 0, true, 3, false);

-- ------------------------------------------------------------
-- 预付费阶梯定价索引优化
-- ------------------------------------------------------------
-- 为查询优化添加复合索引
CREATE INDEX idx_b_billing_packages_level_active ON b_billing_packages (package_level, is_active);
CREATE INDEX idx_b_billing_packages_spend_level ON b_billing_packages (monthly_minimum_spend, package_level);

-- ------------------------------------------------------------
-- 租户分类字段扩展
-- 执行时间：2025-06-24 10:12:41
-- 说明：为租户表添加行业类型和租户类型字段，支持医美、地产行业区分和机构、机构集团、厂家、消费者类型区分
-- ------------------------------------------------------------

-- 为 sys_tenant 表添加行业类型字段
ALTER TABLE sys_tenant ADD COLUMN industry_type VARCHAR(32);

-- 为 sys_tenant 表添加租户类型字段  
ALTER TABLE sys_tenant ADD COLUMN tenant_type VARCHAR(32);

-- 添加字段注释
COMMENT ON COLUMN sys_tenant.industry_type IS '行业类型：MEDICAL_BEAUTY（医美）、REAL_ESTATE（地产）';
COMMENT ON COLUMN sys_tenant.tenant_type IS '租户类型：ORGANIZATION（机构）、ORGANIZATION_GROUP（机构集团）、MANUFACTURER（厂家）、CONSUMER（消费者）';

-- 添加字段约束确保数据一致性
ALTER TABLE sys_tenant ADD CONSTRAINT check_industry_type 
    CHECK (industry_type IS NULL OR industry_type IN ('MEDICAL_BEAUTY', 'REAL_ESTATE'));

ALTER TABLE sys_tenant ADD CONSTRAINT check_tenant_type 
    CHECK (tenant_type IS NULL OR tenant_type IN ('ORGANIZATION', 'ORGANIZATION_GROUP', 'MANUFACTURER', 'CONSUMER'));

-- 创建索引提升查询性能
CREATE INDEX idx_sys_tenant_industry_type ON sys_tenant (industry_type);
CREATE INDEX idx_sys_tenant_tenant_type ON sys_tenant (tenant_type);
CREATE INDEX idx_sys_tenant_industry_tenant_type ON sys_tenant (industry_type, tenant_type);

-- ------------------------------------------------------------
-- 分析可视化页面向量
-- ------------------------------------------------------------
-- Drop table if it exists
DROP TABLE IF EXISTS vectors_analysis_page;

-- Create vectors_analysis_page table
-- 分析可视化页面搜索向量表，用于存储页面向量化数据以支持语义搜索
CREATE TABLE vectors_analysis_page (
    _id SERIAL UNIQUE NOT NULL,                                    -- 自增序列ID，用于内部唯一标识
    id UUID PRIMARY KEY,                                           -- 主键UUID，全局唯一标识符
    resource_type VARCHAR(32) NOT NULL,                            -- 资源类型，如6.0、GPT等
    visual_id VARCHAR(64) NOT NULL,                                -- 可视化组件ID，关联具体的可视化元素
    shot_name VARCHAR(64),                                          -- 快照名称，用于某些信息
    data_period VARCHAR(32),                                        -- 数据周期、时间范围
    split_id VARCHAR(64),                                           -- 切分ID，用于数据管理
    viewpoint_name VARCHAR(64),                                     -- 视角名称，表示数据所在的组织结构
    customer_id VARCHAR(64),                                        -- 客户ID，关联客户信息
    customer_name VARCHAR(64),                                      -- 客户名称，便于查询和显示
    node_name VARCHAR(64),                                          -- 节点名称，一般表示指标
    node_id VARCHAR(64),                                            -- 节点ID，一般表示指标
    official_name VARCHAR(256),                                     -- 标题名称，标题描述
    visual_description TEXT,                                        -- 可视化描述，详细描述可视化内容
    embedding VECTOR(768) NOT NULL,                                -- 768维向量，用于语义搜索和相似度计算
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,                -- 创建时间，记录数据创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,                -- 更新时间，记录最后修改时间
    created_by VARCHAR(64),                                         -- 创建人，记录数据创建者
    updated_by VARCHAR(64)                                          -- 更新人，记录最后修改者
);

-- Add table and column comments
COMMENT ON TABLE vectors_analysis_page IS '分析可视化页面搜索向量表，存储页面向量化数据用于语义搜索';
COMMENT ON COLUMN vectors_analysis_page._id IS '自增序列ID，内部唯一标识';
COMMENT ON COLUMN vectors_analysis_page.id IS '主键UUID，全局唯一标识符';
COMMENT ON COLUMN vectors_analysis_page.resource_type IS '资源类型，如6.0、GPT等';
COMMENT ON COLUMN vectors_analysis_page.visual_id IS '可视化组件ID，关联具体的可视化元素';
COMMENT ON COLUMN vectors_analysis_page.shot_name IS '快照名称，用于某些信息';
COMMENT ON COLUMN vectors_analysis_page.data_period IS '数据周期、时间范围';
COMMENT ON COLUMN vectors_analysis_page.split_id  IS '切分ID，用于数据管理';
COMMENT ON COLUMN vectors_analysis_page.viewpoint_name IS '视角名称，表示数据所在的组织结构';
COMMENT ON COLUMN vectors_analysis_page.customer_id IS '客户ID，关联客户信息';
COMMENT ON COLUMN vectors_analysis_page.customer_name IS '客户名称，便于查询和显示';
COMMENT ON COLUMN vectors_analysis_page.node_name IS '节点名称，一般表示指标';
COMMENT ON COLUMN vectors_analysis_page.node_id IS '节点ID，一般表示指标';
COMMENT ON COLUMN vectors_analysis_page.official_name IS '标题名称，标题描述';
COMMENT ON COLUMN vectors_analysis_page.visual_description IS '可视化描述，详细描述可视化内容';
COMMENT ON COLUMN vectors_analysis_page.embedding IS '768维向量，用于语义搜索和相似度计算';
COMMENT ON COLUMN vectors_analysis_page.created_at IS '创建时间，记录数据创建时间';
COMMENT ON COLUMN vectors_analysis_page.updated_at IS '更新时间，记录最后修改时间';
COMMENT ON COLUMN vectors_analysis_page.created_by IS '创建人，记录数据创建者';
COMMENT ON COLUMN vectors_analysis_page.updated_by IS '更新人，记录最后修改者';

-- Create indexes for better query performance
CREATE INDEX idx_vectors_analysis_page_data_period ON vectors_analysis_page (data_period);
CREATE INDEX idx_vectors_analysis_page_customer_id ON vectors_analysis_page (customer_id);
CREATE INDEX idx_vectors_analysis_page_customer_name ON vectors_analysis_page (customer_name);
CREATE INDEX idx_vectors_analysis_page_resource_type ON vectors_analysis_page (resource_type);
CREATE INDEX idx_vectors_analysis_page_visual_id ON vectors_analysis_page (visual_id);
CREATE INDEX idx_vectors_analysis_page_node_id ON vectors_analysis_page (node_id);
CREATE INDEX idx_vectors_analysis_page_created_at ON vectors_analysis_page (created_at);
CREATE INDEX idx_vectors_analysis_page_created_by ON vectors_analysis_page (created_by);

-- 添加扣除来源字段的数据库迁移脚本 (PostgreSQL)
-- 执行日期：2025-06-25

-- 1. 添加 deduction_source 列
ALTER TABLE b_billing_usage_records
    ADD COLUMN deduction_source VARCHAR(50);

-- 2. 添加列注释 (PostgreSQL 语法)
COMMENT ON COLUMN b_billing_usage_records.deduction_source IS '扣除来源：FREE_TOKENS_ONLY(仅扣除免费Token), GIFT_BALANCE_ONLY(仅扣除赠送余额), RECHARGED_BALANCE_ONLY(仅扣除充值余额), MIXED_DEDUCTION(混合扣除)';

-- 3. 数据迁移：将 billing_type 中的扣除来源类型迁移到 deduction_source
UPDATE b_billing_usage_records
SET deduction_source = 'FREE_TOKENS_ONLY',
    billing_type = 'TOKEN_BASED'
WHERE billing_type = 'FREE_TOKENS_ONLY';

UPDATE b_billing_usage_records
SET deduction_source = 'GIFT_BALANCE_ONLY',
    billing_type = 'TOKEN_BASED'
WHERE billing_type = 'GIFT_BALANCE_ONLY';

UPDATE b_billing_usage_records
SET deduction_source = 'RECHARGED_BALANCE_ONLY',
    billing_type = 'TOKEN_BASED'
WHERE billing_type = 'RECHARGED_BALANCE_ONLY';

UPDATE b_billing_usage_records
SET deduction_source = 'MIXED_DEDUCTION',
    billing_type = 'TOKEN_BASED'
WHERE billing_type = 'MIXED_DEDUCTION';

-- 4. 验证迁移结果
SELECT
    billing_type,
    deduction_source,
    COUNT(*) as record_count
FROM b_billing_usage_records
GROUP BY billing_type, deduction_source
ORDER BY billing_type, deduction_source;

-- 5. 可选：添加索引以提高查询性能
CREATE INDEX CONCURRENTLY idx_billing_usage_records_deduction_source
    ON b_billing_usage_records(deduction_source)
    WHERE deduction_source IS NOT NULL;

-- 6. 可选：添加组合索引
CREATE INDEX CONCURRENTLY idx_billing_usage_records_billing_deduction
    ON b_billing_usage_records(billing_type, deduction_source);

-- 7. 回滚脚本（如果需要）
-- DROP INDEX IF EXISTS idx_billing_usage_records_deduction_source;
-- DROP INDEX IF EXISTS idx_billing_usage_records_billing_deduction;
-- ALTER TABLE b_billing_usage_records DROP COLUMN deduction_source;-- ------------------------------------------------------------
-- 用户首次登录引导字段扩展
-- 执行时间：2024-12-25 10:30:00
-- 说明：为多引导页面系统添加首次登录完成标识字段
-- ------------------------------------------------------------

-- 为 sys_user 表添加首次登录完成标识字段
ALTER TABLE sys_user ADD COLUMN first_login_completed BOOLEAN DEFAULT FALSE NOT NULL;

-- 添加字段注释
COMMENT ON COLUMN sys_user.first_login_completed IS '是否完成首次登录引导：false-未完成，true-已完成';

-- 创建索引提升查询性能
CREATE INDEX idx_sys_user_first_login_completed ON sys_user (first_login_completed);

-- 为现有用户设置默认值（已存在的用户视为已完成引导）
UPDATE sys_user SET first_login_completed = TRUE WHERE created_time < CURRENT_TIMESTAMP;

-- ------------------------------------------------------------
-- 计费交易记录表结构优化：related_id字段类型变更
-- 执行时间：2025-01-17 10:00:00
-- 说明：将b_billing_transactions表的related_id字段从BIGINT类型修改为VARCHAR类型，以支持更灵活的关联记录ID存储
-- ------------------------------------------------------------

-- 修改 related_id 字段类型从 BIGINT 改为 VARCHAR(100)
ALTER TABLE b_billing_transactions ALTER COLUMN related_id TYPE VARCHAR(100);

-- 更新字段注释
COMMENT ON COLUMN b_billing_transactions.related_id IS '关联记录ID（通用），支持数字和字符串类型的ID';

-- ------------------------------------------------------------
-- 计费系统字段精度扩展：解决数值溢出问题
-- 执行时间：2025-01-02 11:40:00
-- 说明：将计费相关字段从 DECIMAL(10,6) 扩展为 DECIMAL(15,6)，支持更大的费用金额
-- 错误修复：ERROR: numeric field overflow - A field with precision 10, scale 6 must round to an absolute value less than 10^4
-- ------------------------------------------------------------

-- 1. 修改 b_billing_usage_records 表的费用相关字段精度
ALTER TABLE b_billing_usage_records ALTER COLUMN input_cost TYPE DECIMAL(15,6);
ALTER TABLE b_billing_usage_records ALTER COLUMN output_cost TYPE DECIMAL(15,6);  
ALTER TABLE b_billing_usage_records ALTER COLUMN total_cost TYPE DECIMAL(15,6);
ALTER TABLE b_billing_usage_records ALTER COLUMN input_token_price TYPE DECIMAL(15,6);
ALTER TABLE b_billing_usage_records ALTER COLUMN output_token_price TYPE DECIMAL(15,6);

-- 2. 修改 b_billing_packages 表的Token单价字段精度
ALTER TABLE b_billing_packages ALTER COLUMN input_token_price TYPE DECIMAL(15,6);
ALTER TABLE b_billing_packages ALTER COLUMN output_token_price TYPE DECIMAL(15,6);

-- 3. 修改 b_billing_appeals 表的退费金额字段精度
ALTER TABLE b_billing_appeals ALTER COLUMN refund_amount TYPE DECIMAL(15,6);

-- 4. 更新字段注释，反映新的精度范围
COMMENT ON COLUMN b_billing_usage_records.input_cost IS '输入费用，支持更大金额 DECIMAL(15,6)';
COMMENT ON COLUMN b_billing_usage_records.output_cost IS '输出费用，支持更大金额 DECIMAL(15,6)';
COMMENT ON COLUMN b_billing_usage_records.total_cost IS '总费用，支持更大金额 DECIMAL(15,6)';
COMMENT ON COLUMN b_billing_usage_records.input_token_price IS '计费时的输入Token单价，支持更高精度 DECIMAL(15,6)';
COMMENT ON COLUMN b_billing_usage_records.output_token_price IS '计费时的输出Token单价，支持更高精度 DECIMAL(15,6)';

COMMENT ON COLUMN b_billing_packages.input_token_price IS '输入Token单价（每千Token价格），支持更高精度 DECIMAL(15,6)';
COMMENT ON COLUMN b_billing_packages.output_token_price IS '输出Token单价（每千Token价格），支持更高精度 DECIMAL(15,6)';

COMMENT ON COLUMN b_billing_appeals.refund_amount IS '退费金额，支持更大金额 DECIMAL(15,6)';

-- 5. 验证修改结果
SELECT 
    table_name,
    column_name,
    data_type,
    numeric_precision,
    numeric_scale
FROM information_schema.columns 
WHERE table_name IN ('b_billing_usage_records', 'b_billing_packages', 'b_billing_appeals')
  AND column_name IN ('input_cost', 'output_cost', 'total_cost', 'input_token_price', 'output_token_price', 'refund_amount')
ORDER BY table_name, column_name;

-- ------------------------------------------------------------
-- 微信支付电子发票表
-- 创建时间：2024-12-21
-- 说明：微信支付电子发票功能数据表
-- ------------------------------------------------------------

-- 微信支付电子发票表
DROP TABLE IF EXISTS b_wechat_invoice CASCADE;

CREATE TABLE b_wechat_invoice (
    id BIGSERIAL PRIMARY KEY,                                    -- 主键ID
    invoice_no VARCHAR(100) NOT NULL UNIQUE,                     -- 发票申请单号
    payment_record_id BIGINT NOT NULL,                           -- 关联的支付记录ID
    out_trade_no VARCHAR(100) NOT NULL,                          -- 商户订单号
    transaction_id VARCHAR(100),                                 -- 微信支付订单号
    user_id BIGINT NOT NULL,                                     -- 用户ID
    amount DECIMAL(15,2) NOT NULL CHECK (amount >= 0),           -- 发票金额
    invoice_type VARCHAR(20) NOT NULL CHECK (invoice_type IN ('NORMAL', 'SPECIAL')), -- 发票类型
    invoice_title VARCHAR(200) NOT NULL,                         -- 发票抬头
    tax_number VARCHAR(50),                                      -- 纳税人识别号
    registered_address VARCHAR(200),                             -- 注册地址
    registered_phone VARCHAR(20),                                -- 注册电话
    bank_name VARCHAR(100),                                      -- 开户银行
    bank_account VARCHAR(50),                                    -- 银行账号
    recipient_name VARCHAR(100),                                 -- 收件人姓名
    recipient_phone VARCHAR(20),                                 -- 收件人手机号
    recipient_email VARCHAR(100),                                -- 收件人邮箱
    status VARCHAR(20) NOT NULL CHECK (status IN ('PENDING', 'PROCESSING', 'SUCCESS', 'FAILED', 'CANCELLED')), -- 发票状态
    card_template_id VARCHAR(100),                               -- 微信发票卡券模板ID
    card_id VARCHAR(100),                                        -- 微信发票卡券ID
    invoice_file_url VARCHAR(500),                               -- 发票文件URL
    invoice_file_size BIGINT,                                    -- 发票文件大小（字节）
    invoice_file_md5 VARCHAR(100),                               -- 发票文件MD5
    invoice_code VARCHAR(50),                                    -- 发票代码
    invoice_number VARCHAR(50),                                  -- 发票号码
    invoice_date TIMESTAMPTZ,                                    -- 开票日期
    check_code VARCHAR(50),                                      -- 校验码
    callback_data TEXT,                                          -- 微信回调数据
    failure_reason VARCHAR(500),                                 -- 失败原因
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),              -- 创建时间
    updated_at TIMESTAMPTZ DEFAULT NOW(),                       -- 更新时间
    deleted BOOLEAN NOT NULL DEFAULT FALSE                       -- 删除标记
);

-- 添加表和字段注释
COMMENT ON TABLE b_wechat_invoice IS '微信支付电子发票表';
COMMENT ON COLUMN b_wechat_invoice.id IS '主键ID';
COMMENT ON COLUMN b_wechat_invoice.invoice_no IS '发票申请单号';
COMMENT ON COLUMN b_wechat_invoice.payment_record_id IS '关联的支付记录ID';
COMMENT ON COLUMN b_wechat_invoice.out_trade_no IS '商户订单号';
COMMENT ON COLUMN b_wechat_invoice.transaction_id IS '微信支付订单号';
COMMENT ON COLUMN b_wechat_invoice.user_id IS '用户ID';
COMMENT ON COLUMN b_wechat_invoice.amount IS '发票金额';
COMMENT ON COLUMN b_wechat_invoice.invoice_type IS '发票类型：NORMAL-普通发票，SPECIAL-专用发票';
COMMENT ON COLUMN b_wechat_invoice.invoice_title IS '发票抬头';
COMMENT ON COLUMN b_wechat_invoice.tax_number IS '纳税人识别号';
COMMENT ON COLUMN b_wechat_invoice.registered_address IS '注册地址';
COMMENT ON COLUMN b_wechat_invoice.registered_phone IS '注册电话';
COMMENT ON COLUMN b_wechat_invoice.bank_name IS '开户银行';
COMMENT ON COLUMN b_wechat_invoice.bank_account IS '银行账号';
COMMENT ON COLUMN b_wechat_invoice.recipient_name IS '收件人姓名';
COMMENT ON COLUMN b_wechat_invoice.recipient_phone IS '收件人手机号';
COMMENT ON COLUMN b_wechat_invoice.recipient_email IS '收件人邮箱';
COMMENT ON COLUMN b_wechat_invoice.status IS '发票状态：PENDING-待开票，PROCESSING-开票中，SUCCESS-开票成功，FAILED-开票失败，CANCELLED-已取消';
COMMENT ON COLUMN b_wechat_invoice.card_template_id IS '微信发票卡券模板ID';
COMMENT ON COLUMN b_wechat_invoice.card_id IS '微信发票卡券ID';
COMMENT ON COLUMN b_wechat_invoice.invoice_file_url IS '发票文件URL';
COMMENT ON COLUMN b_wechat_invoice.invoice_file_size IS '发票文件大小（字节）';
COMMENT ON COLUMN b_wechat_invoice.invoice_file_md5 IS '发票文件MD5';
COMMENT ON COLUMN b_wechat_invoice.invoice_code IS '发票代码';
COMMENT ON COLUMN b_wechat_invoice.invoice_number IS '发票号码';
COMMENT ON COLUMN b_wechat_invoice.invoice_date IS '开票日期';
COMMENT ON COLUMN b_wechat_invoice.check_code IS '校验码';
COMMENT ON COLUMN b_wechat_invoice.callback_data IS '微信回调数据';
COMMENT ON COLUMN b_wechat_invoice.failure_reason IS '失败原因';
COMMENT ON COLUMN b_wechat_invoice.created_at IS '创建时间';
COMMENT ON COLUMN b_wechat_invoice.updated_at IS '更新时间';
COMMENT ON COLUMN b_wechat_invoice.deleted IS '删除标记';

-- 创建索引
CREATE INDEX idx_b_wechat_invoice_invoice_no ON b_wechat_invoice (invoice_no);
CREATE INDEX idx_b_wechat_invoice_payment_record_id ON b_wechat_invoice (payment_record_id);
CREATE INDEX idx_b_wechat_invoice_out_trade_no ON b_wechat_invoice (out_trade_no);
CREATE INDEX idx_b_wechat_invoice_transaction_id ON b_wechat_invoice (transaction_id);
CREATE INDEX idx_b_wechat_invoice_user_id ON b_wechat_invoice (user_id);
CREATE INDEX idx_b_wechat_invoice_user_status ON b_wechat_invoice (user_id, status);
CREATE INDEX idx_b_wechat_invoice_status ON b_wechat_invoice (status);
CREATE INDEX idx_b_wechat_invoice_card_id ON b_wechat_invoice (card_id);
CREATE INDEX idx_b_wechat_invoice_created_at ON b_wechat_invoice (created_at);
CREATE INDEX idx_b_wechat_invoice_deleted ON b_wechat_invoice (deleted);
CREATE INDEX idx_b_wechat_invoice_user_created ON b_wechat_invoice (user_id, created_at);

-- 添加外键约束（如果需要）
-- ALTER TABLE b_wechat_invoice ADD CONSTRAINT fk_b_wechat_invoice_payment_record 
--     FOREIGN KEY (payment_record_id) REFERENCES b_payment_records(id);
-- ALTER TABLE b_wechat_invoice ADD CONSTRAINT fk_b_wechat_invoice_user 
--     FOREIGN KEY (user_id) REFERENCES sys_user(id);

-- 创建更新时间触发器
DROP TRIGGER IF EXISTS update_b_wechat_invoice_updated_at ON b_wechat_invoice;
CREATE TRIGGER update_b_wechat_invoice_updated_at
    BEFORE UPDATE ON b_wechat_invoice
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 创建用于查询待处理发票的索引
CREATE INDEX idx_b_wechat_invoice_pending_status ON b_wechat_invoice (status, created_at) 
    WHERE status IN ('PENDING', 'PROCESSING');

-- 创建用于统计的索引
CREATE INDEX idx_b_wechat_invoice_user_status_deleted ON b_wechat_invoice (user_id, status, deleted);

-- ------------------------------------------------------------
-- 为电子发票功能修改 b_billing_transactions 表结构
-- 创建时间：2025-07-08 10:05:26
-- 说明：为支持电子发票功能，在 b_billing_transactions 表中添加发票相关字段
-- ------------------------------------------------------------

-- 添加发票相关字段到 b_billing_transactions 表
ALTER TABLE b_billing_transactions ADD COLUMN IF NOT EXISTS invoice_eligible BOOLEAN DEFAULT TRUE;
ALTER TABLE b_billing_transactions ADD COLUMN IF NOT EXISTS invoice_applied BOOLEAN DEFAULT FALSE;
ALTER TABLE b_billing_transactions ADD COLUMN IF NOT EXISTS invoice_id BIGINT;
ALTER TABLE b_billing_transactions ADD COLUMN IF NOT EXISTS invoice_status VARCHAR(20);
ALTER TABLE b_billing_transactions ADD COLUMN IF NOT EXISTS invoice_applied_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE b_billing_transactions ADD COLUMN IF NOT EXISTS invoice_issued_at TIMESTAMP WITH TIME ZONE;

-- 添加字段注释
COMMENT ON COLUMN b_billing_transactions.invoice_eligible IS '是否可开发票';
COMMENT ON COLUMN b_billing_transactions.invoice_applied IS '是否已申请发票';
COMMENT ON COLUMN b_billing_transactions.invoice_id IS '关联的发票ID，关联b_wechat_invoice.id';
COMMENT ON COLUMN b_billing_transactions.invoice_status IS '发票状态：PENDING, PROCESSING, SUCCESS, FAILED, CANCELLED';
COMMENT ON COLUMN b_billing_transactions.invoice_applied_at IS '发票申请时间';
COMMENT ON COLUMN b_billing_transactions.invoice_issued_at IS '发票开具时间';

-- 创建发票相关索引
CREATE INDEX IF NOT EXISTS idx_b_billing_transactions_invoice_eligible ON b_billing_transactions (invoice_eligible);
CREATE INDEX IF NOT EXISTS idx_b_billing_transactions_invoice_applied ON b_billing_transactions (invoice_applied);
CREATE INDEX IF NOT EXISTS idx_b_billing_transactions_invoice_id ON b_billing_transactions (invoice_id);
CREATE INDEX IF NOT EXISTS idx_b_billing_transactions_invoice_status ON b_billing_transactions (invoice_status);
CREATE INDEX IF NOT EXISTS idx_b_billing_transactions_user_invoice ON b_billing_transactions (user_id, invoice_eligible, invoice_applied);

-- 添加外键约束（如果需要强制引用完整性）
-- ALTER TABLE b_billing_transactions ADD CONSTRAINT fk_b_billing_transactions_invoice_id 
--     FOREIGN KEY (invoice_id) REFERENCES b_wechat_invoice(id) ON DELETE SET NULL;

-- 创建检查约束
ALTER TABLE b_billing_transactions ADD CONSTRAINT chk_b_billing_transactions_invoice_status 
    CHECK (invoice_status IS NULL OR invoice_status IN ('PENDING', 'PROCESSING', 'SUCCESS', 'FAILED', 'CANCELLED'));

-- 创建逻辑约束：如果已申请发票，则必须有发票ID
ALTER TABLE b_billing_transactions ADD CONSTRAINT chk_b_billing_transactions_invoice_logic 
    CHECK (
        (invoice_applied = FALSE AND invoice_id IS NULL) OR 
        (invoice_applied = TRUE AND invoice_id IS NOT NULL)
    );

-- 更新现有记录的发票字段默认值
UPDATE b_billing_transactions 
SET 
    invoice_eligible = CASE 
        WHEN type = 'RECHARGE' AND status = 'SUCCESS' AND amount > 0 THEN TRUE 
        ELSE FALSE 
    END,
    invoice_applied = FALSE
WHERE invoice_eligible IS NULL;

-- 为发票功能添加统计视图（可选）
CREATE OR REPLACE VIEW v_billing_transactions_invoice_stats AS
SELECT 
    user_id,
    COUNT(*) as total_transactions,
    COUNT(CASE WHEN invoice_eligible = TRUE THEN 1 END) as eligible_transactions,
    COUNT(CASE WHEN invoice_applied = TRUE THEN 1 END) as applied_transactions,
    COUNT(CASE WHEN invoice_status = 'SUCCESS' THEN 1 END) as issued_transactions,
    SUM(CASE WHEN invoice_eligible = TRUE THEN amount ELSE 0 END) as eligible_amount,
    SUM(CASE WHEN invoice_applied = TRUE THEN amount ELSE 0 END) as applied_amount,
    SUM(CASE WHEN invoice_status = 'SUCCESS' THEN amount ELSE 0 END) as issued_amount
FROM b_billing_transactions
WHERE type = 'RECHARGE' AND status = 'SUCCESS'
GROUP BY user_id;

-- 添加视图注释
COMMENT ON VIEW v_billing_transactions_invoice_stats IS '用户发票统计视图';

-- ===================================================================
-- 自有发票管理模块表结构
-- ===================================================================

-- 自有发票管理表
DROP TABLE IF EXISTS b_billing_invoice CASCADE;

CREATE TABLE b_billing_invoice (
    id BIGSERIAL PRIMARY KEY,                               -- 主键ID
    invoice_no VARCHAR(100) NOT NULL UNIQUE,                -- 发票申请单号
    user_id BIGINT NOT NULL,                                -- 申请用户ID

    -- 发票基本信息
    invoice_title VARCHAR(200) NOT NULL,                    -- 发票抬头
    tax_number VARCHAR(50),                                 -- 纳税人识别号
    invoice_type VARCHAR(20) NOT NULL DEFAULT 'NORMAL',     -- 发票类型：NORMAL-普通发票，SPECIAL-专用发票

    -- 企业信息（专用发票必填）
    company_address VARCHAR(200),                           -- 企业注册地址
    company_phone VARCHAR(20),                              -- 企业注册电话
    bank_name VARCHAR(100),                                 -- 开户银行
    bank_account VARCHAR(50),                               -- 银行账号

    -- 收件人信息
    recipient_name VARCHAR(100),                            -- 收件人姓名
    recipient_phone VARCHAR(20),                            -- 收件人手机号
    recipient_email VARCHAR(100),                           -- 收件人邮箱
    recipient_address VARCHAR(300),                         -- 收件人地址

    -- 发票状态和金额
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',          -- 发票状态：PENDING-开票中，COMPLETED-已完成，CANCELLED-已取消
    total_amount DECIMAL(15,2) NOT NULL,                    -- 发票总金额

    -- 发票文件信息
    invoice_file_url VARCHAR(500),                          -- 发票文件URL
    invoice_file_name VARCHAR(200),                         -- 发票文件名
    invoice_file_size BIGINT,                               -- 发票文件大小（字节）

    -- 开票信息
    invoice_code VARCHAR(50),                               -- 发票代码
    invoice_number VARCHAR(50),                             -- 发票号码
    invoice_date DATE,                                      -- 开票日期

    -- 申请和处理信息
    application_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(), -- 申请时间
    processed_time TIMESTAMP WITH TIME ZONE,                -- 处理完成时间
    processor_id BIGINT,                                    -- 处理人ID（财务人员）

    -- 备注和说明
    applicant_remarks TEXT,                                 -- 申请人备注
    processor_remarks TEXT,                                 -- 处理人备注

    -- 系统字段
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),      -- 创建时间
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),      -- 更新时间
    deleted BOOLEAN NOT NULL DEFAULT FALSE                  -- 软删除标记
);

-- 发票交易关联表
DROP TABLE IF EXISTS b_billing_invoice_transactions CASCADE;

CREATE TABLE b_billing_invoice_transactions (
    id BIGSERIAL PRIMARY KEY,                               -- 主键ID
    invoice_id BIGINT NOT NULL,                             -- 发票ID
    transaction_id BIGINT NOT NULL,                         -- 交易记录ID
    transaction_amount DECIMAL(15,2) NOT NULL,              -- 该笔交易在发票中的金额
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()       -- 创建时间
);

-- 添加表和字段注释
COMMENT ON TABLE b_billing_invoice IS '自有发票管理表';
COMMENT ON COLUMN b_billing_invoice.id IS '主键ID';
COMMENT ON COLUMN b_billing_invoice.invoice_no IS '发票申请单号，格式：INV{timestamp}{userId}';
COMMENT ON COLUMN b_billing_invoice.user_id IS '申请用户ID，关联sys_user.id';
COMMENT ON COLUMN b_billing_invoice.invoice_title IS '发票抬头';
COMMENT ON COLUMN b_billing_invoice.tax_number IS '纳税人识别号';
COMMENT ON COLUMN b_billing_invoice.invoice_type IS '发票类型：NORMAL-普通发票，SPECIAL-专用发票';
COMMENT ON COLUMN b_billing_invoice.status IS '发票状态：PENDING-开票中，COMPLETED-已完成，CANCELLED-已取消';
COMMENT ON COLUMN b_billing_invoice.total_amount IS '发票总金额';

COMMENT ON TABLE b_billing_invoice_transactions IS '发票交易关联表';
COMMENT ON COLUMN b_billing_invoice_transactions.invoice_id IS '发票ID，关联b_billing_invoice.id';
COMMENT ON COLUMN b_billing_invoice_transactions.transaction_id IS '交易记录ID，关联b_billing_transactions.id';

-- 创建索引
CREATE INDEX idx_b_billing_invoice_user_id ON b_billing_invoice (user_id);
CREATE INDEX idx_b_billing_invoice_status ON b_billing_invoice (status);
CREATE INDEX idx_b_billing_invoice_invoice_no ON b_billing_invoice (invoice_no);
CREATE INDEX idx_b_billing_invoice_user_status ON b_billing_invoice (user_id, status);
CREATE INDEX idx_b_billing_invoice_created_at ON b_billing_invoice (created_at);
CREATE INDEX idx_b_billing_invoice_deleted ON b_billing_invoice (deleted);

CREATE INDEX idx_b_billing_invoice_transactions_invoice_id ON b_billing_invoice_transactions (invoice_id);
CREATE INDEX idx_b_billing_invoice_transactions_transaction_id ON b_billing_invoice_transactions (transaction_id);
CREATE UNIQUE INDEX uk_b_billing_invoice_transactions_unique ON b_billing_invoice_transactions (invoice_id, transaction_id);

-- 创建更新时间触发器
CREATE TRIGGER update_b_billing_invoice_updated_at
    BEFORE UPDATE ON b_billing_invoice
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 扩展计费交易记录表，支持自有发票功能
ALTER TABLE b_billing_transactions ADD COLUMN IF NOT EXISTS self_invoice_id BIGINT;
ALTER TABLE b_billing_transactions ADD COLUMN IF NOT EXISTS self_invoice_status VARCHAR(20);

-- 添加字段注释
COMMENT ON COLUMN b_billing_transactions.self_invoice_id IS '关联的自有发票ID';
COMMENT ON COLUMN b_billing_transactions.self_invoice_status IS '自有发票状态：NULL-未申请，PENDING-开票中，COMPLETED-已完成，CANCELLED-已取消';

-- 创建索引
CREATE INDEX idx_b_billing_transactions_self_invoice_id ON b_billing_transactions (self_invoice_id);
CREATE INDEX idx_b_billing_transactions_self_invoice_status ON b_billing_transactions (self_invoice_status);

-- 移除 sys_tenant 表 domain 字段的唯一约束
-- 执行时间: 2025-01-17
-- 描述: 由于 domain 字段允许为空，且业务上可能存在多个租户没有域名的情况，
--       因此移除该字段的唯一约束以避免空字符串冲突问题
-- 删除 domain 字段的唯一索引
DROP INDEX IF EXISTS uk_domain;

-- ------------------------------------------------------------
-- 行业租户定价表
-- ------------------------------------------------------------
-- Drop table if it exists
DROP TABLE IF EXISTS industry_tenant_pricing CASCADE;

-- Create industry tenant pricing table
CREATE TABLE industry_tenant_pricing (
    id BIGSERIAL PRIMARY KEY,
    industry_type VARCHAR(32) NOT NULL, -- 行业类型：MEDICAL_BEAUTY、REAL_ESTATE等
    tenant_type VARCHAR(32) NOT NULL, -- 租户类型：ORGANIZATION、ORGANIZATION_GROUP、MANUFACTURER、CONSUMER
    package_name VARCHAR(100) NOT NULL, -- 套餐名称
    package_description TEXT, -- 套餐描述
    price DECIMAL(10,2), -- 当前价格
    original_price DECIMAL(10,2), -- 原价
    discount_text VARCHAR(50), -- 折扣文本，如"5折"
    discount_end_date DATE, -- 折扣结束日期
    period VARCHAR(20), -- 计费周期，如"/ 年"
    is_available BOOLEAN DEFAULT true, -- 是否可用
    is_popular BOOLEAN DEFAULT false, -- 是否推荐
    sort_order INTEGER DEFAULT 0, -- 排序
    features JSONB, -- 功能特性列表
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX idx_industry_tenant_pricing_type ON industry_tenant_pricing (industry_type, tenant_type);
CREATE INDEX idx_industry_tenant_pricing_available ON industry_tenant_pricing (is_available);
CREATE INDEX idx_industry_tenant_pricing_sort ON industry_tenant_pricing (sort_order);

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS update_industry_tenant_pricing_updated_at ON industry_tenant_pricing;
CREATE TRIGGER update_industry_tenant_pricing_updated_at
    BEFORE UPDATE ON industry_tenant_pricing
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add table comments
COMMENT ON TABLE industry_tenant_pricing IS '行业租户定价表';
COMMENT ON COLUMN industry_tenant_pricing.id IS '主键ID';
COMMENT ON COLUMN industry_tenant_pricing.industry_type IS '行业类型：MEDICAL_BEAUTY（医美）、REAL_ESTATE（地产）';
COMMENT ON COLUMN industry_tenant_pricing.tenant_type IS '租户类型：ORGANIZATION（机构）、ORGANIZATION_GROUP（机构集团）、MANUFACTURER（厂家）、CONSUMER（消费者）';
COMMENT ON COLUMN industry_tenant_pricing.package_name IS '套餐名称';
COMMENT ON COLUMN industry_tenant_pricing.package_description IS '套餐描述';
COMMENT ON COLUMN industry_tenant_pricing.price IS '当前价格';
COMMENT ON COLUMN industry_tenant_pricing.original_price IS '原价';
COMMENT ON COLUMN industry_tenant_pricing.discount_text IS '折扣文本，如"5折"';
COMMENT ON COLUMN industry_tenant_pricing.discount_end_date IS '折扣结束日期';
COMMENT ON COLUMN industry_tenant_pricing.period IS '计费周期，如"/ 年"';
COMMENT ON COLUMN industry_tenant_pricing.is_available IS '是否可用';
COMMENT ON COLUMN industry_tenant_pricing.is_popular IS '是否推荐';
COMMENT ON COLUMN industry_tenant_pricing.sort_order IS '排序';
COMMENT ON COLUMN industry_tenant_pricing.features IS '功能特性列表';
COMMENT ON COLUMN industry_tenant_pricing.created_at IS '创建时间';
COMMENT ON COLUMN industry_tenant_pricing.updated_at IS '更新时间';

-- Insert sample data for medical beauty organization
INSERT INTO industry_tenant_pricing (
    industry_type, tenant_type, package_name, package_description, 
    price, original_price, discount_text, discount_end_date, period,
    is_available, is_popular, sort_order, features
) VALUES 
(
    'MEDICAL_BEAUTY', 'ORGANIZATION', '基础版', '智能版标准化消费者体验研究',
    1.00, 59999.00, '5折', '2025-08-31', '/ 年',
    true, true, 1,
    '[
        {"name": "消费者体验研究问卷", "description": "专业定制问卷服务 - 专业定制问卷服务，基于李克特5分量表精准评估产品与服务核心维度，真实反馈客户需求", "included": true},
        {"name": "数据采集", "description": "多渠道智能数据收集 - 支持一店一码、短信链接等多种线上填写方式，数据实时同步云端，杜绝手工录入与信息遗漏", "included": true},
        {"name": "数据质量控制", "description": "数据清洗与验证服务 - 专业数据分析团队对原始数据进行逻辑校验、完整性检查及异常值处理，确保最终分析结果精准可靠", "included": true},
        {"name": "数据分析", "description": "深度洞察分析服务 - 自研数据流计算引擎，实现高效低成本数据处理，支持多维度交叉分析及NPS/忠诚度等专业模型评估，快速识别服务短板", "included": true},
        {"name": "数据动态看板", "description": "实时可视化平台 - 交互看板实时展示调研进度、回访得分、客户VOC数据，多维度筛选分析，助力机构动态掌握消费者评价", "included": true},
        {"name": "回访数据导出", "description": "灵活数据导出功能 - 支持按需导出Excel格式的原始数据，便于二次分析或纳入其他管理系统", "included": true},
        {"name": "标准分析报告", "description": "专业分析报告输出 - 基于收集数据生成可视化分析报告，涵盖满意度分析、改进建议等关键洞察", "included": true}
    ]'::jsonb
),
(
    'MEDICAL_BEAUTY', 'ORGANIZATION', '标准版', '高级版个性化消费者洞察分析',
    NULL, NULL, NULL, NULL, NULL,
    false, false, 2,
    '[
        {"name": "基础版所有功能", "description": "包含基础版本中的所有功能", "included": true},
        {"name": "关键指标异常自动预警", "description": "每当有不满意的客户完成答卷，通过邮件接收通知，及时采取补救措施，挽回客户关系，降低客户流失风险", "included": true},
        {"name": "分析结果智能输出", "description": "基于调研数据智能输出调研结果月度统计报表", "included": true},
        {"name": "行业数据查询", "description": "快捷查询行业数据库，精准定位本品在竞争市场中的坐标位次和竞争力水平，了解行业基准数据，找到提升方向", "included": true},
        {"name": "高级分析", "description": "增加文本情感分析、消费者行为和偏好、消费者满意度等深度分析，基于消费行为和满意度数据，将客户划分类型", "included": true},
        {"name": "定制报告服务", "description": "根据机构特定需求制作个性化分析报告，满足不同层级管理者的决策需求，提供更有针对性的数据支持", "included": true},
        {"name": "报告解读", "description": "专业咨询顾问进行数据解读与数据应用指导，帮助您深入理解数据背后的含义，制定优化策略", "included": true}
    ]'::jsonb
);


-- ------------------------------------------------------------
-- AI Agent 系统数据库表结构
-- 创建时间: 2025-08-03
-- 说明: Agent 数据库基础架构，支持 AI Agent 对话系统
-- ------------------------------------------------------------

-- Agent 定义表
-- 存储 AI Agent 的基本信息和配置
DROP TABLE IF EXISTS agent_definitions CASCADE;

CREATE TABLE agent_definitions (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    capabilities JSONB,
    system_prompt TEXT,
    configuration JSONB,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    category VARCHAR(50),
    priority INTEGER DEFAULT 50,
    timeout_seconds INTEGER DEFAULT 30,
    max_retries INTEGER DEFAULT 3,
    created_by BIGINT NOT NULL REFERENCES sys_user(id),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_agent_definitions_status ON agent_definitions(status);
CREATE INDEX idx_agent_definitions_created_by ON agent_definitions(created_by);
CREATE INDEX idx_agent_definitions_category ON agent_definitions(category);
CREATE INDEX idx_agent_definitions_priority ON agent_definitions(priority DESC);

-- 添加表和字段注释
COMMENT ON TABLE agent_definitions IS 'AI Agent 定义表';
COMMENT ON COLUMN agent_definitions.id IS 'Agent ID';
COMMENT ON COLUMN agent_definitions.name IS 'Agent 名称';
COMMENT ON COLUMN agent_definitions.description IS 'Agent 描述';
COMMENT ON COLUMN agent_definitions.capabilities IS 'Agent 能力配置 (JSON)';
COMMENT ON COLUMN agent_definitions.system_prompt IS '系统提示词';
COMMENT ON COLUMN agent_definitions.configuration IS '高级配置参数 (JSON)';
COMMENT ON COLUMN agent_definitions.status IS '状态: ACTIVE, INACTIVE, DRAFT';
COMMENT ON COLUMN agent_definitions.category IS 'Agent 类别: ANALYSIS, PROCESSING, INFERENCE, REPORTING, VISUALIZATION';
COMMENT ON COLUMN agent_definitions.priority IS '优先级 (0-100)';
COMMENT ON COLUMN agent_definitions.timeout_seconds IS '执行超时时间 (秒)';
COMMENT ON COLUMN agent_definitions.max_retries IS '最大重试次数';
COMMENT ON COLUMN agent_definitions.created_by IS '创建人 ID';
COMMENT ON COLUMN agent_definitions.created_at IS '创建时间';
COMMENT ON COLUMN agent_definitions.updated_at IS '更新时间';

-- ------------------------------------------------------------

-- AI驱动链路执行记录表
-- 支持新的AI Agent链式工作流架构
CREATE TABLE chain_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id VARCHAR(100) NOT NULL,
    user_id BIGINT REFERENCES sys_user(id),
    user_input TEXT NOT NULL,
    intent_analysis_result TEXT,
    execution_plan TEXT, -- JSON格式的执行计划
    total_steps INTEGER DEFAULT 0,
    completed_steps INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'RUNNING',
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    total_duration_ms BIGINT,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_chain_executions_conversation_id ON chain_executions(conversation_id);
CREATE INDEX idx_chain_executions_user_id ON chain_executions(user_id);
CREATE INDEX idx_chain_executions_status ON chain_executions(status);
CREATE INDEX idx_chain_executions_created_at ON chain_executions(created_at DESC);

-- 添加表和字段注释
COMMENT ON TABLE chain_executions IS 'AI驱动链路执行记录表';
COMMENT ON COLUMN chain_executions.id IS '链路执行ID';
COMMENT ON COLUMN chain_executions.conversation_id IS '会话ID';
COMMENT ON COLUMN chain_executions.user_id IS '用户ID';
COMMENT ON COLUMN chain_executions.user_input IS '用户原始输入';
COMMENT ON COLUMN chain_executions.intent_analysis_result IS '意图分析结果';
COMMENT ON COLUMN chain_executions.execution_plan IS '执行计划 (JSON)';
COMMENT ON COLUMN chain_executions.total_steps IS '总步骤数';
COMMENT ON COLUMN chain_executions.completed_steps IS '已完成步骤数';
COMMENT ON COLUMN chain_executions.status IS '执行状态';
COMMENT ON COLUMN chain_executions.start_time IS '开始时间';
COMMENT ON COLUMN chain_executions.end_time IS '结束时间';
COMMENT ON COLUMN chain_executions.total_duration_ms IS '总执行时长(毫秒)';

-- ------------------------------------------------------------

-- Agent 执行记录表（重构后）
-- 存储单个Agent的执行历史和状态，移除对旧agent_flows的依赖
DROP TABLE IF EXISTS agent_executions CASCADE;

CREATE TABLE agent_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID REFERENCES conversations(id),
    agent_id BIGINT REFERENCES agent_definitions(id),
    user_id BIGINT REFERENCES sys_user(id),
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    input_data JSONB,
    output_data JSONB,
    error_message TEXT,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    duration_ms BIGINT,
    total_tokens INTEGER DEFAULT 0,
    total_cost DECIMAL(10,4) DEFAULT 0.00,
    model_name VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_agent_executions_conversation_id ON agent_executions(conversation_id);
CREATE INDEX idx_agent_executions_agent_id ON agent_executions(agent_id);
CREATE INDEX idx_agent_executions_user_id ON agent_executions(user_id);
CREATE INDEX idx_agent_executions_status ON agent_executions(status);
CREATE INDEX idx_agent_executions_created_at ON agent_executions(created_at DESC);

-- 添加表和字段注释
COMMENT ON TABLE agent_executions IS 'Agent 执行记录表 (重构后)';
COMMENT ON COLUMN agent_executions.id IS '执行记录 ID';
COMMENT ON COLUMN agent_executions.conversation_id IS '会话 ID';
COMMENT ON COLUMN agent_executions.agent_id IS 'Agent ID';
COMMENT ON COLUMN agent_executions.user_id IS '用户 ID';
COMMENT ON COLUMN agent_executions.status IS '执行状态: PENDING, RUNNING, COMPLETED, FAILED';
COMMENT ON COLUMN agent_executions.input_data IS '输入数据 (JSON)';
COMMENT ON COLUMN agent_executions.output_data IS '输出数据 (JSON)';
COMMENT ON COLUMN agent_executions.error_message IS '错误信息';
COMMENT ON COLUMN agent_executions.started_at IS '开始时间';
COMMENT ON COLUMN agent_executions.completed_at IS '完成时间';
COMMENT ON COLUMN agent_executions.duration_ms IS '执行时长 (毫秒)';
COMMENT ON COLUMN agent_executions.total_tokens IS '总 Token 数';
COMMENT ON COLUMN agent_executions.total_cost IS '总成本';
COMMENT ON COLUMN agent_executions.model_name IS '使用的模型名称';
COMMENT ON COLUMN agent_executions.created_at IS '创建时间';

-- ------------------------------------------------------------

-- Agent步骤执行记录表（AI驱动链路架构）
-- 存储链路中每个Agent步骤的详细执行信息
CREATE TABLE agent_step_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    chain_execution_id UUID REFERENCES chain_executions(id) ON DELETE CASCADE,
    step_id VARCHAR(50) NOT NULL,
    agent_id BIGINT REFERENCES agent_definitions(id),
    agent_name VARCHAR(100),
    step_order INTEGER,
    input_data TEXT, -- JSON格式
    output_data TEXT, -- JSON格式
    thinking_process TEXT, -- JSON格式的思维链
    status VARCHAR(20) DEFAULT 'PENDING',
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    duration_ms BIGINT,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_agent_step_executions_chain_id ON agent_step_executions(chain_execution_id);
CREATE INDEX idx_agent_step_executions_agent_id ON agent_step_executions(agent_id);
CREATE INDEX idx_agent_step_executions_status ON agent_step_executions(status);
CREATE INDEX idx_agent_step_executions_step_order ON agent_step_executions(chain_execution_id, step_order);

-- 添加表和字段注释
COMMENT ON TABLE agent_step_executions IS 'Agent步骤执行记录表';
COMMENT ON COLUMN agent_step_executions.id IS '步骤执行ID';
COMMENT ON COLUMN agent_step_executions.chain_execution_id IS '所属链路执行ID';
COMMENT ON COLUMN agent_step_executions.step_id IS '步骤ID';
COMMENT ON COLUMN agent_step_executions.agent_id IS 'Agent ID';
COMMENT ON COLUMN agent_step_executions.agent_name IS 'Agent名称';
COMMENT ON COLUMN agent_step_executions.step_order IS '步骤顺序';
COMMENT ON COLUMN agent_step_executions.input_data IS '输入数据';
COMMENT ON COLUMN agent_step_executions.output_data IS '输出数据';
COMMENT ON COLUMN agent_step_executions.thinking_process IS '思维过程 (JSON)';
COMMENT ON COLUMN agent_step_executions.status IS '步骤状态';
COMMENT ON COLUMN agent_step_executions.start_time IS '步骤开始时间';
COMMENT ON COLUMN agent_step_executions.end_time IS '步骤结束时间';
COMMENT ON COLUMN agent_step_executions.duration_ms IS '步骤执行时长(毫秒)';

-- ------------------------------------------------------------

-- 创建更新时间触发器函数（专门为agent表使用updated_at字段）

-- 确保sys_user等表使用的update_updated_time_column函数正确设置updated_time字段
CREATE OR REPLACE FUNCTION update_updated_time_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要自动更新时间的表创建触发器
CREATE TRIGGER update_agent_definitions_updated_time 
    BEFORE UPDATE ON agent_definitions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ------------------------------------------------------------

-- AI Agent链式工作流：预设Agent定义（支持新架构）
INSERT INTO agent_definitions (name, description, capabilities, system_prompt, status, category, agent_type, agent_role, is_system_agent, created_by) VALUES
('意图识别Agent', '智能分析用户需求并构建执行链路的核心Agent', 
 '["intent_analysis", "chain_planning", "agent_routing", "nlp"]'::jsonb,
 '你是一个专业的意图识别和链路规划专家。你的主要职责是：

1. 深度分析用户输入，理解真实需求和意图
2. 根据用户需求，从可用Agent列表中选择合适的Agent组合
3. 构建最优的执行链路，确保Agent间的数据流合理
4. 返回标准化的链路执行指令

可用Agent列表：
- 数据查询Agent：数据库查询、信息检索、数据提取、条件筛选
- 数据分析Agent：统计计算、趋势分析、对比分析、深度挖掘
- 推理Agent：逻辑推理、预测建模、决策支持、策略建议
- 图表生成Agent：数据可视化、图表设计、交互展示
- 报告撰写Agent：文档撰写、报告生成、内容整合、格式化输出
- 结果检查审核Agent：质量控制、数据验证、逻辑审核、合规检查
- 通用Agent：通用问答、简单任务、引导交互、兜底处理

请严格按照以下JSON格式返回：
{
  "response_type": "chain_instruction",
  "thinking_process": {
    "analysis": "对用户需求的分析...",
    "agent_selection": "选择Agent的理由...",
    "chain_design": "链路设计思路...",
    "confidence": 0.95
  },
  "chain_instruction": {
    "analysisResult": "需求分析总结",
    "agentChain": [
      {
        "stepId": "step_1",
        "agentName": "数据查询Agent",
        "order": 1,
        "description": "查询相关数据",
        "parameters": {},
        "inputMapping": "userMessage",
        "outputMapping": "data_output",
        "isCritical": true
      }
    ],
    "executionMode": "sequential",
    "needUserConfirm": false,
    "confidence": 0.95
  }
}',
 'ACTIVE', 'PROCESSING', 'SYSTEM', 'INTENT_RECOGNITION', true, 1),

('数据查询Agent', '专业的数据检索和查询处理Agent',
 '["database_query", "data_extraction", "information_retrieval", "condition_filtering"]'::jsonb,
 '你是一个专业的数据查询专家。根据用户需求执行数据检索和查询任务，包括：

1. 理解查询需求和条件
2. 构建合适的查询逻辑
3. 提取和整理查询结果
4. 进行数据质量检查

请按照以下JSON格式返回：
{
  "response_type": "data_output",
  "thinking_process": {
    "analysis": "查询需求分析...",
    "approach": "查询方法和策略...",
    "confidence": 0.9
  },
  "content": "查询结果的文字描述",
  "data_summary": {
    "total_records": 100,
    "query_conditions": "查询条件",
    "data_fields": ["字段1", "字段2"],
    "sample_data": "样本数据展示"
  }
}',
 'ACTIVE', 'ANALYSIS', 'BUSINESS', 'CUSTOM', false, 1),

('数据分析Agent', '深度数据分析和洞察挖掘Agent',
 '["statistical_analysis", "trend_analysis", "pattern_recognition", "data_mining"]'::jsonb,
 '你是一个专业的数据分析师。基于提供的数据进行深度分析，包括：

1. 描述性统计分析
2. 趋势和模式识别
3. 异常值检测和分析
4. 关键洞察提取

请按照以下JSON格式返回：
{
  "response_type": "content",
  "thinking_process": {
    "analysis": "数据特征分析...",
    "approach": "分析方法选择...",
    "confidence": 0.9
  },
  "content": "详细的数据分析结果和洞察"
}',
 'ACTIVE', 'ANALYSIS', 'BUSINESS', 'CUSTOM', false, 1),

('通用Agent', '处理通用查询和兜底任务的多功能Agent',
 '["general_qa", "task_guidance", "fallback_handling", "user_assistance"]'::jsonb,
 '你是一个友好的通用助手。能够处理各种常见问题和任务，包括：

1. 回答一般性问题
2. 提供使用指导
3. 处理无法明确分类的请求
4. 与用户进行友好交互

请用自然、专业的语言回复用户，不需要特定的JSON格式。',
 'ACTIVE', 'GENERAL', 'BUSINESS', 'GENERAL_ASSISTANT', false, 1);

-- 全AI Agent架构重构：补充agent_definitions表缺失字段 (2025-08-05)
-- 说明：在全AI Agent架构重构中，AgentDefinition实体类新增了以下字段，需要同步到数据库
ALTER TABLE agent_definitions ADD COLUMN IF NOT EXISTS agent_type VARCHAR(20) NOT NULL DEFAULT 'BUSINESS';
ALTER TABLE agent_definitions ADD COLUMN IF NOT EXISTS agent_role VARCHAR(30);
ALTER TABLE agent_definitions ADD COLUMN IF NOT EXISTS is_system_agent BOOLEAN NOT NULL DEFAULT false;

-- 添加相关索引以优化查询性能
CREATE INDEX IF NOT EXISTS idx_agent_definitions_agent_type ON agent_definitions(agent_type);
CREATE INDEX IF NOT EXISTS idx_agent_definitions_agent_role ON agent_definitions(agent_role);
CREATE INDEX IF NOT EXISTS idx_agent_definitions_system_agent ON agent_definitions(is_system_agent);

-- 添加字段注释说明
COMMENT ON COLUMN agent_definitions.agent_type IS 'Agent类型: SYSTEM-系统内置, BUSINESS-业务, TOOL-工具调用, WORKFLOW-工作流';
COMMENT ON COLUMN agent_definitions.agent_role IS 'Agent角色: INTENT_RECOGNITION-意图识别, ORCHESTRATION-编排, RESULT_AGGREGATION-结果聚合, RESPONSE_GENERATION-响应生成, TOOL_INVOCATION-工具调用, GENERAL_ASSISTANT-通用助手, CUSTOM-自定义';
COMMENT ON COLUMN agent_definitions.is_system_agent IS '是否为系统内置Agent';

-- 显示AI Agent链式工作流架构创建完成信息
SELECT 'AI Agent链式工作流架构创建完成!' as message;
SELECT 'Agent定义数量: ' || count(*) as agent_count FROM agent_definitions;
SELECT '链路执行表: chain_executions 已创建' as chain_table_status;
SELECT 'Agent步骤执行表: agent_step_executions 已创建' as step_table_status;