-- PostgreSQL 系统数据库脚本
-- 转换自 MySQL 版本，适配 PostgreSQL 语法和特性

-- 创建通用的更新时间戳触发器函数
CREATE OR REPLACE FUNCTION update_updated_time_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- <editor-fold desc="租户">
-- ----------------------------
-- 租户基础信息表
-- ----------------------------
DROP TABLE IF EXISTS sys_tenant CASCADE;
CREATE TABLE sys_tenant
(
    id            BIGSERIAL    NOT NULL,
    tenant_code   VARCHAR(64)  NOT NULL,
    tenant_name   VARCHAR(128) NOT NULL,
    domain        VARCHAR(128),
    status        SMALLINT     NOT NULL DEFAULT 1,
    package_type  VARCHAR(32)  NOT NULL,
    expire_time   TIMESTAMP,
    contact_name  VARCHAR(64),
    contact_phone VARCHAR(32),
    contact_email VARCHAR(128),
    logo_url      VARCHAR(512),
    description   TEXT,
    created_time  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by    BIGINT,
    updated_by    BIGINT,
    deleted       SMALLINT     NOT NULL DEFAULT 0,
    PRIMARY KEY (id)
);

-- 添加表注释
COMMENT ON TABLE sys_tenant IS '租户基础信息表';
COMMENT ON COLUMN sys_tenant.id IS '租户ID';
COMMENT ON COLUMN sys_tenant.tenant_code IS '租户编码';
COMMENT ON COLUMN sys_tenant.tenant_name IS '租户名称';
COMMENT ON COLUMN sys_tenant.domain IS '租户域名';
COMMENT ON COLUMN sys_tenant.status IS '状态：0-禁用，1-启用，2-过期';
COMMENT ON COLUMN sys_tenant.package_type IS '套餐类型：basic,standard,premium,enterprise';
COMMENT ON COLUMN sys_tenant.expire_time IS '到期时间';
COMMENT ON COLUMN sys_tenant.contact_name IS '联系人姓名';
COMMENT ON COLUMN sys_tenant.contact_phone IS '联系人电话';
COMMENT ON COLUMN sys_tenant.contact_email IS '联系人邮箱';
COMMENT ON COLUMN sys_tenant.logo_url IS 'Logo地址';
COMMENT ON COLUMN sys_tenant.description IS '租户描述';
COMMENT ON COLUMN sys_tenant.created_time IS '创建时间';
COMMENT ON COLUMN sys_tenant.updated_time IS '更新时间';
COMMENT ON COLUMN sys_tenant.created_by IS '创建人';
COMMENT ON COLUMN sys_tenant.updated_by IS '更新人';
COMMENT ON COLUMN sys_tenant.deleted IS '删除标记：0-未删除，1-已删除';

-- 创建索引
CREATE UNIQUE INDEX uk_tenant_code ON sys_tenant (tenant_code);
CREATE UNIQUE INDEX uk_domain ON sys_tenant (domain);
CREATE INDEX idx_status ON sys_tenant (status);
CREATE INDEX idx_package_type ON sys_tenant (package_type);
CREATE INDEX idx_expire_time ON sys_tenant (expire_time);

-- 创建更新时间戳触发器
CREATE TRIGGER update_sys_tenant_updated_time BEFORE UPDATE ON sys_tenant FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();

ALTER TABLE sys_tenant ALTER COLUMN package_type DROP NOT NULL;

-- 为租户表添加encrypt字段的ALTER语句
-- 执行时间：请在执行前备份数据库
-- 1. 添加encrypt字段（先允许NULL，后续填充数据后再设置NOT NULL约束）
ALTER TABLE sys_tenant ADD COLUMN encrypt VARCHAR(12);
-- 2. 添加字段注释
COMMENT ON COLUMN sys_tenant.encrypt IS '租户唯一码（12位字符串）';
-- 3. 创建唯一索引（在填充数据后执行）
CREATE UNIQUE INDEX idx_sys_tenant_encrypt ON sys_tenant(encrypt);
-- 4. 设置NOT NULL约束（在填充数据后执行）
ALTER TABLE sys_tenant ALTER COLUMN encrypt SET NOT NULL;

-- ----------------------------
-- 租户配置表
-- ----------------------------
DROP TABLE IF EXISTS sys_tenant_config CASCADE;
CREATE TABLE sys_tenant_config
(
    id           BIGSERIAL    NOT NULL,
    tenant_id    BIGINT       NOT NULL,
    config_key   VARCHAR(128) NOT NULL,
    config_value TEXT,
    config_type  VARCHAR(32)  NOT NULL,
    description  VARCHAR(512),
    created_time TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by   BIGINT,
    updated_by   BIGINT,
    PRIMARY KEY (id)
);

-- 添加表注释
COMMENT ON TABLE sys_tenant_config IS '租户配置表';
COMMENT ON COLUMN sys_tenant_config.id IS '配置ID';
COMMENT ON COLUMN sys_tenant_config.tenant_id IS '租户ID';
COMMENT ON COLUMN sys_tenant_config.config_key IS '配置键';
COMMENT ON COLUMN sys_tenant_config.config_value IS '配置值';
COMMENT ON COLUMN sys_tenant_config.config_type IS '配置类型：system,business,ui';
COMMENT ON COLUMN sys_tenant_config.description IS '配置描述';
COMMENT ON COLUMN sys_tenant_config.created_time IS '创建时间';
COMMENT ON COLUMN sys_tenant_config.updated_time IS '更新时间';
COMMENT ON COLUMN sys_tenant_config.created_by IS '创建人';
COMMENT ON COLUMN sys_tenant_config.updated_by IS '更新人';

-- 创建索引
CREATE UNIQUE INDEX uk_tenant_config ON sys_tenant_config (tenant_id, config_key);
CREATE INDEX idx_tenant_config_tenant_id ON sys_tenant_config (tenant_id);
CREATE INDEX idx_config_type ON sys_tenant_config (config_type);

-- 创建更新时间戳触发器
CREATE TRIGGER update_sys_tenant_config_updated_time BEFORE UPDATE ON sys_tenant_config FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();

-- ----------------------------
-- 套餐定义表
-- ----------------------------
DROP TABLE IF EXISTS sys_package_definition CASCADE;
CREATE TABLE sys_package_definition
(
    id            BIGSERIAL      NOT NULL,
    package_code  VARCHAR(64)    NOT NULL,
    package_name  VARCHAR(128)   NOT NULL,
    package_type  VARCHAR(32)    NOT NULL,
    price         NUMERIC(10, 2) NOT NULL,
    billing_cycle VARCHAR(32)    NOT NULL,
    user_limit    INTEGER,
    storage_limit BIGINT,
    feature_list  JSONB,
    status        SMALLINT       NOT NULL DEFAULT 1,
    sort_order    INTEGER                 DEFAULT 0,
    created_time  TIMESTAMP      NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time  TIMESTAMP      NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by    BIGINT,
    updated_by    BIGINT,
    PRIMARY KEY (id)
);

-- 添加表注释
COMMENT ON TABLE sys_package_definition IS '套餐定义表';
COMMENT ON COLUMN sys_package_definition.id IS '套餐ID';
COMMENT ON COLUMN sys_package_definition.package_code IS '套餐编码';
COMMENT ON COLUMN sys_package_definition.package_name IS '套餐名称';
COMMENT ON COLUMN sys_package_definition.package_type IS '套餐类型';
COMMENT ON COLUMN sys_package_definition.price IS '价格';
COMMENT ON COLUMN sys_package_definition.billing_cycle IS '计费周期：monthly,yearly';
COMMENT ON COLUMN sys_package_definition.user_limit IS '用户数限制';
COMMENT ON COLUMN sys_package_definition.storage_limit IS '存储限制(MB)';
COMMENT ON COLUMN sys_package_definition.feature_list IS '功能列表';
COMMENT ON COLUMN sys_package_definition.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN sys_package_definition.sort_order IS '排序';
COMMENT ON COLUMN sys_package_definition.created_time IS '创建时间';
COMMENT ON COLUMN sys_package_definition.updated_time IS '更新时间';
COMMENT ON COLUMN sys_package_definition.created_by IS '创建人';
COMMENT ON COLUMN sys_package_definition.updated_by IS '更新人';

-- 创建索引
CREATE UNIQUE INDEX uk_package_code ON sys_package_definition (package_code);
CREATE INDEX idx_package_definition_package_type ON sys_package_definition (package_type);
CREATE INDEX idx_package_definition_status ON sys_package_definition (status);

-- 创建更新时间戳触发器
CREATE TRIGGER update_sys_package_definition_updated_time BEFORE UPDATE ON sys_package_definition FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();

-- ----------------------------
-- 租户使用量统计表
-- ----------------------------
DROP TABLE IF EXISTS sys_tenant_usage CASCADE;
CREATE TABLE sys_tenant_usage
(
    id           BIGSERIAL NOT NULL,
    tenant_id    BIGINT    NOT NULL,
    stat_date    DATE      NOT NULL,
    user_count   INTEGER            DEFAULT 0,
    storage_used BIGINT             DEFAULT 0,
    api_calls    BIGINT             DEFAULT 0,
    login_count  INTEGER            DEFAULT 0,
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

-- 添加表注释
COMMENT ON TABLE sys_tenant_usage IS '租户使用量统计表';
COMMENT ON COLUMN sys_tenant_usage.id IS '统计ID';
COMMENT ON COLUMN sys_tenant_usage.tenant_id IS '租户ID';
COMMENT ON COLUMN sys_tenant_usage.stat_date IS '统计日期';
COMMENT ON COLUMN sys_tenant_usage.user_count IS '用户数量';
COMMENT ON COLUMN sys_tenant_usage.storage_used IS '已用存储(MB)';
COMMENT ON COLUMN sys_tenant_usage.api_calls IS 'API调用次数';
COMMENT ON COLUMN sys_tenant_usage.login_count IS '登录次数';
COMMENT ON COLUMN sys_tenant_usage.created_time IS '创建时间';
COMMENT ON COLUMN sys_tenant_usage.updated_time IS '更新时间';

-- 创建索引
CREATE UNIQUE INDEX uk_tenant_date ON sys_tenant_usage (tenant_id, stat_date);
CREATE INDEX idx_tenant_usage_tenant_id ON sys_tenant_usage (tenant_id);
CREATE INDEX idx_stat_date ON sys_tenant_usage (stat_date);

-- 创建更新时间戳触发器
CREATE TRIGGER update_sys_tenant_usage_updated_time BEFORE UPDATE ON sys_tenant_usage FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
-- </editor-fold>

-- <editor-fold desc="权限管理体系（RBAC模型）">
-- ----------------------------
-- 用户表
-- ----------------------------
DROP TABLE IF EXISTS sys_user CASCADE;
CREATE TABLE sys_user
(
    id                   BIGSERIAL    NOT NULL,
    tenant_id            BIGINT       NOT NULL,
    username             VARCHAR(64)  NOT NULL,
    email                VARCHAR(128),
    phone                VARCHAR(32),
    password             VARCHAR(128) NOT NULL,
    salt                 VARCHAR(64)  NOT NULL,
    real_name            VARCHAR(64),
    nickname             VARCHAR(64),
    avatar               VARCHAR(512),
    gender               SMALLINT,
    birthday             DATE,
    status               SMALLINT     NOT NULL DEFAULT 1,
    user_type            VARCHAR(32)  NOT NULL DEFAULT 'normal',
    last_login_time      TIMESTAMP,
    last_login_ip        VARCHAR(64),
    login_count          INTEGER               DEFAULT 0,
    password_update_time TIMESTAMP,
    created_time         TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time         TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by           BIGINT,
    updated_by           BIGINT,
    deleted              SMALLINT     NOT NULL DEFAULT 0,
    PRIMARY KEY (id)
);

-- 添加表注释
COMMENT ON TABLE sys_user IS '用户表';
COMMENT ON COLUMN sys_user.id IS '用户ID';
COMMENT ON COLUMN sys_user.tenant_id IS '租户ID';
COMMENT ON COLUMN sys_user.username IS '用户名';
COMMENT ON COLUMN sys_user.email IS '邮箱';
COMMENT ON COLUMN sys_user.phone IS '手机号';
COMMENT ON COLUMN sys_user.password IS '密码(加密)';
COMMENT ON COLUMN sys_user.salt IS '密码盐值';
COMMENT ON COLUMN sys_user.real_name IS '真实姓名';
COMMENT ON COLUMN sys_user.nickname IS '昵称';
COMMENT ON COLUMN sys_user.avatar IS '头像地址';
COMMENT ON COLUMN sys_user.gender IS '性别：0-未知，1-男，2-女';
COMMENT ON COLUMN sys_user.birthday IS '生日';
COMMENT ON COLUMN sys_user.status IS '状态：0-禁用，1-启用，2-锁定';
COMMENT ON COLUMN sys_user.user_type IS '用户类型：admin,normal,guest';
COMMENT ON COLUMN sys_user.last_login_time IS '最后登录时间';
COMMENT ON COLUMN sys_user.last_login_ip IS '最后登录IP';
COMMENT ON COLUMN sys_user.login_count IS '登录次数';
COMMENT ON COLUMN sys_user.password_update_time IS '密码更新时间';
COMMENT ON COLUMN sys_user.created_time IS '创建时间';
COMMENT ON COLUMN sys_user.updated_time IS '更新时间';
COMMENT ON COLUMN sys_user.created_by IS '创建人';
COMMENT ON COLUMN sys_user.updated_by IS '更新人';
COMMENT ON COLUMN sys_user.deleted IS '删除标记：0-未删除，1-已删除';

-- 创建索引
CREATE UNIQUE INDEX uk_tenant_username ON sys_user (tenant_id, username);
CREATE UNIQUE INDEX uk_tenant_email ON sys_user (tenant_id, email);
CREATE UNIQUE INDEX uk_tenant_phone ON sys_user (tenant_id, phone);
CREATE INDEX idx_user_tenant_id ON sys_user (tenant_id);
CREATE INDEX idx_user_status ON sys_user (status);
CREATE INDEX idx_user_type ON sys_user (user_type);
CREATE INDEX idx_last_login_time ON sys_user (last_login_time);

-- 创建更新时间戳触发器
CREATE TRIGGER update_sys_user_updated_time BEFORE UPDATE ON sys_user FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();

-- ----------------------------
-- 角色表
-- ----------------------------
DROP TABLE IF EXISTS sys_role CASCADE;
CREATE TABLE sys_role
(
    id            BIGSERIAL   NOT NULL,
    name          varchar(64) NOT NULL,
    code          varchar(32) NOT NULL,
    industry_type varchar(32) NOT NULL,
    sort          int NULL,
    data_scope    smallint NULL,
    status        smallint     DEFAULT 1,
    is_deleted    smallint     DEFAULT 0,
    create_by     varchar(255) DEFAULT NULL,
    update_by     varchar(255) DEFAULT NULL,
    create_time   TIMESTAMP    DEFAULT CURRENT_TIMESTAMP,
    update_time   TIMESTAMP    DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

-- 创建索引
CREATE INDEX idx_role_industry_type ON sys_role (industry_type);
CREATE INDEX idx_role_status ON sys_role (status);

-- ----------------------------
-- 角色和菜单关联表
-- ----------------------------
DROP TABLE IF EXISTS sys_role_menu;
CREATE TABLE sys_role_menu
(
    role_id bigint NOT NULL,
    menu_id bigint NOT NULL
);

CREATE UNIQUE INDEX uk_roleId_menuId ON sys_role_menu (role_id, menu_id);

-- ----------------------------
-- 用户和角色关联表
-- ----------------------------
DROP TABLE IF EXISTS sys_user_role;
CREATE TABLE sys_user_role
(
    user_id bigint NOT NULL,
    role_id bigint NOT NULL,
    PRIMARY KEY (user_id, role_id)
);

-- ----------------------------
-- 权限表
-- ----------------------------
DROP TABLE IF EXISTS sys_permission CASCADE;
CREATE TABLE sys_permission
(
    id              BIGSERIAL    NOT NULL,
    tenant_id       BIGINT,
    permission_code VARCHAR(128) NOT NULL,
    permission_name VARCHAR(128) NOT NULL,
    permission_type VARCHAR(32)  NOT NULL,
    resource_id     BIGINT,
    parent_id       BIGINT,
    level           INTEGER               DEFAULT 1,
    sort_order      INTEGER               DEFAULT 0,
    status          SMALLINT     NOT NULL DEFAULT 1,
    description     VARCHAR(512),
    created_time    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by      BIGINT,
    updated_by      BIGINT,
    deleted         SMALLINT     NOT NULL DEFAULT 0,
    PRIMARY KEY (id)
);

-- 添加表注释
COMMENT ON TABLE sys_permission IS '权限表';
COMMENT ON COLUMN sys_permission.id IS '权限ID';
COMMENT ON COLUMN sys_permission.tenant_id IS '租户ID(为空表示系统级权限)';
COMMENT ON COLUMN sys_permission.permission_code IS '权限编码';
COMMENT ON COLUMN sys_permission.permission_name IS '权限名称';
COMMENT ON COLUMN sys_permission.permission_type IS '权限类型：menu,button,api,data';
COMMENT ON COLUMN sys_permission.resource_id IS '关联资源ID';
COMMENT ON COLUMN sys_permission.parent_id IS '父权限ID';
COMMENT ON COLUMN sys_permission.level IS '权限层级';
COMMENT ON COLUMN sys_permission.sort_order IS '排序';
COMMENT ON COLUMN sys_permission.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN sys_permission.description IS '权限描述';
COMMENT ON COLUMN sys_permission.created_time IS '创建时间';
COMMENT ON COLUMN sys_permission.updated_time IS '更新时间';
COMMENT ON COLUMN sys_permission.created_by IS '创建人';
COMMENT ON COLUMN sys_permission.updated_by IS '更新人';
COMMENT ON COLUMN sys_permission.deleted IS '删除标记：0-未删除，1-已删除';

-- 创建索引
CREATE UNIQUE INDEX uk_permission_code ON sys_permission (permission_code);
CREATE INDEX idx_permission_tenant_id ON sys_permission (tenant_id);
CREATE INDEX idx_permission_type ON sys_permission (permission_type);
CREATE INDEX idx_permission_resource_id ON sys_permission (resource_id);
CREATE INDEX idx_permission_parent_id ON sys_permission (parent_id);
CREATE INDEX idx_permission_status ON sys_permission (status);

-- 创建更新时间戳触发器
CREATE TRIGGER update_sys_permission_updated_time BEFORE UPDATE ON sys_permission FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();

-- ----------------------------
-- 用户组表
-- ----------------------------
DROP TABLE IF EXISTS sys_user_group CASCADE;
CREATE TABLE sys_user_group
(
    id           BIGSERIAL    NOT NULL,
    tenant_id    BIGINT       NOT NULL,
    group_code   VARCHAR(64)  NOT NULL,
    group_name   VARCHAR(128) NOT NULL,
    group_type   VARCHAR(32)  NOT NULL DEFAULT 'custom',
    parent_id    BIGINT,
    level        INTEGER               DEFAULT 1,
    sort_order   INTEGER               DEFAULT 0,
    status       SMALLINT     NOT NULL DEFAULT 1,
    description  VARCHAR(512),
    created_time TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by   BIGINT,
    updated_by   BIGINT,
    deleted      SMALLINT     NOT NULL DEFAULT 0,
    PRIMARY KEY (id)
);

-- 添加表注释
COMMENT ON TABLE sys_user_group IS '用户组表';
COMMENT ON COLUMN sys_user_group.id IS '用户组ID';
COMMENT ON COLUMN sys_user_group.tenant_id IS '租户ID';
COMMENT ON COLUMN sys_user_group.group_code IS '用户组编码';
COMMENT ON COLUMN sys_user_group.group_name IS '用户组名称';
COMMENT ON COLUMN sys_user_group.group_type IS '用户组类型：system,custom';
COMMENT ON COLUMN sys_user_group.parent_id IS '父用户组ID';
COMMENT ON COLUMN sys_user_group.level IS '用户组层级';
COMMENT ON COLUMN sys_user_group.sort_order IS '排序';
COMMENT ON COLUMN sys_user_group.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN sys_user_group.description IS '用户组描述';
COMMENT ON COLUMN sys_user_group.created_time IS '创建时间';
COMMENT ON COLUMN sys_user_group.updated_time IS '更新时间';
COMMENT ON COLUMN sys_user_group.created_by IS '创建人';
COMMENT ON COLUMN sys_user_group.updated_by IS '更新人';
COMMENT ON COLUMN sys_user_group.deleted IS '删除标记：0-未删除，1-已删除';

-- 创建索引
CREATE UNIQUE INDEX uk_tenant_group_code ON sys_user_group (tenant_id, group_code);
CREATE INDEX idx_user_group_tenant_id ON sys_user_group (tenant_id);
CREATE INDEX idx_user_group_parent_id ON sys_user_group (parent_id);
CREATE INDEX idx_group_type ON sys_user_group (group_type);
CREATE INDEX idx_user_group_status ON sys_user_group (status);

-- 创建更新时间戳触发器
CREATE TRIGGER update_sys_user_group_updated_time BEFORE UPDATE ON sys_user_group FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();

-- ----------------------------
-- 菜单资源表
-- ----------------------------
DROP TABLE IF EXISTS sys_menu_resource CASCADE;
CREATE TABLE sys_menu_resource
(
    id            BIGSERIAL    NOT NULL,
    tenant_id     BIGINT,
    menu_code     VARCHAR(64)  NOT NULL,
    menu_name     VARCHAR(128) NOT NULL,
    menu_type     VARCHAR(32)  NOT NULL,
    parent_id     BIGINT,
    level         INTEGER               DEFAULT 1,
    path          VARCHAR(256),
    component     VARCHAR(256),
    icon          VARCHAR(128),
    sort_order    INTEGER               DEFAULT 0,
    visible       SMALLINT     NOT NULL DEFAULT 1,
    status        SMALLINT     NOT NULL DEFAULT 1,
    external_link SMALLINT              DEFAULT 0,
    cache         SMALLINT              DEFAULT 0,
    description   VARCHAR(512),
    created_time  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by    BIGINT,
    updated_by    BIGINT,
    deleted       SMALLINT     NOT NULL DEFAULT 0,
    PRIMARY KEY (id)
);

-- 添加表注释
COMMENT ON TABLE sys_menu_resource IS '菜单资源表';
COMMENT ON COLUMN sys_menu_resource.id IS '菜单ID';
COMMENT ON COLUMN sys_menu_resource.tenant_id IS '租户ID(为空表示系统级菜单)';
COMMENT ON COLUMN sys_menu_resource.menu_code IS '菜单编码';
COMMENT ON COLUMN sys_menu_resource.menu_name IS '菜单名称';
COMMENT ON COLUMN sys_menu_resource.menu_type IS '菜单类型：catalog,menu,button';
COMMENT ON COLUMN sys_menu_resource.parent_id IS '父菜单ID';
COMMENT ON COLUMN sys_menu_resource.level IS '菜单层级';
COMMENT ON COLUMN sys_menu_resource.path IS '路由路径';
COMMENT ON COLUMN sys_menu_resource.component IS '组件路径';
COMMENT ON COLUMN sys_menu_resource.icon IS '菜单图标';
COMMENT ON COLUMN sys_menu_resource.sort_order IS '排序';
COMMENT ON COLUMN sys_menu_resource.visible IS '是否可见：0-隐藏，1-显示';
COMMENT ON COLUMN sys_menu_resource.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN sys_menu_resource.external_link IS '是否外链：0-否，1-是';
COMMENT ON COLUMN sys_menu_resource.cache IS '是否缓存：0-否，1-是';
COMMENT ON COLUMN sys_menu_resource.description IS '菜单描述';
COMMENT ON COLUMN sys_menu_resource.created_time IS '创建时间';
COMMENT ON COLUMN sys_menu_resource.updated_time IS '更新时间';
COMMENT ON COLUMN sys_menu_resource.created_by IS '创建人';
COMMENT ON COLUMN sys_menu_resource.updated_by IS '更新人';
COMMENT ON COLUMN sys_menu_resource.deleted IS '删除标记：0-未删除，1-已删除';

-- 创建索引
CREATE UNIQUE INDEX uk_menu_code ON sys_menu_resource (menu_code);
CREATE INDEX idx_menu_resource_tenant_id ON sys_menu_resource (tenant_id);
CREATE INDEX idx_menu_resource_parent_id ON sys_menu_resource (parent_id);
CREATE INDEX idx_menu_type ON sys_menu_resource (menu_type);
CREATE INDEX idx_menu_resource_status ON sys_menu_resource (status);
CREATE INDEX idx_menu_resource_sort_order ON sys_menu_resource (sort_order);

-- 创建更新时间戳触发器
CREATE TRIGGER update_sys_menu_resource_updated_time BEFORE UPDATE ON sys_menu_resource FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();

-- ----------------------------
-- API资源表
-- ----------------------------
DROP TABLE IF EXISTS sys_api_resource CASCADE;
CREATE TABLE sys_api_resource
(
    id             BIGSERIAL    NOT NULL,
    tenant_id      BIGINT,
    api_code       VARCHAR(128) NOT NULL,
    api_name       VARCHAR(128) NOT NULL,
    api_path       VARCHAR(256) NOT NULL,
    http_method    VARCHAR(16)  NOT NULL,
    resource_group VARCHAR(64),
    auth_required  SMALLINT     NOT NULL DEFAULT 1,
    rate_limit     INTEGER,
    status         SMALLINT     NOT NULL DEFAULT 1,
    description    VARCHAR(512),
    created_time   TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time   TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by     BIGINT,
    updated_by     BIGINT,
    deleted        SMALLINT     NOT NULL DEFAULT 0,
    PRIMARY KEY (id)
);

-- 添加表注释
COMMENT ON TABLE sys_api_resource IS 'API资源表';
COMMENT ON COLUMN sys_api_resource.id IS 'API资源ID';
COMMENT ON COLUMN sys_api_resource.tenant_id IS '租户ID(为空表示系统级API)';
COMMENT ON COLUMN sys_api_resource.api_code IS 'API编码';
COMMENT ON COLUMN sys_api_resource.api_name IS 'API名称';
COMMENT ON COLUMN sys_api_resource.api_path IS 'API路径';
COMMENT ON COLUMN sys_api_resource.http_method IS 'HTTP方法：GET,POST,PUT,DELETE';
COMMENT ON COLUMN sys_api_resource.resource_group IS '资源分组';
COMMENT ON COLUMN sys_api_resource.auth_required IS '是否需要认证：0-否，1-是';
COMMENT ON COLUMN sys_api_resource.rate_limit IS '限流配置(次/分钟)';
COMMENT ON COLUMN sys_api_resource.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN sys_api_resource.description IS 'API描述';
COMMENT ON COLUMN sys_api_resource.created_time IS '创建时间';
COMMENT ON COLUMN sys_api_resource.updated_time IS '更新时间';
COMMENT ON COLUMN sys_api_resource.created_by IS '创建人';
COMMENT ON COLUMN sys_api_resource.updated_by IS '更新人';
COMMENT ON COLUMN sys_api_resource.deleted IS '删除标记：0-未删除，1-已删除';

-- 创建索引
CREATE UNIQUE INDEX uk_api_code ON sys_api_resource (api_code);
CREATE UNIQUE INDEX uk_api_path_method ON sys_api_resource (api_path, http_method);
CREATE INDEX idx_api_resource_tenant_id ON sys_api_resource (tenant_id);
CREATE INDEX idx_resource_group ON sys_api_resource (resource_group);
CREATE INDEX idx_api_resource_status ON sys_api_resource (status);

-- 创建更新时间戳触发器
CREATE TRIGGER update_sys_api_resource_updated_time BEFORE UPDATE ON sys_api_resource FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
-- </editor-fold>

-- <editor-fold desc="关联关系中间表">
-- ----------------------------
-- 用户角色关联表
-- ----------------------------
DROP TABLE IF EXISTS sys_user_role_relation CASCADE;
CREATE TABLE sys_user_role_relation
(
    id           BIGSERIAL NOT NULL,
    tenant_id    BIGINT    NOT NULL,
    user_id      BIGINT    NOT NULL,
    role_id      BIGINT    NOT NULL,
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by   BIGINT,
    PRIMARY KEY (id)
);

-- 添加表注释
COMMENT ON TABLE sys_user_role_relation IS '用户角色关联表';
COMMENT ON COLUMN sys_user_role_relation.id IS '关联ID';
COMMENT ON COLUMN sys_user_role_relation.tenant_id IS '租户ID';
COMMENT ON COLUMN sys_user_role_relation.user_id IS '用户ID';
COMMENT ON COLUMN sys_user_role_relation.role_id IS '角色ID';
COMMENT ON COLUMN sys_user_role_relation.created_time IS '创建时间';
COMMENT ON COLUMN sys_user_role_relation.created_by IS '创建人';

-- 创建索引
CREATE UNIQUE INDEX uk_user_role ON sys_user_role_relation (user_id, role_id);
CREATE INDEX idx_user_role_relation_tenant_id ON sys_user_role_relation (tenant_id);
CREATE INDEX idx_user_role_relation_user_id ON sys_user_role_relation (user_id);
CREATE INDEX idx_user_role_relation_role_id ON sys_user_role_relation (role_id);

-- ----------------------------
-- 角色权限关联表
-- ----------------------------
DROP TABLE IF EXISTS sys_role_permission_relation CASCADE;
CREATE TABLE sys_role_permission_relation
(
    id            BIGSERIAL NOT NULL,
    tenant_id     BIGINT    NOT NULL,
    role_id       BIGINT    NOT NULL,
    permission_id BIGINT    NOT NULL,
    created_time  TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by    BIGINT,
    PRIMARY KEY (id)
);

-- 添加表注释
COMMENT ON TABLE sys_role_permission_relation IS '角色权限关联表';
COMMENT ON COLUMN sys_role_permission_relation.id IS '关联ID';
COMMENT ON COLUMN sys_role_permission_relation.tenant_id IS '租户ID';
COMMENT ON COLUMN sys_role_permission_relation.role_id IS '角色ID';
COMMENT ON COLUMN sys_role_permission_relation.permission_id IS '权限ID';
COMMENT ON COLUMN sys_role_permission_relation.created_time IS '创建时间';
COMMENT ON COLUMN sys_role_permission_relation.created_by IS '创建人';

-- 创建索引
CREATE UNIQUE INDEX uk_role_permission ON sys_role_permission_relation (role_id, permission_id);
CREATE INDEX idx_role_permission_relation_tenant_id ON sys_role_permission_relation (tenant_id);
CREATE INDEX idx_role_permission_relation_role_id ON sys_role_permission_relation (role_id);
CREATE INDEX idx_role_permission_relation_permission_id ON sys_role_permission_relation (permission_id);

-- ----------------------------
-- 用户组用户关联表
-- ----------------------------
DROP TABLE IF EXISTS sys_user_group_user_relation CASCADE;
CREATE TABLE sys_user_group_user_relation
(
    id            BIGSERIAL NOT NULL,
    tenant_id     BIGINT    NOT NULL,
    user_group_id BIGINT    NOT NULL,
    user_id       BIGINT    NOT NULL,
    created_time  TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by    BIGINT,
    PRIMARY KEY (id)
);

-- 添加表注释
COMMENT ON TABLE sys_user_group_user_relation IS '用户组用户关联表';
COMMENT ON COLUMN sys_user_group_user_relation.id IS '关联ID';
COMMENT ON COLUMN sys_user_group_user_relation.tenant_id IS '租户ID';
COMMENT ON COLUMN sys_user_group_user_relation.user_group_id IS '用户组ID';
COMMENT ON COLUMN sys_user_group_user_relation.user_id IS '用户ID';
COMMENT ON COLUMN sys_user_group_user_relation.created_time IS '创建时间';
COMMENT ON COLUMN sys_user_group_user_relation.created_by IS '创建人';

-- 创建索引
CREATE UNIQUE INDEX uk_group_user ON sys_user_group_user_relation (user_group_id, user_id);
CREATE INDEX idx_user_group_user_relation_tenant_id ON sys_user_group_user_relation (tenant_id);
CREATE INDEX idx_user_group_user_relation_user_group_id ON sys_user_group_user_relation (user_group_id);
CREATE INDEX idx_user_group_user_relation_user_id ON sys_user_group_user_relation (user_id);

-- ----------------------------
-- 用户组角色关联表
-- ----------------------------
DROP TABLE IF EXISTS sys_user_group_role_relation CASCADE;
CREATE TABLE sys_user_group_role_relation
(
    id            BIGSERIAL NOT NULL,
    tenant_id     BIGINT    NOT NULL,
    user_group_id BIGINT    NOT NULL,
    role_id       BIGINT    NOT NULL,
    created_time  TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by    BIGINT,
    PRIMARY KEY (id)
);

-- 添加表注释
COMMENT ON TABLE sys_user_group_role_relation IS '用户组角色关联表';
COMMENT ON COLUMN sys_user_group_role_relation.id IS '关联ID';
COMMENT ON COLUMN sys_user_group_role_relation.tenant_id IS '租户ID';
COMMENT ON COLUMN sys_user_group_role_relation.user_group_id IS '用户组ID';
COMMENT ON COLUMN sys_user_group_role_relation.role_id IS '角色ID';
COMMENT ON COLUMN sys_user_group_role_relation.created_time IS '创建时间';
COMMENT ON COLUMN sys_user_group_role_relation.created_by IS '创建人';

-- 创建索引
CREATE UNIQUE INDEX uk_group_role ON sys_user_group_role_relation (user_group_id, role_id);
CREATE INDEX idx_user_group_role_relation_tenant_id ON sys_user_group_role_relation (tenant_id);
CREATE INDEX idx_user_group_role_relation_user_group_id ON sys_user_group_role_relation (user_group_id);
CREATE INDEX idx_user_group_role_relation_role_id ON sys_user_group_role_relation (role_id);
-- </editor-fold>

-- <editor-fold desc="组织架构管理">
-- ----------------------------
-- 部门表
-- ----------------------------
DROP TABLE IF EXISTS sys_department CASCADE;
CREATE TABLE sys_department
(
    id           BIGSERIAL    NOT NULL,
    tenant_id    BIGINT       NOT NULL,
    dept_code    VARCHAR(64)  NOT NULL,
    dept_name    VARCHAR(128) NOT NULL,
    parent_id    BIGINT,
    level        INTEGER               DEFAULT 1,
    dept_path    VARCHAR(512),
    leader_id    BIGINT,
    phone        VARCHAR(32),
    email        VARCHAR(128),
    address      VARCHAR(256),
    sort_order   INTEGER               DEFAULT 0,
    status       SMALLINT     NOT NULL DEFAULT 1,
    description  VARCHAR(512),
    created_time TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by   BIGINT,
    updated_by   BIGINT,
    deleted      SMALLINT     NOT NULL DEFAULT 0,
    PRIMARY KEY (id)
);

-- 添加表注释
COMMENT ON TABLE sys_department IS '部门表';
COMMENT ON COLUMN sys_department.id IS '部门ID';
COMMENT ON COLUMN sys_department.tenant_id IS '租户ID';
COMMENT ON COLUMN sys_department.dept_code IS '部门编码';
COMMENT ON COLUMN sys_department.dept_name IS '部门名称';
COMMENT ON COLUMN sys_department.parent_id IS '父部门ID';
COMMENT ON COLUMN sys_department.level IS '部门层级';
COMMENT ON COLUMN sys_department.dept_path IS '部门路径';
COMMENT ON COLUMN sys_department.leader_id IS '部门负责人ID';
COMMENT ON COLUMN sys_department.phone IS '部门电话';
COMMENT ON COLUMN sys_department.email IS '部门邮箱';
COMMENT ON COLUMN sys_department.address IS '部门地址';
COMMENT ON COLUMN sys_department.sort_order IS '排序';
COMMENT ON COLUMN sys_department.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN sys_department.description IS '部门描述';
COMMENT ON COLUMN sys_department.created_time IS '创建时间';
COMMENT ON COLUMN sys_department.updated_time IS '更新时间';
COMMENT ON COLUMN sys_department.created_by IS '创建人';
COMMENT ON COLUMN sys_department.updated_by IS '更新人';
COMMENT ON COLUMN sys_department.deleted IS '删除标记：0-未删除，1-已删除';

-- 创建索引
CREATE UNIQUE INDEX uk_tenant_dept_code ON sys_department (tenant_id, dept_code);
CREATE INDEX idx_department_tenant_id ON sys_department (tenant_id);
CREATE INDEX idx_department_parent_id ON sys_department (parent_id);
CREATE INDEX idx_department_leader_id ON sys_department (leader_id);
CREATE INDEX idx_department_status ON sys_department (status);
CREATE INDEX idx_department_sort_order ON sys_department (sort_order);

-- 创建更新时间戳触发器
CREATE TRIGGER update_sys_department_updated_time BEFORE UPDATE ON sys_department FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();

-- ----------------------------
-- 岗位表
-- ----------------------------
DROP TABLE IF EXISTS sys_position CASCADE;
CREATE TABLE sys_position
(
    id               BIGSERIAL    NOT NULL,
    tenant_id        BIGINT       NOT NULL,
    position_code    VARCHAR(64)  NOT NULL,
    position_name    VARCHAR(128) NOT NULL,
    position_level   INTEGER,
    dept_id          BIGINT,
    sort_order       INTEGER               DEFAULT 0,
    status           SMALLINT     NOT NULL DEFAULT 1,
    responsibilities TEXT,
    requirements     TEXT,
    description      VARCHAR(512),
    created_time     TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time     TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by       BIGINT,
    updated_by       BIGINT,
    deleted          SMALLINT     NOT NULL DEFAULT 0,
    PRIMARY KEY (id)
);

-- 添加表注释
COMMENT ON TABLE sys_position IS '岗位表';
COMMENT ON COLUMN sys_position.id IS '岗位ID';
COMMENT ON COLUMN sys_position.tenant_id IS '租户ID';
COMMENT ON COLUMN sys_position.position_code IS '岗位编码';
COMMENT ON COLUMN sys_position.position_name IS '岗位名称';
COMMENT ON COLUMN sys_position.position_level IS '岗位级别';
COMMENT ON COLUMN sys_position.dept_id IS '所属部门ID';
COMMENT ON COLUMN sys_position.sort_order IS '排序';
COMMENT ON COLUMN sys_position.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN sys_position.responsibilities IS '岗位职责';
COMMENT ON COLUMN sys_position.requirements IS '任职要求';
COMMENT ON COLUMN sys_position.description IS '岗位描述';
COMMENT ON COLUMN sys_position.created_time IS '创建时间';
COMMENT ON COLUMN sys_position.updated_time IS '更新时间';
COMMENT ON COLUMN sys_position.created_by IS '创建人';
COMMENT ON COLUMN sys_position.updated_by IS '更新人';
COMMENT ON COLUMN sys_position.deleted IS '删除标记：0-未删除，1-已删除';

-- 创建索引
CREATE UNIQUE INDEX uk_tenant_position_code ON sys_position (tenant_id, position_code);
CREATE INDEX idx_position_tenant_id ON sys_position (tenant_id);
CREATE INDEX idx_position_dept_id ON sys_position (dept_id);
CREATE INDEX idx_position_level ON sys_position (position_level);
CREATE INDEX idx_position_status ON sys_position (status);

-- 创建更新时间戳触发器
CREATE TRIGGER update_sys_position_updated_time BEFORE UPDATE ON sys_position FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();

-- ----------------------------
-- 用户部门关联表
-- ----------------------------
DROP TABLE IF EXISTS sys_user_department_relation CASCADE;
CREATE TABLE sys_user_department_relation
(
    id           BIGSERIAL NOT NULL,
    tenant_id    BIGINT    NOT NULL,
    user_id      BIGINT    NOT NULL,
    dept_id      BIGINT    NOT NULL,
    is_primary   SMALLINT  NOT NULL DEFAULT 1,
    join_time    TIMESTAMP,
    leave_time   TIMESTAMP,
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by   BIGINT,
    PRIMARY KEY (id)
);

-- 添加表注释
COMMENT ON TABLE sys_user_department_relation IS '用户部门关联表';
COMMENT ON COLUMN sys_user_department_relation.id IS '关联ID';
COMMENT ON COLUMN sys_user_department_relation.tenant_id IS '租户ID';
COMMENT ON COLUMN sys_user_department_relation.user_id IS '用户ID';
COMMENT ON COLUMN sys_user_department_relation.dept_id IS '部门ID';
COMMENT ON COLUMN sys_user_department_relation.is_primary IS '是否主部门：0-否，1-是';
COMMENT ON COLUMN sys_user_department_relation.join_time IS '加入时间';
COMMENT ON COLUMN sys_user_department_relation.leave_time IS '离开时间';
COMMENT ON COLUMN sys_user_department_relation.created_time IS '创建时间';
COMMENT ON COLUMN sys_user_department_relation.created_by IS '创建人';

-- 创建索引
CREATE UNIQUE INDEX uk_user_dept_primary ON sys_user_department_relation (user_id, dept_id, is_primary);
CREATE INDEX idx_user_department_relation_tenant_id ON sys_user_department_relation (tenant_id);
CREATE INDEX idx_user_department_relation_user_id ON sys_user_department_relation (user_id);
CREATE INDEX idx_user_department_relation_dept_id ON sys_user_department_relation (dept_id);

-- ----------------------------
-- 用户岗位关联表
-- ----------------------------
DROP TABLE IF EXISTS sys_user_position_relation CASCADE;
CREATE TABLE sys_user_position_relation
(
    id           BIGSERIAL NOT NULL,
    tenant_id    BIGINT    NOT NULL,
    user_id      BIGINT    NOT NULL,
    position_id  BIGINT    NOT NULL,
    is_primary   SMALLINT  NOT NULL DEFAULT 1,
    start_time   TIMESTAMP,
    end_time     TIMESTAMP,
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by   BIGINT,
    PRIMARY KEY (id)
);

-- 添加表注释
COMMENT ON TABLE sys_user_position_relation IS '用户岗位关联表';
COMMENT ON COLUMN sys_user_position_relation.id IS '关联ID';
COMMENT ON COLUMN sys_user_position_relation.tenant_id IS '租户ID';
COMMENT ON COLUMN sys_user_position_relation.user_id IS '用户ID';
COMMENT ON COLUMN sys_user_position_relation.position_id IS '岗位ID';
COMMENT ON COLUMN sys_user_position_relation.is_primary IS '是否主岗位：0-否，1-是';
COMMENT ON COLUMN sys_user_position_relation.start_time IS '开始时间';
COMMENT ON COLUMN sys_user_position_relation.end_time IS '结束时间';
COMMENT ON COLUMN sys_user_position_relation.created_time IS '创建时间';
COMMENT ON COLUMN sys_user_position_relation.created_by IS '创建人';

-- 创建索引
CREATE UNIQUE INDEX uk_user_position ON sys_user_position_relation (user_id, position_id);
CREATE INDEX idx_user_position_relation_tenant_id ON sys_user_position_relation (tenant_id);
CREATE INDEX idx_user_position_relation_user_id ON sys_user_position_relation (user_id);
CREATE INDEX idx_user_position_relation_position_id ON sys_user_position_relation (position_id);
-- </editor-fold>

-- <editor-fold desc="系统日志和审计">
-- ----------------------------
-- 操作日志表
-- ----------------------------
DROP TABLE IF EXISTS sys_operation_log CASCADE;
CREATE TABLE sys_operation_log
(
    id              BIGSERIAL   NOT NULL,
    tenant_id       BIGINT,
    user_id         BIGINT,
    username        VARCHAR(64),
    operation_type  VARCHAR(32) NOT NULL,
    module          VARCHAR(64),
    business_type   VARCHAR(64),
    business_id     BIGINT,
    operation_desc  VARCHAR(512),
    request_method  VARCHAR(16),
    request_url     VARCHAR(256),
    request_params  TEXT,
    response_result TEXT,
    ip_address      VARCHAR(64),
    user_agent      VARCHAR(512),
    status          SMALLINT    NOT NULL,
    error_msg       TEXT,
    cost_time       BIGINT,
    created_time    TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

-- 添加表注释
COMMENT ON TABLE sys_operation_log IS '操作日志表';
COMMENT ON COLUMN sys_operation_log.id IS '日志ID';
COMMENT ON COLUMN sys_operation_log.tenant_id IS '租户ID';
COMMENT ON COLUMN sys_operation_log.user_id IS '操作用户ID';
COMMENT ON COLUMN sys_operation_log.username IS '操作用户名';
COMMENT ON COLUMN sys_operation_log.operation_type IS '操作类型：CREATE,UPDATE,DELETE,LOGIN,LOGOUT';
COMMENT ON COLUMN sys_operation_log.module IS '操作模块';
COMMENT ON COLUMN sys_operation_log.business_type IS '业务类型';
COMMENT ON COLUMN sys_operation_log.business_id IS '业务ID';
COMMENT ON COLUMN sys_operation_log.operation_desc IS '操作描述';
COMMENT ON COLUMN sys_operation_log.request_method IS '请求方法';
COMMENT ON COLUMN sys_operation_log.request_url IS '请求URL';
COMMENT ON COLUMN sys_operation_log.request_params IS '请求参数';
COMMENT ON COLUMN sys_operation_log.response_result IS '响应结果';
COMMENT ON COLUMN sys_operation_log.ip_address IS 'IP地址';
COMMENT ON COLUMN sys_operation_log.user_agent IS '用户代理';
COMMENT ON COLUMN sys_operation_log.status IS '操作状态：0-失败，1-成功';
COMMENT ON COLUMN sys_operation_log.error_msg IS '错误信息';
COMMENT ON COLUMN sys_operation_log.cost_time IS '耗时(毫秒)';
COMMENT ON COLUMN sys_operation_log.created_time IS '创建时间';

-- 创建索引
CREATE INDEX idx_operation_log_tenant_id ON sys_operation_log (tenant_id);
CREATE INDEX idx_operation_log_user_id ON sys_operation_log (user_id);
CREATE INDEX idx_operation_type ON sys_operation_log (operation_type);
CREATE INDEX idx_operation_log_module ON sys_operation_log (module);
CREATE INDEX idx_business_type ON sys_operation_log (business_type);
CREATE INDEX idx_operation_log_created_time ON sys_operation_log (created_time);

-- ----------------------------
-- 登录日志表
-- ----------------------------
DROP TABLE IF EXISTS sys_login_log CASCADE;
CREATE TABLE sys_login_log
(
    id          BIGSERIAL   NOT NULL,
    tenant_id   BIGINT,
    user_id     BIGINT,
    username    VARCHAR(64),
    login_type  VARCHAR(32) NOT NULL,
    ip_address  VARCHAR(64),
    location    VARCHAR(128),
    user_agent  VARCHAR(512),
    device_type VARCHAR(32),
    status      SMALLINT    NOT NULL,
    fail_reason VARCHAR(256),
    login_time  TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    logout_time TIMESTAMP,
    PRIMARY KEY (id)
);

-- 添加表注释
COMMENT ON TABLE sys_login_log IS '登录日志表';
COMMENT ON COLUMN sys_login_log.id IS '日志ID';
COMMENT ON COLUMN sys_login_log.tenant_id IS '租户ID';
COMMENT ON COLUMN sys_login_log.user_id IS '用户ID';
COMMENT ON COLUMN sys_login_log.username IS '用户名';
COMMENT ON COLUMN sys_login_log.login_type IS '登录类型：password,sms,oauth';
COMMENT ON COLUMN sys_login_log.ip_address IS 'IP地址';
COMMENT ON COLUMN sys_login_log.location IS '登录地点';
COMMENT ON COLUMN sys_login_log.user_agent IS '用户代理';
COMMENT ON COLUMN sys_login_log.device_type IS '设备类型：web,mobile,app';
COMMENT ON COLUMN sys_login_log.status IS '登录状态：0-失败，1-成功';
COMMENT ON COLUMN sys_login_log.fail_reason IS '失败原因';
COMMENT ON COLUMN sys_login_log.login_time IS '登录时间';
COMMENT ON COLUMN sys_login_log.logout_time IS '登出时间';

-- 创建索引
CREATE INDEX idx_login_log_tenant_id ON sys_login_log (tenant_id);
CREATE INDEX idx_login_log_user_id ON sys_login_log (user_id);
CREATE INDEX idx_login_log_username ON sys_login_log (username);
CREATE INDEX idx_login_type ON sys_login_log (login_type);
CREATE INDEX idx_login_log_status ON sys_login_log (status);
CREATE INDEX idx_login_time ON sys_login_log (login_time);
-- </editor-fold>

DROP TABLE IF EXISTS sys_menu;
CREATE TABLE sys_menu
(
    id            BIGSERIAL   NOT NULL,
    parent_id     bigint      NOT NULL,
    tree_path     varchar(255),
    name          varchar(64) NOT NULL,
    industry_type varchar(32) NOT NULL,
    type          smallint    NOT NULL,
    route_name    varchar(255),
    route_path    varchar(128),
    component     varchar(128),
    perm          varchar(128),
    always_show   smallint     DEFAULT 0,
    keep_alive    smallint     DEFAULT 0,
    visible       smallint     DEFAULT 1,
    sort          int          DEFAULT 0,
    icon          varchar(64),
    redirect      varchar(128),
    description   varchar(512),
    create_by     varchar(255) DEFAULT NULL,
    update_by     varchar(255) DEFAULT NULL,
    create_time   TIMESTAMP    DEFAULT CURRENT_TIMESTAMP,
    update_time   TIMESTAMP    DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

-- 为已存在的sys_menu表添加description字段
-- 如果字段不存在则添加
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'sys_menu' AND column_name = 'description'
    ) THEN
        ALTER TABLE sys_menu ADD COLUMN description varchar(512);
        COMMENT ON COLUMN sys_menu.description IS '菜单描述信息';
    END IF;
END $$;

-- =========================================================================
-- PostgreSQL 创建只读用户脚本 - 只能查询 sys_tenant 表
-- =========================================================================

-- 1. 创建新用户（密码根据需要修改）
CREATE USER tenant_reader WITH PASSWORD 'xxx';

-- 2. 为用户添加注释说明
COMMENT ON ROLE tenant_reader IS '租户表只读用户 - 仅可查询 sys_tenant 表';

-- 3. 连接到目标数据库（假设数据库名为 dips_pro，请根据实际情况修改）
-- 注意：以下命令需要在对应的数据库中执行
-- \c dips_pro;

-- 4. 授予用户连接数据库的权限
GRANT CONNECT ON DATABASE dips TO tenant_reader;

-- 5. 授予用户在 public schema 上的使用权限
GRANT USAGE ON SCHEMA public TO tenant_reader;

-- 6. 仅授予 sys_tenant 表的 SELECT 权限
GRANT SELECT ON TABLE sys_tenant TO tenant_reader;
GRANT SELECT ON TABLE sys_tenant_config TO tenant_reader;

-- =========================================================================
-- 用户协议功能数据库迁移脚本
-- 为sys_user表添加user_agreement_accepted字段
-- 执行时间：请在执行前备份数据库
-- 2025-07-23 13:54:03
-- =========================================================================

-- 1. 添加user_agreement_accepted字段
ALTER TABLE sys_user ADD COLUMN user_agreement_accepted BOOLEAN DEFAULT FALSE NOT NULL;

-- 2. 添加字段注释
COMMENT ON COLUMN sys_user.user_agreement_accepted IS '是否已同意用户协议：false-未同意，true-已同意';

-- 3. 创建索引以提高查询性能
CREATE INDEX idx_sys_user_user_agreement_accepted ON sys_user (user_agreement_accepted);

-- ----------------------------
-- 系统通知
-- ----------------------------
DROP TABLE IF EXISTS sys_notification CASCADE;
CREATE TABLE sys_notification
(
    id          BIGSERIAL    NOT NULL,
    user_id     bigint       NOT NULL,
    type        varchar(64)  NOT NULL,
    title       varchar(64)  NOT NULL,
    details     varchar(512) NOT NULL,
    path        varchar(512),
    status      varchar(64)  NOT NULL,
    create_by   varchar(255) DEFAULT NULL,
    update_by   varchar(255) DEFAULT NULL,
    create_time TIMESTAMP    DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP    DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE INDEX idx_notification_user_status ON sys_notification (user_id, status);