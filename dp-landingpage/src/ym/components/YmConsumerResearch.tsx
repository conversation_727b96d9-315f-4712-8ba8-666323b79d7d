import React, { useEffect, useRef } from 'react';
import { motion, useAnimation, useInView } from 'framer-motion';
import { targetGroups } from '../data/ym-data';
import YmResponsiveImage from './YmResponsiveImage';
import YmContactButton from './YmContactButton';

export const YmConsumerResearch: React.FC = () => {
  const controls = useAnimation();
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: '-100px' });

  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [controls, isInView]);

  // 动画变体
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.2
      }
    }
  };

  const titleVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.9 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  return (
    <section id="consumer-research" className="py-20 lg:py-32 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={controls}
        >
          {/* 标题区域 */}
          <motion.div variants={titleVariants} className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6" style={{ color: 'var(--ym-text-primary)' }}>
              求美者体验研究
              <span className="ym-text-gradient-title">
                {/* 方向 */}
              </span>
            </h2>
            
            <p className="text-xl max-w-4xl mx-auto leading-relaxed" style={{ color: 'var(--ym-text-secondary)' }}>
            基于AI技术，我们为医学美容机构、集团及厂商提供精准的求美者行为洞察与数据驱动的决策支持，同时输出透明可信的行业分析报告，助力企业把握市场趋势，优化运营策略
            </p>
          </motion.div>

          {/* 目标群体卡片 */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12">
            {targetGroups.map((group, index) => (
              <motion.div
                key={group.id}
                variants={cardVariants}
                className="group relative"
                whileHover={{ y: -10 }}
                transition={{ type: 'spring', stiffness: 300 }}
              >
                {/* 背景装饰 */}
                <div className="absolute inset-0 ym-gradient-subtle rounded-3xl transform group-hover:scale-105 transition-transform duration-300" />
                <motion.div
                  className="absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 rounded-3xl transition-opacity duration-300"
                  style={{
                    background: `linear-gradient(135deg, ${group.color}15, ${group.color}05)`
                  }}
                />

                <div className="relative z-10 p-8 lg:p-10">
                  {/* 图标区域 */}
                  <motion.div
                    className="mb-8"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ type: 'spring', stiffness: 300 }}
                  >
                    <div 
                      className="w-16 h-16 rounded-2xl flex items-center justify-center text-white text-2xl font-bold shadow-lg"
                      style={{ backgroundColor: group.color }}
                    >
                      {group.iconType === 'image' ? (
                        <img 
                          src={group.icon} 
                          alt={group.title}
                          className="w-12 h-12 object-contain"
                        />
                      ) : (
                        group.icon
                      )}
                    </div>
                  </motion.div>

                  {/* 内容区域 */}
                  <div className="mb-8">
                    <h3 className="text-2xl font-bold mb-4" style={{ color: 'var(--ym-text-primary)' }}>
                      {group.title}
                    </h3>
                    
                    <p className="leading-relaxed mb-6" style={{ color: 'var(--ym-text-secondary)' }}>
                      {group.description}
                    </p>

                    {/* 核心需求 */}
                    <div className="space-y-3">
                      <h4 className="text-sm font-semibold uppercase tracking-wide" style={{ color: 'var(--ym-text-primary)' }}>
                        核心需求
                      </h4>
                      <ul className="space-y-2">
                        {group.needs.map((need, needIndex) => (
                          <motion.li
                            key={needIndex}
                            className="flex items-start space-x-3"
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.1 + needIndex * 0.05 }}
                          >
                            <motion.div
                              className="w-1.5 h-1.5 rounded-full flex-shrink-0 mt-2"
                              style={{ backgroundColor: group.color }}
                              whileHover={{ scale: 1.5 }}
                            />
                            <span className="text-sm leading-relaxed" style={{ color: 'var(--ym-text-secondary)' }}>
                              {need}
                            </span>
                          </motion.li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                {/* 悬停效果：显示更多信息 - 移到外层容器 */}
                <motion.div
                  className="absolute inset-x-0 bottom-0 p-6 ym-glass rounded-b-3xl opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-full group-hover:translate-y-0 z-20"
                  initial={false}
                >
                  <div className="text-center">
                    <motion.button
                      className="text-white px-6 py-2 rounded-full text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-200"
                      style={{ backgroundColor: group.color }}
                      whileHover={{ scale: 1.05, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => window.location.href = '/#ym/cr'}
                    >
                      了解详情
                    </motion.button>
                  </div>
                </motion.div>

                {/* 卡片边框发光效果 */}
                <motion.div
                  className="absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"
                  style={{
                    background: `linear-gradient(135deg, transparent, var(--ym-primary-300), transparent)`,
                    padding: '2px'
                  }}
                >
                  <div className="w-full h-full rounded-3xl" style={{ backgroundColor: 'var(--ym-bg-tertiary)' }} />
                </motion.div>
              </motion.div>
            ))}
          </div>

          {/* 查看详情按钮 */}
          <motion.div
            variants={titleVariants}
            className="mt-20 text-center"
          >
            <motion.button
              className="inline-flex items-center px-8 py-4 text-lg font-semibold text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
              style={{ 
                background: 'linear-gradient(135deg, var(--ym-primary-500), var(--ym-secondary-500))',
              }}
              whileHover={{ 
                scale: 1.05,
                boxShadow: '0 20px 40px rgba(0,0,0,0.15)'
              }}
              whileTap={{ scale: 0.95 }}
              onClick={() => window.location.href = '/#ym/cr'}
            >
              <span className="mr-2">查看详细研究报告</span>
              <svg 
                className="w-5 h-5" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M9 5l7 7-7 7" 
                />
              </svg>
            </motion.button>
          </motion.div>

          {/* 底部统计信息 */}
          <motion.div
            variants={titleVariants}
            className="mt-20 text-center"
          >
            <div className="ym-gradient-subtle rounded-3xl p-8 lg:p-12">
              <h3 className="text-2xl font-bold mb-8" style={{ color: 'var(--ym-text-primary)' }}>
                专业顾问指标设计，全流程 AI 自动化，开箱即用
              </h3>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                {[
                  { number: '立体化视角', label: '涵盖多种类型产品、不同触点服务体验', color: 'var(--ym-accent-500)' },
                  { number: '数据收集', label: '高效收集反馈，有效控制成本', color: 'var(--ym-primary-500)' },
                  { number: '数据看板', label: '数据自动可视化，调研动态实时刷新', color: 'var(--ym-secondary-600)' },
                  { number: '标准报告', label: '自动生成、优化，每月更新', color: 'var(--ym-secondary-500)' }
                ].map((stat, index) => (
                  <motion.div
                    key={index}
                    className="text-center"
                    whileHover={{ scale: 1.1 }}
                    transition={{ type: 'spring', stiffness: 300 }}
                  >
                    <motion.div
                      className="text-3xl font-bold mb-2"
                      style={{ color: stat.color }}
                      initial={{ opacity: 0, scale: 0 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: index * 0.1 + 0.5 }}
                    >
                      {stat.number}
                    </motion.div>
                    <div className="text-sm" style={{ color: 'var(--ym-text-secondary)' }}>{stat.label}</div>
                  </motion.div>
                ))}
              </div>

            <motion.div
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mt-16"
            >
              <YmContactButton />
            </motion.div>
            </div>
          </motion.div>

        </motion.div>
      </div>
    </section>
  );
};

export default YmConsumerResearch; 