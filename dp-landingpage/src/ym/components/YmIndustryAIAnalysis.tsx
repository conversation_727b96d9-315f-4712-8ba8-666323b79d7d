import React, { useEffect, useRef } from 'react';
import { motion, useAnimation, useInView } from 'framer-motion';
import { advantages } from '../data/ym-data';
import YmResponsiveImage from './YmResponsiveImage';

// 导入技术亮点图标
import analysisIcon from '../../assets/icons/analysis-2.png';
import statisticsIcon from '../../assets/icons/statistics.png';
import supplierIcon from '../../assets/icons/supplier.png';
import aiIcon from '../../assets/icons/ai.png';

export const YmIndustryAIAnalysis: React.FC = () => {
  const controls = useAnimation();
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: '-100px' });

  // 图标组件渲染函数
  const renderIcon = (advantage: any) => {
    if (advantage.iconType === 'image') {
      return (
        <img 
          src={advantage.icon} 
          alt={advantage.title}
          className="w-10 h-10 object-contain"
        />
      );
    } else {
      // 文本图标（emoji）
      return advantage.icon;
    }
  };

  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [controls, isInView]);

  // 动画变体
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.2
      }
    }
  };

  const titleVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  return (
    <section id="industry-ai-analysis" className="py-20 lg:py-32 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={controls}
        >
          {/* 标题区域 */}
          <motion.div variants={titleVariants} className="text-center mb-16">
            {/* <motion.span
              className="inline-block px-4 py-2 ym-tag-style rounded-full text-sm font-medium mb-6"
              whileHover={{ scale: 1.05 }}
            >
              行业 AI 分析
            </motion.span> */}
            
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6" style={{ color: 'var(--ym-text-primary)' }}>
              行业 AI 分析
              {/* <span className="ym-text-gradient-title">
                我们的平台
              </span> */}
            </h2>
            
            <p className="text-xl max-w-3xl mx-auto leading-relaxed" style={{ color: 'var(--ym-text-secondary)' }}>
              从数据到洞察，AI全流程自动化——深度推理，精准赋能商业决策
            </p>
          </motion.div>

          {/* 优势卡片网格 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {advantages.map((advantage, index) => (
              <motion.div
                key={advantage.id}
                variants={cardVariants}
                className="group relative overflow-hidden"
                whileHover={{ y: -10 }}
                transition={{ type: 'spring', stiffness: 300, damping: 30 }}
              >
                {/* 卡片背景 */}
                <div className="absolute inset-0 ym-gradient-subtle rounded-3xl shadow-lg group-hover:shadow-2xl transition-shadow duration-300" />
                
                {/* 悬停时的彩色背景 */}
                <motion.div
                  className="absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  style={{
                    background: `linear-gradient(135deg, ${advantage.color}10, ${advantage.color}05)`
                  }}
                />

                {/* 左上角装饰 */}
                <motion.div
                  className="absolute -top-10 -left-10 w-20 h-20 rounded-full opacity-20 group-hover:opacity-30 transition-opacity duration-300"
                  style={{ backgroundColor: advantage.color }}
                  animate={{
                    scale: [1, 1.2, 1],
                    rotate: [0, 180, 360]
                  }}
                  transition={{
                    duration: 8,
                    repeat: Infinity,
                    ease: 'linear'
                  }}
                />

                <div className="relative z-10 p-8 h-full flex flex-col">
                  {/* 图标区域 */}
                  <motion.div
                    className="mb-6"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ type: 'spring', stiffness: 300 }}
                  >
                    <div 
                      className="w-16 h-16 rounded-2xl flex items-center justify-center text-white text-2xl font-bold shadow-lg group-hover:shadow-xl transition-shadow duration-300"
                      style={{ backgroundColor: advantage.color }}
                    >
                      {renderIcon(advantage)}
                    </div>
                  </motion.div>

                  {/* 内容区域 */}
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-gray-800 transition-colors duration-300">
                      {advantage.title}
                    </h3>
                    
                    <p className="text-gray-600 leading-relaxed mb-6 group-hover:text-gray-700 transition-colors duration-300">
                      {advantage.description}
                    </p>

                    {/* 特性列表 */}
                    <ul className="space-y-3">
                      {advantage.features.map((feature, featureIndex) => (
                        <motion.li
                          key={featureIndex}
                          className="flex items-center space-x-3"
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 + featureIndex * 0.05 }}
                        >
                          <motion.div
                            className="w-2 h-2 rounded-full flex-shrink-0"
                            style={{ backgroundColor: advantage.color }}
                            whileHover={{ scale: 1.5 }}
                          />
                          <span className="text-gray-700 text-sm">{feature}</span>
                        </motion.li>
                      ))}
                    </ul>
                  </div>

                  {/* 悬停时显示的数据 */}
                  {/* <motion.div
                    className="mt-6 p-4 rounded-xl bg-gray-50 group-hover:bg-white transition-all duration-300 transform translate-y-2 opacity-0 group-hover:translate-y-0 group-hover:opacity-100"
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">
                        核心指标
                      </span>
                      <motion.span
                        className="text-lg font-bold"
                        style={{ color: advantage.color }}
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.3 }}
                      >
                        {advantage.metric}
                      </motion.span>
                    </div>
                  </motion.div> */}
                </div>

                {/* 右下角装饰 */}
                <motion.div
                  className="absolute -bottom-5 -right-5 w-12 h-12 rounded-full opacity-10 group-hover:opacity-20 transition-opacity duration-300"
                  style={{ backgroundColor: advantage.color }}
                  animate={{
                    scale: [1, 1.3, 1],
                    opacity: [0.1, 0.3, 0.1]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: 'easeInOut'
                  }}
                />
              </motion.div>
            ))}
          </div>

          {/* 查看详细分析按钮 */}
          <motion.div
            variants={titleVariants}
            className="mt-16 text-center"
          >
            <motion.a
              href="#ym/iaa"
              className="inline-flex items-center px-8 py-4 rounded-full text-lg font-semibold text-white shadow-lg hover:shadow-xl transition-all duration-300"
              style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
              }}
              whileHover={{ 
                scale: 1.05, 
                y: -2,
                boxShadow: '0 20px 40px rgba(102, 126, 234, 0.3)'
              }}
              whileTap={{ scale: 0.95 }}
            >
              <span className="mr-2">查看详细 AI 分析报告</span>
              <motion.span
                animate={{ x: [0, 5, 0] }}
                transition={{ 
                  duration: 1.5, 
                  repeat: Infinity, 
                  ease: 'easeInOut' 
                }}
              >
                →
              </motion.span>
            </motion.a>
            
            <motion.p
              className="mt-4 text-gray-600 text-sm"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              深入了解医学美容行业AI分析能力与技术实现
            </motion.p>
          </motion.div>

          {/* 技术亮点展示 */}
          <motion.div
            variants={titleVariants}
            className="mt-16"
          >
            <div className="bg-gradient-to-r from-gray-900 to-blue-900 rounded-3xl p-8 lg:p-12 text-white overflow-hidden relative">
              {/* 背景装饰 */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-400 to-purple-400 rounded-3xl transform rotate-3" />
                <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-green-400 to-blue-400 rounded-3xl transform -rotate-3" />
              </div>

              <div className="relative z-10">
                <div className="text-center mb-12">
                  <h3 className="text-3xl font-bold mb-4">
                    覆盖标准分析和深度分析
                  </h3>
                  {/* <p className="text-xl text-gray-300 max-w-2xl mx-auto">
                    采用前沿技术和先进算法，确保数据的准确性和可靠性
                  </p> */}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                  {[
                    {
                      icon: analysisIcon,
                      iconType: 'image',
                      title: '标准分析',
                      description: '关键指标日更，月度/季度/年度报告',
                      value: '99.2%'
                    },
                    {
                      icon: statisticsIcon,
                      iconType: 'image',
                      title: '深度分析',
                      description: '行业分析，季度/半年度/年度报告',
                      value: '<50ms'
                    },
                    {
                      icon: supplierIcon,
                      iconType: 'image',
                      title: '定制服务',
                      description: '客户画像、竞品分析、策略分析(按需)',
                      value: '256位'
                    },
                    {
                      icon: aiIcon,
                      iconType: 'image',
                      title: '100%',
                      description: 'AI自动化',
                      value: '99.9%'
                    }
                  ].map((tech, index) => (
                    <motion.div
                      key={index}
                      className="text-center"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 + 0.5 }}
                      whileHover={{ scale: 1.05 }}
                    >
                      <div className="mb-4 flex justify-center">
                        {tech.iconType === 'image' ? (
                          <img 
                            src={tech.icon} 
                            alt={tech.title}
                            className="w-12 h-12 object-contain"
                          />
                        ) : (
                          <div className="text-4xl">{tech.icon}</div>
                        )}
                      </div>
                      <h4 className="text-lg font-semibold mb-2">{tech.title}</h4>
                      <p className="text-gray-300 text-sm mb-3">{tech.description}</p>
                      {/* <motion.div
                        className="text-2xl font-bold text-blue-400"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: index * 0.1 + 0.8, type: 'spring', stiffness: 300 }}
                      >
                        {tech.value}
                      </motion.div> */}
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>

          {/* 合作伙伴展示 */}
          <motion.div
            variants={titleVariants}
            className="mt-20 text-center"
          >
            <h3 className="text-2xl font-bold text-gray-900 mb-8">
              信赖我们的合作伙伴
            </h3>
            
            <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
              {[
                '中国整形美容协会', '医师协会', '医药器械生产销售单位', '求美者', '医学美容机构'
              ].map((partner, index) => (
                <motion.div
                  key={index}
                  className="px-6 py-3 bg-gray-100 rounded-full text-gray-600 font-medium"
                  whileHover={{ scale: 1.1, opacity: 1 }}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 0.6, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                >
                  {partner}
                </motion.div>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default YmIndustryAIAnalysis; 