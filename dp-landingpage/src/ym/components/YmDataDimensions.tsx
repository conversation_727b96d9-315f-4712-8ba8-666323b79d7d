import React, { useEffect, useRef, useState } from 'react';
import { motion, useAnimation, useInView } from 'framer-motion';
import { dataDimensions } from '../data/ym-data';
import YmResponsiveImage from './YmResponsiveImage';

export const YmDataDimensions: React.FC = () => {
  const controls = useAnimation();
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: '-100px' });
  const [selectedDimension, setSelectedDimension] = useState(0);

  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [controls, isInView]);

  // 自动切换选中的维度
  useEffect(() => {
    const interval = setInterval(() => {
      setSelectedDimension((prev) => (prev + 1) % dataDimensions.length);
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  // 动画变体
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const titleVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  const dimensionVariants = {
    hidden: { opacity: 0, x: -30 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  };

  const contentVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        ease: 'easeOut'
      }
    }
  };

  return (
    <section id="data-dimensions" className="py-20 lg:py-32 ym-bg-section">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={controls}
        >
          {/* 标题区域 */}
          <motion.div variants={titleVariants} className="text-center mb-16">
            {/* <motion.span
              className="inline-block px-4 py-2 ym-tag-style rounded-full text-sm font-medium mb-6"
              whileHover={{ scale: 1.05 }}
            >
              行业 AI 分析
            </motion.span> */}
            
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6" style={{ color: 'var(--ym-text-primary)' }}>
              行业  
              <span className="ym-text-gradient-title">
              &nbsp;AI&nbsp;
              </span>
              分析
            </h2>
            
            <p className="text-xl max-w-3xl mx-auto leading-relaxed" style={{ color: 'var(--ym-text-secondary)' }}>
              从多个角度深度分析医学美容服务质量，构建全面的评估体系，
              为行业提供科学的数据支撑
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
            {/* 左侧：维度选择器 */}
            <motion.div variants={dimensionVariants} className="space-y-6">
              {dataDimensions.map((dimension, index) => (
                <motion.div
                  key={dimension.id}
                  className={`group cursor-pointer transition-all duration-300 ${
                    selectedDimension === index
                      ? 'transform scale-105'
                      : 'hover:transform hover:scale-102'
                  }`}
                  onClick={() => setSelectedDimension(index)}
                  whileHover={{ x: 10 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className={`relative p-6 rounded-2xl border-2 transition-all duration-300 ${
                    selectedDimension === index
                      ? 'border-blue-300 bg-gradient-to-r from-blue-100 to-purple-100 shadow-lg'
                      : 'ym-border-light ym-bg-primary hover:ym-border-medium hover:shadow-md'
                  }`}>
                    {/* 选中指示器 */}
                    {selectedDimension === index && (
                      <motion.div
                        className="absolute left-0 top-0 bottom-0 w-1 ym-gradient-primary rounded-l-2xl"
                        layoutId="dimensionIndicator"
                        initial={{ scaleY: 0 }}
                        animate={{ scaleY: 1 }}
                        transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                      />
                    )}

                    <div className="flex items-start space-x-4">
                      {/* 图标 */}
                      <motion.div
                        className={`w-12 h-12 rounded-xl flex items-center justify-center text-white text-xl font-bold shadow-lg transition-all duration-300 ${
                          selectedDimension === index ? 'scale-110' : ''
                        }`}
                        style={{ backgroundColor: dimension.color }}
                        whileHover={{ rotate: 10 }}
                      >
                        {dimension.icon}
                      </motion.div>

                      {/* 内容 */}
                      <div className="flex-1">
                        <h3 className={`text-lg font-bold mb-2 transition-colors duration-300 ${
                          selectedDimension === index ? 'text-blue-800' : 'ym-text-primary'
                        }`}>
                          {dimension.title}
                        </h3>
                        
                        <p className={`text-sm leading-relaxed mb-3 ${
                          selectedDimension === index ? 'text-blue-700' : ''
                        }`} style={selectedDimension === index ? {} : { color: 'var(--ym-text-secondary)' }}>
                          {dimension.description}
                        </p>

                        {/* 指标预览 */}
                        <div className="flex flex-wrap gap-2">
                          {dimension.metrics.slice(0, 3).map((metric, metricIndex) => (
                            <span
                              key={metricIndex}
                              className={`px-2 py-1 rounded-full text-xs font-medium transition-all duration-300 ${
                                selectedDimension === index
                                  ? 'bg-blue-200 text-blue-800 border border-blue-300'
                                  : 'ym-bg-subtle ym-text-secondary'
                              }`}
                            >
                              {metric.label}
                            </span>
                          ))}
                          {dimension.metrics.length > 3 && (
                            <span className="text-xs" style={{ color: 'var(--ym-text-tertiary)' }}>
                              +{dimension.metrics.length - 3} 更多
                            </span>
                          )}
                        </div>
                      </div>

                      {/* 箭头指示器 */}
                      <motion.div
                        className={`w-6 h-6 rounded-full flex items-center justify-center transition-all duration-300 ${
                          selectedDimension === index
                            ? 'ym-bg-primary text-white'
                            : 'ym-bg-subtle ym-text-tertiary group-hover:ym-bg-light'
                        }`}
                        animate={selectedDimension === index ? { x: 5 } : { x: 0 }}
                      >
                        →
                      </motion.div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>

            {/* 右侧：选中维度详情 */}
            <motion.div
              key={selectedDimension}
              variants={contentVariants}
              initial="hidden"
              animate="visible"
              className="bg-white rounded-3xl p-8 lg:p-10 shadow-xl"
            >
              {/* 头部 */}
              <div className="mb-8">
                <div className="flex items-center space-x-4 mb-6">
                  <motion.div
                    className="w-16 h-16 rounded-2xl flex items-center justify-center text-white text-2xl font-bold shadow-lg"
                    style={{ backgroundColor: dataDimensions[selectedDimension].color }}
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{ type: 'spring', stiffness: 300, damping: 20 }}
                  >
                    {dataDimensions[selectedDimension].icon}
                  </motion.div>
                  
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">
                      {dataDimensions[selectedDimension].title}
                    </h3>
                    <p className="text-gray-600">
                      {dataDimensions[selectedDimension].description}
                    </p>
                  </div>
                </div>

                {/* 权重显示 */}
                <div className="flex items-center space-x-3 mb-6">
                  <span className="text-sm font-medium text-gray-700">权重占比</span>
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <motion.div
                      className="h-2 rounded-full"
                      style={{ backgroundColor: dataDimensions[selectedDimension].color }}
                      initial={{ width: 0 }}
                      animate={{ width: `${dataDimensions[selectedDimension].weight}%` }}
                      transition={{ duration: 0.8, ease: 'easeOut' }}
                    />
                  </div>
                  <span className="text-sm font-bold text-gray-900">
                    {dataDimensions[selectedDimension].weight}%
                  </span>
                </div>
              </div>

              {/* 指标列表 */}
              <div className="mb-8">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">
                  核心评估指标
                </h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  {dataDimensions[selectedDimension].metrics.map((metric, index) => (
                    <motion.div
                      key={index}
                      className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      whileHover={{ scale: 1.02 }}
                    >
                      <motion.div
                        className="w-2 h-2 rounded-full flex-shrink-0"
                        style={{ backgroundColor: dataDimensions[selectedDimension].color }}
                        whileHover={{ scale: 1.5 }}
                      />
                      <span className="text-sm text-gray-700">{metric.label}</span>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* 示例数据可视化 */}
              <div className="p-6 bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">
                  数据示例
                </h4>
                
                {/* 模拟图表 */}
                <div className="space-y-3">
                  {[
                    { label: '优秀', value: 85, color: '#bf4e5f' },  // misty_rose.300
                    { label: '良好', value: 70, color: '#7c638f' },  // thistle.300
                    { label: '一般', value: 55, color: '#818fff' },  // lavender_web.400
                    { label: '待改进', value: 30, color: '#f530ce' }  // pale_purple.300
                  ].map((item, index) => (
                    <motion.div
                      key={index}
                      className="flex items-center space-x-3"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 + 0.3 }}
                    >
                      <span className="text-sm font-medium text-gray-700 w-16">
                        {item.label}
                      </span>
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <motion.div
                          className="h-2 rounded-full"
                          style={{ backgroundColor: item.color }}
                          initial={{ width: 0 }}
                          animate={{ width: `${item.value}%` }}
                          transition={{ duration: 0.8, delay: index * 0.1 + 0.5 }}
                        />
                      </div>
                      <span className="text-sm font-medium text-gray-900 w-8">
                        {item.value}%
                      </span>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          </div>

          {/* 底部总结 */}
          <motion.div
            variants={titleVariants}
            className="mt-20 text-center"
          >
            <div className="bg-white rounded-3xl p-8 lg:p-12 shadow-lg">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">
                数据驱动的科学评估体系
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {[
                  {
                    icon: '📊',
                    title: '多维度分析',
                    description: '从5个核心维度全面评估医学美容服务质量'
                  },
                  {
                    icon: '🎯',
                    title: '精准权重',
                    description: '基于行业标准和用户反馈优化权重配置'
                  },
                  {
                    icon: '📈',
                    title: '实时监测',
                    description: '持续跟踪评估结果，动态调整评估模型'
                  }
                ].map((feature, index) => (
                  <motion.div
                    key={index}
                    className="text-center"
                    whileHover={{ y: -5 }}
                    transition={{ type: 'spring', stiffness: 300 }}
                  >
                    <div className="text-4xl mb-4">{feature.icon}</div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-2">
                      {feature.title}
                    </h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {feature.description}
                    </p>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default YmDataDimensions;