import React, { useEffect, useRef, useState } from 'react';
import { motion, useAnimation, useInView } from 'framer-motion';
import { statistics } from '../data/ym-data';
import { formatNumber, animateValue } from '../utils/ym-utils';

// 导入图片
import chart1 from '../assets/images/demo_chart/1.jpg';
import chart2 from '../assets/images/demo_chart/2.png';
import chart3 from '../assets/images/demo_chart/3.png';
import chart4 from '../assets/images/demo_chart/4.png';
import chart5 from '../assets/images/demo_chart/5.png';

export const YmStatistics: React.FC = () => {
  const controls = useAnimation();
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: '-100px' });
  const [animatedValues, setAnimatedValues] = useState<{ [key: string]: number }>({});
  
  // 图片轮播状态
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const carouselImages = [
    chart1,
    chart2,
    chart3,
    chart4,
    chart5
  ];

  useEffect(() => {
    if (isInView) {
      controls.start('visible');
      
      // 启动数字动画
      statistics.forEach((stat) => {
        const numericValue = stat.value;
        if (!isNaN(numericValue)) {
          animateValue(0, numericValue, 2000, (value) => {
            setAnimatedValues(prev => ({
              ...prev,
              [stat.id]: value
            }));
          });
        }
      });
    }
  }, [controls, isInView]);

  // 图片轮播自动切换
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) => 
        (prevIndex + 1) % carouselImages.length
      );
    }, 4000); // 每4秒切换一次

    return () => clearInterval(interval);
  }, [carouselImages.length]);

  // 动画变体
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const titleVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  const statVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  // 格式化动画值
  const formatAnimatedValue = (stat: any) => {
    const animatedValue = animatedValues[stat.id];
    if (animatedValue === undefined) {
      return `${stat.value}${stat.unit}`;
    }
    
    if (stat.unit === '%') {
      return `${Math.round(animatedValue)}%`;
    } else if (stat.unit === '+') {
      return `${formatNumber(animatedValue)}+`;
    } else {
      return `${formatNumber(animatedValue)}${stat.unit}`;
    }
  };

  return (
    <section id="statistics" className="py-20 lg:py-32 ym-bg-section overflow-hidden relative">
      {/* 背景装饰 */}
      <div className="absolute inset-0 opacity-10">
        <motion.div
          className="absolute top-1/4 left-1/4 w-64 h-64 bg-blue-500 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.1, 0.3, 0.1]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: 'easeInOut'
          }}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-purple-500 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.1, 0.2, 0.1]
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: 'easeInOut'
          }}
        />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={controls}
        >
          {/* 标题区域 */}
          <motion.div variants={titleVariants} className="text-center mb-16">
            <motion.span
              className="inline-block px-4 py-2 ym-tag-style rounded-full text-sm font-medium mb-6"
              whileHover={{ scale: 1.05 }}
            >
              案例展示
            </motion.span>
            
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6">
              
              <span className="ym-text-gradient-title">
                AI解码未来
              </span>
            </h2>
            
            <p className="text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
            从数据海洋到商业洞见，AI一键解码未来
            </p>
          </motion.div>

          {/* 图片轮播组件 */}
          <motion.div 
            variants={titleVariants}
            className="mb-16"
          >
            <div className="relative max-w-4xl mx-auto">
                             {/* 轮播容器 */}
               <div className="relative h-96 lg:h-[500px] rounded-3xl overflow-hidden shadow-2xl bg-white p-4">
                 {/* 图片容器 */}
                 <div className="relative w-full h-full rounded-2xl overflow-hidden">
                  {carouselImages.map((image, index) => (
                    <motion.div
                      key={index}
                      className="absolute inset-0"
                      initial={{ opacity: 0, scale: 1.1 }}
                      animate={{ 
                        opacity: index === currentImageIndex ? 1 : 0,
                        scale: index === currentImageIndex ? 1 : 1.1
                      }}
                      transition={{ 
                        duration: 0.8,
                        ease: 'easeInOut'
                      }}
                    >
                      <img
                        src={image}
                        alt={`数据图表 ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </motion.div>
                  ))}
                </div>

                {/* 轮播指示器 */}
                <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-3">
                  {carouselImages.map((_, index) => (
                    <motion.button
                      key={index}
                      className={`w-3 h-3 rounded-full transition-all duration-300 ${
                        index === currentImageIndex 
                          ? 'bg-white scale-125' 
                          : 'bg-white/50 hover:bg-white/75'
                      }`}
                      onClick={() => setCurrentImageIndex(index)}
                      whileHover={{ scale: 1.2 }}
                      whileTap={{ scale: 0.9 }}
                    />
                  ))}
                </div>

                {/* 左右导航箭头 */}
                <motion.button
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-black/60 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/75 transition-all duration-300 shadow-lg"
                  onClick={() => setCurrentImageIndex((prev) => 
                    prev === 0 ? carouselImages.length - 1 : prev - 1
                  )}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </motion.button>

                <motion.button
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-black/60 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/75 transition-all duration-300 shadow-lg"
                  onClick={() => setCurrentImageIndex((prev) => 
                    (prev + 1) % carouselImages.length
                  )}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </motion.button>
              </div>

              {/* 轮播标题和描述 */}
              <motion.div 
                className="text-center mt-8"
                key={currentImageIndex}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <h3 className="text-2xl font-bold mb-3 text-gray-800">
                   AI 分析示例 {currentImageIndex + 1}
                </h3>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  专业的数据分析图表，直观展示调研结果和统计数据，帮助用户更好地理解医学美容行业趋势
                </p>
              </motion.div>
            </div>
          </motion.div>

          {/* 统计数据网格 */}
          {/* <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {statistics.map((stat, index) => (
              <motion.div
                key={stat.id}
                variants={statVariants}
                className="group relative"
                whileHover={{ y: -10 }}
                transition={{ type: 'spring', stiffness: 300 }}
              >
                 卡片背景 
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-white/5 rounded-3xl backdrop-blur-sm group-hover:from-white/20 group-hover:to-white/10 transition-all duration-300" />
                
                 边框发光效果 
                <motion.div
                  className="absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  style={{
                    background: `linear-gradient(135deg, ${stat.color}20, transparent, ${stat.color}20)`,
                    padding: '1px'
                  }}
                >
                  <div className="w-full h-full bg-gray-900/50 rounded-3xl" />
                </motion.div>

                <div className="relative z-10 p-8 text-center">
                   图标 
                  <motion.div
                    className="w-16 h-16 mx-auto mb-6 rounded-2xl flex items-center justify-center text-white text-2xl font-bold shadow-lg"
                    style={{ backgroundColor: stat.color }}
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ type: 'spring', stiffness: 300 }}
                  >
                    {stat.icon}
                  </motion.div>

                   数值 
                  <motion.div
                    className="text-4xl lg:text-5xl font-bold mb-2"
                    style={{ color: stat.color }}
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: index * 0.1 + 0.5, type: 'spring', stiffness: 300 }}
                  >
                    {formatAnimatedValue(stat)}
                  </motion.div>

                   标签 
                  <div className="text-lg font-semibold text-gray-700 mb-2">
                    {stat.label}
                  </div>

                   描述 
                  <div className="text-sm text-gray-600 leading-relaxed">
                    {stat.description}
                  </div>

                   趋势指示器 
                  <motion.div
                    className="mt-4 flex items-center justify-center space-x-2"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: index * 0.1 + 1 }}
                  >
                    <motion.div
                      className="w-2 h-2 bg-green-600 rounded-full"
                      animate={{
                        scale: [1, 1.5, 1],
                        opacity: [0.5, 1, 0.5]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: 'easeInOut'
                      }}
                    />
                    <span className="text-xs text-green-600 font-medium">
                      持续增长
                    </span>
                  </motion.div>
                </div>

                 装饰粒子 
                <motion.div
                  className="absolute top-4 right-4 w-2 h-2 rounded-full opacity-40"
                  style={{ backgroundColor: stat.color }}
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [0.4, 0.8, 0.4]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: 'easeInOut',
                    delay: index * 0.5
                  }}
                />
              </motion.div>
            ))}
          </div> */}

          {/* 成长历程时间线 */}
          {/* 成长历程时间线 - 暂时注释掉
          <motion.div
            variants={titleVariants}
            className="mb-16"
          >
            <div className="text-center mb-12">
              <h3 className="text-3xl font-bold mb-4">
                成长历程
              </h3>
              <p className="text-gray-700 max-w-2xl mx-auto">
                从创立至今，我们不断突破，用数据见证每一个重要时刻
              </p>
            </div>

            <div className="relative">
              时间线
              <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-blue-500 to-purple-500 rounded-full" />

              <div className="space-y-12">
                {[
                  { year: '2023', title: '平台上线', description: '医学美容调研平台正式上线运营', milestone: '1,000+ 用户' },
                  { year: '2024', title: '快速增长', description: '用户规模快速增长，功能持续优化', milestone: '10,000+ 用户' },
                  { year: '2024', title: '行业认可', description: '获得多项行业认证和合作伙伴', milestone: '50+ 机构' },
                  { year: '未来', title: '持续创新', description: '不断创新，引领医学美容行业数字化转型', milestone: '无限可能' }
                ].map((milestone, index) => (
                  <motion.div
                    key={index}
                    className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}
                    initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.2 + 0.5 }}
                  >
                    <div className={`w-5/12 ${index % 2 === 0 ? 'text-right pr-8' : 'text-left pl-8'}`}>
                      <motion.div
                        className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 hover:bg-white/20 transition-colors duration-300"
                        whileHover={{ scale: 1.05 }}
                      >
                        <div className="text-2xl font-bold text-blue-400 mb-2">
                          {milestone.year}
                        </div>
                        <div className="text-lg font-semibold text-gray-800 mb-2">
                          {milestone.title}
                        </div>
                        <div className="text-gray-700 text-sm mb-3">
                          {milestone.description}
                        </div>
                        <div className="text-purple-400 font-medium text-sm">
                          {milestone.milestone}
                        </div>
                      </motion.div>
                    </div>

                    时间点
                    <motion.div
                      className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full border-4 border-gray-900 z-10"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: index * 0.2 + 0.7, type: 'spring', stiffness: 300 }}
                      whileHover={{ scale: 1.5 }}
                    />

                    <div className="w-5/12" />
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
          */}

          {/* 实时数据展示 */}
          {/* <motion.div
            variants={titleVariants}
            className="text-center"
          >
            <div className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm rounded-3xl p-8 lg:p-12 border border-blue-500/30">
              <h3 className="text-2xl font-bold mb-6">
                实时数据监控
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {[
                  { label: '今日活跃用户', value: '2,847', trend: '+12%' },
                  { label: '本月新增调研', value: '156', trend: '+8%' },
                  { label: '平台满意度', value: '98.7%', trend: '+0.3%' }
                ].map((item, index) => (
                  <motion.div
                    key={index}
                    className="text-center"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 + 1 }}
                  >
                    <div className="text-3xl font-bold text-gray-800 mb-2">
                      {item.value}
                    </div>
                    <div className="text-gray-700 text-sm mb-2">
                      {item.label}
                    </div>
                    <motion.div
                      className="text-green-600 text-sm font-medium"
                      animate={{
                        scale: [1, 1.1, 1]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: 'easeInOut'
                      }}
                    >
                      {item.trend}
                    </motion.div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div> */}
        </motion.div>
      </div>
    </section>
  );
};

export default YmStatistics;