import React from 'react';
import { motion } from 'framer-motion';
import ReactECharts from 'echarts-for-react';

// 通用表格列定义
interface TableColumn {
  title: string;
  dataIndex: string;
  key: string;
  width?: number | string;
  className?: string;
  render?: string;
}

// 通用表格数据
interface TableData {
  key: string;
  [key: string]: any;
}

// 通用表格配置
interface TableConfig {
  columns: TableColumn[];
  data: TableData[];
}

// 洞察区域配置
interface InsightConfig {
  title: string;
  icon?: string | React.ReactNode;
  items: string[];
}

// 文本卡片项配置
interface TextCardItem {
  title: string;
  description: string;
  bulletPoints?: string[]; // 新增：支持 bullet points 列表
}

// 文本卡片配置
interface TextCardConfig {
  title: string;
  items: TextCardItem[];
}

// 布局类型
type LayoutType = 'split' | 'stacked';

// 分栏比例类型
type SplitRatioType = '1:1' | '2:1' | '1:2';

// 组件属性
interface UniversalChartCardProps {
  title: string;
  description: string;
  chartConfig?: any;
  tableData?: TableConfig;
  chartImage?: string; // 新增：支持图片显示
  textCard?: TextCardConfig; // 新增：支持文本卡片显示，替换 insights
  insights?: InsightConfig; // 修改：设为可选，与 textCard 互斥
  recommendations?: InsightConfig; // 修改：设为可选
  isReversed?: boolean;
  layout?: LayoutType;
  splitRatio?: SplitRatioType; // 新增：分栏布局比例配置
  className?: string;
  chartHeight?: string;
  echartsContainerHeight?: string; // 新增：ECharts 容器整体高度配置
  titleAlign?: 'left' | 'center' | 'right'; // 新增：标题对齐方式
  textAlign?: 'left' | 'center' | 'right'; // 新增：文本对齐方式
}

export const UniversalChartCard: React.FC<UniversalChartCardProps> = ({
  title,
  description,
  chartConfig,
  tableData,
  chartImage,
  textCard,
  insights,
  recommendations,
  isReversed = false,
  layout = 'split',
  splitRatio = '1:1',
  className = '',
  chartHeight = '400px',
  echartsContainerHeight = '500px',
  titleAlign = 'left',
  textAlign = 'left'
}) => {
  // 新增：图片放大显示状态
  const [isImageModalOpen, setIsImageModalOpen] = React.useState(false);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  // 新增：图片放大模态框组件
  const ImageModal: React.FC = () => {
    if (!isImageModalOpen || !chartImage) return null;

    const handleBackdropClick = (e: React.MouseEvent) => {
      if (e.target === e.currentTarget) {
        setIsImageModalOpen(false);
      }
    };

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setIsImageModalOpen(false);
      }
    };

    React.useEffect(() => {
      if (isImageModalOpen) {
        document.addEventListener('keydown', handleKeyDown);
        document.body.style.overflow = 'hidden';
      } else {
        document.removeEventListener('keydown', handleKeyDown);
        document.body.style.overflow = 'unset';
      }

      return () => {
        document.removeEventListener('keydown', handleKeyDown);
        document.body.style.overflow = 'unset';
      };
    }, [isImageModalOpen]);

    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3 }}
        className="fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center p-4"
        onClick={handleBackdropClick}
      >
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.8, opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="relative max-w-[95vw] max-h-[95vh] bg-white rounded-lg overflow-hidden shadow-2xl"
        >
          {/* 关闭按钮 */}
          <button
            onClick={() => setIsImageModalOpen(false)}
            className="absolute top-4 right-4 z-10 w-10 h-10 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full flex items-center justify-center shadow-lg transition-all duration-200 hover:scale-110"
            aria-label="关闭图片"
          >
            <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          {/* 放大的图片 */}
          <img
            src={chartImage}
            alt="放大图片"
            className="w-full h-full object-contain"
            style={{ maxWidth: '95vw', maxHeight: '95vh' }}
          />

          {/* 图片标题（可选） */}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-6">
            <h3 className="text-white text-lg font-semibold">{title}</h3>
            {description && (
              <p className="text-gray-200 text-sm mt-2 line-clamp-2">{description}</p>
            )}
          </div>
        </motion.div>
      </motion.div>
    );
  };

  // 渲染进度条
  const renderProgressBar = (value: number) => (
    <div className="w-full bg-gray-200 rounded-full h-2">
      <div 
        className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-1000"
        style={{ width: `${value}%` }}
      />
      <span className="text-xs text-gray-600 ml-2">{value}%</span>
    </div>
  );

  // 渲染状态标签
  const renderStatusTag = (status: string, color: string) => {
    const colorMap = {
      green: 'bg-green-100 text-green-800',
      blue: 'bg-blue-100 text-blue-800',
      orange: 'bg-orange-100 text-orange-800',
      gray: 'bg-gray-100 text-gray-800'
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${colorMap[color as keyof typeof colorMap] || colorMap.gray}`}>
        {status}
      </span>
    );
  };

  // 渲染表格
  const renderTable = (config: TableConfig) => (
    <div className="overflow-x-auto bg-white rounded-lg shadow-sm border border-gray-200">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            {config.columns.map((column, index) => (
              <th
                key={index}
                style={{ width: column.width }}
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                {column.title}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {config.data.map((row, rowIndex) => (
            <tr key={rowIndex} className="hover:bg-gray-50 transition-colors duration-200">
              {config.columns.map((column, colIndex) => (
                <td
                  key={colIndex}
                  className={`px-6 py-4 whitespace-nowrap text-sm text-gray-900 ${column.className || ''}`}
                >
                  {column.render === 'progress' && typeof row[column.dataIndex] === 'number' 
                    ? renderProgressBar(row[column.dataIndex])
                    : column.render === 'tag' 
                    ? renderStatusTag(row[column.dataIndex], row[`${column.dataIndex}Color`])
                    : row[column.dataIndex]
                  }
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  // 获取对齐样式类
  const getAlignClass = (align: 'left' | 'center' | 'right') => {
    switch (align) {
      case 'center':
        return 'text-center';
      case 'right':
        return 'text-right';
      default:
        return 'text-left';
    }
  };

  // 渲染文本卡片
  const renderTextCard = (config: TextCardConfig) => (
    <div className={`bg-white rounded-lg p-6 ${getAlignClass(textAlign)}`}>
      {config.title && (
        <h3 className={`text-lg font-bold mb-6 text-gray-800 ${getAlignClass(titleAlign)}`}>
          {config.title}
        </h3>
      )}
      <div className="space-y-6">
        {config.items.map((item, index) => (
          <div key={index}>
            <h4 className="text-base font-semibold text-gray-800 mb-2">
              {item.title}
            </h4>
            <p className="text-gray-600 text-xs leading-relaxed">
              {item.description}
            </p>
            {/* 新增：支持 bullet points 列表 */}
            {item.bulletPoints && item.bulletPoints.length > 0 && (
              <ul className="mt-3 space-y-2">
                {item.bulletPoints.map((point, pointIndex) => (
                  <li key={pointIndex} className="flex items-start">
                    <span className="flex-shrink-0 w-1.5 h-1.5 bg-gray-400 rounded-full mt-2.5 mr-3"></span>
                    <span className="text-gray-600 text-xs leading-relaxed">{point}</span>
                  </li>
                ))}
              </ul>
            )}
          </div>
        ))}
      </div>
    </div>
  );

  // 渲染图表
  const renderChart = (containerHeight?: string) => {
    if (!chartConfig && !chartImage) return null;

    // 确定实际使用的容器高度
    const actualContainerHeight = containerHeight || echartsContainerHeight;

    return (
      <div 
        className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        style={{ height: actualContainerHeight }}
      >
        {chartImage ? (
          <div 
            className="flex justify-center items-center cursor-pointer group relative overflow-hidden rounded-lg h-full" 
            onClick={() => setIsImageModalOpen(true)}
          >
            <img 
              src={chartImage} 
              alt="Chart" 
              className="max-w-full max-h-full object-contain rounded-lg transition-transform duration-300 group-hover:scale-105"
            />
            {/* 悬停时的放大提示 */}
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center rounded-lg">
              <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-white bg-opacity-90 rounded-full p-3">
                <svg className="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                </svg>
              </div>
            </div>
          </div>
        ) : (
          <div className="h-full">
            <ReactECharts
              option={chartConfig}
              style={{ height: '100%', width: '100%' }}
              opts={{ renderer: 'canvas' }}
            />
          </div>
        )}
      </div>
    );
  };

  // 渲染洞察区域
  const renderInsightSection = (config: InsightConfig) => (
    <div className="bg-gray-50 rounded-lg p-6 border border-gray-100">
      <h4 className="text-lg font-semibold mb-4 text-gray-800 flex items-center">
        {typeof config.icon === 'string' && (config.icon.includes('.') || config.icon.includes('/')) ? (
          <img src={config.icon} alt={config.title} className="w-5 h-5 mr-2" />
        ) : config.icon ? (
          <span className="mr-2">{config.icon}</span>
        ) : (
          <span className="text-gray-500 mr-2">📊</span>
        )}
        {config.title}
      </h4>
      <ul className="space-y-3">
        {config.items.map((item, index) => (
          <li key={index} className="flex items-start">
            <span className="flex-shrink-0 w-1.5 h-1.5 bg-gray-400 rounded-full mt-2.5 mr-3"></span>
            <span className="text-gray-700 text-sm leading-relaxed">{item}</span>
          </li>
        ))}
      </ul>
    </div>
  );

  // 渲染内容区域（分栏布局）
  const renderSplitLayout = () => {
    // 根据 splitRatio 确定 grid 布局类
    const getGridClass = () => {
      switch (splitRatio) {
        case '2:1':
          return 'grid grid-cols-1 lg:grid-cols-3 gap-12 items-start';
        case '1:2':
          return 'grid grid-cols-1 lg:grid-cols-3 gap-12 items-start';
        default:
          return 'grid grid-cols-1 lg:grid-cols-2 gap-12 items-start';
      }
    };

    // 根据 splitRatio 确定文本区域的列跨度
    const getTextColSpan = () => {
      if (splitRatio === '2:1') return isReversed ? 'lg:col-span-2 lg:order-1' : 'lg:col-span-1';
      if (splitRatio === '1:2') return isReversed ? 'lg:col-span-1 lg:order-2' : 'lg:col-span-2';
      return isReversed ? 'lg:order-2' : '';
    };

    // 根据 splitRatio 确定图表区域的列跨度
    const getChartColSpan = () => {
      if (splitRatio === '2:1') return isReversed ? 'lg:col-span-1 lg:order-2' : 'lg:col-span-2';
      if (splitRatio === '1:2') return isReversed ? 'lg:col-span-2 lg:order-1' : 'lg:col-span-1';
      return isReversed ? 'lg:order-1' : '';
    };

    return (
      <div className={getGridClass()}>
        {/* 内容区域 */}
        <motion.div variants={itemVariants} className={`space-y-6 ${getTextColSpan()}`}>
          {/* 标题和描述区域 - 与 textCard 保持一致的样式 */}
          <div className={`bg-white rounded-lg p-6 ${getAlignClass(textAlign)}`}>
            <h3 className={`text-lg font-bold mb-6 ${getAlignClass(titleAlign)}`} style={{ color: 'var(--ym-text-primary)' }}>
              {title}
            </h3>
            <p className="text-gray-600 text-sm leading-relaxed w-full">
              {description}
            </p>
          </div>

          {/* 洞察区域或文本卡片 */}
          {textCard ? renderTextCard(textCard) : insights && renderInsightSection(insights)}

          {/* 建议区域 */}
          {recommendations && renderInsightSection(recommendations)}
        </motion.div>

        {/* 图表/表格区域 */}
        <motion.div variants={itemVariants} className={getChartColSpan()}>
          {(chartConfig || chartImage) && renderChart()}
          {tableData && renderTable(tableData)}
        </motion.div>
      </div>
    );
  };

  // 渲染内容区域（堆叠布局）
  const renderStackedLayout = () => (
    <div className={`grid grid-cols-1 lg:grid-cols-5 gap-8 items-start`}>
      {/* 图表/表格区域 */}
      <div className={`${isReversed ? 'lg:order-2 lg:col-span-3' : 'lg:col-span-3'}`}>
        <motion.div 
          variants={itemVariants}
          className="bg-white rounded-xl p-6 shadow-lg border border-gray-100"
        >
          {(chartConfig || chartImage) && renderChart(echartsContainerHeight)}
          {tableData && renderTable(tableData)}
        </motion.div>
      </div>

      {/* 分析解读区域 */}
      <div className={`${isReversed ? 'lg:order-1 lg:col-span-2' : 'lg:col-span-2'} space-y-6`}>
        {/* 洞察区域或文本卡片 */}
        <motion.div variants={itemVariants}>
          {textCard ? renderTextCard(textCard) : insights && renderInsightSection(insights)}
        </motion.div>

        {/* 建议区域 */}
        {recommendations && (
          <motion.div variants={itemVariants}>
            {renderInsightSection(recommendations)}
          </motion.div>
        )}
      </div>
    </div>
  );

  return (
    <section className={`py-16 bg-white ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {/* 标题和描述（仅在堆叠布局时显示） */}
          {layout === 'stacked' && (
            <motion.div variants={itemVariants} className={`mb-12`}>
              <div className={`bg-white rounded-lg p-6 ${getAlignClass(textAlign)}`}>
                <h3 className={`text-lg font-bold mb-6 ${getAlignClass(titleAlign)}`} style={{ color: 'var(--ym-text-primary)' }}>
                  {title}
                </h3>
                <p className={`text-sm text-gray-600 leading-relaxed w-full`}>
                  {description}
                </p>
              </div>
            </motion.div>
          )}

          {/* 内容区域 */}
          {layout === 'split' ? renderSplitLayout() : renderStackedLayout()}
        </motion.div>
      </div>

      {/* 图片放大模态框 */}
      <ImageModal />
    </section>
  );
};

export default UniversalChartCard; 