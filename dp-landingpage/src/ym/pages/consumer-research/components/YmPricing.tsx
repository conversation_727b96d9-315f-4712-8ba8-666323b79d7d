import React, { useState } from 'react';
import { motion } from 'framer-motion';
import checkIcon from '../../../../assets/icons/check.png';
import YmDialog from '../../../components/YmDialog';
import salesQrCode from '../../../assets/images/dips-agent.png';

const YmPricing: React.FC = () => {
  // 联系销售顾问弹窗状态
  const [isContactDialogOpen, setContactDialogOpen] = useState(false);

  const openContactDialog = () => setContactDialogOpen(true);
  const closeContactDialog = () => setContactDialogOpen(false);

  // 套餐数据
  const pricingPlans = [
    {
      id: 'basic',
      name: '基础版',
      price: '待定',
      originalPrice: '',
      discount: '',
      discountEndDate: '',
      period: '/ 年',
      description: '智能版标准化求美者体验研究',
      isAvailable: true,
      isPopular: true,
      features: [
        { 
          name: '求美者体验研究问卷', 
          description: '专业定制问卷服务 - 专业定制问卷服务，基于李克特5分量表精准评估产品与服务核心维度，真实反馈客户需求',
          included: true 
        },
        { 
          name: '数据采集', 
          description: '多渠道智能数据收集 - 支持一店一码、短信链接等多种线上填写方式，数据实时同步云端，杜绝手工录入与信息遗漏',
          included: true 
        },
        { 
          name: '数据质量控制', 
          description: '数据清洗与验证服务 - 专业数据分析团队对原始数据进行逻辑校验、完整性检查及异常值处理，确保最终分析结果精准可靠',
          included: true 
        },
        { 
          name: '数据分析', 
          description: '深度洞察分析服务 - 自研数据流计算引擎，实现高效低成本数据处理，支持多维度交叉分析及NPS/忠诚度等专业模型评估，快速识别服务短板',
          included: true 
        },
        { 
          name: '数据动态看板', 
          description: '实时可视化平台 - 交互看板实时展示调研进度、回访得分、客户VOC数据，多维度筛选分析，助力机构动态掌握求美者评价',
          included: true 
        },
        { 
          name: '回访数据导出', 
          description: '灵活数据导出功能 - 支持按需导出Excel格式的原始数据，便于二次分析或纳入其他管理系统',
          included: true 
        },
        { 
          name: '标准分析报告', 
          description: '标准分析报告服务 - 调研周期内月度更新标准报告，支持多维度分析及可视化图表，配置灵活高效',
          included: true 
        },
        { 
          name: '使用手册', 
          description: '手册服务-本手册详细说明系统的操作流程与功能模块，帮助用户快速掌握系统使用方法，实现高效数据管理与分析',
          included: true 
        },
        { 
          name: '客服支持', 
          description: '专业客服团队服务-解答使用问题，协助您高效开展求美者体验研究工作',
          included: true 
        }
      ]
    },
    {
      id: 'premium',
      name: '升级专业版',
      price: '即将推出',
      period: '',
      description: '定制化全流程行业AI',
      isAvailable: true,
      isPopular: false,
      features: [
        { 
          name: '基础版所有功能', 
          description: '包含基础版本中的所有功能',
          included: true 
        },
        { 
          name: '关键指标异常自动预警', 
          description: '每当有不满意的客户完成答卷，通过邮件接收通知，及时采取补救措施，挽回客户关系，降低客户流失风险',
          included: true 
        },
        { 
          name: '分析结果智能输出', 
          description: '基于调研数据智能输出调研结果月度统计报表',
          included: true 
        },
        { 
          name: '行业数据查询', 
          description: '快捷查询行业数据库，精准定位本品在竞争市场中的坐标位次和竞争力水平，了解行业基准数据，找到提升方向',
          included: true 
        },
        { 
          name: '高级分析', 
          description: '增加文本情感分析、求美者行为和偏好、求美者满意度等深度分析，基于消费行为和满意度数据，将客户划分类型',
          included: true 
        },
        { 
          name: '定制报告服务', 
          description: '根据机构特定需求制作个性化分析报告，满足不同层级管理者的决策需求，提供更有针对性的数据支持',
          included: true 
        },
        { 
          name: '报告解读', 
          description: '专业咨询顾问进行数据解读与数据应用指导，帮助您深入理解数据背后的含义，制定优化策略',
          included: true 
        }
      ]
    }
  ];

  // 动画变体
  const containerVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        staggerChildren: 0.2
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  const handlePurchase = (planId: string) => {
    // 所有套餐都通过联系销售顾问进行咨询
    openContactDialog();
  };

  return (
    <section className="py-24 bg-gradient-to-br from-purple-50 to-violet-50 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-grid-purple-100 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))]" />
      
      <div className="container mx-auto px-6 relative z-10">
        {/* 标题区域 */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            <span className="ym-gradient-text">套餐介绍</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            为您精选了以下核心功能，助您快速起步
          </p>
        </motion.div>

        {/* 套餐卡片 */}
        <motion.div
          className="grid md:grid-cols-2 gap-12 max-w-6xl mx-auto"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {pricingPlans.map((plan) => (
            <motion.div
              key={plan.id}
              variants={cardVariants}
              className={`relative bg-white rounded-3xl shadow-xl overflow-hidden ${
                plan.isPopular ? 'ring-2 ring-purple-500' : ''
              } ${!plan.isAvailable ? 'opacity-75' : ''}`}
            >
              {/* 推荐标签 */}
              {plan.isPopular && (
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 bg-purple-500 text-white px-4 py-1 rounded-b-lg text-sm font-medium">
                  推荐
                </div>
              )}

              <div className="p-10">
                {/* 套餐标题 */}
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">
                    {plan.name}
                  </h3>
                  <div className="flex flex-col items-center mb-2">
                    {/* 价格显示 */}
                    <div className="flex items-baseline justify-center">
                      <span className="text-5xl font-bold text-purple-600">
                        {plan.price === '即将推出' ? plan.price : `¥${plan.price}`}
                      </span>
                      <span className="text-lg text-purple-500 ml-1">
                        {plan.period}
                      </span>
                    </div>
                    
                    {/* 原价显示 */}
                    {plan.originalPrice && (
                      <div className="flex items-center justify-center mt-1 gap-3">
                        <span className="text-lg text-gray-400 line-through">
                          原价 ¥{plan.originalPrice}
                        </span>
                        <span className="text-sm text-green-600 font-medium">
                          节省 ¥{(parseFloat(plan.originalPrice.replace(',', '')) - parseFloat(plan.price.replace(',', ''))).toLocaleString()}
                        </span>
                      </div>
                    )}
                  </div>
                  <p className="text-gray-600">{plan.description}</p>
                </div>

                {/* 折扣提示 */}
                {plan.discount && (
                  <div className="mb-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                    <div className="flex items-center justify-center gap-2">
                      <span className="text-orange-600 text-sm font-medium">
                        🔥 限时优惠
                      </span>
                      <span className="text-orange-700 text-sm">
                        {plan.discountEndDate}前享受{plan.discount}优惠
                      </span>
                    </div>
                  </div>
                )}

                {/* 购买按钮 */}
                <div className="mb-8">
                  <button
                      onClick={() => handlePurchase(plan.id)}
                      className="w-full py-4 px-6 rounded-xl font-semibold text-lg transition-all duration-300 bg-purple-600 hover:bg-purple-700 text-white hover:shadow-lg transform hover:scale-[1.02]"
                    >
                      立即咨询
                    </button>
                </div>

                {/* 功能列表 */}
                <div className="space-y-6">
                  {plan.features.map((feature, index) => (
                    <div
                      key={index}
                      className="flex items-start space-x-4 p-3 rounded-lg transition-all duration-300 hover:bg-purple-50 hover:shadow-sm cursor-pointer group"
                    >
                      <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center mt-1 ${
                        feature.included ? 'bg-purple-100' : 'bg-gray-100'
                      }`}>
                        {feature.included ? (
                          <img src={checkIcon} alt="check" className="w-6 h-6" />
                        ) : (
                          <span className="text-gray-400 text-xs font-bold">×</span>
                        )}
                      </div>
                      <div className="flex-1">
                        <h4 className={`mb-1 transition-all duration-300 group-hover:font-bold group-hover:text-lg group-hover:text-purple-700 ${
                          feature.included ? 'text-gray-900 font-semibold' : 'text-gray-400 font-semibold'
                        }`}>
                          {feature.name}
                        </h4>
                        <p className={`text-sm leading-relaxed transition-all duration-300 group-hover:text-gray-800 ${
                          feature.included ? 'text-gray-600' : 'text-gray-400'
                        }`}>
                          {feature.description}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 装饰性渐变 */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-200 to-violet-200 rounded-full blur-3xl opacity-20" />
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-br from-purple-200 to-violet-200 rounded-full blur-2xl opacity-15" />
            </motion.div>
          ))}
        </motion.div>

        {/* 底部说明 */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <p className="text-gray-600 text-lg">
            需要定制化方案？
            <button 
              onClick={openContactDialog}
              className="text-purple-600 hover:text-purple-700 font-medium ml-2 underline bg-transparent border-none cursor-pointer"
            >
              联系我们
            </button>
          </p>
        </motion.div>
      </div>

      {/* 联系销售顾问弹窗 */}
      <YmDialog isOpen={isContactDialogOpen} onClose={closeContactDialog}>
        <div className="text-center p-6">
          <h3 className="text-2xl font-bold mb-4 text-gray-800">联系销售顾问</h3>
          <p className="mb-6 text-gray-600">微信扫码，我们即刻为您服务</p>
          <div className="bg-white p-4 rounded-lg inline-block shadow-lg border border-gray-200">
            <img
              src={salesQrCode}
              alt="销售顾问微信二维码"
              className="w-56 h-56 mx-auto"
            />
          </div>
          <p className="mt-6 text-sm text-gray-500">工作时间：周一至周五 9:00-18:00</p>
          <button
            onClick={closeContactDialog}
            className="mt-6 px-6 py-2 rounded-full text-base bg-gray-100 hover:bg-gray-200 text-gray-700 transition-colors"
          >
            关闭
          </button>
        </div>
      </YmDialog>
    </section>
  );
};

export default YmPricing; 