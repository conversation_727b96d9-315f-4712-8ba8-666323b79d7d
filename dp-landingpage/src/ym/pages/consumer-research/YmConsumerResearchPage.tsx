import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// 组件导入
import { ConsumerResearchNavbar, YmPricing } from './components';
import UniversalChartCard from '../../components/UniversalChartCard';
import YmFooter from '../../components/YmFooter';

// 图片导入
import statisticsIcon from '../../../assets/icons/statistics.png';
import ideaIcon from '../../../assets/icons/idea.png';
import userProfileIcon from '../../../assets/icons/user-profile.png';
import checkIcon from '../../../assets/icons/check.png';
import trackIcon from '../../../assets/icons/track.png';
import increaseIcon from '../../../assets/icons/increase.png';
import fireworkIcon from '../../../assets/icons/firework.png';
// 示例：导入图片用于图表显示
import chartImage1 from '../../assets/images/cr/1.png';
import chartImage2 from '../../assets/images/cr/2.png';
import chartImage3 from '../../assets/images/cr/3.jpg';
import chartImage4 from '../../assets/images/cr/4.png';
import chartImage5 from '../../assets/images/cr/5.png';
import chartImage6 from '../../assets/images/cr/6.png';
import chartImage7 from '../../assets/images/cr/7.jpg';

// 样式导入
import '../../styles/ym-custom.css';

// 数据导入
// import satisfactionData from './data/satisfaction-analysis.json';
// import consumerBehaviorData from './data/consumer-behavior.json';
// import marketTrendsData from './data/market-trends.json';
import consumerProfileTableData from './data/consumer-profile-table.json';
// import orgnizationStoreSatis from './data/orgnization_store_satis.json';
// import orgnizationKeyIndex from './data/orgnization_key_index.json';
// import orgnizationLoyalMonth from './data/orgnization_loyal_month.json';
import combinedCharts from './data/combined_charts.json';
import consumerBehaviorData from './data/consumer-behavior.json';
import storeData from './data/store.json';


export const YmConsumerResearchPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // 设置页面标题
    document.title = '求美者体验研究 - 医学美容满意度调研平台';
    
    // 设置meta描述
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', '深入了解医学美容求美者行为模式、需求偏好和满意度体验，为医学美容机构和厂家提供精准的求美者洞察数据');
    }

    // 设置关键词
    const metaKeywords = document.querySelector('meta[name="keywords"]');
    if (metaKeywords) {
      metaKeywords.setAttribute('content', '医学美容求美者体验研究,求美者行为分析,医学美容用户画像,求美者满意度调研,医学美容市场调研');
    }

    // 模拟数据加载
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    // 清理函数
    return () => {
      document.title = 'DIPS Pro'; // 恢复默认标题
      clearTimeout(timer);
    };
  }, []);

  // 页面加载动画变体
  const pageVariants = {
    initial: {
      opacity: 0,
      scale: 0.98
    },
    animate: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    },
    exit: {
      opacity: 0,
      scale: 0.98,
      transition: {
        duration: 0.4,
        ease: 'easeIn'
      }
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">正在加载求美者体验研究数据...</p>
        </div>
      </div>
    );
  }

  return (
    <AnimatePresence>
      <motion.div
        className="ym-consumer-research-page"
        variants={pageVariants}
        initial="initial"
        animate="animate"
        exit="exit"
      >
        {/* 导航栏 */}
        <ConsumerResearchNavbar />

        {/* 主要内容区域 */}
        <main className="relative pt-16">
          {/* 页面头部 */}
          <section id="hero" className="py-20 lg:py-32 bg-gradient-to-br from-blue-50 to-purple-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <motion.div
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                className="text-center"
              >
                <motion.div variants={itemVariants}>
                  <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6" style={{ color: 'var(--ym-text-primary)' }}>
                    求美者体验研究
                    <span className="ym-text-gradient-title block mt-2">
                      深度洞察用户行为
                    </span>
                  </h1>
                </motion.div>
                
                <motion.div variants={itemVariants}>
                  <p className="text-xl lg:text-2xl max-w-4xl mx-auto leading-relaxed" style={{ color: 'var(--ym-text-secondary)' }}>
                    基于大数据和AI技术，深入分析医学美容求美者的行为模式、需求偏好和满意度评价，
                    为医学美容机构和厂家提供精准的求美者洞察，助力业务决策优化
                  </p>
                </motion.div>

                <motion.div variants={itemVariants} className="mt-10">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
                    <div className="bg-white rounded-lg p-6 shadow-lg">
                      <div className="mb-4">
                        <img src={statisticsIcon} alt="数据驱动洞察" className="w-12 h-12 mx-auto" />
                      </div>
                      <h3 className="font-semibold text-gray-800 mb-2">数据驱动洞察</h3>
                      <p className="text-sm text-gray-600">基于真实用户数据的深度分析</p>
                    </div>
                    <div className="bg-white rounded-lg p-6 shadow-lg">
                      <div className="mb-4">
                        <img src={userProfileIcon} alt="精准用户画像" className="w-12 h-12 mx-auto" />
                      </div>
                      <h3 className="font-semibold text-gray-800 mb-2">精准用户画像</h3>
                      <p className="text-sm text-gray-600">多维度构建求美者特征模型</p>
                    </div>
                    <div className="bg-white rounded-lg p-6 shadow-lg">
                      <div className="mb-4">
                        <img src={checkIcon} alt="业务优化建议" className="w-12 h-12 mx-auto" />
                      </div>
                      <h3 className="font-semibold text-gray-800 mb-2">业务优化建议</h3>
                      <p className="text-sm text-gray-600">可执行的商业策略指导</p>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            </div>
          </section>

          {/* 评价体系 */}
          <div id="evaluation-system">
            <section className="py-20 bg-gradient-to-br from-white to-blue-50">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <motion.div
                  variants={containerVariants}
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true }}
                  className="text-center mb-16"
                >
                  <motion.div variants={itemVariants}>
                    <h2 className="text-3xl font-bold mb-6" style={{ color: 'var(--ym-text-primary)' }}>
                      评价体系
                    </h2>
                    <p className="text-xl max-w-4xl mx-auto leading-relaxed" style={{ color: 'var(--ym-text-secondary)' }}>
                      建立科学完善的评价体系，全面评估医学美容项目的用户满意度和服务质量
                    </p>
                  </motion.div>
                </motion.div>
              </div>
            </section>
            <UniversalChartCard
              title="科学度量，专业评估消费体验​"
              description=""
              chartImage={chartImage2}
              textCard={{
                title: "",
                items: [
                  {
                    title: "评价体系，科学度量",
                    description: "",
                    bulletPoints: [
                      "科学量化体验： 转变模糊感受为可衡量数据，实现求美者满意度的精准评估",
                      "匹配业务场景： 灵活适应不同业务环节（如服务过程、术后支持等）的特定需求",
                      "深挖多元体验： 不仅关注整体满意度，更能深度挖掘不同求美者群体的具体感受与痛点"
                    ]
                  },
                  {
                    title: "专业问卷，开箱即用",
                    description: "",
                    bulletPoints: [
                      "权威专业设计： 由专业顾问研发，确保调研内容的专业性和深度",
                      "开箱即用便捷： 预设模板，快速部署，节省时间成本，支持灵活调整",
                      "智能逻辑跳转： 支持复杂分支，引导深度回答，挖掘潜在需求，优化用户体验",
                      "实时数据记录： 数据即时同步存储，动态追踪进度，掌握实时动态，支持快速响应"
                    ]
                  }
                ]
              }}
              layout="split"
              splitRatio="1:1"    // 图表占三分之二，文本占三分之一
              titleAlign="left"  // 标题右对齐
              textAlign="left"    // 文本右对齐
            />
          </div>

          {/* 数据采集 */}
          <div id="data-collection">
            <section className="py-20 bg-gradient-to-br from-purple-50 to-pink-50">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <motion.div
                  variants={containerVariants}
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true }}
                  className="text-center mb-16"
                >
                  <motion.div variants={itemVariants}>
                    <h2 className="text-3xl font-bold mb-6" style={{ color: 'var(--ym-text-primary)' }}>
                      数据采集
                    </h2>
                    <p className="text-xl max-w-4xl mx-auto leading-relaxed" style={{ color: 'var(--ym-text-secondary)' }}>
                      运用先进的数据采集技术，全面收集求美者行为数据和市场反馈信息
                    </p>
                  </motion.div>
                </motion.div>
              </div>
            </section>
            <UniversalChartCard
              title="线上渠道，高效低成本反馈收集"
              description=""
              chartImage={chartImage3}
              textCard={{
                title: "",
                items: [
                  {
                    title: "线上渠道发送，实时收集",
                    description: "",
                    bulletPoints: [
                      "多渠道精准投放： 支持一店一码、手机短信等多种方式",
                      "实时数据收集： 告别等待，问卷反馈即时回传",
                      "高效便捷管理： 简化回访流程，提升回收效率"
                    ]
                  },
                  {
                    title: "名单管理，精准触达",
                    description: "",
                    bulletPoints: [
                      "绑定名单信息：精准锁定回访目标客户",
                      "智能有效清理：剔除无效数据，提升触达效率",
                      "确保精准投放：让问卷直达真正体验过产品和服务的消费群"
                    ]
                  },
                  {
                    title: "数据清理，确保数据质量",
                    description: "",
                    bulletPoints: [
                      "数据逻辑校验：过滤无效与矛盾回答",
                      "有效数据筛选：制定有效数据判断规则，避免低质反馈",
                      "保障数据质量：为可靠分析和精准决策奠定坚实基础"
                    ]
                  }
                ]
              }}
              layout="split"
              splitRatio="1:2"    // 图表占三分之二，文本占三分之一
              titleAlign="left"   // 标题左对齐
              textAlign="left"    // 文本左对齐
              isReversed={true}
            />
          </div>

          {/* 数据看板 */}
          <div id="data-dashboard">
            <section className="py-20 bg-gradient-to-br from-green-50 to-teal-50">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <motion.div
                  variants={containerVariants}
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true }}
                  className="text-center mb-16"
                >
                  <motion.div variants={itemVariants}>
                    <h2 className="text-3xl font-bold mb-6" style={{ color: 'var(--ym-text-primary)' }}>
                      数据看板
                    </h2>
                    <p className="text-xl max-w-4xl mx-auto leading-relaxed" style={{ color: 'var(--ym-text-secondary)' }}>
                      通过可视化数据看板，实时展示市场趋势和求美者行为分析结果
                    </p>
                  </motion.div>
                </motion.div>
              </div>
            </section>
            <UniversalChartCard
              title="调研进度监控"
              description=""
              chartImage={chartImage5}
              textCard={{
                title: "",
                items: [
                  {
                    title: "全局调研进度概览​​",
                    description: "",
                    bulletPoints: [
                      "实时查询样本回收数量，掌握调研进度",
                      "支持按月度、累计查看样本投放量、回收量",
                      "区分渠道（到店扫码、短信推送等）统计样本回收分布",
                      "动态展示整体回收进度，实时计算完成率"
                    ]
                  },
                  {
                    title: "多维度机构调研进度分析​​",
                    description: "",
                    bulletPoints: [
                      "各机构样本回收对比，了解低回收率机构",
                      "异常进度查询，及时采取措施提升参与率"
                    ]
                  },
                  {
                    title: "数据可视化与决策支持​​",
                    description: "",
                    bulletPoints: [
                      "​​支持Excel数据导出，便于汇报与复盘"
                    ]
                  }
                ]
              }}
              layout="split"
              splitRatio="2:1"    // 图表占三分之二，文本占三分之一
              titleAlign="left"  // 标题右对齐
              textAlign="left"    // 文本右对齐
              isReversed={false}
            />
            <UniversalChartCard
              title="图表得分概览"
              description=""
              chartImage={chartImage4}
              textCard={{
                title: "",
                items: [
                  {
                    title: "每日动态更新过程得分",
                    description: "",
                    bulletPoints: [
                      "覆盖所有关键业务指标（如机构忠诚度、服务总体评价等）",
                      "支持机构、机构、厂商多角色查看，便于快速响应客户反馈，及时改进求美者全流程体验"
                    ]
                  },
                  {
                    title: "多维度数据可视化​​",
                    description: "",
                    bulletPoints: [
                      "交互式图表​​，支持动态筛选，快速切换不同分析维度"
                    ]
                  },
                  {
                    title: "​​趋势分析与排名监控​​",
                    description: "",
                    bulletPoints: [
                      "​​月度趋势图​​，直观展示得分变化，识别长期改进效果",
                      "排名看板​​，集团视角下级得分对比，了解管理短板"
                    ]
                  }
                ]
              }}
              layout="split"
              splitRatio="1:2"
              titleAlign="left"  // 标题右对齐
              textAlign="left"    // 文本右对齐
              isReversed={true}
            />
            <UniversalChartCard
              title="客户原声管理"
              description=""
              chartImage={chartImage7}
              textCard={{
                title: "",
                items: [
                  {
                    title: "客户原声反馈精准查询",
                    description: "",
                    bulletPoints: [
                      "原声实时更新，及时掌握最新的客户动态和想法"
                    ]
                  },
                  {
                    title: "反馈记录展示​",
                    description: "",
                    bulletPoints: [
                      "支持日期、关键字等多维度搜索，快速查询关注业务客户原声",
                      "直观呈现客户原声与评分，帮助业务人员快速识别求美者核心痛点"
                    ]
                  },
                  {
                    title: "支持数据导出",
                    description: "",
                    bulletPoints: [
                      "支持筛选数据导出，为机构复盘、总部汇报等场景提供标准化数据支持"
                    ]
                  }
                ]
              }}
              layout="split"
              splitRatio="2:1"    // 图表占三分之二，文本占三分之一
              titleAlign="left"  // 标题右对齐
              textAlign="left"    // 文本右对齐
              isReversed={false}
            />
            <UniversalChartCard
              title="低分评价预警"
              description=""
              chartImage={chartImage6}
              textCard={{
                title: "",
                items: [
                  {
                    title: "预警通知规则",
                    description: "",
                    bulletPoints: [
                      "低分评价判定标准​​：满意度评价量表为1~5分，求美者评价为1~3分则表示不满意评价，产品和服务体验有待提升，可支持自定义预警指标和低分阈值（如1分≤评分≤3分）",
                      "触发条件​​：每日固定时间触发，也可自定义时间范围，如“3天一次”"
                    ]
                  },
                  {
                    title: "预警邮件内容生成​",
                    description: "",
                    bulletPoints: [
                      "提供邮件模板，固定呈现关键信息，减少相关负责人的信息整理时间，加速决策流程",
                      "以邮件形式发送，明细列表支持下载"
                    ]
                  }
                ]
              }}
              layout="split"
              splitRatio="1:2"
              titleAlign="left"  // 标题右对齐
              textAlign="left"    // 文本右对齐
              isReversed={true}
            />
          </div>

          {/* 标准报告 */}
          <div id="standard-reports">
            <section className="py-20 bg-gradient-to-br from-orange-50 to-red-50">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <motion.div
                  variants={containerVariants}
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true }}
                  className="text-center mb-16"
                >
                  <motion.div variants={itemVariants}>
                    <h2 className="text-3xl font-bold mb-6" style={{ color: 'var(--ym-text-primary)' }}>
                      标准报告
                    </h2>
                    <p className="text-xl max-w-4xl mx-auto leading-relaxed" style={{ color: 'var(--ym-text-secondary)' }}>
                      提供标准化的分析报告，支持机构视角的深度洞察
                    </p>
                  </motion.div>
                </motion.div>
              </div>
            </section>
            
            {/* 副标题区域 */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-8 mb-8">
              <motion.div
                variants={itemVariants}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-orange-100 to-red-100 rounded-full shadow-lg border border-orange-200 mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-full"></div>
                    <h3 className="text-2xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                      机构视角 - 客户评价
                    </h3>
                    <div className="w-3 h-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-full"></div>
                  </div>
                </div>
              </motion.div>
            </div>
            
            <UniversalChartCard
              title="根据用户到访后满意度评价倒推业务表现"
              description="通过定期客户追踪（月度/季度/年度）了解客户评价变化
基于业务表现搭建的指标体系，提示业务短板和不足
支持不同层级组织架构、用户分层、业务条线输出相应评价，拉通对比"
              chartConfig={combinedCharts}
              echartsContainerHeight="850px"
              insights={{
                title: "AI洞察",
                icon: statisticsIcon,
                items: [
                  "2025年，客户忠诚度逐月下滑，客户口碑正在逐步被稀释，关注老客户的流失情况",
                  "相较2024年，2025年综合满意度下滑的同时机构满意度提升，需进一步关注产品客户评价是否出现下滑",
                  "聚焦业务层面，客户对流程和硬件评价整体好于服务评价，但服务水平较2024年也也提升",
                  "不同机构的客户评价有所差异，大部分机构客户满意度达到90%或更高，苏州客户满意度达100%，但成都稍弱，仅83%"
                ]
              }}
              recommendations={{
                title: "策略建议",
                icon: ideaIcon,
                items: [
                  "加强老业主回访，建立VIP客户忠诚度体系，推动老客户口碑传播",
                  "建立满意度预警机制，对低分机构及时干预改进",
                  "开发满意度提升工具包，标准化服务流程",
                  "配合迭代后的标准化流程，对一线服务人员进行培训"
                ]
              }}
              layout="stacked"
            />

            {/* 副标题区域 */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-8 mb-8">
              <motion.div
                variants={itemVariants}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-orange-100 to-red-100 rounded-full shadow-lg border border-orange-200 mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-full"></div>
                    <h3 className="text-2xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                      机构视角 - 消费行为和需求分析
                    </h3>
                    <div className="w-3 h-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-full"></div>
                  </div>
                </div>
              </motion.div>
            </div>
            
            <UniversalChartCard
              title="精准的求美者画像和消费需求研究"
              description="精准的求美者画像协助锁定高潜力客群，深入了解老客户的消费需求和消费场景"
              chartConfig={consumerBehaviorData}
              echartsContainerHeight="850px"
              insights={{
                title: "AI洞察",
                icon: statisticsIcon,
                items: [
                  "85后、90后95前两个年龄层是当前机构医美消费的主力军，这部分人群处于事业上升期，注重效果与效率的平衡；其次70后的占比也较高，这部分群体消费力强但注重产品和服务品质，以及对安全性要求高",
                  "聚焦求美者的求美经历，经验在两年之内（半年内、半年指两年）的求美者较为普遍，求美者的消费习惯仍需逐步培养"
                ]
              }}
              recommendations={{
                title: "策略建议",
                icon: ideaIcon,
                items: [
                  "针对事业上升期、没有太多求美经历的求美者推出相应的午休轻医美套餐，突出快速恢复、不占用休息时间",
                  "针对年龄层较高的资深客户，建立VIP服务体系，提供私密化、个性化的高端服务，并根据预算做好个性化产品规划"
                ]
              }}
              layout="stacked"
              isReversed={true}
            />

            {/* 副标题区域 */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-8 mb-8">
              <motion.div
                variants={itemVariants}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-orange-100 to-red-100 rounded-full shadow-lg border border-orange-200 mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-full"></div>
                    <h3 className="text-2xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                      机构视角
                    </h3>
                    <div className="w-3 h-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-full"></div>
                  </div>
                </div>
              </motion.div>
            </div>

            <UniversalChartCard
              title="机构多维定位业务问题"
              description="内部不同机构拉通对比、VOC（求美者声音）自动分析和问题归纳、同城竞品对比（需外部数据支撑）"
              chartConfig={storeData}
              echartsContainerHeight="850px"
              insights={{
                title: "AI洞察",
                icon: statisticsIcon,
                items: [
                  "绝大部门机构2025年客户忠诚度相较2024年都有所下滑，长沙机构需要重点关注，连续两年客户忠诚度较低",
                  "广州机构、上海机构优势明显，连续两年维持客户较高忠诚度，并且广州机构接待客户体量远超其他机构",
                  "聚焦机构业务表现，南京机构业务表现较机构总体均较为薄弱从求美者声音来看，客户主要对治疗效果、医生专业性、就医流程（等待时间过长）较为敏感"
                ]
              }}
              recommendations={{
                title: "策略建议",
                icon: ideaIcon,
                items: [
                  "建立“忠诚度预警机制”：对忠诚度下滑的机构，自动触发总部督导小组介入，分析原因并制定改进方案",
                  "全国统一“客户体验标准”：制定标准化服务流程（如面诊话术、术后关怀），确保各机构执行一致",
                  "定期发布“忠诚度健康报告”：每月/季度对比各机构数据，奖励进步明显的团队，对持续低迷的机构进行整改"
                ]
              }}
              layout="stacked"
            />

          </div>

          {/* 数据安全 */}
          {/* <div id="data-security">
            <section className="py-20 bg-gradient-to-br from-blue-50 to-indigo-50">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <motion.div
                  variants={containerVariants}
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <motion.div variants={itemVariants}>
                    <h2 className="text-3xl font-bold mb-6" style={{ color: 'var(--ym-text-primary)' }}>
                      数据安全与隐私保护
                    </h2>
                    <p className="text-xl max-w-4xl mx-auto leading-relaxed mb-12" style={{ color: 'var(--ym-text-secondary)' }}>
                      我们严格遵循数据保护法规，建立完善的数据安全体系，确保用户隐私和数据安全
                    </p>
                  </motion.div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <motion.div variants={itemVariants} className="bg-white rounded-xl p-8 shadow-lg">
                      <div className="mb-4">
                        <img src={checkIcon} alt="数据加密" className="w-12 h-12 mx-auto" />
                      </div>
                      <h3 className="text-xl font-semibold mb-4" style={{ color: 'var(--ym-text-primary)' }}>
                        数据加密传输
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        采用AES-256加密算法，确保数据在传输和存储过程中的安全性，防止数据泄露和篡改
                      </p>
                    </motion.div>

                    <motion.div variants={itemVariants} className="bg-white rounded-xl p-8 shadow-lg">
                      <div className="mb-4">
                        <img src={userProfileIcon} alt="隐私保护" className="w-12 h-12 mx-auto" />
                      </div>
                      <h3 className="text-xl font-semibold mb-4" style={{ color: 'var(--ym-text-primary)' }}>
                        隐私保护机制
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        实施数据脱敏处理，个人身份信息完全匿名化，确保用户隐私不被泄露
                      </p>
                    </motion.div>

                    <motion.div variants={itemVariants} className="bg-white rounded-xl p-8 shadow-lg">
                      <div className="mb-4">
                        <img src={trackIcon} alt="访问控制" className="w-12 h-12 mx-auto" />
                      </div>
                      <h3 className="text-xl font-semibold mb-4" style={{ color: 'var(--ym-text-primary)' }}>
                        访问权限控制
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        建立分级权限管理体系，严格控制数据访问权限，确保只有授权人员能够访问相关数据
                      </p>
                    </motion.div>

                    <motion.div variants={itemVariants} className="bg-white rounded-xl p-8 shadow-lg">
                      <div className="mb-4">
                        <img src={statisticsIcon} alt="合规监管" className="w-12 h-12 mx-auto" />
                      </div>
                      <h3 className="text-xl font-semibold mb-4" style={{ color: 'var(--ym-text-primary)' }}>
                        合规监管
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        严格遵循《个人信息保护法》、《数据安全法》等相关法规，定期进行合规审计
                      </p>
                    </motion.div>

                    <motion.div variants={itemVariants} className="bg-white rounded-xl p-8 shadow-lg">
                      <div className="mb-4">
                        <img src={ideaIcon} alt="技术保障" className="w-12 h-12 mx-auto" />
                      </div>
                      <h3 className="text-xl font-semibold mb-4" style={{ color: 'var(--ym-text-primary)' }}>
                        技术保障
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        部署先进的安全防护系统，包括防火墙、入侵检测、异常行为监控等多层防护措施
                      </p>
                    </motion.div>

                    <motion.div variants={itemVariants} className="bg-white rounded-xl p-8 shadow-lg">
                      <div className="mb-4">
                        <img src={increaseIcon} alt="持续改进" className="w-12 h-12 mx-auto" />
                      </div>
                      <h3 className="text-xl font-semibold mb-4" style={{ color: 'var(--ym-text-primary)' }}>
                        持续改进
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        建立安全事件响应机制，定期进行安全评估和漏洞扫描，持续提升安全防护能力
                      </p>
                    </motion.div>
                  </div>

                  <motion.div variants={itemVariants} className="mt-12">
                    <div className="bg-white rounded-2xl p-8 shadow-lg max-w-4xl mx-auto">
                      <h4 className="text-2xl font-bold mb-6" style={{ color: 'var(--ym-text-primary)' }}>
                        数据安全承诺
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
                        <div>
                          <h5 className="font-semibold text-gray-800 mb-3">🔒 安全保障</h5>
                          <ul className="space-y-2 text-sm text-gray-600">
                            <li>• 99.9%数据安全保障率</li>
                            <li>• 24/7安全监控服务</li>
                            <li>• 定期安全审计和评估</li>
                            <li>• 专业安全团队维护</li>
                          </ul>
                        </div>
                        <div>
                          <h5 className="font-semibold text-gray-800 mb-3">📋 合规认证</h5>
                          <ul className="space-y-2 text-sm text-gray-600">
                            <li>• ISO 27001信息安全管理体系认证</li>
                            <li>• 等保三级认证</li>
                            <li>• 个人信息保护影响评估</li>
                            <li>• 数据处理活动记录备案</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                </motion.div>
              </div>
            </section>
          </div> */}

          {/* 套餐介绍 */}
          <div id="pricing">
            <YmPricing />
          </div>

          {/* 总结与展望 */}
          {/* <section id="summary" className="py-20 bg-gradient-to-br from-purple-50 to-blue-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <motion.div
                variants={containerVariants}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                className="text-center"
              >
                <motion.div variants={itemVariants}>
                  <h2 className="text-3xl font-bold mb-12" style={{ color: 'var(--ym-text-primary)' }}>
                    研究总结与未来展望
                  </h2>
                </motion.div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  <motion.div variants={itemVariants} className="bg-white rounded-xl p-8 shadow-lg">
                    <div className="mb-4">
                      <img src={trackIcon} alt="精准定位" className="w-12 h-12 mx-auto" />
                    </div>
                    <h3 className="text-xl font-semibold mb-4" style={{ color: 'var(--ym-text-primary)' }}>
                      精准定位
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      基于数据驱动的求美者洞察，帮助医学美容机构实现精准的市场定位和客户服务策略优化
                    </p>
                  </motion.div>

                  <motion.div variants={itemVariants} className="bg-white rounded-xl p-8 shadow-lg">
                    <div className="mb-4">
                      <img src={increaseIcon} alt="持续优化" className="w-12 h-12 mx-auto" />
                    </div>
                    <h3 className="text-xl font-semibold mb-4" style={{ color: 'var(--ym-text-primary)' }}>
                      持续优化
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      建立动态监测体系，实时跟踪市场变化和求美者需求演变，持续优化服务质量和用户体验
                    </p>
                  </motion.div>

                  <motion.div variants={itemVariants} className="bg-white rounded-xl p-8 shadow-lg">
                    <div className="mb-4">
                      <img src={fireworkIcon} alt="创新发展" className="w-12 h-12 mx-auto" />
                    </div>
                    <h3 className="text-xl font-semibold mb-4" style={{ color: 'var(--ym-text-primary)' }}>
                      创新发展
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      结合AI技术和大数据分析，不断创新研究方法，为医学美容行业的健康发展提供科学支撑
                    </p>
                  </motion.div>
                </div>

                <motion.div variants={itemVariants} className="mt-12">
                  <div className="bg-white rounded-2xl p-8 shadow-lg max-w-4xl mx-auto">
                    <h4 className="text-2xl font-bold mb-6" style={{ color: 'var(--ym-text-primary)' }}>
                      下一阶段研究计划
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
                      <div>
                        <h5 className="font-semibold text-gray-800 mb-3">🔬 深度研究方向</h5>
                        <ul className="space-y-2 text-sm text-gray-600">
                          <li>• 求美者决策路径分析</li>
                          <li>• 社交媒体影响力研究</li>
                          <li>• 跨平台用户行为追踪</li>
                          <li>• 满意度预测模型构建</li>
                        </ul>
                      </div>
                      <div>
                        <h5 className="font-semibold text-gray-800 mb-3">🛠️ 技术创新应用</h5>
                        <ul className="space-y-2 text-sm text-gray-600">
                          <li>• AI驱动的个性化推荐</li>
                          <li>• 实时情感分析系统</li>
                          <li>• 多维度数据可视化</li>
                          <li>• 智能客户服务优化</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            </div>
          </section> */}
        </main>

        {/* 页脚 */}
        <div className="footer-container">
          <YmFooter />
        </div>

        {/* 覆盖页脚背景的样式 */}
        <style>
          {`
            .footer-container .ym-bg-dark {
              background: #ffffff !important;
              color: #4B5563 !important;
            }
            .footer-container .text-white {
              color: #4B5563 !important;
            }
            .footer-container .text-gray-400 {
              color: #6B7280 !important;
            }
            .footer-container .border-gray-800 {
              border-color: #E5E7EB !important;
            }
          `}
        </style>
      </motion.div>
    </AnimatePresence>
  );
};

export default YmConsumerResearchPage; 

/*
=== UniversalChartCard 使用示例 ===

1. 不显示 recommendations 部分：
   只需要不传递 recommendations 属性即可

<UniversalChartCard
  title="标题"
  description="描述"
  chartConfig={chartData}
  insights={{
    title: "数据洞察",
    icon: statisticsIcon,
    items: ["洞察1", "洞察2", "洞察3"]
  }}
  layout="split"
/>

2. 使用图片替代图表：
   首先导入图片，然后使用 chartImage 属性

// 在文件顶部导入图片
import chartImage1 from '../../../assets/images/chart1.png';

<UniversalChartCard
  title="标题"
  description="描述"
  chartImage={chartImage1}
  insights={{
    title: "数据洞察",
    icon: statisticsIcon,
    items: ["洞察1", "洞察2", "洞察3"]
  }}
  layout="split"
/>

3. 使用文本卡片替代 insights：
   使用 textCard 属性替代 insights 部分，创建结构化的文本内容

<UniversalChartCard
  title="标题"
  description="描述"
  chartConfig={chartData}
  textCard={{
    title: "主标题",
    items: [
      {
        title: "子标题1",
        description: "详细描述1"
      },
      {
        title: "子标题2",
        description: "详细描述2"
      },
      {
        title: "子标题3",
        description: "详细描述3"
      }
    ]
  }}
  layout="split"
/>

4. 同时使用图片和文本卡片（无重复标题）：

<UniversalChartCard
  title="标题"
  description="描述"
  chartImage={chartImage1}
  textCard={{
    title: "", // 空字符串避免标题重复
    items: [
      { title: "子标题1", description: "详细描述1" },
      { title: "子标题2", description: "详细描述2" }
    ]
  }}
  layout="split"
/>

5. 使用对齐配置：

<UniversalChartCard
  title="居中标题"
  description="右对齐描述文本"
  chartImage={chartImage1}
  textCard={{
    title: "",
    items: [
      { title: "子标题1", description: "详细描述1" },
      { title: "子标题2", description: "详细描述2" }
    ]
  }}
  titleAlign="center"  // 标题居中显示
  textAlign="right"    // 文本右对齐
  layout="split"
/>

6. 使用布局比例配置：
   使用 splitRatio 属性控制分栏布局的比例

<UniversalChartCard
  title="图表占三分之二，文本占三分之一"
  description="描述"
  chartImage={chartImage1}
  textCard={{
    title: "",
    items: [
      { title: "子标题1", description: "详细描述1" },
      { title: "子标题2", description: "详细描述2" }
    ]
  }}
  layout="split"
  splitRatio="2:1"  // 图表:文本 = 2:1（图表占三分之二）
/>

<UniversalChartCard
  title="文本占三分之二，图表占三分之一"
  description="描述"
  chartImage={chartImage1}
  textCard={{
    title: "",
    items: [
      { title: "子标题1", description: "详细描述1" },
      { title: "子标题2", description: "详细描述2" }
    ]
  }}
  layout="split"
  splitRatio="1:2"  // 图表:文本 = 1:2（文本占三分之二）
/>

<UniversalChartCard
  title="默认等比例布局"
  description="描述"
  chartImage={chartImage1}
  textCard={{
    title: "",
    items: [
      { title: "子标题1", description: "详细描述1" },
      { title: "子标题2", description: "详细描述2" }
    ]
  }}
  layout="split"
  splitRatio="1:1"  // 图表:文本 = 1:1（各占一半，默认值）
/>

注意：
- textCard 和 insights 不能同时使用，textCard 优先级更高
- textCard 替代 insights 部分，不影响图表/图片显示
- 图表/图片部分独立显示：chartImage > chartConfig > tableData
- 文本卡片支持多个结构化文本项
- titleAlign 和 textAlign 支持 'left' | 'center' | 'right' 三种对齐方式
- splitRatio 支持 '1:1' | '2:1' | '1:2' 三种比例配置
  - '1:1': 文本和图表各占一半（默认）
  - '2:1': 图表占三分之二，文本占三分之一
  - '1:2': 文本占三分之二，图表占三分之一
- splitRatio 只在 layout="split" 时生效，对 layout="stacked" 无效
- 默认值都是 'left'（左对齐）和 '1:1'（等比例）
*/ 