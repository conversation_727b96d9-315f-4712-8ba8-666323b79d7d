import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { scrollToElement } from '../../../utils/ym-utils';

interface NavItem {
  id: string;
  label: string;
  href: string;
}

const navItems: NavItem[] = [
  { id: 'hero', label: '开始', href: '#hero' },
  { id: 'evaluation-system', label: '消费市场研究', href: '#evaluation-system' },
  { id: 'data-collection', label: '企业竞争力', href: '#data-collection' }
];

export const IndustryAIAnalysisNavbar: React.FC = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeSection, setActiveSection] = useState('hero');

  // 监听滚动事件
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 50);

      // 检测当前激活的section
      const sections = navItems.map(item => item.href.substring(1));
      for (const section of sections) {
        const element = document.getElementById(section);
        if (element) {
          const rect = element.getBoundingClientRect();
          if (rect.top <= 100 && rect.bottom >= 100) {
            setActiveSection(section);
            break;
          }
        }
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // 处理导航点击
  const handleNavClick = (href: string, event: React.MouseEvent) => {
    event.preventDefault();
    const targetId = href.substring(1);
    scrollToElement(targetId, 80); // 80px offset for navbar
    setIsMobileMenuOpen(false);
  };

  // 返回主页面
  const handleBackToMain = () => {
    window.location.href = '/#ym';
  };

  // 导航栏动画变体
  const navbarVariants = {
    top: {
      backgroundColor: 'rgba(249, 250, 255, 0)',
      backdropFilter: 'blur(0px)',
      borderBottom: '1px solid rgba(41, 33, 48, 0)'
    },
    scrolled: {
      backgroundColor: 'rgba(249, 250, 255, 0.95)',
      backdropFilter: 'blur(20px)',
      borderBottom: '1px solid rgba(41, 33, 48, 0.1)'
    }
  };

  // 移动端菜单动画变体
  const mobileMenuVariants = {
    closed: {
      opacity: 0,
      height: 0,
      transition: {
        duration: 0.3,
        ease: 'easeInOut'
      }
    },
    open: {
      opacity: 1,
      height: 'auto',
      transition: {
        duration: 0.3,
        ease: 'easeInOut'
      }
    }
  };

  // 移动端菜单项动画变体
  const mobileItemVariants = {
    closed: {
      opacity: 0,
      x: -20
    },
    open: {
      opacity: 1,
      x: 0
    }
  };

  return (
    <motion.nav
      className="fixed top-0 left-0 right-0 z-50 transition-all duration-300"
      variants={navbarVariants}
      initial="top"
      animate={isScrolled ? 'scrolled' : 'top'}
      style={{
        borderBottom: isScrolled ? '1px solid rgba(41, 33, 48, 0.1)' : '1px solid transparent'
      }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <motion.div
            className="flex-shrink-0"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <a
              href="#hero"
              onClick={(e) => handleNavClick('#hero', e)}
              className="flex items-center space-x-2"
            >
              <img src="/logo.png" alt="行业AI分析Logo" className="w-8 h-8" />
              <p className="font-bold text-gradient-primary text-xl">行业 AI 分析</p>
            </a>
          </motion.div>

          {/* 桌面端导航菜单 */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-6">
              {navItems.map((item, index) => (
                <motion.a
                  key={item.id}
                  href={item.href}
                  onClick={(e) => handleNavClick(item.href, e)}
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 relative ${
                    activeSection === item.href.substring(1)
                      ? 'ym-text-primary font-semibold'
                      : 'ym-text-secondary hover:ym-text-primary'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  {item.label}
                  
                  {/* 激活指示器 */}
                  {activeSection === item.href.substring(1) && (
                    <motion.div
                      className="absolute bottom-0 left-0 right-0 h-0.5 ym-gradient-subtle rounded-full"
                      layoutId="activeIndicator"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                    />
                  )}
                </motion.a>
              ))}
            </div>
          </div>

          {/* 右侧按钮组 */}
          <div className="hidden md:flex items-center space-x-4">
            {/* 返回主页按钮 */}
            <motion.button
              onClick={handleBackToMain}
              className="px-4 py-2 text-sm font-medium ym-text-secondary hover:ym-text-primary transition-colors duration-200"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              返回主页
            </motion.button>
            
            {/* CTA按钮 */}
            <motion.a
              href="https://dipsai.cn/app/#/login"
              target="_blank"
              rel="noopener noreferrer"
              className="ym-button ym-button-primary px-6 py-2 rounded-full text-sm font-medium"
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
            >
              体验 AI
            </motion.a>
          </div>

          {/* 移动端菜单按钮 */}
          <div className="md:hidden">
            <motion.button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md ym-text-secondary hover:ym-text-primary ym-focus"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span className="sr-only">打开主菜单</span>
              <motion.div
                className="w-6 h-6 flex flex-col justify-center items-center"
                animate={isMobileMenuOpen ? 'open' : 'closed'}
              >
                <motion.span
                  className="w-5 h-0.5 bg-current block transition-all duration-300"
                  variants={{
                    closed: { rotate: 0, y: 0 },
                    open: { rotate: 45, y: 2 }
                  }}
                />
                <motion.span
                  className="w-5 h-0.5 bg-current block mt-1 transition-all duration-300"
                  variants={{
                    closed: { opacity: 1 },
                    open: { opacity: 0 }
                  }}
                />
                <motion.span
                  className="w-5 h-0.5 bg-current block mt-1 transition-all duration-300"
                  variants={{
                    closed: { rotate: 0, y: 0 },
                    open: { rotate: -45, y: -2 }
                  }}
                />
              </motion.div>
            </motion.button>
          </div>
        </div>
      </div>

      {/* 移动端下拉菜单 */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            className="md:hidden ym-mobile-menu border-t"
            variants={mobileMenuVariants}
            initial="closed"
            animate="open"
            exit="closed"
            style={{
              backgroundColor: 'rgba(249, 250, 255, 0.95)',
              backdropFilter: 'blur(20px)'
            }}
          >
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
              {navItems.map((item, index) => (
                <motion.a
                  key={item.id}
                  href={item.href}
                  onClick={(e) => handleNavClick(item.href, e)}
                  className={`block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${
                    activeSection === item.href.substring(1)
                      ? 'ym-text-primary ym-bg-light'
                      : 'ym-text-secondary hover:ym-text-primary hover:ym-bg-subtle'
                  }`}
                  variants={mobileItemVariants}
                  initial="closed"
                  animate="open"
                  transition={{ delay: index * 0.05 }}
                >
                  {item.label}
                </motion.a>
              ))}
              
              {/* 移动端按钮组 */}
              <motion.div
                className="pt-4 pb-2 space-y-2"
                variants={mobileItemVariants}
                initial="closed"
                animate="open"
                transition={{ delay: navItems.length * 0.05 }}
              >
                <button
                  onClick={handleBackToMain}
                  className="block w-full text-center px-6 py-3 rounded-full text-base font-medium ym-text-secondary hover:ym-text-primary ym-bg-subtle"
                >
                  返回主页
                </button>
                <a
                  href="https://dipsai.cn/app/#/login"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block w-full text-center ym-button ym-button-primary px-6 py-3 rounded-full text-base font-medium"
                >
                  体验 AI
                </a>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 滚动进度条 */}
      <motion.div
        className="absolute bottom-0 left-0 h-0.5 ym-gradient-primary"
        style={{
          scaleX: typeof window !== 'undefined' ? window.scrollY / (document.documentElement.scrollHeight - window.innerHeight) : 0,
          transformOrigin: '0%'
        }}
        initial={{ scaleX: 0 }}
        animate={{ 
          scaleX: typeof window !== 'undefined' 
            ? window.scrollY / (document.documentElement.scrollHeight - window.innerHeight) 
            : 0 
        }}
        transition={{ type: 'spring', stiffness: 300, damping: 30 }}
      />
    </motion.nav>
  );
};

export default IndustryAIAnalysisNavbar; 