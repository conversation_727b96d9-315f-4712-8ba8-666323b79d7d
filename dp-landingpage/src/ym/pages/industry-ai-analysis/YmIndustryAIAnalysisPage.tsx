import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// 组件导入
import { IndustryAIAnalysisNavbar } from './components';
import UniversalChartCard from '../../components/UniversalChartCard';
import YmFooter from '../../components/YmFooter';

// 图片导入
import aiIcon from '@assets/icons/ai.png';
import alarmIcon from '@assets/icons/alarm.png';
import strategyIcon from '@assets/icons/strategy.png';
import fireworkIcon from '@assets/icons/firework.png';
import appIcon from '@assets/icons/app.png';
import charityIcon from '@assets/icons/charity.png';
import statisticsIcon from '@assets/icons/statistics.png';
import ideaIcon from '@assets/icons/idea.png';
import checkIcon from '@assets/icons/check.png';
import userProfileIcon from '@assets/icons/user-profile.png';
import trackIcon from '@assets/icons/track.png';
import increaseIcon from '@assets/icons/increase.png';
// 示例：导入图片用于图表显示
import image1 from '../../assets/images/iaa/1.png';
import image3 from '../../assets/images/iaa/3.png';
import chartImage2 from '../../assets/images/cr/2.png';
import chartImage3 from '../../assets/images/cr/3.jpg';
import chartImage4 from '../../assets/images/cr/4.png';
import chartImage5 from '../../assets/images/cr/5.png';
import chartImage6 from '../../assets/images/cr/6.png';
import chartImage7 from '../../assets/images/cr/7.jpg';

// 样式导入
import '../../styles/ym-custom.css';

// 数据导入
import marketPredictionData from './data/market-prediction.json';
import competitorAnalysisData from './data/competitor-analysis.json';
import riskAssessmentData from './data/risk-assessment.json';
import aiCapabilityTableData from './data/ai-capability-table.json';
import marketTrendData from './data/market-trend/combined_charts.json';
import hotProductData from './data/hot-product/combined_hot_product.json';
import businessDistrictData from './data/business-district/chart.json';
import knowEnemyData from './data/know-enemy/combined_competitor_analysis.json';

export const YmIndustryAIAnalysisPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // 设置页面标题
    document.title = '行业 AI 分析 - 医学美容满意度调研平台';
    
    // 设置meta描述
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', '基于人工智能技术的医学美容行业深度分析，提供市场趋势预测、竞争对手分析和行业洞察报告');
    }

    // 设置关键词
    const metaKeywords = document.querySelector('meta[name="keywords"]');
    if (metaKeywords) {
      metaKeywords.setAttribute('content', '医学美容行业AI分析,医学美容市场分析,行业趋势预测,竞争对手分析,医学美容数据挖掘');
    }

    // 模拟AI数据加载
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);

    // 清理函数
    return () => {
      document.title = 'DIPS Pro'; // 恢复默认标题
      clearTimeout(timer);
    };
  }, []);

  // 页面加载动画变体
  const pageVariants = {
    initial: {
      opacity: 0,
      scale: 0.98
    },
    animate: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    },
    exit: {
      opacity: 0,
      scale: 0.98,
      transition: {
        duration: 0.4,
        ease: 'easeIn'
      }
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 to-pink-50">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 mb-2">AI 模型正在分析行业数据...</p>
          <div className="flex items-center justify-center space-x-1">
            <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
            <div className="w-2 h-2 bg-pink-500 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <AnimatePresence>
      <motion.div
        className="ym-industry-ai-analysis-page"
        variants={pageVariants}
        initial="initial"
        animate="animate"
        exit="exit"
      >
        {/* 导航栏 */}
        <IndustryAIAnalysisNavbar />

        {/* 主要内容区域 */}
        <main className="relative pt-16">
          {/* 页面头部 */}
          <section id="hero" className="py-20 lg:py-32 bg-gradient-to-br from-purple-50 to-pink-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <motion.div
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                className="text-center"
              >
                <motion.div variants={itemVariants}>
                  <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6" style={{ color: 'var(--ym-text-primary)' }}>
                    行业 AI 分析
                    <span className="ym-text-gradient-title block mt-2">
                      智能洞察行业趋势
                    </span>
                  </h1>
                </motion.div>
                
                <motion.div variants={itemVariants}>
                  <p className="text-xl lg:text-2xl max-w-4xl mx-auto leading-relaxed" style={{ color: 'var(--ym-text-secondary)' }}>
                    运用先进的人工智能算法，深度挖掘医学美容行业数据，提供精准的市场趋势预测、
                    竞争对手分析和行业洞察，为企业战略决策提供科学依据
                  </p>
                </motion.div>

                <motion.div variants={itemVariants} className="mt-10">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
                    <div className="bg-white rounded-lg p-6 shadow-lg">
                      <div className="mb-4">
                        <img src={aiIcon} alt="AI驱动分析" className="w-12 h-12 mx-auto" />
                      </div>
                      <h3 className="font-semibold text-gray-800 mb-2">AI驱动分析</h3>
                      <p className="text-sm text-gray-600">基于机器学习的智能数据挖掘</p>
                    </div>
                    <div className="bg-white rounded-lg p-6 shadow-lg">
                      <div className="mb-4">
                        <img src={alarmIcon} alt="实时监控预警" className="w-12 h-12 mx-auto" />
                      </div>
                      <h3 className="font-semibold text-gray-800 mb-2">实时监控预警</h3>
                      <p className="text-sm text-gray-600">7x24小时市场动态追踪</p>
                    </div>
                    <div className="bg-white rounded-lg p-6 shadow-lg">
                      <div className="mb-4">
                        <img src={strategyIcon} alt="精准策略建议" className="w-12 h-12 mx-auto" />
                      </div>
                      <h3 className="font-semibold text-gray-800 mb-2">精准策略建议</h3>
                      <p className="text-sm text-gray-600">可执行的商业决策指导</p>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            </div>
          </section>

          {/* 消费市场研究 */}
          <div id="evaluation-system">
            <section className="py-20 bg-gradient-to-br from-white to-blue-50">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <motion.div
                  variants={containerVariants}
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true }}
                  className="text-center mb-16"
                >
                  <motion.div variants={itemVariants}>
                    <h2 className="text-3xl font-bold mb-6" style={{ color: 'var(--ym-text-primary)' }}>
                      消费市场研究
                    </h2>
                    <p className="text-xl max-w-4xl mx-auto leading-relaxed" style={{ color: 'var(--ym-text-secondary)' }}>
                      了解求美者需求，紧跟市场趋势，预测未来走向
                    </p>
                  </motion.div>
                </motion.div>
              </div>
            </section>
            <UniversalChartCard
              title="追踪市场趋势，挖掘潜在机会"
              description=""
              chartConfig={marketTrendData}
              echartsContainerHeight="750px"
              textCard={{
                title: "",
                items: [
                  {
                    title: "",
                    description: "",
                    bulletPoints: [
                      "分析全国性及区域性医美市场规模与增长情况，并为未来的销售规模提供模型预测",
                      "从销售数量、店均销售量、同环比增长、热点产品类型与用途等多个维度重点分析市场消费行情"
                    ]
                  }
                ]
              }}
              layout="split"
              splitRatio="2:1"
              titleAlign="left"
              textAlign="left" 
            />
            <UniversalChartCard
              title="关注热点品类，锁定需求爆发窗口期"
              description=""
              chartConfig={hotProductData}
              echartsContainerHeight="700px"
              textCard={{
                title: "",
                items: [
                  {
                    title: "",
                    description: "",
                    bulletPoints: [
                      "聚焦月度高增长的细分品类，监测新兴赛道动态，捕捉趋势信号，发掘潜在机遇",
                      "通过分析新兴机会点，解构热点品类增长动因，帮助企业捕获爆款背后的消费驱动力"
                    ]
                  }
                ]
              }}
              layout="split"
              splitRatio="1:2"
              titleAlign="left"
              textAlign="left" 
              isReversed={true}
            />
            <UniversalChartCard
              title="快捷查询行业数据库"
              description=""
              chartImage={image3}
              textCard={{
                title: "",
                items: [
                  {
                    title: "",
                    description: "",
                    bulletPoints: [
                      "深度洞察对标竞争对手核心优劣势，挖掘潜在机会点，提升产品市场竞争力，赋能企业精准洞察营销效能与竞争格局",
                      "快捷查询行业数据库，精准定位本品在竞争市场中的坐标位次和竞争力水平"
                    ]
                  }
                ]
              }}
              layout="split"
              splitRatio="2:1"
              titleAlign="left"
              textAlign="left" 
            />
          </div>

          {/* 企业竞争力 */}
          <div id="data-collection">
            <section className="py-20 bg-gradient-to-br from-purple-50 to-pink-50">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <motion.div
                  variants={containerVariants}
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true }}
                  className="text-center mb-16"
                >
                  <motion.div variants={itemVariants}>
                    <h2 className="text-3xl font-bold mb-6" style={{ color: 'var(--ym-text-primary)' }}>
                      企业竞争力
                    </h2>
                    <p className="text-xl max-w-4xl mx-auto leading-relaxed" style={{ color: 'var(--ym-text-secondary)' }}>
                      全面洞察您与竞争企业的表现对比，助您持续领跑行业
                    </p>
                  </motion.div>
                </motion.div>
              </div>
            </section>
            <UniversalChartCard
              title="地理细分至商圈，高效识别竞争对手"
              description=""
              chartConfig={businessDistrictData}
              echartsContainerHeight="400px"
              textCard={{
                title: "",
                items: [
                  {
                    title: "",
                    description: "DIPS Pro将竞争区域细分至城市商圈，不仅能够评估当地的竞争强度，还可智能识别商圈竞对，帮助了解竞争者的数量、规模、产品情况",
                    bulletPoints: [
                    ]
                  }
                ]
              }}
              layout="split"
              splitRatio="1:2"    // 图表占三分之一，文本占三分之二
              titleAlign="left"   // 标题左对齐
              textAlign="left"    // 文本左对齐
              isReversed={true}
            />
            <UniversalChartCard
              title="知己知彼，百战不殆"
              description=""
              chartConfig={knowEnemyData}
              echartsContainerHeight="800px"
              textCard={{
                title: "",
                items: [
                  {
                    title: "",
                    description: "更好地认识自己和竞争者，了解彼此的优劣势才能采取有效的市场策略，进而学习优秀竞对的商业模式，提升单店盈利能力，规避低效陷阱，并在市场竞争中胜出",
                    bulletPoints: [
                      
                    ]
                  }
                ]
              }}
              layout="split"
              splitRatio="2:1"    // 图表占三分之一，文本占三分之二
              titleAlign="left"   // 标题左对齐
              textAlign="left"    // 文本左对齐
              isReversed={false}
            />
          </div>

          {/* AI能力评估表格 */}
          {/* <div id="ai-capabilities">
            <UniversalChartCard
            title="医学美容行业AI技术能力成熟度评估矩阵"
            description="基于技术成熟度模型（TMM），全面评估医学美容行业各AI技术模块的发展水平、应用效果和商业价值，为AI技术投资和应用提供决策参考"
            tableData={aiCapabilityTableData}
            insights={{
              title: "AI洞察",
              icon: statisticsIcon,
              items: [
                "营销效果分析模块技术最成熟（88%），准确率达94.1%，已广泛应用于实际业务",
                "市场趋势预测和求美者洞察模块发展良好，技术成熟度超过80%，商业价值显著",
                "竞争对手分析和风险评估模块处于快速发展期，技术成熟度75-78%",
                "价格策略优化和医学美容项目推荐模块仍在开发完善中，具有较大提升空间",
                "智能客服助手模块技术成熟度相对较低（65%），但降本增效潜力巨大",
                "已上线模块（3个）贡献了主要的商业价值，测试中模块（2个）即将投入使用"
              ]
            }}
            recommendations={{
              title: "策略建议",
              icon: ideaIcon,
              items: [
                "加大对营销效果分析等成熟模块的推广应用，最大化技术投资回报",
                "加速竞争对手分析和风险评估模块的技术完善，尽快投入商业应用",
                "重点投入智能客服助手的技术研发，抢占人工智能客服市场先机",
                "建立AI技术评估和迭代机制，持续提升各模块的准确率和成熟度",
                "制定分阶段的AI技术应用路线图，优先发展高价值、低风险的模块",
                "加强AI技术人才培养和团队建设，为技术发展提供人才保障"
              ]
            }}
            layout="stacked"
            isReversed={true}
          />
          </div> */}

          {/* AI技术展望 */}
          {/* <section id="ai-future" className="py-20 bg-gradient-to-br from-purple-50 to-pink-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <motion.div
                variants={containerVariants}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                className="text-center"
              >
                <motion.div variants={itemVariants}>
                  <h2 className="text-3xl font-bold mb-12" style={{ color: 'var(--ym-text-primary)' }}>
                    AI技术发展展望与未来规划
                  </h2>
                </motion.div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  <motion.div variants={itemVariants} className="bg-white rounded-xl p-8 shadow-lg">
                    <div className="mb-4">
                      <img src={fireworkIcon} alt="技术创新突破" className="w-12 h-12 mx-auto" />
                    </div>
                    <h3 className="text-xl font-semibold mb-4" style={{ color: 'var(--ym-text-primary)' }}>
                      技术创新突破
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      持续投入前沿AI技术研发，在计算机视觉、自然语言处理、深度学习等领域实现突破创新
                    </p>
                  </motion.div>

                  <motion.div variants={itemVariants} className="bg-white rounded-xl p-8 shadow-lg">
                    <div className="mb-4">
                      <img src={appIcon} alt="精准应用落地" className="w-12 h-12 mx-auto" />
                    </div>
                    <h3 className="text-xl font-semibold mb-4" style={{ color: 'var(--ym-text-primary)' }}>
                      精准应用落地
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      深化AI技术在医学美容行业的应用场景，实现从数据分析到智能决策的全流程AI化
                    </p>
                  </motion.div>

                  <motion.div variants={itemVariants} className="bg-white rounded-xl p-8 shadow-lg">
                    <div className="mb-4">
                      <img src={charityIcon} alt="生态协同发展" className="w-12 h-12 mx-auto" />
                    </div>
                    <h3 className="text-xl font-semibold mb-4" style={{ color: 'var(--ym-text-primary)' }}>
                      生态协同发展
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      构建开放的AI技术生态，与行业伙伴共同推动医学美容行业的智能化转型升级
                    </p>
                  </motion.div>
                </div>

                <motion.div variants={itemVariants} className="mt-12">
                  <div className="bg-white rounded-2xl p-8 shadow-lg max-w-4xl mx-auto">
                    <h4 className="text-2xl font-bold mb-6" style={{ color: 'var(--ym-text-primary)' }}>
                      2024-2025年AI技术发展路线图
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
                      <div>
                        <h5 className="font-semibold text-gray-800 mb-3">🔬 核心技术突破</h5>
                        <ul className="space-y-2 text-sm text-gray-600">
                          <li>• 多模态AI融合分析技术</li>
                          <li>• 实时决策支持系统</li>
                          <li>• 自适应学习算法优化</li>
                          <li>• 联邦学习隐私保护技术</li>
                        </ul>
                      </div>
                      <div>
                        <h5 className="font-semibold text-gray-800 mb-3">🛠️ 应用场景拓展</h5>
                        <ul className="space-y-2 text-sm text-gray-600">
                          <li>• 智能诊断辅助系统</li>
                          <li>• 个性化治疗方案推荐</li>
                          <li>• 全链路客户体验优化</li>
                          <li>• 智能供应链管理平台</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            </div>
          </section> */}
        </main>

        {/* 页脚 */}
        <div className="footer-container">
          <YmFooter />
        </div>

        {/* 覆盖页脚背景的样式 */}
        <style>
          {`
            .footer-container .ym-bg-dark {
              background: #ffffff !important;
              color: #4B5563 !important;
            }
            .footer-container .text-white {
              color: #4B5563 !important;
            }
            .footer-container .text-gray-400 {
              color: #6B7280 !important;
            }
            .footer-container .border-gray-800 {
              border-color: #E5E7EB !important;
            }
          `}
        </style>
      </motion.div>
    </AnimatePresence>
  );
};

export default YmIndustryAIAnalysisPage; 