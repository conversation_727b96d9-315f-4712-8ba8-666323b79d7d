{"title": {"text": "同商圈竞品销售综合分析报告", "left": "center", "top": "1%", "textStyle": {"fontSize": 18, "fontWeight": "bold"}}, "tooltip": {"trigger": "axis", "axisPointer": {"type": "cross", "crossStyle": {"color": "#999"}}}, "backgroundColor": "#ffffff", "grid": [{"left": "10%", "right": "10%", "top": "8%", "height": "20%", "containLabel": true}, {"left": "10%", "right": "10%", "top": "35%", "height": "25%", "containLabel": true}, {"left": "10%", "right": "45%", "top": "70%", "height": "25%", "containLabel": true}, {"left": "55%", "right": "10%", "top": "70%", "height": "25%", "containLabel": true}], "legend": [{"data": ["同商圈市场占有率"], "top": "8%", "right": "10%"}, {"data": ["销量（支）", "竞品平均销量"], "top": "35%", "right": "10%"}], "xAxis": [{"gridIndex": 0, "type": "category", "data": ["竞品15", "竞品13", "竞品8", "竞品5", "竞品18", "竞品7", "竞品19", "竞品16", "竞品20", "本机构", "竞品9", "竞品6", "竞品14", "竞品10", "竞品17", "竞品1", "竞品4", "竞品3", "竞品2", "竞品12", "竞品11"], "axisTick": {"show": true, "alignWithLabel": false}, "axisLabel": {"fontWeight": "bold", "fontSize": 9, "rotate": 45, "interval": 0}, "name": "同商圈市场占有率", "nameLocation": "middle", "nameGap": 45}, {"gridIndex": 1, "type": "category", "data": ["竞品15", "竞品13", "竞品8", "竞品5", "竞品18", "竞品7", "竞品19", "竞品16", "竞品20", "本机构", "竞品9", "竞品6", "竞品14", "竞品10", "竞品17", "竞品1", "竞品4", "竞品3", "竞品2", "竞品12", "竞品11"], "axisTick": {"show": true, "alignWithLabel": false}, "axisLabel": {"fontWeight": "bold", "fontSize": 9, "rotate": 45, "interval": 0}, "name": "同商圈内竞品销售量对比", "nameLocation": "middle", "nameGap": 45}], "yAxis": [{"gridIndex": 0, "type": "value", "axisLabel": {"fontSize": 10, "formatter": "{value}%"}, "splitLine": {"show": true, "lineStyle": {"type": "dashed", "opacity": 0.3}}}, {"gridIndex": 1, "type": "value", "axisLabel": {"fontSize": 10}, "splitLine": {"show": true, "lineStyle": {"type": "dashed", "opacity": 0.3}}}], "series": [{"name": "同商圈市场占有率", "type": "line", "xAxisIndex": 0, "yAxisIndex": 0, "data": [{"value": 15, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)", "borderWidth": 2, "borderColor": "#fff"}, "symbolSize": 8}, {"value": 13, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)", "borderWidth": 2, "borderColor": "#fff"}, "symbolSize": 8}, {"value": 12, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)", "borderWidth": 2, "borderColor": "#fff"}, "symbolSize": 8}, {"value": 8, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)", "borderWidth": 2, "borderColor": "#fff"}, "symbolSize": 8}, {"value": 8, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)", "borderWidth": 2, "borderColor": "#fff"}, "symbolSize": 8}, {"value": 8, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)", "borderWidth": 2, "borderColor": "#fff"}, "symbolSize": 8}, {"value": 6, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)", "borderWidth": 2, "borderColor": "#fff"}, "symbolSize": 8}, {"value": 5, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)", "borderWidth": 2, "borderColor": "#fff"}, "symbolSize": 8}, {"value": 5, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)", "borderWidth": 2, "borderColor": "#fff"}, "symbolSize": 8}, {"value": 5, "itemStyle": {"color": "rgb(249, 115, 22)", "borderWidth": 2, "borderColor": "#fff"}, "symbolSize": 16, "label": {"show": true, "formatter": "{c}%", "position": "top", "fontSize": 9, "color": "rgb(249, 115, 22)", "fontWeight": "bold"}}, {"value": 4, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)", "borderWidth": 2, "borderColor": "#fff"}, "symbolSize": 8}, {"value": 4, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)", "borderWidth": 2, "borderColor": "#fff"}, "symbolSize": 8}, {"value": 2, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)", "borderWidth": 2, "borderColor": "#fff"}, "symbolSize": 8}, {"value": 1, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)", "borderWidth": 2, "borderColor": "#fff"}, "symbolSize": 8}, {"value": 1, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)", "borderWidth": 2, "borderColor": "#fff"}, "symbolSize": 8}, {"value": 1, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)", "borderWidth": 2, "borderColor": "#fff"}, "symbolSize": 8}, {"value": 1, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)", "borderWidth": 2, "borderColor": "#fff"}, "symbolSize": 8}, {"value": 0, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)", "borderWidth": 2, "borderColor": "#fff"}, "symbolSize": 8}, {"value": 0, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)", "borderWidth": 2, "borderColor": "#fff"}, "symbolSize": 8}, {"value": 0, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)", "borderWidth": 2, "borderColor": "#fff"}, "symbolSize": 8}, {"value": 0, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)", "borderWidth": 2, "borderColor": "#fff"}, "symbolSize": 8}], "symbol": "circle", "lineStyle": {"width": 3, "type": "solid", "color": "rgba(167, 139, 250, 0.8)"}, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)", "borderWidth": 2, "borderColor": "#fff"}, "label": {"show": true, "formatter": "{c}%", "position": "top", "fontSize": 9, "color": "rgba(167, 139, 250, 0.8)", "fontWeight": "bold"}, "emphasis": {"itemStyle": {"color": "rgba(167, 139, 250, 0.8)", "shadowBlur": 10, "shadowColor": "rgba(167, 139, 250, 0.5)"}}}, {"name": "销量（支）", "type": "bar", "xAxisIndex": 1, "yAxisIndex": 1, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)"}, "data": [{"value": 4222, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)"}}, {"value": 3697, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)"}}, {"value": 3597, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)"}}, {"value": 2256, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)"}}, {"value": 2182, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)"}}, {"value": 2168, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)"}}, {"value": 1778, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)"}}, {"value": 1512, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)"}}, {"value": 1444, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)"}}, {"value": 1420, "itemStyle": {"color": "rgb(249, 115, 22)"}, "label": {"show": true, "formatter": "{c}", "position": "top", "color": "rgb(249, 115, 22)", "fontSize": 9, "fontWeight": "bold"}}, {"value": 1133, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)"}}, {"value": 1086, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)"}}, {"value": 717, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)"}}, {"value": 448, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)"}}, {"value": 410, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)"}}, {"value": 398, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)"}}, {"value": 165, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)"}}, {"value": 83, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)"}}, {"value": 55, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)"}}, {"value": 46, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)"}}, {"value": 9, "itemStyle": {"color": "rgba(167, 139, 250, 0.8)"}}], "label": {"show": true, "formatter": "{c}", "position": "top", "color": "rgb(124, 58, 237)", "fontSize": 9, "fontWeight": "bold"}, "barWidth": 8}, {"name": "竞品平均销量", "type": "line", "xAxisIndex": 1, "yAxisIndex": 1, "data": [1373, 1373, 1373, 1373, 1373, 1373, 1373, 1373, 1373, 1373, 1373, 1373, 1373, 1373, 1373, 1373, 1373, 1373, 1373, 1373, 1373], "symbol": "none", "lineStyle": {"width": 2, "type": "dashed"}, "itemStyle": {"color": "rgb(34, 197, 94)"}, "label": {"show": false}}, {"name": "同商圈各品类销售分布", "type": "pie", "center": ["27.5%", "80%"], "radius": ["8%", "15%"], "roseType": "radius", "data": [{"name": "玻尿酸", "value": 56.3, "itemStyle": {"color": "rgba(159, 122, 234, 0.8)"}}, {"name": "肉毒素", "value": 24.5, "itemStyle": {"color": "rgba(203, 195, 227, 0.8)"}}, {"name": "再生材料", "value": 9.6, "itemStyle": {"color": "rgba(147, 197, 253, 0.8)"}}, {"name": "胶原蛋白", "value": 8.8, "itemStyle": {"color": "rgba(191, 219, 254, 0.8)"}}, {"name": "射频", "value": 0.7, "itemStyle": {"color": "rgba(100, 149, 237, 0.8)"}}, {"name": "其他", "value": 0.1, "itemStyle": {"color": "rgba(180, 160, 230, 0.8)"}}], "label": {"show": true, "formatter": "{b}: {d}%", "position": "outside", "fontSize": 9}, "labelLine": {"show": true}}, {"name": "本机构产品组合销售分布", "type": "pie", "center": ["72.5%", "80%"], "radius": ["8%", "15%"], "roseType": "radius", "data": [{"name": "肉毒素", "value": 76.1, "itemStyle": {"color": "rgba(159, 122, 234, 0.8)"}}, {"name": "玻尿酸", "value": 10.9, "itemStyle": {"color": "rgba(203, 195, 227, 0.8)"}}, {"name": "再生材料", "value": 7.0, "itemStyle": {"color": "rgba(147, 197, 253, 0.8)"}}, {"name": "胶原蛋白", "value": 6.0, "itemStyle": {"color": "rgba(191, 219, 254, 0.8)"}}], "label": {"show": true, "formatter": "{b}: {d}%", "position": "outside", "fontSize": 9}, "labelLine": {"show": true}}], "graphic": [{"type": "text", "left": "20.5%", "top": "95%", "style": {"text": "同商圈各品类销售分布", "fontSize": 12, "fontWeight": "normal", "textAlign": "center"}}, {"type": "text", "left": "63.5%", "top": "95%", "style": {"text": "本机构产品组合销售分布", "fontSize": 12, "fontWeight": "normal", "textAlign": "center"}}]}