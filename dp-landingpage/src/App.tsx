import React, { useState, useEffect, useRef } from "react";
import { ParallaxBanner, ParallaxBannerLayer } from 'react-scroll-parallax';
import { Hero } from "./components/hero";
import { Problems } from "./components/problems";
import { Features } from "./components/features";
import { UseCases } from "./components/use-cases";
import { Testimonials } from "./components/testimonials";
import { CallToAction } from "./components/call-to-action";
import { MainNavbar } from "./components/navbar";
import { Footer } from "./components/footer";
import { RealEstateLogosMarquee, type LogoItem } from "./components/RealEstateLogosMarquee";

import wankeLogo from '@assets/logos/wanke.jpg';
import biguiyuanLogo from '@assets/logos/biguiyuan.jpg';
import zhongnanLogo from '@assets/logos/zhongnan.jpg';
import zhaoshangLogo from '@assets/logos/zhaoshang.jpg';
import jindiLogo from '@assets/logos/jindi.png';
import shangkunLogo from '@assets/logos/shangkun.png';
import baoliLogo from '@assets/logos/baoli.png';
import longhuLogo from '@assets/logos/longhu.png';
import rongchuangLogo from '@assets/logos/rongchuang.jpg';
import zhonghaiLogo from '@assets/logos/zhonghai.png';
import yuanyangLogo from '@assets/logos/yuanyang.png';
import jinmaoLogo from '@assets/logos/jinmao.jpg';
import shimaoLogo from '@assets/logos/shimao.jpg';
// import heshengLogo from '@assets/logos/hesheng.png';
import lvchengLogo from '@assets/logos/lvcheng.jpg';
import jianfaLogo from '@assets/logos/jianfa.png';
import yuexiuLogo from '@assets/logos/yuexiu.png';
import huafaLogo from '@assets/logos/huafa.jpg';
import zhongguotiejianLogo from '@assets/logos/zhongguotiejian.png';
import mideaLogo from '@assets/logos/midea.jpg';
import xinchengLogo from '@assets/logos/xincheng.jpg';
import dayuechengLogo from '@assets/logos/dayuecheng.png';
import zhuoyueLogo from '@assets/logos/zhuoyue.jpg';
import guomaoLogo from '@assets/logos/guomao.png';
import zhongtieLogo from '@assets/logos/zhongtie.png';
import shoukaiLogo from '@assets/logos/shoukai.png';


// 导入自定义样式
import './styles.css';
import { lcov } from "node:test/reporters";
import { lchownSync } from "fs";

// Placeholder Logos - replace with actual logos later
const realEstateLogos: LogoItem[] = [
  { id: '1', src: wankeLogo, alt: '万科Logo', name: '万科' },
  { id: '2', src: biguiyuanLogo, alt: '碧桂园Logo', name: '碧桂园' },
  { id: '3', src: zhongnanLogo, alt: '中南集团Logo', name: '中南集团' },
  { id: '4', src: zhaoshangLogo, alt: '招商蛇口Logo', name: '招商蛇口' },
  { id: '5', src: jindiLogo, alt: '金地集团Logo', name: '金地集团' },
  { id: '6', src: shangkunLogo, alt: '上坤Logo', name: '上坤' },
  { id: '7', src: baoliLogo, alt: '保利地产Logo', name: '保利地产' },
  { id: '8', src: 'data:image/png;base64,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', alt: '华润置地Logo', name: '华润置地' },
  { id: '9', src: longhuLogo, alt: '龙湖地产Logo', name: '龙湖地产' },
  { id: '10', src: 'data:image/png;base64,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', alt: '绿地Logo', name: '绿地' },
  { id: '11', src: rongchuangLogo, alt: '融创Logo', name: '融创' },
  { id: '12', src: zhonghaiLogo, alt: '中海Logo', name: '中海' },
  { id: '13', src: yuanyangLogo, alt: '远洋Logo', name: '远洋' },
  { id: '14', src: jinmaoLogo, alt: '金茂Logo', name: '金茂' },
  { id: '15', src: shimaoLogo, alt: '世茂Logo', name: '世茂' },
  // { id: '16', src: heshengLogo, alt: '合生创展Logo', name: '合生创展' },
  { id: '17', src: lvchengLogo, alt: '绿城中国Logo', name: '绿城中国' },
  { id: '18', src: jianfaLogo, alt: '建发Logo', name: '建发' },
  { id: '19', src: yuexiuLogo, alt: '越秀地产Logo', name: '越秀地产' },
  { id: '20', src: huafaLogo, alt: '华发股份Logo', name: '华发股份' },
  { id: '21', src: zhongguotiejianLogo, alt: '中国铁建Logo', name: '中国铁建' },
  { id: '22', src: zhongtieLogo, alt: '中国中铁Logo', name: '中国中铁' },
  { id: '23', src: shoukaiLogo, alt: '首开股份Logo', name: '首开股份' },
  { id: '24', src: mideaLogo, alt: '美的置业Logo', name: '美的置业' },
  { id: '25', src: xinchengLogo, alt: '新城控股Logo', name: '新城控股' },
  { id: '26', src: guomaoLogo, alt: '国贸地产Logo', name: '国贸地产' },
  { id: '27', src: dayuechengLogo, alt: '大悦城控股Logo', name: '大悦城控股' },
  { id: '28', src: zhuoyueLogo, alt: '卓越集团Logo', name: '卓越集团' },
];

export default function App() {
  const sectionsRef = useRef<HTMLDivElement>(null);
  const [activeSection, setActiveSection] = useState(0);
  const [currentPage, setCurrentPage] = useState<'main' | 'ym' | 'ym-consumer-research' | 'ym-industry-ai'>('main');
  
  // 检查URL hash来确定要显示的页面
  useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash;
      if (hash === '#ym/cr') {
        setCurrentPage('ym-consumer-research');
      } else if (hash === '#ym/iaa') {
        setCurrentPage('ym-industry-ai');
      } else if (hash.startsWith('#ym')) {
        setCurrentPage('ym');
      } else {
        setCurrentPage('main');
      }
    };
    
    // 初始化检查
    handleHashChange();
    
    // 监听hash变化
    window.addEventListener('hashchange', handleHashChange);
    
    return () => window.removeEventListener('hashchange', handleHashChange);
  }, []);
  
  // 滚动到指定部分
  const scrollToSection = (index: number) => {
    const sections = document.querySelectorAll('.scroll-section');
    if (sections[index]) {
      sections[index].scrollIntoView({ behavior: 'smooth' });
      setActiveSection(index);
    }
  };
  
  // 设置所有section-content为active，确保内容可见
  useEffect(() => {
    // 初始化时将所有section-content设为active
    const sectionContents = document.querySelectorAll('.section-content');
    sectionContents.forEach((content) => {
      content.classList.add('active');
    });

    // 然后根据可见部分更新activeSection
    const handleScroll = () => {
      if (sectionsRef.current) {
        const sections = document.querySelectorAll('.scroll-section');
        const scrollPosition = window.scrollY;
        
        sections.forEach((section, index) => {
          const sectionTop = (section as HTMLElement).offsetTop;
          const sectionHeight = (section as HTMLElement).offsetHeight;
          
          if (
            scrollPosition >= sectionTop - window.innerHeight/2 &&
            scrollPosition < sectionTop + sectionHeight - window.innerHeight/2
          ) {
            setActiveSection(index);
            // 确保激活区域的内容有active类
            const content = section.querySelector('.section-content');
            if (content && !content.classList.contains('active')) {
              content.classList.add('active');
            }
          }
        });
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    // 初始加载时触发一次滚动检测
    setTimeout(handleScroll, 100);
    
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  // 键盘导航
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        if (activeSection < 0) {
          scrollToSection(activeSection + 1);
        }
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        if (activeSection > 0) {
          scrollToSection(activeSection - 1);
        }
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [activeSection]);
  
  // 如果要显示医学美容页面，导入并渲染医学美容组件
  if (currentPage.startsWith('ym')) {
    // 使用React.lazy进行动态导入
    let ComponentToRender;
    if (currentPage === 'ym-consumer-research') {
      ComponentToRender = React.lazy(() => import('./ym/pages').then(module => ({ default: module.YmConsumerResearchPage })));
    } else if (currentPage === 'ym-industry-ai') {
      ComponentToRender = React.lazy(() => import('./ym/pages').then(module => ({ default: module.YmIndustryAIAnalysisPage })));
    } else {
      ComponentToRender = React.lazy(() => import('./ym').then(module => ({ default: module.YmMedicalBeautyPage })));
    }
    
    return (
      <React.Suspense fallback={<div className="flex items-center justify-center min-h-screen">
        <div className="text-xl">加载中...</div>
      </div>}>
        <ComponentToRender />
      </React.Suspense>
    );
  }
  
  return (
    <div className="min-h-screen text-foreground">
      
      {/* 固定导航栏 */}
      <MainNavbar />
      
      {/* 导航点 */}
      <div className="fp-nav fixed right-6 top-1/2 transform -translate-y-1/2 z-50">
        {/* {['首页', '问题', '特性', '用例', '评价', '合作伙伴', '联系'].map((tooltip, index) => ( */}
        {['首页'].map((tooltip, index) => (
          <div 
            key={index}
            className={`nav-dot my-4 cursor-pointer group relative ${activeSection === index ? 'active' : ''}`}
            onClick={() => scrollToSection(index)}
          >
            <span 
              className={`block w-3 h-3 rounded-full transition-all duration-300 ${activeSection === index ? 'bg-pink-500 shadow-glow' : 'bg-indigo-500'}`}
            ></span>
            <span className="nav-tooltip opacity-0 group-hover:opacity-100 absolute right-6 top-0 bg-white/80 text-indigo-600 rounded px-2 py-1 text-sm whitespace-nowrap transition-opacity">
              {tooltip}
            </span>
          </div>
        ))}
      </div>
      
      {/* 内容区域 */}
      <div ref={sectionsRef} className="smooth-scroll">
        <section id="hero" className="scroll-section min-h-screen flex items-center">
          <div className={`section-content ${activeSection === 0 ? 'active' : ''}`}>
            <Hero />
          </div>
          {activeSection === 0 && (
            <div className="scroll-indicator" onClick={() => scrollToSection(1)}></div>
          )}
        </section>
        
        {/* <section id="problems" className="scroll-section min-h-screen flex items-center">
          <div className={`section-content ${activeSection === 1 ? 'active' : ''}`}>
            <Problems />
          </div>
          {activeSection === 1 && (
            <div className="scroll-indicator" onClick={() => scrollToSection(2)}></div>
          )}
        </section> */}
        
        {/* <section id="features" className="scroll-section min-h-screen flex items-center">
          <div className={`section-content ${activeSection === 2 ? 'active' : ''}`}>
            <Features />
          </div>
          {activeSection === 2 && (
            <div className="scroll-indicator" onClick={() => scrollToSection(3)}></div>
          )}
        </section> */}
        
        {/* <section id="use-cases" className="scroll-section min-h-screen flex items-center">
          <div className={`section-content ${activeSection === 3 ? 'active' : ''}`}>
            <UseCases />
          </div>
          {activeSection === 3 && (
            <div className="scroll-indicator" onClick={() => scrollToSection(4)}></div>
          )}
        </section> */}
        
        {/* <section id="testimonials" className="scroll-section min-h-screen flex items-center">
          <div className={`section-content ${activeSection === 4 ? 'active' : ''}`}>
            <Testimonials />
          </div>
          {activeSection === 4 && (
            <div className="scroll-indicator" onClick={() => scrollToSection(5)}></div>
          )}
        </section> */}
        
        {/* Added Real Estate Logos Section */}
        {/* <section id="real-estate-logos" className="scroll-section bg-slate-50 dark:bg-slate-900 py-12 md:py-20">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl md:text-4xl font-bold text-center mb-2 text-slate-800 dark:text-slate-100">合作伙伴</h2>
            <p className="text-center text-slate-600 dark:text-slate-400 mb-8 md:mb-12">一些与我们合作的优秀房地产开发商</p>
            <RealEstateLogosMarquee logos={realEstateLogos} speed="30s" logoClassName="h-14" />
          </div>
        </section> */}
        
        {/* <section id="cta" className="scroll-section min-h-screen flex flex-col items-center">
          <div className={`section-content ${activeSection === 5 ? 'active' : ''}`}>
            <CallToAction />
          </div>
        </section> */}
      </div>
      
      {/* 页脚 */}
      <Footer />
    </div>
  );
}