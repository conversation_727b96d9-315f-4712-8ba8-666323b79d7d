# AI Agent 对话系统设计文档

## 文档信息

- **创建时间**: 2025-08-02 09:43:09
- **设计原则**: MVP 最小化可行产品
- **设计目标**: 基于现有 DIPS Pro 系统构建多 Agent 协作对话系统

## 1. 系统概述

### 1.1 核心理念

基于多个 AI Agent 协作完成复杂任务的对话系统，每个 Agent 专注于特定领域的处理，通过流式输出和思维链展示，为用户提供透明、高效的 AI 服务体验。

### 1.2 设计目标

- **多 Agent 协作**: 支持多个专业化 Agent 排列组合完成复杂任务
- **配置化管理**: 管理员可独立配置 Agent 和流程，用户无感使用
- **流式体验**: 支持实时流式输出，避免用户长时间等待
- **思维链展示**: 展示每个 Agent 的执行过程和思考链路
- **系统复用**: 最大化复用现有 Chat 模块的基础设施

## 2. 系统架构

### 2.1 整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                        用户界面层                                  │
├─────────────────────────────────────────────────────────────────┤
│  普通聊天界面          │           AI Agent 聊天界面              │
│  /api/chat            │           /api/agent-chat              │
└─────────────────────────────────────────────────────────────────┘
                                    │
┌─────────────────────────────────────────────────────────────────┐
│                        控制器层                                   │
├─────────────────────────────────────────────────────────────────┤
│  ChatController       │           AgentChatController           │
└─────────────────────────────────────────────────────────────────┘
                                    │
┌─────────────────────────────────────────────────────────────────┐
│                        服务层                                     │
├─────────────────────────────────────────────────────────────────┤
│  ChatService          │           AgentChatService              │
│                       │                  │                      │
│                       │           AgentFlowService              │
│                       │                  │                      │
│                       │           AgentExecutionService         │
└─────────────────────────────────────────────────────────────────┘
                                    │
┌─────────────────────────────────────────────────────────────────┐
│                        Agent 层                                  │
├─────────────────────────────────────────────────────────────────┤
│  意图识别Agent │ 数据处理Agent │ 数据分析Agent │ 推理Agent │ 其他...│
└─────────────────────────────────────────────────────────────────┘
                                    │
┌─────────────────────────────────────────────────────────────────┐
│                        数据层                                     │
├─────────────────────────────────────────────────────────────────┤
│  复用现有 Chat 数据表    │      新增 Agent 配置相关表              │
│  - conversations       │      - agent_definitions                │
│  - chat_messages        │      - agent_flows                     │
│                        │      - agent_executions                │
└─────────────────────────────────────────────────────────────────┘
```

### 2.2 核心组件说明

#### 2.2.1 Agent 定义组件

- **AgentDefinition**: 单个 Agent 的定义，包含提示词、能力描述等
- **AgentFlow**: Agent 流程定义，描述 Agent 之间的调用关系
- **AgentExecution**: Agent 执行记录，记录每次执行的详细信息

#### 2.2.2 执行引擎组件

- **AgentExecutionService**: Agent 执行引擎，负责单个 Agent 的执行
- **AgentFlowService**: 流程编排服务，负责 Agent 间的协调调用
- **AgentChatService**: Agent 聊天服务，整合流程执行和响应生成

#### 2.2.3 配置管理组件

- **AgentConfigController**: Agent 配置管理接口
- **FlowConfigController**: 流程配置管理接口

## 3. 数据模型设计

### 3.1 Agent 定义表 (agent_definitions)

```sql
CREATE TABLE agent_definitions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL COMMENT 'Agent名称',
    display_name VARCHAR(200) NOT NULL COMMENT 'Agent显示名称',
    description TEXT COMMENT 'Agent功能描述',
    system_prompt TEXT NOT NULL COMMENT '系统提示词',
    capabilities JSONB COMMENT 'Agent能力配置 JSON',
    model_config JSONB COMMENT '模型配置 JSON',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态: ACTIVE, INACTIVE',
    created_by VARCHAR(32) COMMENT '创建者ID',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_agent_definitions_name ON agent_definitions(name);
CREATE INDEX idx_agent_definitions_status ON agent_definitions(status);
```

### 3.2 Agent 流程表 (agent_flows)

```sql
CREATE TABLE agent_flows (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL COMMENT '流程名称',
    display_name VARCHAR(200) NOT NULL COMMENT '流程显示名称',
    description TEXT COMMENT '流程描述',
    flow_definition JSONB NOT NULL COMMENT '流程定义 JSON',
    trigger_conditions JSONB COMMENT '触发条件配置',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态: ACTIVE, INACTIVE',
    priority INTEGER DEFAULT 0 COMMENT '优先级，数字越大优先级越高',
    created_by VARCHAR(32) COMMENT '创建者ID',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_agent_flows_name ON agent_flows(name);
CREATE INDEX idx_agent_flows_status ON agent_flows(status);
CREATE INDEX idx_agent_flows_priority ON agent_flows(priority DESC);
```

### 3.3 Agent 执行记录表 (agent_executions)

```sql
CREATE TABLE agent_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID NOT NULL COMMENT '对话ID，关联conversations表',
    message_id UUID NOT NULL COMMENT '消息ID，关联chat_messages表',
    flow_id UUID COMMENT '流程ID，关联agent_flows表',
    agent_id UUID NOT NULL COMMENT 'Agent ID，关联agent_definitions表',
    agent_name VARCHAR(100) NOT NULL COMMENT 'Agent名称快照',
    execution_order INTEGER NOT NULL COMMENT '执行顺序',
    input_data JSONB COMMENT '输入数据',
    output_data JSONB COMMENT '输出数据',
    thinking_process JSONB COMMENT '思维过程记录',
    execution_status VARCHAR(20) NOT NULL COMMENT '执行状态: PENDING, RUNNING, COMPLETED, FAILED',
    error_message TEXT COMMENT '错误信息',
    token_usage JSONB COMMENT 'Token使用统计',
    execution_time_ms INTEGER COMMENT '执行耗时（毫秒）',
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_agent_executions_conversation ON agent_executions(conversation_id);
CREATE INDEX idx_agent_executions_message ON agent_executions(message_id);
CREATE INDEX idx_agent_executions_flow ON agent_executions(flow_id);
CREATE INDEX idx_agent_executions_status ON agent_executions(execution_status);
```

### 3.4 复用现有表结构

现有的 `conversations` 和 `chat_messages` 表将继续使用，通过 `message_id` 关联 Agent 执行记录。

**增强现有 chat_messages 表**:

```sql
-- 为 chat_messages 表添加新字段（如果不存在）
ALTER TABLE chat_messages ADD COLUMN IF NOT EXISTS message_type VARCHAR(20) DEFAULT 'NORMAL' COMMENT '消息类型: NORMAL, AGENT_FLOW';
ALTER TABLE chat_messages ADD COLUMN IF NOT EXISTS agent_flow_id UUID COMMENT '关联的Agent流程ID';

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_chat_messages_type ON chat_messages(message_type);
CREATE INDEX IF NOT EXISTS idx_chat_messages_agent_flow ON chat_messages(agent_flow_id);
```

## 4. 核心业务流程

### 4.1 典型执行流程示例

#### 用户询问数据问题的处理流程:

```
1. 用户发送消息: "分析一下我们Q4的销售数据趋势"
   ↓
2. 意图识别Agent: 识别为数据分析需求
   ↓
3. 数据处理Agent: 查询Q4销售数据
   ↓
4. 数据分析Agent: 分析数据趋势和模式
   ↓
5. 推理Agent: 基于分析结果进行业务推理
   ↓
6. 可视化Agent: 生成趋势图表
   ↓
7. 报告生成Agent: 整合分析结果和图表生成报告
   ↓
8. 结果审核Agent: 审核报告质量和准确性
   ↓
9. 返回最终结果给用户
```

### 4.2 流程配置示例

```json
{
  "flowId": "data-analysis-flow",
  "name": "数据分析流程",
  "description": "处理用户数据分析请求的完整流程",
  "trigger": {
    "type": "intent",
    "conditions": ["data_analysis", "data_query", "report_generation"]
  },
  "steps": [
    {
      "stepId": "intent-recognition",
      "agentId": "intent-agent",
      "agentName": "意图识别Agent",
      "description": "识别用户意图和需求",
      "inputMapping": {
        "userMessage": "{{userInput}}"
      },
      "outputMapping": {
        "intent": "{{output.intent}}",
        "entities": "{{output.entities}}"
      },
      "nextSteps": ["data-processing"]
    },
    {
      "stepId": "data-processing",
      "agentId": "data-agent",
      "agentName": "数据处理Agent",
      "description": "查询和处理相关数据",
      "inputMapping": {
        "intent": "{{intent}}",
        "entities": "{{entities}}"
      },
      "outputMapping": {
        "rawData": "{{output.data}}",
        "dataSource": "{{output.source}}"
      },
      "nextSteps": ["data-analysis"]
    },
    {
      "stepId": "data-analysis",
      "agentId": "analysis-agent",
      "agentName": "数据分析Agent",
      "description": "分析数据并提取洞察",
      "inputMapping": {
        "data": "{{rawData}}",
        "analysisType": "{{intent}}"
      },
      "outputMapping": {
        "insights": "{{output.insights}}",
        "patterns": "{{output.patterns}}"
      },
      "nextSteps": ["reasoning"]
    },
    {
      "stepId": "reasoning",
      "agentId": "reasoning-agent",
      "agentName": "推理Agent",
      "description": "基于分析结果进行推理",
      "inputMapping": {
        "insights": "{{insights}}",
        "patterns": "{{patterns}}"
      },
      "outputMapping": {
        "conclusions": "{{output.conclusions}}",
        "recommendations": "{{output.recommendations}}"
      },
      "nextSteps": ["visualization", "report-generation"]
    },
    {
      "stepId": "visualization",
      "agentId": "viz-agent",
      "agentName": "可视化Agent",
      "description": "生成数据可视化图表",
      "inputMapping": {
        "data": "{{rawData}}",
        "insights": "{{insights}}"
      },
      "outputMapping": {
        "charts": "{{output.charts}}",
        "chartConfig": "{{output.config}}"
      },
      "nextSteps": ["report-generation"]
    },
    {
      "stepId": "report-generation",
      "agentId": "report-agent",
      "agentName": "报告生成Agent",
      "description": "整合信息生成最终报告",
      "inputMapping": {
        "insights": "{{insights}}",
        "conclusions": "{{conclusions}}",
        "recommendations": "{{recommendations}}",
        "charts": "{{charts}}"
      },
      "outputMapping": {
        "report": "{{output.report}}"
      },
      "nextSteps": ["review"]
    },
    {
      "stepId": "review",
      "agentId": "review-agent",
      "agentName": "审核Agent",
      "description": "审核报告质量和准确性",
      "inputMapping": {
        "report": "{{report}}",
        "originalData": "{{rawData}}"
      },
      "outputMapping": {
        "finalReport": "{{output.finalReport}}",
        "qualityScore": "{{output.qualityScore}}"
      },
      "nextSteps": []
    }
  ]
}
```

## 5. API 接口设计

### 5.1 Agent 聊天接口

#### 5.1.1 发送 Agent 聊天消息

```http
POST /api/agent-chat
Content-Type: application/json

{
  "message": "分析一下我们Q4的销售数据趋势",
  "conversationId": "optional-conversation-id",
  "flowId": "optional-specific-flow-id",
  "options": {
    "enableThinking": true,
    "enableStreaming": true,
    "maxExecutionTime": 300
  }
}
```

**响应格式**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "conversationId": "conv-uuid",
    "messageId": "msg-uuid",
    "executionId": "exec-uuid",
    "streamingEnabled": true,
    "estimatedSteps": 7
  }
}
```

#### 5.1.2 获取执行状态和结果

```http
GET /api/agent-chat/{executionId}/status
```

**响应格式**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "executionId": "exec-uuid",
    "status": "RUNNING",
    "currentStep": 3,
    "totalSteps": 7,
    "currentAgentName": "数据分析Agent",
    "progress": 42.8,
    "steps": [
      {
        "stepId": "intent-recognition",
        "agentName": "意图识别Agent",
        "status": "COMPLETED",
        "thinkingProcess": "用户询问Q4销售数据趋势，这是一个典型的数据分析需求...",
        "output": "识别为数据分析需求，需要查询Q4销售数据",
        "executionTime": 1200
      },
      {
        "stepId": "data-processing",
        "agentName": "数据处理Agent",
        "status": "COMPLETED",
        "thinkingProcess": "正在查询Q4销售数据库...",
        "output": "成功获取Q4销售数据，包含1000条记录",
        "executionTime": 2300
      },
      {
        "stepId": "data-analysis",
        "agentName": "数据分析Agent",
        "status": "RUNNING",
        "thinkingProcess": "正在分析销售趋势，计算同比环比数据...",
        "output": null,
        "executionTime": null
      }
    ]
  }
}
```

#### 5.1.3 流式获取执行过程

```http
GET /api/agent-chat/{executionId}/stream
Accept: text/event-stream
```

**SSE 事件格式**:

```
event: step-started
data: {"stepId": "data-analysis", "agentName": "数据分析Agent", "description": "分析数据并提取洞察"}

event: thinking
data: {"stepId": "data-analysis", "thinking": "正在计算Q4销售数据的环比增长率..."}

event: step-output
data: {"stepId": "data-analysis", "output": "Q4销售额同比增长15.2%，环比增长8.7%"}

event: step-completed
data: {"stepId": "data-analysis", "status": "COMPLETED", "executionTime": 3400}

event: execution-completed
data: {"executionId": "exec-uuid", "finalResult": "生成的完整分析报告..."}
```

### 5.2 Agent 配置管理接口

#### 5.2.1 Agent 定义管理

```http
# 获取 Agent 列表
GET /api/admin/agents?page=0&size=20&status=ACTIVE

# 获取 Agent 详情
GET /api/admin/agents/{agentId}

# 创建 Agent
POST /api/admin/agents
{
  "name": "data-analysis-agent",
  "displayName": "数据分析Agent",
  "description": "专门用于数据分析和洞察提取的Agent",
  "systemPrompt": "你是一个专业的数据分析师...",
  "capabilities": {
    "dataQuery": true,
    "statisticalAnalysis": true,
    "trendAnalysis": true
  },
  "modelConfig": {
    "model": "deepseek-chat",
    "temperature": 0.2,
    "maxTokens": 4000
  }
}

# 更新 Agent
PUT /api/admin/agents/{agentId}

# 删除 Agent
DELETE /api/admin/agents/{agentId}
```

#### 5.2.2 流程配置管理

```http
# 获取流程列表
GET /api/admin/flows?page=0&size=20&status=ACTIVE

# 获取流程详情
GET /api/admin/flows/{flowId}

# 创建流程
POST /api/admin/flows
{
  "name": "data-analysis-flow",
  "displayName": "数据分析流程",
  "description": "处理数据分析请求的完整流程",
  "flowDefinition": { ... },
  "triggerConditions": {
    "intents": ["data_analysis", "data_query"],
    "keywords": ["分析", "数据", "趋势"]
  }
}

# 更新流程
PUT /api/admin/flows/{flowId}

# 删除流程
DELETE /api/admin/flows/{flowId}

# 测试流程
POST /api/admin/flows/{flowId}/test
{
  "testInput": "测试消息",
  "mockData": true
}
```

## 6. 技术实现方案

### 6.1 核心类设计

#### 6.1.1 Agent 定义类

```java
// AgentDefinition.java
@Entity
@Table(name = "agent_definitions")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgentDefinition {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Column(nullable = false, length = 100)
    private String name;

    @Column(name = "display_name", nullable = false, length = 200)
    private String displayName;

    @Column(columnDefinition = "TEXT")
    private String description;

    @Column(name = "system_prompt", columnDefinition = "TEXT", nullable = false)
    private String systemPrompt;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    private String capabilities;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "model_config", columnDefinition = "jsonb")
    private String modelConfig;

    @Enumerated(EnumType.STRING)
    private AgentStatus status = AgentStatus.ACTIVE;

    @Column(name = "created_by", length = 32)
    private String createdBy;

    @CreationTimestamp
    @Column(name = "created_at")
    private Instant createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private Instant updatedAt;
}

// AgentStatus.java
public enum AgentStatus {
    ACTIVE, INACTIVE
}
```

#### 6.1.2 Agent 执行服务

```java
// AgentExecutionService.java
@Service
@Slf4j
@RequiredArgsConstructor
public class AgentExecutionService {

    private final ChatClient chatClient;
    private final AgentDefinitionRepository agentDefinitionRepository;
    private final AgentExecutionRepository agentExecutionRepository;

    /**
     * 执行单个 Agent
     */
    public Mono<AgentExecutionResult> executeAgent(AgentExecutionRequest request) {
        return Mono.fromCallable(() -> {
            // 1. 获取 Agent 定义
            AgentDefinition agent = agentDefinitionRepository.findById(request.getAgentId())
                .orElseThrow(() -> new RuntimeException("Agent not found"));

            // 2. 创建执行记录
            AgentExecution execution = createExecution(request, agent);
            execution.setExecutionStatus(ExecutionStatus.RUNNING);
            execution.setStartedAt(Instant.now());
            agentExecutionRepository.save(execution);

            // 3. 构建提示词
            String prompt = buildPrompt(agent, request.getInputData());

            // 4. 调用 AI 模型
            String response = chatClient.prompt()
                .system(agent.getSystemPrompt())
                .user(prompt)
                .call()
                .content();

            // 5. 解析响应
            AgentExecutionResult result = parseResponse(response);

            // 6. 更新执行记录
            execution.setOutputData(JsonUtils.toJson(result.getOutput()));
            execution.setThinkingProcess(JsonUtils.toJson(result.getThinking()));
            execution.setExecutionStatus(ExecutionStatus.COMPLETED);
            execution.setCompletedAt(Instant.now());
            execution.setExecutionTimeMs(
                (int) Duration.between(execution.getStartedAt(), execution.getCompletedAt()).toMillis()
            );
            agentExecutionRepository.save(execution);

            return result;
        })
        .subscribeOn(Schedulers.boundedElastic())
        .doOnError(error -> {
            log.error("Agent execution failed", error);
            // 更新执行记录为失败状态
            updateExecutionStatus(request.getExecutionId(), ExecutionStatus.FAILED, error.getMessage());
        });
    }

    /**
     * 流式执行 Agent
     */
    public Flux<AgentExecutionEvent> executeAgentStream(AgentExecutionRequest request) {
        return Flux.create(sink -> {
            try {
                // 发送开始事件
                sink.next(AgentExecutionEvent.started(request.getAgentId()));

                // 执行 Agent 逻辑
                executeAgent(request)
                    .subscribe(
                        result -> {
                            // 发送思维过程事件
                            if (result.getThinking() != null) {
                                sink.next(AgentExecutionEvent.thinking(result.getThinking()));
                            }

                            // 发送输出事件
                            sink.next(AgentExecutionEvent.output(result.getOutput()));

                            // 发送完成事件
                            sink.next(AgentExecutionEvent.completed(result));
                            sink.complete();
                        },
                        error -> sink.error(error)
                    );
            } catch (Exception e) {
                sink.error(e);
            }
        });
    }
}
```

#### 6.1.3 Agent 流程服务

```java
// AgentFlowService.java
@Service
@Slf4j
@RequiredArgsConstructor
public class AgentFlowService {

    private final AgentExecutionService agentExecutionService;
    private final AgentFlowRepository agentFlowRepository;
    private final IntentRecognitionService intentRecognitionService;

    /**
     * 执行 Agent 流程
     */
    public Flux<AgentFlowEvent> executeFlow(String userMessage, String conversationId, UUID messageId) {
        return Flux.create(sink -> {
            try {
                // 1. 意图识别，选择合适的流程
                AgentFlow flow = selectFlow(userMessage);

                // 2. 发送流程开始事件
                sink.next(AgentFlowEvent.flowStarted(flow.getId(), flow.getDisplayName()));

                // 3. 执行流程中的各个步骤
                executeFlowSteps(flow, userMessage, conversationId, messageId)
                    .subscribe(
                        event -> sink.next(event),
                        error -> sink.error(error),
                        () -> {
                            sink.next(AgentFlowEvent.flowCompleted(flow.getId()));
                            sink.complete();
                        }
                    );

            } catch (Exception e) {
                sink.error(e);
            }
        });
    }

    /**
     * 执行流程步骤
     */
    private Flux<AgentFlowEvent> executeFlowSteps(AgentFlow flow, String userMessage,
                                                  String conversationId, UUID messageId) {
        FlowDefinition definition = JsonUtils.fromJson(flow.getFlowDefinition(), FlowDefinition.class);
        FlowContext context = new FlowContext(userMessage);

        return Flux.fromIterable(definition.getSteps())
            .concatMap(step -> executeStep(step, context, conversationId, messageId))
            .doOnNext(event -> {
                // 更新流程上下文
                if (event.getType() == AgentFlowEventType.STEP_OUTPUT) {
                    context.addStepOutput(event.getStepId(), event.getData());
                }
            });
    }

    /**
     * 执行单个步骤
     */
    private Flux<AgentFlowEvent> executeStep(FlowStep step, FlowContext context,
                                           String conversationId, UUID messageId) {
        return Flux.create(sink -> {
            // 1. 发送步骤开始事件
            sink.next(AgentFlowEvent.stepStarted(step.getStepId(), step.getAgentName()));

            // 2. 准备输入数据
            Map<String, Object> inputData = prepareInputData(step, context);

            // 3. 创建执行请求
            AgentExecutionRequest request = AgentExecutionRequest.builder()
                .agentId(UUID.fromString(step.getAgentId()))
                .conversationId(conversationId)
                .messageId(messageId)
                .stepId(step.getStepId())
                .inputData(inputData)
                .build();

            // 4. 执行 Agent
            agentExecutionService.executeAgentStream(request)
                .subscribe(
                    agentEvent -> {
                        // 转换 Agent 事件为流程事件
                        AgentFlowEvent flowEvent = convertToFlowEvent(step.getStepId(), agentEvent);
                        sink.next(flowEvent);
                    },
                    error -> sink.error(error),
                    () -> {
                        sink.next(AgentFlowEvent.stepCompleted(step.getStepId()));
                        sink.complete();
                    }
                );
        });
    }

    /**
     * 选择合适的流程
     */
    private AgentFlow selectFlow(String userMessage) {
        // 1. 调用意图识别服务
        String intent = intentRecognitionService.recognizeIntent(userMessage);

        // 2. 根据意图匹配流程
        List<AgentFlow> candidateFlows = agentFlowRepository.findByTriggerIntent(intent);

        // 3. 选择优先级最高的流程
        return candidateFlows.stream()
            .filter(flow -> flow.getStatus() == FlowStatus.ACTIVE)
            .max(Comparator.comparing(AgentFlow::getPriority))
            .orElseThrow(() -> new RuntimeException("No suitable flow found for intent: " + intent));
    }
}
```

### 6.2 复用现有基础设施

#### 6.2.1 复用 Spring AI 配置

- 直接使用现有的 `SpringAiConfig` 和 `ChatClient`
- 复用 DeepSeek API 配置和模型参数
- 利用现有的超时和重试机制

#### 6.2.2 复用数据库配置

- 使用现有的 PostgreSQL 数据源配置
- 复用 JPA 和 Hibernate 配置
- 利用现有的连接池和事务管理

#### 6.2.3 复用 WebSocket/SSE 基础设施

- 利用现有的 WebClient 配置进行 HTTP 通信
- 复用异步处理和响应式编程模式
- 使用现有的超时和错误处理机制

## 7. 前端集成方案

### 7.1 新增 Agent 聊天页面

在现有的前端项目中新增 Agent 聊天模块，与普通聊天功能并列存在：

```
src/views/
├── chat/                 # 现有普通聊天
│   ├── ChatView.vue
│   └── components/
└── agent-chat/           # 新增 Agent 聊天
    ├── AgentChatView.vue
    ├── components/
    │   ├── AgentFlowStatus.vue
    │   ├── ThinkingProcess.vue
    │   └── StepProgress.vue
    └── stores/
        └── agentChatStore.ts
```

### 7.2 Agent 执行状态展示

```vue
<!-- AgentFlowStatus.vue -->
<template>
  <div class="agent-flow-status">
    <div class="flow-header">
      <h3>{{ flowName }}</h3>
      <div class="progress-bar">
        <div class="progress" :style="{ width: progress + '%' }"></div>
      </div>
      <span class="progress-text">{{ currentStep }}/{{ totalSteps }}</span>
    </div>

    <div class="steps-container">
      <div
        v-for="step in steps"
        :key="step.stepId"
        :class="['step-item', step.status.toLowerCase()]"
      >
        <div class="step-header">
          <span class="step-name">{{ step.agentName }}</span>
          <span class="step-status">{{ step.status }}</span>
        </div>

        <div v-if="step.thinkingProcess" class="thinking-process">
          <div class="thinking-header">思考过程:</div>
          <div class="thinking-content">{{ step.thinkingProcess }}</div>
        </div>

        <div v-if="step.output" class="step-output">
          <div class="output-header">输出结果:</div>
          <div class="output-content">{{ step.output }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
```

### 7.3 流式数据处理

```typescript
// agentChatStore.ts
export const useAgentChatStore = defineStore('agentChat', () => {
  const executionStatus = ref<AgentExecutionStatus | null>(null)
  const isStreaming = ref(false)

  // 发送 Agent 聊天消息
  const sendAgentMessage = async (message: string, conversationId?: string) => {
    try {
      const response = await agentChatApi.sendMessage({
        message,
        conversationId,
        options: {
          enableThinking: true,
          enableStreaming: true
        }
      })

      // 开始监听流式响应
      if (response.data.streamingEnabled) {
        startStreamListening(response.data.executionId)
      }

      return response.data
    } catch (error) {
      console.error('Failed to send agent message:', error)
      throw error
    }
  }

  // 监听流式响应
  const startStreamListening = (executionId: string) => {
    isStreaming.value = true

    const eventSource = new EventSource(`/api/agent-chat/${executionId}/stream`)

    eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data)

      switch (event.type) {
        case 'step-started':
          updateStepStatus(data.stepId, 'RUNNING')
          break
        case 'thinking':
          updateStepThinking(data.stepId, data.thinking)
          break
        case 'step-output':
          updateStepOutput(data.stepId, data.output)
          break
        case 'step-completed':
          updateStepStatus(data.stepId, 'COMPLETED')
          break
        case 'execution-completed':
          isStreaming.value = false
          eventSource.close()
          break
      }
    }

    eventSource.onerror = (error) => {
      console.error('SSE error:', error)
      isStreaming.value = false
      eventSource.close()
    }
  }

  return {
    executionStatus,
    isStreaming,
    sendAgentMessage,
    startStreamListening
  }
})
```

## 8. 部署和配置

### 8.1 数据库迁移脚本

```sql
-- V1__Create_Agent_Tables.sql

-- 创建 Agent 定义表
CREATE TABLE agent_definitions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    display_name VARCHAR(200) NOT NULL,
    description TEXT,
    system_prompt TEXT NOT NULL,
    capabilities JSONB,
    model_config JSONB,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    created_by VARCHAR(32),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_agent_definitions_name ON agent_definitions(name);
CREATE INDEX idx_agent_definitions_status ON agent_definitions(status);

-- 创建 Agent 流程表
CREATE TABLE agent_flows (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    display_name VARCHAR(200) NOT NULL,
    description TEXT,
    flow_definition JSONB NOT NULL,
    trigger_conditions JSONB,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    priority INTEGER DEFAULT 0,
    created_by VARCHAR(32),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_agent_flows_name ON agent_flows(name);
CREATE INDEX idx_agent_flows_status ON agent_flows(status);
CREATE INDEX idx_agent_flows_priority ON agent_flows(priority DESC);

-- 创建 Agent 执行记录表
CREATE TABLE agent_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID NOT NULL,
    message_id UUID NOT NULL,
    flow_id UUID,
    agent_id UUID NOT NULL,
    agent_name VARCHAR(100) NOT NULL,
    execution_order INTEGER NOT NULL,
    input_data JSONB,
    output_data JSONB,
    thinking_process JSONB,
    execution_status VARCHAR(20) NOT NULL,
    error_message TEXT,
    token_usage JSONB,
    execution_time_ms INTEGER,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_agent_executions_conversation ON agent_executions(conversation_id);
CREATE INDEX idx_agent_executions_message ON agent_executions(message_id);
CREATE INDEX idx_agent_executions_flow ON agent_executions(flow_id);
CREATE INDEX idx_agent_executions_status ON agent_executions(execution_status);

-- 为现有 chat_messages 表添加字段
ALTER TABLE chat_messages ADD COLUMN IF NOT EXISTS message_type VARCHAR(20) DEFAULT 'NORMAL';
ALTER TABLE chat_messages ADD COLUMN IF NOT EXISTS agent_flow_id UUID;

-- 创建新增字段的索引
CREATE INDEX IF NOT EXISTS idx_chat_messages_type ON chat_messages(message_type);
CREATE INDEX IF NOT EXISTS idx_chat_messages_agent_flow ON chat_messages(agent_flow_id);

-- 添加外键约束
ALTER TABLE agent_executions ADD CONSTRAINT fk_agent_executions_conversation
    FOREIGN KEY (conversation_id) REFERENCES conversations(id);
ALTER TABLE agent_executions ADD CONSTRAINT fk_agent_executions_message
    FOREIGN KEY (message_id) REFERENCES chat_messages(id);
ALTER TABLE agent_executions ADD CONSTRAINT fk_agent_executions_flow
    FOREIGN KEY (flow_id) REFERENCES agent_flows(id);
ALTER TABLE agent_executions ADD CONSTRAINT fk_agent_executions_agent
    FOREIGN KEY (agent_id) REFERENCES agent_definitions(id);
```

### 8.2 初始数据脚本

```sql
-- V2__Insert_Default_Agents_And_Flows.sql

-- 插入默认 Agent 定义
INSERT INTO agent_definitions (name, display_name, description, system_prompt, capabilities, model_config) VALUES
('intent-agent', '意图识别Agent', '识别用户意图和需求分类',
 '你是一个专业的意图识别助手。你的任务是分析用户输入，识别用户的真实意图和需求类型。请以JSON格式返回结果，包含intent、confidence、entities等字段。',
 '{"intentRecognition": true, "entityExtraction": true}',
 '{"model": "deepseek-chat", "temperature": 0.1, "maxTokens": 1000}'),

('data-agent', '数据处理Agent', '查询和处理各类数据源',
 '你是一个专业的数据处理助手。你的任务是根据用户需求查询和处理相关数据。请以JSON格式返回结果，包含data、source、summary等字段。',
 '{"dataQuery": true, "dataProcessing": true, "databaseAccess": true}',
 '{"model": "deepseek-chat", "temperature": 0.2, "maxTokens": 2000}'),

('analysis-agent', '数据分析Agent', '专业数据分析和洞察提取',
 '你是一个专业的数据分析师。你的任务是分析数据并提取有价值的洞察。请以JSON格式返回结果，包含insights、patterns、trends等字段。',
 '{"statisticalAnalysis": true, "trendAnalysis": true, "patternRecognition": true}',
 '{"model": "deepseek-chat", "temperature": 0.2, "maxTokens": 3000}'),

('reasoning-agent', '推理Agent', '基于数据进行逻辑推理和预测',
 '你是一个专业的推理分析师。你的任务是基于分析结果进行逻辑推理和预测。请以JSON格式返回结果，包含conclusions、recommendations、predictions等字段。',
 '{"logicalReasoning": true, "prediction": true, "decisionSupport": true}',
 '{"model": "deepseek-chat", "temperature": 0.3, "maxTokens": 3000}'),

('viz-agent', '可视化Agent', '生成数据可视化图表和配置',
 '你是一个专业的数据可视化专家。你的任务是基于数据生成合适的图表配置。请以JSON格式返回结果，包含chartType、chartConfig、chartData等字段。',
 '{"dataVisualization": true, "chartGeneration": true, "chartConfiguration": true}',
 '{"model": "deepseek-chat", "temperature": 0.1, "maxTokens": 2000}'),

('report-agent', '报告生成Agent', '整合信息生成结构化报告',
 '你是一个专业的报告编写专家。你的任务是整合各种信息生成结构化的分析报告。请以JSON格式返回结果，包含report、sections、summary等字段。',
 '{"reportGeneration": true, "contentOrganization": true, "summaryWriting": true}',
 '{"model": "deepseek-chat", "temperature": 0.4, "maxTokens": 4000}'),

('review-agent', '审核Agent', '审核内容质量和准确性',
 '你是一个专业的内容审核专家。你的任务是审核分析报告的质量和准确性。请以JSON格式返回结果，包含qualityScore、issues、suggestions等字段。',
 '{"contentReview": true, "qualityAssurance": true, "accuracyCheck": true}',
 '{"model": "deepseek-chat", "temperature": 0.1, "maxTokens": 2000}');

-- 插入默认流程定义
INSERT INTO agent_flows (name, display_name, description, flow_definition, trigger_conditions, priority) VALUES
('data-analysis-flow', '数据分析流程', '处理用户数据分析请求的完整流程',
 '{
   "steps": [
     {
       "stepId": "intent-recognition",
       "agentId": "(SELECT id FROM agent_definitions WHERE name = ''intent-agent'')",
       "agentName": "意图识别Agent",
       "description": "识别用户意图和需求",
       "inputMapping": {"userMessage": "{{userInput}}"},
       "outputMapping": {"intent": "{{output.intent}}", "entities": "{{output.entities}}"},
       "nextSteps": ["data-processing"]
     },
     {
       "stepId": "data-processing",
       "agentId": "(SELECT id FROM agent_definitions WHERE name = ''data-agent'')",
       "agentName": "数据处理Agent",
       "description": "查询和处理相关数据",
       "inputMapping": {"intent": "{{intent}}", "entities": "{{entities}}"},
       "outputMapping": {"rawData": "{{output.data}}", "dataSource": "{{output.source}}"},
       "nextSteps": ["data-analysis"]
     },
     {
       "stepId": "data-analysis",
       "agentId": "(SELECT id FROM agent_definitions WHERE name = ''analysis-agent'')",
       "agentName": "数据分析Agent",
       "description": "分析数据并提取洞察",
       "inputMapping": {"data": "{{rawData}}", "analysisType": "{{intent}}"},
       "outputMapping": {"insights": "{{output.insights}}", "patterns": "{{output.patterns}}"},
       "nextSteps": ["reasoning", "visualization"]
     },
     {
       "stepId": "reasoning",
       "agentId": "(SELECT id FROM agent_definitions WHERE name = ''reasoning-agent'')",
       "agentName": "推理Agent",
       "description": "基于分析结果进行推理",
       "inputMapping": {"insights": "{{insights}}", "patterns": "{{patterns}}"},
       "outputMapping": {"conclusions": "{{output.conclusions}}", "recommendations": "{{output.recommendations}}"},
       "nextSteps": ["report-generation"]
     },
     {
       "stepId": "visualization",
       "agentId": "(SELECT id FROM agent_definitions WHERE name = ''viz-agent'')",
       "agentName": "可视化Agent",
       "description": "生成数据可视化图表",
       "inputMapping": {"data": "{{rawData}}", "insights": "{{insights}}"},
       "outputMapping": {"charts": "{{output.charts}}", "chartConfig": "{{output.config}}"},
       "nextSteps": ["report-generation"]
     },
     {
       "stepId": "report-generation",
       "agentId": "(SELECT id FROM agent_definitions WHERE name = ''report-agent'')",
       "agentName": "报告生成Agent",
       "description": "整合信息生成最终报告",
       "inputMapping": {"insights": "{{insights}}", "conclusions": "{{conclusions}}", "recommendations": "{{recommendations}}", "charts": "{{charts}}"},
       "outputMapping": {"report": "{{output.report}}"},
       "nextSteps": ["review"]
     },
     {
       "stepId": "review",
       "agentId": "(SELECT id FROM agent_definitions WHERE name = ''review-agent'')",
       "agentName": "审核Agent",
       "description": "审核报告质量和准确性",
       "inputMapping": {"report": "{{report}}", "originalData": "{{rawData}}"},
       "outputMapping": {"finalReport": "{{output.finalReport}}", "qualityScore": "{{output.qualityScore}}"},
       "nextSteps": []
     }
   ]
 }',
 '{"intents": ["data_analysis", "data_query", "report_generation"], "keywords": ["分析", "数据", "趋势", "报告"]}',
 100);
```

### 8.3 应用配置更新

在 `application.yml` 中添加 Agent 相关配置：

```yaml
# Agent 系统配置
app:
  agent:
    # 执行配置
    execution:
      # 默认超时时间（秒）
      default-timeout: 300
      # 最大并发执行数
      max-concurrent-executions: 10
      # 重试次数
      max-retries: 3

    # 流式输出配置
    streaming:
      # 是否启用流式输出
      enabled: true
      # 缓冲区大小
      buffer-size: 1024
      # 心跳间隔（秒）
      heartbeat-interval: 30

    # 意图识别配置
    intent:
      # 置信度阈值
      confidence-threshold: 0.7
      # 默认意图
      default-intent: "general_chat"
```

## 9. MVP 实施计划

### 9.1 第一阶段：核心框架搭建（2 周）

**目标**：建立基础的 Agent 执行框架

**任务清单**：

1. 创建数据库表结构和迁移脚本
2. 实现 Agent 定义和流程配置的基础 CRUD 操作
3. 开发 AgentExecutionService 核心服务
4. 创建简单的意图识别 Agent
5. 实现基础的流程执行逻辑

**交付物**：

- 数据库表结构完成
- Agent 定义和执行的基础 API
- 简单的流程执行演示

### 9.2 第二阶段：流式输出和前端集成（2 周）

**目标**：实现流式输出和基础的前端展示

**任务清单**：

1. 实现 SSE 流式输出功能
2. 开发前端 Agent 聊天界面
3. 实现执行状态和进度的实时展示
4. 添加思维链过程的展示功能
5. 完成基础的错误处理和异常展示

**交付物**：

- 完整的流式输出 API
- Agent 聊天前端界面
- 实时执行状态展示

### 9.3 第三阶段：示例流程和优化（1 周）

**目标**：完成数据分析示例流程和系统优化

**任务清单**：

1. 实现完整的数据分析 Agent 流程
2. 添加可视化图表生成功能
3. 完善报告生成和审核功能
4. 性能优化和错误处理改进
5. 编写用户使用文档

**交付物**：

- 完整的数据分析流程演示
- 系统性能优化完成
- 用户和管理员文档

## 10. 风险评估和缓解措施

### 10.1 技术风险

**风险 1**: AI 模型响应时间不稳定

- **缓解措施**: 实现超时机制和重试逻辑，提供降级处理方案

**风险 2**: 流程执行中某个 Agent 失败

- **缓解措施**: 实现容错机制，支持跳过失败步骤或使用备用 Agent

**风险 3**: 数据一致性问题

- **缓解措施**: 使用数据库事务确保执行记录的一致性

### 10.2 业务风险

**风险 1**: Agent 输出质量不稳定

- **缓解措施**: 实现结果审核机制，提供人工干预接口

**风险 2**: 用户体验复杂性增加

- **缓解措施**: 保持用户界面简洁，隐藏复杂的 Agent 协作细节

### 10.3 运维风险

**风险 1**: 系统资源消耗过大

- **缓解措施**: 实现并发控制和资源限制，添加监控和告警

**风险 2**: Agent 配置错误导致系统异常

- **缓解措施**: 实现配置验证和测试功能，提供配置回滚机制

## 11. 监控和运维

### 11.1 关键指标监控

**性能指标**：

- Agent 执行时间分布
- 流程完成率和失败率
- 系统并发执行数
- API 响应时间

**业务指标**：

- 各类意图识别准确率
- Agent 输出质量评分
- 用户满意度反馈
- 流程使用频率统计

### 11.2 日志策略

**执行日志**：

- 每个 Agent 的输入输出详情
- 执行时间和资源消耗
- 错误信息和堆栈跟踪

**业务日志**：

- 用户交互行为记录
- 流程选择和执行路径
- 配置变更审计日志

## 12. 总结

本设计文档基于 MVP 原则，设计了一个可扩展的 AI Agent 对话系统。核心特点包括：

1. **模块化设计**：每个 Agent 独立配置，流程灵活组合
2. **用户体验优先**：流式输出和思维链展示，提供透明的执行过程
3. **系统复用**：最大化利用现有 Chat 模块和 Spring AI 基础设施
4. **配置化管理**：管理员可独立配置 Agent 和流程，无需代码修改
5. **可观测性**：完整的执行记录和监控体系

该系统将为 DIPS Pro 平台提供强大的 AI 协作能力，支持复杂任务的自动化处理，同时保持良好的用户体验和系统可维护性。

---

**文档版本**: v1.0  
**最后更新**: 2025-08-02 09:43:09
