# 编码标准和约定

## 现有标准合规

**代码风格**: 遵循现有 Java 代码规范，使用 Spring Boot 注解和模式  
**Linting 规则**: 继承现有项目的 Checkstyle 和 PMD 规则  
**测试模式**: 使用现有的 JUnit 5 + Mockito 测试框架  
**文档风格**: 遵循现有 JavaDoc 和 API 文档标准

## Agent 特定标准

### 命名约定

- **Agent 类命名**: 以 `Agent` 结尾，如 `DataAnalysisAgent`
- **服务类命名**: 以 `Service` 结尾，如 `AgentOrchestrationService`
- **控制器命名**: 以 `Controller` 结尾，如 `AgentChatController`
- **DTO 命名**: 以具体用途命名，如 `AgentExecutionStatus`

### 代码组织

- **包结构**: 严格按照现有 `modules/{module}/{layer}` 结构
- **依赖注入**: 使用 `@Autowired` 或构造函数注入
- **配置管理**: 使用 `@ConfigurationProperties` 管理 Agent 配置
- **异常处理**: 继承现有异常处理体系

## 关键集成规则

- **现有 API 兼容性**: 绝对不能修改现有 `/api/chat` 端点的行为
- **数据库集成**: 只能添加新表，不能修改现有表结构
- **错误处理**: 使用现有的全局异常处理器和错误响应格式
- **日志一致性**: 使用现有的 SLF4J 日志框架和日志格式

### Agent 开发规范

```java
// Agent 服务类示例
@Service
@Slf4j
@Validated
public class AgentOrchestrationService {

    private final ChatService chatService; // 复用现有服务
    private final AgentDefinitionService agentDefinitionService;
    private final RedisTemplate<String, Object> redisTemplate; // 复用现有缓存

    @Autowired
    public AgentOrchestrationService(
            ChatService chatService,
            AgentDefinitionService agentDefinitionService,
            RedisTemplate<String, Object> redisTemplate) {
        this.chatService = chatService;
        this.agentDefinitionService = agentDefinitionService;
        this.redisTemplate = redisTemplate;
    }

    @Async
    @Transactional
    public Mono<AgentExecutionResult> executeAgentFlow(
            @NotNull String conversationId,
            @Valid AgentExecutionRequest request) {

        log.info("开始执行 Agent 流程: conversationId={}, flowId={}",
                 conversationId, request.getFlowId());

        // 具体实现...
    }
}
```
