# 测试策略

## 与现有测试集成

**现有测试框架**: JUnit 5 + Mockito + Spring Boot Test  
**测试组织**: 按照现有的 `src/test/java` 结构组织  
**覆盖率要求**: 维持现有的 80% 代码覆盖率标准

## 新测试要求

### 单元测试 (新组件)

**框架**: JUnit 5 + Mockito  
**位置**: `src/test/java/com/dipspro/modules/agent/`  
**覆盖率目标**: 85% (高于现有标准，因为是新功能)  
**与现有集成**: 复用现有测试配置和工具类

```java
// 测试示例
@ExtendWith(MockitoExtension.class)
class AgentOrchestrationServiceTest {

    @Mock
    private ChatService chatService; // Mock 现有服务

    @Mock
    private AgentDefinitionService agentDefinitionService;

    @InjectMocks
    private AgentOrchestrationService orchestrationService;

    @Test
    void shouldExecuteAgentFlowSuccessfully() {
        // 测试 Agent 流程执行
    }

    @Test
    void shouldHandleAgentExecutionFailure() {
        // 测试错误处理
    }
}
```

### 集成测试

**范围**: 验证 Agent 功能与现有系统的集成点  
**现有系统验证**: 确保现有聊天功能完全不受影响  
**新功能测试**: 端到端测试 Agent 执行流程

```java
@SpringBootTest
@TestPropertySource(properties = {
    "app.ai.agent.enabled=true",
    "app.ai.agent.mock-mode=true"
})
class AgentChatIntegrationTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @Test
    void shouldCreateAgentConversationWithoutAffectingExistingChat() {
        // 测试 Agent 聊天创建不影响现有聊天
    }

    @Test
    void shouldMaintainExistingChatFunctionality() {
        // 确保现有聊天功能完全正常
    }
}
```

### 回归测试

**现有功能验证**: 自动化测试确保现有聊天功能不受影响  
**自动化回归套件**: 扩展现有的回归测试，包含 Agent 相关测试  
**手动测试要求**: 新功能发布前必须手动验证现有功能

**关键回归测试点**:

- 现有 `/api/chat` 端点功能不变
- 现有聊天历史和消息正常显示
- 用户认证和权限机制正常工作
- 现有数据库查询性能不受影响