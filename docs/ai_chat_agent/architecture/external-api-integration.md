# 外部 API 集成

## DeepSeek API 增强集成

- **Purpose**: 扩展现有 DeepSeek API 集成以支持多 Agent 并发调用
- **Documentation**: https://api.deepseek.com/docs
- **Base URL**: https://api.deepseek.com (复用现有配置)
- **Authentication**: API Key (复用现有配置)
- **Integration Method**: 扩展现有 Spring AI ChatClient 配置

**关键端点使用**:

- `POST /chat/completions` - Agent 思维生成和推理
- `POST /embeddings` - 意图识别和语义匹配

**错误处理**: 复用现有异常处理机制，增加 Agent 特定的重试逻辑

**增强配置**:

```yaml
spring:
  ai:
    openai:
      api-key: ${DEEPSEEK_API_KEY}
      base-url: ${DEEPSEEK_BASE_URL:https://api.deepseek.com}
      chat:
        options:
          model: ${DEEPSEEK_MODEL:deepseek-chat}
          temperature: ${DEEPSEEK_TEMPERATURE:0.2}
          max-tokens: ${DEEPSEEK_MAX_TOKENS:4000}

app:
  ai:
    agent:
      max-concurrent-calls: 5
      timeout: 60
      retry-attempts: 3
      rate-limit:
        requests-per-minute: 100
```
