# 数据模型和架构变更

## 新数据模型

### Agent 定义模型 (AgentDefinition)

**目的**: 定义单个 Agent 的能力、配置和元数据  
**与现有集成**: 独立新表，通过用户 ID 关联到现有用户系统

**关键属性**:

- `id`: BIGINT - 主键
- `name`: VARCHAR(100) - Agent 名称
- `description`: TEXT - Agent 描述
- `capabilities`: JSONB - Agent 能力定义
- `systemPrompt`: TEXT - 系统提示词
- `configuration`: JSONB - Agent 配置参数
- `status`: VARCHAR(20) - 状态 (ACTIVE, INACTIVE, DEPRECATED)
- `createdBy`: BIGINT - 创建者用户 ID (外键到 users 表)
- `createdAt`: TIMESTAMP - 创建时间
- `updatedAt`: TIMESTAMP - 更新时间

**关系**:

- **与现有**: `createdBy` 外键关联到现有 `users` 表
- **与新**: 一对多关联到 `AgentFlowStep`

### Agent 流程模型 (AgentFlow)

**目的**: 定义多个 Agent 的协作流程和执行顺序  
**与现有集成**: 独立管理，通过对话 ID 关联到现有聊天系统

**关键属性**:

- `id`: BIGINT - 主键
- `name`: VARCHAR(100) - 流程名称
- `description`: TEXT - 流程描述
- `intentPattern`: VARCHAR(500) - 意图识别模式
- `steps`: JSONB - 流程步骤定义
- `status`: VARCHAR(20) - 状态
- `priority`: INTEGER - 优先级
- `createdBy`: BIGINT - 创建者 ID
- `createdAt`: TIMESTAMP - 创建时间

**关系**:

- **与现有**: `createdBy` 外键关联到现有 `users` 表
- **与新**: 一对多关联到 `AgentExecution`

### Agent 执行模型 (AgentExecution)

**目的**: 记录 Agent 流程的具体执行实例和状态  
**与现有集成**: 通过 conversationId 关联到现有对话系统

**关键属性**:

- `id`: BIGINT - 主键
- `conversationId`: VARCHAR(255) - 关联对话 ID (外键到现有 conversations)
- `flowId`: BIGINT - 流程 ID
- `status`: VARCHAR(20) - 执行状态 (PENDING, RUNNING, COMPLETED, FAILED)
- `currentStep`: INTEGER - 当前步骤
- `executionData`: JSONB - 执行数据和中间结果
- `startedAt`: TIMESTAMP - 开始时间
- `completedAt`: TIMESTAMP - 完成时间
- `errorMessage`: TEXT - 错误信息

**关系**:

- **与现有**: `conversationId` 关联到现有 `conversations` 表
- **与新**: `flowId` 外键关联到 `AgentFlow`

## 架构集成策略

### 数据库变更

**新表列表**:

- `agent_definitions` - Agent 定义表
- `agent_flows` - Agent 流程表
- `agent_executions` - Agent 执行记录表
- `agent_flow_steps` - 流程步骤详情表

**修改表**: 无 - 完全增量添加

**新索引**:

- `idx_agent_executions_conversation` - 按对话 ID 查询执行记录
- `idx_agent_flows_intent` - 按意图模式查询流程
- `idx_agent_definitions_status` - 按状态查询可用 Agent

**迁移策略**:

- 使用 Flyway 进行版本控制迁移
- 先添加表结构，再添加索引
- 提供回滚脚本确保安全

### 向后兼容性

**兼容性措施**:

- 现有 `chat_messages` 表完全不变
- 利用现有 `intermediateStepsJson` 字段存储 Agent 思维链
- 现有对话和消息数据完全保持不变
- 新旧系统可以同时操作相同的对话数据
