# 技术栈对齐

## 现有技术栈维护

| 分类          | 当前技术        | 版本    | 在增强中的使用 | 备注                               |
| ------------- | --------------- | ------- | -------------- | ---------------------------------- |
| **后端框架**  | Spring Boot     | 3.3.2   | 完全复用       | 新增 Agent 相关 Auto Configuration |
| **Java 版本** | Java            | 23      | 完全复用       | 利用最新语言特性                   |
| **数据库**    | PostgreSQL      | Latest  | 完全复用       | 新增 Agent 相关表                  |
| **缓存**      | Redis           | Latest  | 完全复用       | Agent 执行状态缓存                 |
| **安全框架**  | Spring Security | 3.3.2   | 完全复用       | 扩展权限定义                       |
| **认证方式**  | JWT             | Current | 完全复用       | Agent API 使用相同认证             |
| **ORM 框架**  | Spring Data JPA | 3.3.2   | 完全复用       | 新增 Agent 实体                    |
| **前端框架**  | Vue             | 3.5+    | 完全复用       | 新增 Agent 专用组件                |
| **构建工具**  | Vite            | 6.x     | 完全复用       | 前端构建流程不变                   |
| **状态管理**  | Pinia           | 3.x     | 完全复用       | 新增 Agent 状态管理                |
| **AI 集成**   | Spring AI       | Current | 扩展使用       | 支持多 Agent 调用模式              |

## 新技术添加

| 技术                   | 版本              | 目的                   | 理由                              | 集成方法                    |
| ---------------------- | ----------------- | ---------------------- | --------------------------------- | --------------------------- |
| **Server-Sent Events** | HTTP/1.1 Standard | Agent 执行流程实时推送 | 现有 WebSocket 不适合单向推送场景 | Spring WebFlux 原生支持     |
| **Reactor Core**       | 3.6.x             | 响应式流处理           | Agent 执行的异步流式处理          | Spring Boot Starter WebFlux |
| **Jackson Streaming**  | 2.17.x            | 大数据量 JSON 流式处理 | Agent 输出数据可能很大            | 现有 Jackson 扩展           |
