# Story #002: Agent 执行引擎和编排服务

## 故事信息

- **故事 ID**: AGENT-002
- **故事标题**: 实现 Agent 执行引擎和编排服务核心逻辑
- **优先级**: P0 (最高)
- **估算**: 8 Story Points (3-4 工作日)
- **创建时间**: 2025-08-03
- **分配给**: Backend Developer
- **Epic**: AI Agent 对话系统 MVP
- **依赖**: AGENT-001 (Agent 数据库基础)

## 故事描述

### 用户故事

作为一个**系统架构师**，我希望**实现 Agent 执行引擎和编排服务**，以便**支持多 Agent 协作和意图识别功能，为 AI Agent 对话提供核心业务逻辑**。

### 背景和目标

基于 AGENT-001 建立的数据库基础，现在需要实现核心的 Agent 执行逻辑。这个故事重点实现：

1. Agent 执行引擎 - 单个 Agent 的执行逻辑
2. Agent 编排服务 - 多 Agent 协作流程
3. 意图识别服务 - 智能匹配合适的 Agent 流程

**业务价值**:

- 建立 AI Agent 系统的核心执行能力
- 实现智能意图识别和流程匹配
- 为流式对话奠定业务逻辑基础

## 验收标准

### 主要验收标准

#### ✅ AC1: Agent 执行引擎服务

- [ ] 实现 `AgentExecutionEngine` 核心类
- [ ] 支持单个 Agent 的独立执行
- [ ] 集成现有 Spring AI ChatClient
- [ ] 支持 Agent 配置的动态加载
- [ ] 实现执行状态跟踪和持久化
- [ ] 支持执行过程的中断和恢复

#### ✅ AC2: Agent 编排服务

- [ ] 实现 `AgentOrchestrationService` 服务
- [ ] 支持多 Agent 按流程协作
- [ ] 实现 Agent 间数据传递机制
- [ ] 支持条件分支和并行执行
- [ ] 集成现有 conversation 系统
- [ ] 实现执行历史记录和审计

#### ✅ AC3: 意图识别服务

- [ ] 实现 `IntentRecognitionService` 服务
- [ ] 基于用户输入匹配合适的 Agent 流程
- [ ] 支持模式匹配和语义理解
- [ ] 集成现有 Spring AI 能力
- [ ] 实现流程优先级和置信度评估
- [ ] 提供 fallback 机制

#### ✅ AC4: 流程管理服务

- [ ] 实现 `AgentFlowService` 服务
- [ ] 支持流程的动态配置和管理
- [ ] 实现流程模板和实例化
- [ ] 支持流程的启用/禁用
- [ ] 提供流程执行统计和监控

#### ✅ AC5: 与现有系统集成

- [ ] 与现有 `ChatService` 无缝协作
- [ ] 复用现有的用户认证和权限系统
- [ ] 利用现有 `ChatMessage.intermediateStepsJson` 存储思维链
- [ ] 确保现有聊天功能完全不受影响

### 技术验收标准

#### 📋 T1: 反应式编程支持

- [ ] 使用 Spring WebFlux 实现异步处理
- [ ] 支持流式执行和实时状态更新
- [ ] 实现背压控制和错误处理
- [ ] 优化资源使用和并发性能

#### 📋 T2: 错误处理和恢复

- [ ] 完整的异常处理和错误分类
- [ ] 支持执行失败的自动重试
- [ ] 实现断点续传和状态恢复
- [ ] 提供详细的错误日志和诊断信息

#### 📋 T3: 性能和监控

- [ ] 执行性能监控和指标收集
- [ ] 内存使用和资源管理优化
- [ ] 支持执行超时和资源限制
- [ ] 提供性能诊断和调优工具

## 技术实现指南

### 核心服务架构

**AgentExecutionEngine.java**:

```java
@Service
@Slf4j
@AllArgsConstructor
public class AgentExecutionEngine {

    private final ChatClient chatClient;
    private final AgentDefinitionService agentDefinitionService;
    private final AgentExecutionRepository executionRepository;
    private final ChatService chatService;

    /**
     * 执行单个 Agent
     */
    public Mono<AgentExecutionResult> executeAgent(AgentExecutionRequest request) {
        return Mono.fromCallable(() -> validateRequest(request))
            .flatMap(this::loadAgentDefinition)
            .flatMap(this::prepareExecution)
            .flatMap(this::executeWithAI)
            .flatMap(this::saveExecutionResult)
            .doOnNext(this::logExecutionMetrics)
            .onErrorResume(this::handleExecutionError);
    }

    /**
     * 流式执行 Agent (支持 SSE)
     */
    public Flux<AgentExecutionEvent> executeAgentStream(AgentExecutionRequest request) {
        return Flux.defer(() -> {
            return Flux.concat(
                Flux.just(AgentExecutionEvent.started(request)),
                executeAgentInternal(request)
                    .flatMapMany(this::streamExecutionSteps),
                Flux.just(AgentExecutionEvent.completed())
            );
        })
        .onErrorResume(error ->
            Flux.just(AgentExecutionEvent.failed(error.getMessage()))
        );
    }

    private Mono<AgentDefinition> loadAgentDefinition(AgentExecutionRequest request) {
        return Mono.fromCallable(() ->
            agentDefinitionService.findById(request.getAgentId())
                .orElseThrow(() -> new AgentNotFoundException(request.getAgentId()))
        );
    }

    private Mono<ChatResponse> executeWithAI(AgentExecutionContext context) {
        return Mono.fromCallable(() -> {
            String prompt = buildPrompt(context);
            return chatClient.prompt()
                .system(context.getAgent().getSystemPrompt())
                .user(prompt)
                .call()
                .content();
        })
        .map(content -> ChatResponse.builder()
            .content(content)
            .agentId(context.getAgent().getId())
            .executionId(context.getExecutionId())
            .build());
    }
}
```

**AgentOrchestrationService.java**:

```java
@Service
@Slf4j
@AllArgsConstructor
public class AgentOrchestrationService {

    private final AgentExecutionEngine executionEngine;
    private final AgentFlowService flowService;
    private final IntentRecognitionService intentService;
    private final ChatService chatService;

    /**
     * 编排多 Agent 执行流程
     */
    public Mono<OrchestrationResult> orchestrate(OrchestrationRequest request) {
        return intentService.recognizeIntent(request.getUserMessage())
            .flatMap(intent -> flowService.findFlowByIntent(intent))
            .flatMap(flow -> executeFlow(flow, request))
            .flatMap(this::aggregateResults)
            .doOnNext(this::updateConversation);
    }

    /**
     * 流式编排执行 (支持 SSE)
     */
    public Flux<OrchestrationEvent> orchestrateStream(OrchestrationRequest request) {
        return Flux.defer(() -> {
            return intentService.recognizeIntent(request.getUserMessage())
                .flatMapMany(intent ->
                    flowService.findFlowByIntent(intent)
                        .flatMapMany(flow -> executeFlowStream(flow, request))
                );
        })
        .doOnNext(event -> log.info("Orchestration event: {}", event.getType()))
        .onErrorResume(this::handleOrchestrationError);
    }

    private Flux<OrchestrationEvent> executeFlowStream(AgentFlow flow, OrchestrationRequest request) {
        return Flux.fromIterable(flow.getSteps())
            .concatMap(step -> executeStepStream(step, request))
            .doOnNext(event -> updateExecutionProgress(flow.getId(), event));
    }

    private Flux<OrchestrationEvent> executeStepStream(AgentFlowStep step, OrchestrationRequest request) {
        AgentExecutionRequest execRequest = buildExecutionRequest(step, request);

        return executionEngine.executeAgentStream(execRequest)
            .map(executionEvent -> OrchestrationEvent.fromAgentEvent(step, executionEvent))
            .doOnNext(event -> log.debug("Step {} event: {}", step.getStepId(), event));
    }
}
```

**IntentRecognitionService.java**:

```java
@Service
@Slf4j
@AllArgsConstructor
public class IntentRecognitionService {

    private final ChatClient chatClient;
    private final AgentFlowRepository flowRepository;
    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * 识别用户意图并匹配合适的 Agent 流程
     */
    public Mono<Intent> recognizeIntent(String userMessage) {
        return Mono.fromCallable(() -> getCachedIntent(userMessage))
            .switchIfEmpty(analyzeIntentWithAI(userMessage))
            .doOnNext(intent -> cacheIntent(userMessage, intent));
    }

    private Mono<Intent> analyzeIntentWithAI(String userMessage) {
        return Mono.fromCallable(() -> {
            String systemPrompt = buildIntentRecognitionPrompt();

            String response = chatClient.prompt()
                .system(systemPrompt)
                .user("分析以下用户消息的意图：" + userMessage)
                .call()
                .content();

            return parseIntentFromResponse(response);
        })
        .doOnNext(intent -> log.info("Intent recognized: {} for message: {}",
            intent.getCategory(), userMessage));
    }

    private String buildIntentRecognitionPrompt() {
        List<AgentFlow> availableFlows = flowRepository.findByStatus(FlowStatus.ACTIVE);

        StringBuilder prompt = new StringBuilder();
        prompt.append("你是一个意图识别专家。根据用户消息，判断最合适的处理流程。\n\n");
        prompt.append("可用的流程类型：\n");

        for (AgentFlow flow : availableFlows) {
            prompt.append("- ").append(flow.getName())
                  .append(": ").append(flow.getDescription())
                  .append(" (模式: ").append(flow.getIntentPattern()).append(")\n");
        }

        prompt.append("\n请返回JSON格式: {\"category\": \"类别\", \"confidence\": 0.95, \"flowId\": 123}");

        return prompt.toString();
    }

    /**
     * 获取缓存的意图识别结果
     */
    private Intent getCachedIntent(String userMessage) {
        String cacheKey = "intent:" + DigestUtils.md5Hex(userMessage);
        return (Intent) redisTemplate.opsForValue().get(cacheKey);
    }

    /**
     * 缓存意图识别结果
     */
    private void cacheIntent(String userMessage, Intent intent) {
        String cacheKey = "intent:" + DigestUtils.md5Hex(userMessage);
        redisTemplate.opsForValue().set(cacheKey, intent, Duration.ofHours(1));
    }
}
```

### 数据传输对象

**执行请求对象**:

```java
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentExecutionRequest {
    private UUID agentId;
    private String conversationId;
    private UUID messageId;
    private String stepId;
    private UUID executionId;
    private Map<String, Object> inputData;
    private ExecutionOptions options;
}

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExecutionOptions {
    private Duration timeout;
    private Integer maxRetries;
    private Boolean enableCache;
    private Map<String, String> parameters;
}
```

**执行事件对象**:

```java
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentExecutionEvent {
    private String eventType;
    private UUID executionId;
    private String agentName;
    private String stepId;
    private Map<String, Object> data;
    private LocalDateTime timestamp;
    private String status;

    public static AgentExecutionEvent started(AgentExecutionRequest request) {
        return AgentExecutionEvent.builder()
            .eventType("EXECUTION_STARTED")
            .executionId(request.getExecutionId())
            .timestamp(LocalDateTime.now())
            .status("RUNNING")
            .build();
    }

    public static AgentExecutionEvent completed() {
        return AgentExecutionEvent.builder()
            .eventType("EXECUTION_COMPLETED")
            .timestamp(LocalDateTime.now())
            .status("COMPLETED")
            .build();
    }

    public static AgentExecutionEvent failed(String errorMessage) {
        return AgentExecutionEvent.builder()
            .eventType("EXECUTION_FAILED")
            .timestamp(LocalDateTime.now())
            .status("FAILED")
            .data(Map.of("error", errorMessage))
            .build();
    }
}
```

## 测试策略

### 单元测试

**AgentExecutionEngineTest.java**:

```java
@ExtendWith(MockitoExtension.class)
class AgentExecutionEngineTest {

    @Mock
    private ChatClient chatClient;

    @Mock
    private AgentDefinitionService agentDefinitionService;

    @Mock
    private AgentExecutionRepository executionRepository;

    @InjectMocks
    private AgentExecutionEngine executionEngine;

    @Test
    void should_execute_agent_successfully() {
        // Given
        AgentExecutionRequest request = buildTestRequest();
        AgentDefinition agent = buildTestAgent();
        when(agentDefinitionService.findById(any())).thenReturn(Optional.of(agent));
        when(chatClient.prompt()).thenReturn(mockPromptBuilder());

        // When
        Mono<AgentExecutionResult> result = executionEngine.executeAgent(request);

        // Then
        StepVerifier.create(result)
            .assertNext(res -> {
                assertThat(res.getStatus()).isEqualTo(ExecutionStatus.COMPLETED);
                assertThat(res.getAgentId()).isEqualTo(request.getAgentId());
            })
            .verifyComplete();
    }

    @Test
    void should_handle_execution_failure() {
        // Given
        AgentExecutionRequest request = buildTestRequest();
        when(agentDefinitionService.findById(any()))
            .thenThrow(new AgentNotFoundException("Agent not found"));

        // When
        Mono<AgentExecutionResult> result = executionEngine.executeAgent(request);

        // Then
        StepVerifier.create(result)
            .expectError(AgentNotFoundException.class)
            .verify();
    }
}
```

### 集成测试

**AgentOrchestrationIntegrationTest.java**:

```java
@SpringBootTest
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.ai.openai.api-key=test-key"
})
@DirtiesContext
class AgentOrchestrationIntegrationTest {

    @Autowired
    private AgentOrchestrationService orchestrationService;

    @Autowired
    private AgentFlowRepository flowRepository;

    @MockBean
    private ChatClient chatClient;

    @Test
    void should_orchestrate_multi_agent_flow() {
        // Given
        AgentFlow flow = createTestFlow();
        flowRepository.save(flow);

        OrchestrationRequest request = OrchestrationRequest.builder()
            .userMessage("我需要分析销售数据")
            .conversationId("test-conv-001")
            .userId(1L)
            .build();

        // When
        Mono<OrchestrationResult> result = orchestrationService.orchestrate(request);

        // Then
        StepVerifier.create(result)
            .assertNext(res -> {
                assertThat(res.getStatus()).isEqualTo(OrchestrationStatus.COMPLETED);
                assertThat(res.getExecutedSteps()).hasSize(3);
            })
            .verifyComplete();
    }
}
```

## 定义完成 (Definition of Done)

### 功能完成

- [ ] 所有验收标准通过测试
- [ ] Agent 执行引擎稳定运行
- [ ] 多 Agent 编排流程正常
- [ ] 意图识别准确率 > 85%
- [ ] 代码审查完成并批准

### 质量保证

- [ ] 单元测试覆盖率 ≥ 90%
- [ ] 集成测试覆盖主要业务场景
- [ ] 性能测试满足响应时间要求 (< 2s)
- [ ] 内存使用在合理范围内
- [ ] 并发测试通过

### 反应式编程验证

- [ ] 流式执行响应及时 (< 100ms 首次响应)
- [ ] 背压控制正常工作
- [ ] 资源泄漏检查通过
- [ ] 异步异常处理正确

### 系统集成验证

- [ ] 与现有 ChatService 协作无问题
- [ ] 现有聊天功能完全正常
- [ ] 用户权限系统集成正确
- [ ] 数据库性能无影响

## 风险和依赖

### 风险识别

- **高风险**: 反应式编程复杂性可能导致难以调试的问题
- **中风险**: AI 服务调用的稳定性和性能
- **中风险**: 多 Agent 协作的状态管理复杂性
- **低风险**: 意图识别准确率不达预期

### 缓解措施

- 充分的单元测试和集成测试
- 详细的日志记录和监控
- 实现断路器模式处理 AI 服务故障
- 建立完整的错误处理和恢复机制
- 使用特性开关控制功能启用

### 依赖项

- **AGENT-001**: Agent 数据库基础必须完成
- 现有 Spring AI 配置稳定可用
- ChatService 接口稳定
- Redis 缓存服务正常

## 后续故事

完成此故事后，下一个故事将是：

- **Story #003**: Agent Chat API 端点和流式推送
- **Story #004**: 前端 Agent 对话界面集成

---

**故事状态**: Ready for Development  
**最后更新**: 2025-08-03  
**负责人**: Backend Developer  
**预计完成**: AGENT-001 完成后 3-4 工作日
