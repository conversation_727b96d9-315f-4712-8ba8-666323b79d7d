# Story #004: 前端 Agent 对话界面和组件

## 故事信息

- **故事 ID**: AGENT-004
- **故事标题**: 实现前端 Agent 对话界面和专用组件
- **优先级**: P0 (最高)
- **估算**: 7 Story Points (3-4 工作日)
- **创建时间**: 2025-08-03
- **分配给**: Frontend Developer
- **Epic**: AI Agent 对话系统 MVP
- **依赖**: AGENT-003 (Agent Chat API 端点和流式推送)

## 故事描述

### 用户故事

作为一个**最终用户**，我希望**有专用的 AI Agent 对话界面**，以便**能够清楚地看到 AI Agent 的协作过程，理解每个步骤的执行情况，获得透明的 AI 协作体验**。

### 背景和目标

基于后端提供的 Agent Chat API 和 SSE 流式推送能力，现在需要实现用户交互的前端界面。这个故事重点实现：

1. 独立的 Agent 对话界面 (与现有聊天界面分离)
2. 实时显示 Agent 执行过程和思维链
3. 专用的 Agent 状态和进度组件
4. 与现有前端架构的无缝集成

**业务价值**:

- 为用户提供透明的 AI Agent 协作体验
- 通过实时反馈提升用户体验
- 建立可复用的 Agent UI 组件库

## 验收标准

### 主要验收标准

#### ✅ AC1: Agent 对话界面

- [ ] 创建独立的 Agent 对话页面 (`/agent-chat`)
- [ ] 实现对话列表和会话管理
- [ ] 支持创建新的 Agent 对话
- [ ] 集成现有的用户认证和权限系统
- [ ] 遵循现有的 UI 设计规范和主题

#### ✅ AC2: Agent 状态显示组件

- [ ] 创建 `AgentStatusCard` 组件
- [ ] 实时显示当前执行的 Agent 信息
- [ ] 显示执行进度和状态指示器
- [ ] 支持多 Agent 并行状态显示
- [ ] 提供执行历史和审计日志

#### ✅ AC3: 流程概览组件

- [ ] 创建 `FlowOverview` 组件
- [ ] 可视化显示 Agent 执行流程
- [ ] 高亮当前执行步骤
- [ ] 显示各步骤的执行时间和结果
- [ ] 支持流程的展开和折叠

#### ✅ AC4: 智能输入组件

- [ ] 创建 `SmartInput` 组件
- [ ] 支持意图提示和自动补全
- [ ] 集成语音输入功能
- [ ] 提供常用指令的快捷按钮
- [ ] 支持附件和文件上传

#### ✅ AC5: SSE 集成和实时更新

- [ ] 实现 SSE 客户端连接管理
- [ ] 实时接收 Agent 执行事件
- [ ] 动态更新界面状态和进度
- [ ] 支持断线重连和状态恢复
- [ ] 优化性能和内存使用

### 技术验收标准

#### 📋 T1: 组件设计

- [ ] 遵循 Vue 3 Composition API 规范
- [ ] 使用 TypeScript 类型安全
- [ ] 组件可复用和可测试
- [ ] 支持主题切换和响应式设计

#### 📋 T2: 状态管理

- [ ] 使用 Pinia 管理 Agent 对话状态
- [ ] 实现状态持久化和恢复
- [ ] 优化状态更新性能
- [ ] 支持离线状态管理

#### 📋 T3: 用户体验

- [ ] 加载状态和骨架屏
- [ ] 错误处理和友好提示
- [ ] 无障碍访问支持
- [ ] 移动端适配

## 技术实现指南

### 页面组件架构

**AgentChatPage.vue**:

```vue
<template>
  <div class="agent-chat-page">
    <!-- 头部导航 -->
    <div class="agent-chat-header">
      <h1 class="page-title">AI Agent 对话</h1>
      <div class="header-actions">
        <button @click="createNewConversation" class="btn-primary">
          新建对话
        </button>
        <button @click="showSettings" class="btn-secondary">
          设置
        </button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="agent-chat-content">
      <!-- 对话列表侧边栏 -->
      <div class="conversation-sidebar">
        <ConversationList
          :conversations="conversations"
          :current-conversation="currentConversation"
          @select="selectConversation"
          @delete="deleteConversation"
        />
      </div>

      <!-- 对话主区域 -->
      <div class="conversation-main">
        <div v-if="currentConversation" class="conversation-container">
          <!-- 流程概览 -->
          <FlowOverview
            v-if="currentFlow"
            :flow="currentFlow"
            :current-step="currentStep"
            :execution-history="executionHistory"
            class="flow-overview"
          />

          <!-- 消息列表 -->
          <div class="message-list" ref="messageList">
            <MessageItem
              v-for="message in messages"
              :key="message.id"
              :message="message"
              :is-agent-message="message.type === 'agent'"
            />

            <!-- Agent 状态卡片 -->
            <AgentStatusCard
              v-if="activeExecution"
              :execution="activeExecution"
              :agents="activeAgents"
              class="agent-status"
            />
          </div>

          <!-- 输入区域 -->
          <SmartInput
            v-model="inputMessage"
            :disabled="isProcessing"
            :suggestions="inputSuggestions"
            @send="sendMessage"
            @voice-input="handleVoiceInput"
            class="message-input"
          />
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-state">
          <div class="empty-content">
            <h3>开始 AI Agent 对话</h3>
            <p>选择一个对话或创建新的对话来体验 AI Agent 协作</p>
            <button @click="createNewConversation" class="btn-primary">
              创建新对话
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useAgentChatStore } from '@/store/modules/agentChat'
import { useUserStore } from '@/store/modules/user'
import { AgentChatService } from '@/api/agentChat'
import { SseService } from '@/services/sseService'
import ConversationList from '@/components/agent/ConversationList.vue'
import FlowOverview from '@/components/agent/FlowOverview.vue'
import MessageItem from '@/components/agent/MessageItem.vue'
import AgentStatusCard from '@/components/agent/AgentStatusCard.vue'
import SmartInput from '@/components/agent/SmartInput.vue'

// Store
const agentChatStore = useAgentChatStore()
const userStore = useUserStore()

// 响应式数据
const inputMessage = ref('')
const messageList = ref<HTMLElement>()
const sseService = ref<SseService>()

// 计算属性
const conversations = computed(() => agentChatStore.conversations)
const currentConversation = computed(() => agentChatStore.currentConversation)
const messages = computed(() => agentChatStore.messages)
const currentFlow = computed(() => agentChatStore.currentFlow)
const currentStep = computed(() => agentChatStore.currentStep)
const executionHistory = computed(() => agentChatStore.executionHistory)
const activeExecution = computed(() => agentChatStore.activeExecution)
const activeAgents = computed(() => agentChatStore.activeAgents)
const isProcessing = computed(() => agentChatStore.isProcessing)
const inputSuggestions = computed(() => agentChatStore.inputSuggestions)

// 生命周期
onMounted(async () => {
  await loadConversations()
  setupSSE()
})

onUnmounted(() => {
  cleanupSSE()
})

// 监听当前对话变化
watch(currentConversation, async (newConv, oldConv) => {
  if (newConv && newConv.id !== oldConv?.id) {
    await loadMessages(newConv.id)
    setupSSEForConversation(newConv.id)
  }
}, { immediate: true })

// 方法
async function loadConversations() {
  try {
    await agentChatStore.loadConversations()
  } catch (error) {
    console.error('Failed to load conversations:', error)
    // 显示错误提示
  }
}

async function createNewConversation() {
  try {
    const title = `AI Agent 对话 ${new Date().toLocaleString()}`
    await agentChatStore.createConversation(title)
  } catch (error) {
    console.error('Failed to create conversation:', error)
  }
}

async function selectConversation(conversation: Conversation) {
  agentChatStore.setCurrentConversation(conversation)
}

async function deleteConversation(conversationId: string) {
  try {
    await agentChatStore.deleteConversation(conversationId)
  } catch (error) {
    console.error('Failed to delete conversation:', error)
  }
}

async function loadMessages(conversationId: string) {
  try {
    await agentChatStore.loadMessages(conversationId)
    await nextTick()
    scrollToBottom()
  } catch (error) {
    console.error('Failed to load messages:', error)
  }
}

async function sendMessage() {
  if (!inputMessage.value.trim() || !currentConversation.value) return

  try {
    const message = inputMessage.value.trim()
    inputMessage.value = ''

    await agentChatStore.sendMessage(currentConversation.value.id, message)

    await nextTick()
    scrollToBottom()
  } catch (error) {
    console.error('Failed to send message:', error)
  }
}

function handleVoiceInput(transcription: string) {
  inputMessage.value = transcription
}

function setupSSE() {
  sseService.value = new SseService()
}

function setupSSEForConversation(conversationId: string) {
  if (!sseService.value) return

  sseService.value.connect(conversationId, {
    onEvent: handleSSEEvent,
    onError: handleSSEError,
    onReconnect: handleSSEReconnect
  })
}

function cleanupSSE() {
  if (sseService.value) {
    sseService.value.disconnect()
  }
}

function handleSSEEvent(event: SseEvent) {
  agentChatStore.handleSseEvent(event)

  // 自动滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
}

function handleSSEError(error: any) {
  console.error('SSE error:', error)
  // 显示连接错误提示
}

function handleSSEReconnect() {
  console.log('SSE reconnected')
  // 显示重连成功提示
}

function scrollToBottom() {
  if (messageList.value) {
    messageList.value.scrollTop = messageList.value.scrollHeight
  }
}

function showSettings() {
  // 显示设置对话框
}
</script>

<style scoped>
.agent-chat-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--bg-color);
}

.agent-chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--surface-color);
}

.page-title {
  margin: 0;
  font-size: 1.5rem;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.agent-chat-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.conversation-sidebar {
  width: 300px;
  border-right: 1px solid var(--border-color);
  background: var(--surface-color);
}

.conversation-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.conversation-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.flow-overview {
  flex-shrink: 0;
  border-bottom: 1px solid var(--border-color);
}

.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.message-input {
  flex-shrink: 0;
  border-top: 1px solid var(--border-color);
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.empty-content {
  text-align: center;
  color: var(--text-secondary);
}

.empty-content h3 {
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.empty-content p {
  margin-bottom: 1.5rem;
}
</style>
```

### 核心组件

**AgentStatusCard.vue**:

```vue
<template>
  <div class="agent-status-card">
    <div class="card-header">
      <h4 class="card-title">AI Agent 协作状态</h4>
      <div class="status-indicator" :class="statusClass">
        {{ statusText }}
      </div>
    </div>

    <div class="card-content">
      <!-- 当前执行的 Agent -->
      <div v-if="currentAgent" class="current-agent">
        <div class="agent-info">
          <div class="agent-avatar">
            <i :class="currentAgent.icon || 'icon-robot'"></i>
          </div>
          <div class="agent-details">
            <h5 class="agent-name">{{ currentAgent.name }}</h5>
            <p class="agent-description">{{ currentAgent.description }}</p>
          </div>
        </div>

        <!-- 进度条 -->
        <div class="progress-container">
          <div class="progress-bar">
            <div
              class="progress-fill"
              :style="{ width: `${progress}%` }"
            ></div>
          </div>
          <span class="progress-text">{{ Math.round(progress) }}%</span>
        </div>

        <!-- 思维链 -->
        <div v-if="chainOfThought.length > 0" class="chain-of-thought">
          <h6 class="chain-title">思维过程</h6>
          <div class="thought-list">
            <div
              v-for="thought in chainOfThought"
              :key="thought.id"
              class="thought-item"
              :class="{ active: thought.isActive }"
            >
              <div class="thought-step">{{ thought.step }}</div>
              <div class="thought-content">{{ thought.content }}</div>
              <div v-if="thought.duration" class="thought-duration">
                {{ formatDuration(thought.duration) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 并行执行的 Agents -->
      <div v-if="parallelAgents.length > 0" class="parallel-agents">
        <h6 class="parallel-title">并行执行</h6>
        <div class="agents-grid">
          <div
            v-for="agent in parallelAgents"
            :key="agent.id"
            class="mini-agent-card"
          >
            <div class="mini-agent-info">
              <i :class="agent.icon || 'icon-robot'"></i>
              <span class="mini-agent-name">{{ agent.name }}</span>
            </div>
            <div class="mini-progress">
              <div
                class="mini-progress-fill"
                :style="{ width: `${agent.progress}%` }"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 执行统计 -->
      <div class="execution-stats">
        <div class="stat-item">
          <span class="stat-label">已执行步骤</span>
          <span class="stat-value">{{ completedSteps }}/{{ totalSteps }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">执行时间</span>
          <span class="stat-value">{{ formatDuration(executionDuration) }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">预计剩余</span>
          <span class="stat-value">{{ formatDuration(estimatedRemaining) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { AgentExecution, Agent } from '@/types/agent'

interface Props {
  execution: AgentExecution
  agents: Agent[]
}

const props = defineProps<Props>()

// 计算属性
const currentAgent = computed(() => {
  return props.agents.find(agent => agent.id === props.execution.currentAgentId)
})

const statusClass = computed(() => {
  switch (props.execution.status) {
    case 'RUNNING': return 'status-running'
    case 'COMPLETED': return 'status-completed'
    case 'FAILED': return 'status-failed'
    case 'PAUSED': return 'status-paused'
    default: return 'status-pending'
  }
})

const statusText = computed(() => {
  switch (props.execution.status) {
    case 'RUNNING': return '执行中'
    case 'COMPLETED': return '已完成'
    case 'FAILED': return '执行失败'
    case 'PAUSED': return '已暂停'
    default: return '等待中'
  }
})

const progress = computed(() => {
  return props.execution.progress || 0
})

const chainOfThought = computed(() => {
  return props.execution.chainOfThought || []
})

const parallelAgents = computed(() => {
  return props.agents.filter(agent =>
    agent.id !== props.execution.currentAgentId && agent.status === 'RUNNING'
  )
})

const completedSteps = computed(() => {
  return props.execution.completedSteps || 0
})

const totalSteps = computed(() => {
  return props.execution.totalSteps || 0
})

const executionDuration = computed(() => {
  if (!props.execution.startTime) return 0
  const start = new Date(props.execution.startTime).getTime()
  const end = props.execution.endTime
    ? new Date(props.execution.endTime).getTime()
    : Date.now()
  return end - start
})

const estimatedRemaining = computed(() => {
  if (progress.value === 0) return 0
  const elapsed = executionDuration.value
  const total = elapsed / (progress.value / 100)
  return Math.max(0, total - elapsed)
})

// 方法
function formatDuration(ms: number): string {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)

  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`
  } else {
    return `${seconds}s`
  }
}
</script>

<style scoped>
.agent-status-card {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  margin: 1rem 0;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--surface-secondary);
}

.card-title {
  margin: 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.status-indicator {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-running {
  background: var(--primary-light);
  color: var(--primary-dark);
}

.status-completed {
  background: var(--success-light);
  color: var(--success-dark);
}

.status-failed {
  background: var(--error-light);
  color: var(--error-dark);
}

.status-paused {
  background: var(--warning-light);
  color: var(--warning-dark);
}

.card-content {
  padding: 1rem;
}

.current-agent {
  margin-bottom: 1.5rem;
}

.agent-info {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.agent-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--primary-light);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  color: var(--primary);
  font-size: 1.25rem;
}

.agent-details {
  flex: 1;
}

.agent-name {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.agent-description {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: var(--bg-secondary);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--primary);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.875rem;
  color: var(--text-secondary);
  min-width: 3rem;
  text-align: right;
}

.chain-of-thought {
  margin-bottom: 1.5rem;
}

.chain-title {
  margin: 0 0 0.75rem 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.thought-list {
  space-y: 0.5rem;
}

.thought-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.thought-item.active {
  background: var(--primary-light);
}

.thought-step {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--text-secondary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  flex-shrink: 0;
}

.thought-item.active .thought-step {
  background: var(--primary);
}

.thought-content {
  flex: 1;
  font-size: 0.875rem;
  color: var(--text-primary);
  line-height: 1.4;
}

.thought-duration {
  font-size: 0.75rem;
  color: var(--text-secondary);
  flex-shrink: 0;
}
</style>
```

### SSE 服务

**sseService.ts**:

```typescript
export interface SseEvent {
  eventType: string
  conversationId: string
  agentName?: string
  stepId?: string
  timestamp: string
  data: Record<string, any>
}

export interface SseOptions {
  onEvent: (event: SseEvent) => void
  onError: (error: any) => void
  onReconnect: () => void
  reconnectInterval?: number
  maxReconnectAttempts?: number
}

export class SseService {
  private eventSource: EventSource | null = null
  private conversationId: string | null = null
  private options: SseOptions | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectInterval = 3000

  constructor() {
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this))
  }

  /**
   * 连接到 SSE 端点
   */
  connect(conversationId: string, options: SseOptions): void {
    this.conversationId = conversationId
    this.options = options
    this.maxReconnectAttempts = options.maxReconnectAttempts || 5
    this.reconnectInterval = options.reconnectInterval || 3000

    this.createConnection()
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    if (this.eventSource) {
      this.eventSource.close()
      this.eventSource = null
    }
    this.conversationId = null
    this.options = null
    this.reconnectAttempts = 0
  }

  /**
   * 创建 SSE 连接
   */
  private createConnection(): void {
    if (!this.conversationId || !this.options) {
      return
    }

    try {
      const token = this.getAuthToken()
      const url = `/api/agent-chat/v1/conversations/${this.conversationId}/stream`

      this.eventSource = new EventSource(url)

      this.eventSource.onopen = () => {
        console.log('SSE connection opened')
        this.reconnectAttempts = 0
      }

      this.eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          this.options?.onEvent(data)
        } catch (error) {
          console.error('Failed to parse SSE event data:', error)
        }
      }

      this.eventSource.addEventListener('agent-event', (event) => {
        try {
          const data = JSON.parse(event.data)
          this.options?.onEvent(data)
        } catch (error) {
          console.error('Failed to parse agent event data:', error)
        }
      })

      this.eventSource.onerror = (error) => {
        console.error('SSE connection error:', error)
        this.options?.onError(error)

        // 自动重连
        this.handleReconnect()
      }

    } catch (error) {
      console.error('Failed to create SSE connection:', error)
      this.options?.onError(error)
    }
  }

  /**
   * 处理重连
   */
  private handleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnect attempts reached')
      return
    }

    this.reconnectAttempts++
    console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)

    setTimeout(() => {
      if (this.conversationId && this.options) {
        this.eventSource?.close()
        this.createConnection()
        this.options.onReconnect()
      }
    }, this.reconnectInterval)
  }

  /**
   * 处理页面可见性变化
   */
  private handleVisibilityChange(): void {
    if (document.hidden) {
      // 页面隐藏时暂停连接
      console.log('Page hidden, pausing SSE connection')
    } else {
      // 页面可见时重新连接
      console.log('Page visible, resuming SSE connection')
      if (this.conversationId && this.options && !this.eventSource) {
        this.createConnection()
      }
    }
  }

  /**
   * 获取认证令牌
   */
  private getAuthToken(): string {
    return localStorage.getItem('auth_token') || ''
  }

  /**
   * 获取连接状态
   */
  get isConnected(): boolean {
    return this.eventSource?.readyState === EventSource.OPEN
  }

  /**
   * 获取连接状态文本
   */
  get connectionState(): string {
    if (!this.eventSource) return 'DISCONNECTED'

    switch (this.eventSource.readyState) {
      case EventSource.CONNECTING: return 'CONNECTING'
      case EventSource.OPEN: return 'OPEN'
      case EventSource.CLOSED: return 'CLOSED'
      default: return 'UNKNOWN'
    }
  }
}
```

### Pinia Store

**agentChat.ts**:

```typescript
import { defineStore } from 'pinia'
import { AgentChatService } from '@/api/agentChat'
import type {
  Conversation,
  Message,
  AgentFlow,
  AgentExecution,
  Agent,
  SseEvent
} from '@/types/agent'

interface AgentChatState {
  conversations: Conversation[]
  currentConversation: Conversation | null
  messages: Message[]
  currentFlow: AgentFlow | null
  currentStep: string | null
  executionHistory: AgentExecution[]
  activeExecution: AgentExecution | null
  activeAgents: Agent[]
  isProcessing: boolean
  inputSuggestions: string[]
  loading: boolean
  error: string | null
}

export const useAgentChatStore = defineStore('agentChat', {
  state: (): AgentChatState => ({
    conversations: [],
    currentConversation: null,
    messages: [],
    currentFlow: null,
    currentStep: null,
    executionHistory: [],
    activeExecution: null,
    activeAgents: [],
    isProcessing: false,
    inputSuggestions: [],
    loading: false,
    error: null
  }),

  getters: {
    currentConversationId(): string | null {
      return this.currentConversation?.id || null
    },

    hasActiveExecution(): boolean {
      return this.activeExecution?.status === 'RUNNING'
    },

    executionProgress(): number {
      return this.activeExecution?.progress || 0
    }
  },

  actions: {
    /**
     * 加载对话列表
     */
    async loadConversations() {
      this.loading = true
      this.error = null

      try {
        const response = await AgentChatService.getConversations()
        this.conversations = response.data
      } catch (error) {
        this.error = '加载对话列表失败'
        console.error('Failed to load conversations:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 创建新对话
     */
    async createConversation(title: string) {
      this.loading = true
      this.error = null

      try {
        const response = await AgentChatService.createConversation({ title })
        const newConversation = response.data

        this.conversations.unshift(newConversation)
        this.setCurrentConversation(newConversation)

        return newConversation
      } catch (error) {
        this.error = '创建对话失败'
        console.error('Failed to create conversation:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 设置当前对话
     */
    setCurrentConversation(conversation: Conversation) {
      this.currentConversation = conversation
      this.messages = []
      this.currentFlow = null
      this.currentStep = null
      this.activeExecution = null
      this.activeAgents = []
    },

    /**
     * 加载消息列表
     */
    async loadMessages(conversationId: string) {
      this.loading = true
      this.error = null

      try {
        const response = await AgentChatService.getMessages(conversationId)
        this.messages = response.data
      } catch (error) {
        this.error = '加载消息失败'
        console.error('Failed to load messages:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 发送消息
     */
    async sendMessage(conversationId: string, content: string) {
      this.isProcessing = true
      this.error = null

      try {
        // 添加用户消息到界面
        const userMessage: Message = {
          id: `temp-${Date.now()}`,
          conversationId,
          content,
          type: 'user',
          timestamp: new Date().toISOString(),
          isTemporary: true
        }

        this.messages.push(userMessage)

        // 发送到后端
        const response = await AgentChatService.sendMessage(conversationId, {
          content,
          messageType: 'USER'
        })

        // 更新消息ID
        const messageIndex = this.messages.findIndex(m => m.id === userMessage.id)
        if (messageIndex >= 0) {
          this.messages[messageIndex] = {
            ...userMessage,
            id: response.data.messageId,
            isTemporary: false
          }
        }

      } catch (error) {
        this.error = '发送消息失败'
        console.error('Failed to send message:', error)
        throw error
      }
    },

    /**
     * 处理 SSE 事件
     */
    handleSseEvent(event: SseEvent) {
      console.log('Handling SSE event:', event.eventType)

      switch (event.eventType) {
        case 'CONNECTED':
          console.log('SSE connected')
          break

        case 'AGENT_EXECUTION':
          this.handleAgentExecutionEvent(event)
          break

        case 'ORCHESTRATION':
          this.handleOrchestrationEvent(event)
          break

        case 'HEARTBEAT':
          // 忽略心跳事件
          break

        default:
          console.warn('Unknown SSE event type:', event.eventType)
      }
    },

    /**
     * 处理 Agent 执行事件
     */
    handleAgentExecutionEvent(event: SseEvent) {
      const { data } = event

      if (!this.activeExecution) {
        this.activeExecution = {
          id: data.executionId,
          conversationId: event.conversationId,
          status: data.status,
          progress: 0,
          startTime: new Date().toISOString(),
          chainOfThought: []
        }
      }

      // 更新执行状态
      this.activeExecution.status = data.status
      this.activeExecution.progress = data.progress || this.activeExecution.progress

      // 处理思维链更新
      if (data.chainOfThought) {
        this.activeExecution.chainOfThought = data.chainOfThought
      }

      // 如果执行完成，添加到历史记录
      if (data.status === 'COMPLETED' || data.status === 'FAILED') {
        this.activeExecution.endTime = new Date().toISOString()
        this.executionHistory.unshift({ ...this.activeExecution })
        this.activeExecution = null
        this.isProcessing = false
      }
    },

    /**
     * 处理编排事件
     */
    handleOrchestrationEvent(event: SseEvent) {
      const { data } = event

      // 更新当前流程信息
      if (data.flowName) {
        this.currentFlow = {
          id: data.flowId,
          name: data.flowName,
          steps: []
        }
      }

      // 更新当前步骤
      if (data.currentStep) {
        this.currentStep = data.currentStep
      }

      // 处理流程完成
      if (data.status === 'COMPLETED') {
        this.isProcessing = false

        // 添加 Agent 响应消息
        if (data.result && this.currentConversation) {
          const agentMessage: Message = {
            id: `agent-${Date.now()}`,
            conversationId: this.currentConversation.id,
            content: data.result.content,
            type: 'agent',
            timestamp: new Date().toISOString(),
            agentName: data.result.agentName,
            metadata: data.result.metadata
          }

          this.messages.push(agentMessage)
        }
      }
    },

    /**
     * 删除对话
     */
    async deleteConversation(conversationId: string) {
      try {
        await AgentChatService.deleteConversation(conversationId)

        this.conversations = this.conversations.filter(
          conv => conv.id !== conversationId
        )

        if (this.currentConversation?.id === conversationId) {
          this.currentConversation = null
          this.messages = []
        }
      } catch (error) {
        this.error = '删除对话失败'
        console.error('Failed to delete conversation:', error)
        throw error
      }
    },

    /**
     * 清除错误
     */
    clearError() {
      this.error = null
    }
  }
})
```

## 测试策略

### 组件测试

**AgentStatusCard.test.ts**:

```typescript
import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import AgentStatusCard from '@/components/agent/AgentStatusCard.vue'

describe('AgentStatusCard', () => {
  const mockExecution = {
    id: 'exec-001',
    conversationId: 'conv-001',
    status: 'RUNNING',
    progress: 65,
    startTime: '2025-08-03T10:00:00Z',
    chainOfThought: [
      {
        id: 'thought-001',
        step: '1',
        content: '正在分析用户输入',
        isActive: false,
        duration: 1500
      },
      {
        id: 'thought-002',
        step: '2',
        content: '正在处理数据查询',
        isActive: true,
        duration: null
      }
    ]
  }

  const mockAgents = [
    {
      id: 'agent-001',
      name: '数据分析师',
      description: '专门处理数据分析任务',
      icon: 'icon-chart'
    }
  ]

  it('should render execution status correctly', () => {
    const wrapper = mount(AgentStatusCard, {
      props: {
        execution: mockExecution,
        agents: mockAgents
      }
    })

    expect(wrapper.find('.status-indicator').text()).toBe('执行中')
    expect(wrapper.find('.progress-text').text()).toBe('65%')
  })

  it('should display chain of thought', () => {
    const wrapper = mount(AgentStatusCard, {
      props: {
        execution: mockExecution,
        agents: mockAgents
      }
    })

    const thoughtItems = wrapper.findAll('.thought-item')
    expect(thoughtItems).toHaveLength(2)
    expect(thoughtItems[1].classes()).toContain('active')
  })

  it('should calculate execution duration', () => {
    const wrapper = mount(AgentStatusCard, {
      props: {
        execution: {
          ...mockExecution,
          startTime: new Date(Date.now() - 60000).toISOString() // 1分钟前
        },
        agents: mockAgents
      }
    })

    expect(wrapper.find('.stat-value').text()).toContain('1m')
  })
})
```

### E2E 测试

**agentChat.e2e.ts**:

```typescript
import { test, expect } from '@playwright/test'

test.describe('Agent Chat Interface', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/agent-chat')
    await page.waitForLoadState('networkidle')
  })

  test('should create new conversation and send message', async ({ page }) => {
    // 创建新对话
    await page.click('button:has-text("新建对话")')
    await expect(page.locator('.conversation-container')).toBeVisible()

    // 发送消息
    const input = page.locator('.message-input input')
    await input.fill('请分析一下销售数据')
    await page.keyboard.press('Enter')

    // 验证消息发送
    await expect(page.locator('.message-list').last()).toContainText('请分析一下销售数据')

    // 验证 Agent 状态卡片出现
    await expect(page.locator('.agent-status-card')).toBeVisible()
  })

  test('should display real-time execution status', async ({ page }) => {
    // 模拟 SSE 连接和事件
    await page.route('/api/agent-chat/v1/conversations/*/stream', (route) => {
      // 模拟 SSE 流
      route.fulfill({
        status: 200,
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache'
        },
        body: `
          event: agent-event
          data: {"eventType":"EXECUTION_STARTED","status":"RUNNING"}

          event: agent-event
          data: {"eventType":"AGENT_EXECUTION","status":"RUNNING","progress":50}

          event: agent-event
          data: {"eventType":"EXECUTION_COMPLETED","status":"COMPLETED"}
        `
      })
    })

    // 发送消息触发执行
    await page.fill('.message-input input', '测试消息')
    await page.keyboard.press('Enter')

    // 验证状态更新
    await expect(page.locator('.status-indicator')).toContainText('执行中')
    await expect(page.locator('.progress-text')).toContainText('50%')
  })

  test('should handle SSE reconnection', async ({ page }) => {
    // 模拟网络中断和重连
    await page.route('/api/agent-chat/v1/conversations/*/stream', (route) => {
      // 第一次请求失败
      route.abort()
    })

    // 等待重连尝试
    await page.waitForTimeout(5000)

    // 模拟重连成功
    await page.route('/api/agent-chat/v1/conversations/*/stream', (route) => {
      route.fulfill({
        status: 200,
        headers: { 'Content-Type': 'text/event-stream' },
        body: 'event: agent-event\ndata: {"eventType":"CONNECTED"}\n\n'
      })
    })

    // 验证连接状态
    await expect(page.locator('[data-testid="connection-status"]')).toContainText('已连接')
  })
})
```

## 定义完成 (Definition of Done)

### 功能完成

- [ ] 所有验收标准通过测试
- [ ] Agent 对话界面正常运行
- [ ] 实时状态更新正常工作
- [ ] SSE 连接稳定可靠
- [ ] 代码审查完成并批准

### 质量保证

- [ ] 组件测试覆盖率 ≥ 85%
- [ ] E2E 测试覆盖主要用户场景
- [ ] 跨浏览器兼容性测试通过
- [ ] 移动端适配测试通过
- [ ] 无障碍访问测试通过

### 用户体验验证

- [ ] 界面响应速度 < 100ms
- [ ] 动画流畅无卡顿
- [ ] 错误提示友好清晰
- [ ] 加载状态明确可见
- [ ] 离线状态处理正确

### 技术标准

- [ ] TypeScript 类型检查通过
- [ ] ESLint 代码规范检查通过
- [ ] 组件可复用性良好
- [ ] 状态管理清晰合理

## 风险和依赖

### 风险识别

- **高风险**: SSE 连接在移动端的稳定性
- **中风险**: 复杂状态管理导致的同步问题
- **中风险**: 大量实时更新的性能影响
- **低风险**: 浏览器兼容性问题

### 缓解措施

- 实现完善的 SSE 重连机制
- 使用 Pinia 的持久化插件
- 优化渲染性能和虚拟滚动
- 全面的浏览器兼容性测试

### 依赖项

- **AGENT-003**: Agent Chat API 端点和流式推送必须完成
- 现有前端架构和组件库稳定
- Vue 3 和 TypeScript 技术栈熟悉
- 设计系统和 UI 组件规范

## 后续故事

完成此故事后，下一个故事将是：

- **Story #005**: Agent 管理和配置界面
- **Story #006**: 用户设置和偏好配置

---

**故事状态**: Ready for Development  
**最后更新**: 2025-08-03  
**负责人**: Frontend Developer  
**预计完成**: AGENT-003 完成后 3-4 工作日
