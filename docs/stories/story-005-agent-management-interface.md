# Story #005: Agent 管理和配置界面

## 故事信息

- **故事 ID**: AGENT-005
- **故事标题**: 实现 Agent 管理和配置界面
- **优先级**: P1 (高)
- **估算**: 6 Story Points (3-4 工作日)
- **创建时间**: 2025-08-03
- **分配给**: Frontend Developer
- **Epic**: AI Agent 对话系统 MVP
- **依赖**: AGENT-001 (Agent 数据库基础), AGENT-002 (Agent 执行引擎)

## 故事描述

### 用户故事

作为一个**系统管理员**，我希望**有专用的 Agent 管理和配置界面**，以便**能够创建、配置和管理 AI Agents，定义执行流程，监控系统运行状态**。

### 背景和目标

为了支持 AI Agent 系统的运营和管理，需要提供管理员界面来配置和监控 Agent 系统。这个故事重点实现：

1. Agent 定义的创建和编辑界面
2. Agent 流程的可视化配置
3. 系统监控和统计仪表板
4. 权限管理和用户授权

**业务价值**:

- 降低 Agent 系统的管理复杂度
- 提供直观的配置和监控工具
- 支持非技术人员管理 Agent 系统

## 验收标准

### 主要验收标准

#### ✅ AC1: Agent 管理界面

- [ ] 创建 Agent 管理页面 (`/admin/agents`)
- [ ] 支持 Agent 的增删改查操作
- [ ] 提供 Agent 模板和预设配置
- [ ] 支持 Agent 能力和提示词配置
- [ ] 集成现有的管理员权限系统

#### ✅ AC2: 流程配置界面

- [ ] 创建流程配置页面 (`/admin/flows`)
- [ ] 可视化的流程设计器
- [ ] 支持拖拽式流程构建
- [ ] 流程测试和验证功能
- [ ] 流程版本管理和回滚

#### ✅ AC3: 系统监控仪表板

- [ ] 创建监控仪表板 (`/admin/dashboard`)
- [ ] 实时显示 Agent 执行统计
- [ ] 系统性能和资源监控
- [ ] 错误和异常追踪
- [ ] 用户使用情况分析

#### ✅ AC4: 配置管理界面

- [ ] 系统参数配置界面
- [ ] AI 模型和 API 配置
- [ ] 缓存和性能参数设置
- [ ] 日志级别和监控配置
- [ ] 备份和恢复功能

#### ✅ AC5: 权限和安全管理

- [ ] 用户角色和权限配置
- [ ] Agent 访问控制设置
- [ ] API 密钥和安全配置
- [ ] 审计日志和操作记录
- [ ] 安全策略配置

### 技术验收标准

#### 📋 T1: 界面设计

- [ ] 遵循管理后台设计规范
- [ ] 响应式设计支持各种屏幕
- [ ] 一致的交互模式和视觉风格
- [ ] 良好的可用性和用户体验

#### 📋 T2: 数据验证

- [ ] 前端表单验证和错误提示
- [ ] 配置参数的合理性检查
- [ ] 流程定义的完整性验证
- [ ] 实时保存和自动恢复

#### 📋 T3: 性能优化

- [ ] 大数据列表的虚拟滚动
- [ ] 图表和监控数据的懒加载
- [ ] 配置界面的防抖和节流
- [ ] 合理的缓存策略

## 技术实现指南

### 页面组件架构

**AgentManagementPage.vue**:

```vue
<template>
  <div class="agent-management-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">AI Agent 管理</h1>
      <div class="header-actions">
        <button @click="importAgents" class="btn-secondary">
          <i class="icon-upload"></i>
          导入配置
        </button>
        <button @click="createAgent" class="btn-primary">
          <i class="icon-plus"></i>
          创建 Agent
        </button>
      </div>
    </div>

    <!-- 过滤器和搜索 -->
    <div class="filter-bar">
      <div class="search-container">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索 Agent..."
          class="search-input"
        >
        <i class="icon-search search-icon"></i>
      </div>

      <div class="filter-controls">
        <select v-model="statusFilter" class="filter-select">
          <option value="">所有状态</option>
          <option value="ACTIVE">启用</option>
          <option value="INACTIVE">禁用</option>
          <option value="DRAFT">草稿</option>
        </select>

        <select v-model="categoryFilter" class="filter-select">
          <option value="">所有类别</option>
          <option value="ANALYSIS">数据分析</option>
          <option value="PROCESSING">数据处理</option>
          <option value="INFERENCE">推理</option>
          <option value="REPORTING">报告生成</option>
        </select>
      </div>
    </div>

    <!-- Agent 列表 -->
    <div class="agent-list-container">
      <div class="list-header">
        <span class="result-count">共 {{ filteredAgents.length }} 个 Agent</span>
        <div class="view-controls">
          <button
            @click="viewMode = 'grid'"
            :class="{ active: viewMode === 'grid' }"
            class="view-btn"
          >
            <i class="icon-grid"></i>
          </button>
          <button
            @click="viewMode = 'list'"
            :class="{ active: viewMode === 'list' }"
            class="view-btn"
          >
            <i class="icon-list"></i>
          </button>
        </div>
      </div>

      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="agent-grid">
        <AgentCard
          v-for="agent in filteredAgents"
          :key="agent.id"
          :agent="agent"
          @edit="editAgent"
          @duplicate="duplicateAgent"
          @delete="deleteAgent"
          @toggle-status="toggleAgentStatus"
        />
      </div>

      <!-- 列表视图 -->
      <div v-else class="agent-table">
        <AgentTable
          :agents="filteredAgents"
          :loading="loading"
          @edit="editAgent"
          @delete="deleteAgent"
          @sort="handleSort"
        />
      </div>
    </div>

    <!-- Agent 编辑对话框 -->
    <AgentEditDialog
      v-model:visible="editDialogVisible"
      :agent="editingAgent"
      :mode="editMode"
      @save="saveAgent"
      @cancel="cancelEdit"
    />

    <!-- 导入对话框 -->
    <ImportDialog
      v-model:visible="importDialogVisible"
      @import="handleImport"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useAgentManagementStore } from '@/store/modules/agentManagement'
import { useMessage } from '@/composables/useMessage'
import AgentCard from '@/components/admin/AgentCard.vue'
import AgentTable from '@/components/admin/AgentTable.vue'
import AgentEditDialog from '@/components/admin/AgentEditDialog.vue'
import ImportDialog from '@/components/admin/ImportDialog.vue'
import type { Agent, AgentStatus } from '@/types/agent'

// Store 和工具
const agentStore = useAgentManagementStore()
const { showSuccess, showError, showConfirm } = useMessage()

// 响应式数据
const searchQuery = ref('')
const statusFilter = ref<string>('')
const categoryFilter = ref<string>('')
const viewMode = ref<'grid' | 'list'>('grid')
const editDialogVisible = ref(false)
const importDialogVisible = ref(false)
const editingAgent = ref<Agent | null>(null)
const editMode = ref<'create' | 'edit'>('create')
const loading = ref(false)

// 计算属性
const filteredAgents = computed(() => {
  let result = agentStore.agents

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(agent =>
      agent.name.toLowerCase().includes(query) ||
      agent.description.toLowerCase().includes(query)
    )
  }

  // 状态过滤
  if (statusFilter.value) {
    result = result.filter(agent => agent.status === statusFilter.value)
  }

  // 类别过滤
  if (categoryFilter.value) {
    result = result.filter(agent => agent.category === categoryFilter.value)
  }

  return result
})

// 生命周期
onMounted(async () => {
  await loadAgents()
})

// 监听搜索变化
watch([searchQuery, statusFilter, categoryFilter], () => {
  // 防抖搜索
  // 这里可以添加防抖逻辑
})

// 方法
async function loadAgents() {
  loading.value = true
  try {
    await agentStore.loadAgents()
  } catch (error) {
    showError('加载 Agent 列表失败')
  } finally {
    loading.value = false
  }
}

function createAgent() {
  editingAgent.value = null
  editMode.value = 'create'
  editDialogVisible.value = true
}

function editAgent(agent: Agent) {
  editingAgent.value = { ...agent }
  editMode.value = 'edit'
  editDialogVisible.value = true
}

async function duplicateAgent(agent: Agent) {
  try {
    const duplicated = {
      ...agent,
      id: undefined,
      name: `${agent.name} (副本)`,
      status: 'DRAFT' as AgentStatus
    }

    await agentStore.createAgent(duplicated)
    showSuccess('Agent 复制成功')
  } catch (error) {
    showError('复制 Agent 失败')
  }
}

async function deleteAgent(agent: Agent) {
  const confirmed = await showConfirm(
    '确认删除',
    `确定要删除 Agent "${agent.name}" 吗？此操作不可恢复。`
  )

  if (confirmed) {
    try {
      await agentStore.deleteAgent(agent.id)
      showSuccess('Agent 删除成功')
    } catch (error) {
      showError('删除 Agent 失败')
    }
  }
}

async function toggleAgentStatus(agent: Agent) {
  try {
    const newStatus = agent.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE'
    await agentStore.updateAgentStatus(agent.id, newStatus)
    showSuccess(`Agent ${newStatus === 'ACTIVE' ? '启用' : '禁用'}成功`)
  } catch (error) {
    showError('更新 Agent 状态失败')
  }
}

async function saveAgent(agentData: Agent) {
  try {
    if (editMode.value === 'create') {
      await agentStore.createAgent(agentData)
      showSuccess('Agent 创建成功')
    } else {
      await agentStore.updateAgent(agentData)
      showSuccess('Agent 更新成功')
    }

    editDialogVisible.value = false
    editingAgent.value = null
  } catch (error) {
    showError(`${editMode.value === 'create' ? '创建' : '更新'} Agent 失败`)
  }
}

function cancelEdit() {
  editDialogVisible.value = false
  editingAgent.value = null
}

function handleSort(field: string, order: 'asc' | 'desc') {
  agentStore.sortAgents(field, order)
}

function importAgents() {
  importDialogVisible.value = true
}

async function handleImport(data: any) {
  try {
    await agentStore.importAgents(data)
    showSuccess('Agent 导入成功')
    importDialogVisible.value = false
  } catch (error) {
    showError('导入 Agent 失败')
  }
}
</script>

<style scoped>
.agent-management-page {
  padding: 1.5rem;
  background: var(--bg-color);
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.page-title {
  margin: 0;
  font-size: 1.75rem;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--surface-color);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.search-container {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-input {
  width: 100%;
  padding: 0.5rem 0.75rem 0.5rem 2.5rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-color);
  color: var(--text-primary);
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
}

.filter-controls {
  display: flex;
  gap: 0.75rem;
}

.filter-select {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-color);
  color: var(--text-primary);
}

.agent-list-container {
  background: var(--surface-color);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--surface-secondary);
}

.result-count {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.view-controls {
  display: flex;
  gap: 0.25rem;
}

.view-btn {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  background: var(--bg-color);
  color: var(--text-secondary);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.view-btn:hover {
  color: var(--text-primary);
  border-color: var(--primary);
}

.view-btn.active {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

.agent-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  padding: 1.5rem;
}

.agent-table {
  overflow-x: auto;
}

@media (max-width: 768px) {
  .filter-bar {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .filter-controls {
    justify-content: stretch;
  }

  .filter-select {
    flex: 1;
  }

  .agent-grid {
    grid-template-columns: 1fr;
  }
}
</style>
```

### Agent 编辑对话框

**AgentEditDialog.vue**:

```vue
<template>
  <el-dialog
    v-model="visible"
    :title="mode === 'create' ? '创建 Agent' : '编辑 Agent'"
    width="800px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      label-position="top"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <h4 class="section-title">基本信息</h4>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="Agent 名称" prop="name">
              <el-input
                v-model="formData.name"
                placeholder="请输入 Agent 名称"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="类别" prop="category">
              <el-select
                v-model="formData.category"
                placeholder="选择 Agent 类别"
                style="width: 100%"
              >
                <el-option label="数据分析" value="ANALYSIS" />
                <el-option label="数据处理" value="PROCESSING" />
                <el-option label="推理" value="INFERENCE" />
                <el-option label="报告生成" value="REPORTING" />
                <el-option label="可视化" value="VISUALIZATION" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请描述这个 Agent 的功能和用途"
          />
        </el-form-item>
      </div>

      <!-- 能力配置 -->
      <div class="form-section">
        <h4 class="section-title">能力配置</h4>

        <el-form-item label="Agent 能力" prop="capabilities">
          <el-checkbox-group v-model="formData.capabilities">
            <el-checkbox label="data_analysis">数据分析</el-checkbox>
            <el-checkbox label="chart_generation">图表生成</el-checkbox>
            <el-checkbox label="report_writing">报告撰写</el-checkbox>
            <el-checkbox label="data_processing">数据处理</el-checkbox>
            <el-checkbox label="pattern_recognition">模式识别</el-checkbox>
            <el-checkbox label="prediction">预测分析</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="优先级" prop="priority">
          <el-slider
            v-model="formData.priority"
            :min="0"
            :max="100"
            :step="10"
            show-stops
            show-input
          />
          <div class="priority-help">
            数值越高优先级越高，相同意图下优先级高的 Agent 将被优先选择
          </div>
        </el-form-item>
      </div>

      <!-- 提示词配置 -->
      <div class="form-section">
        <h4 class="section-title">提示词配置</h4>

        <el-form-item label="系统提示词" prop="systemPrompt">
          <div class="prompt-editor">
            <el-input
              v-model="formData.systemPrompt"
              type="textarea"
              :rows="8"
              placeholder="请输入系统提示词..."
            />
            <div class="prompt-toolbar">
              <el-button size="small" @click="insertTemplate('analysis')">
                插入分析模板
              </el-button>
              <el-button size="small" @click="insertTemplate('processing')">
                插入处理模板
              </el-button>
              <el-button size="small" @click="previewPrompt">
                预览效果
              </el-button>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="提示词变量">
          <div class="variables-editor">
            <div
              v-for="(variable, index) in formData.promptVariables"
              :key="index"
              class="variable-item"
            >
              <el-input
                v-model="variable.name"
                placeholder="变量名"
                style="width: 200px"
              />
              <el-input
                v-model="variable.description"
                placeholder="变量描述"
                style="flex: 1; margin: 0 0.5rem"
              />
              <el-input
                v-model="variable.defaultValue"
                placeholder="默认值"
                style="width: 200px"
              />
              <el-button
                type="danger"
                size="small"
                icon="Delete"
                @click="removeVariable(index)"
              />
            </div>

            <el-button
              type="primary"
              size="small"
              icon="Plus"
              @click="addVariable"
            >
              添加变量
            </el-button>
          </div>
        </el-form-item>
      </div>

      <!-- 高级配置 -->
      <div class="form-section">
        <h4 class="section-title">高级配置</h4>

        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="超时时间(秒)" prop="timeout">
              <el-input-number
                v-model="formData.timeout"
                :min="1"
                :max="300"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="重试次数" prop="maxRetries">
              <el-input-number
                v-model="formData.maxRetries"
                :min="0"
                :max="5"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="状态" prop="status">
              <el-select v-model="formData.status" style="width: 100%">
                <el-option label="启用" value="ACTIVE" />
                <el-option label="禁用" value="INACTIVE" />
                <el-option label="草稿" value="DRAFT" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="配置参数">
          <JsonEditor
            v-model="formData.configuration"
            :height="200"
            :options="{ mode: 'code' }"
          />
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="testAgent" :loading="testing">测试</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          :loading="saving"
        >
          {{ mode === 'create' ? '创建' : '保存' }}
        </el-button>
      </div>
    </template>

    <!-- 提示词预览对话框 -->
    <PromptPreviewDialog
      v-model:visible="previewVisible"
      :prompt="formData.systemPrompt"
      :variables="formData.promptVariables"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import JsonEditor from '@/components/common/JsonEditor.vue'
import PromptPreviewDialog from '@/components/admin/PromptPreviewDialog.vue'
import type { Agent } from '@/types/agent'

interface Props {
  visible: boolean
  agent: Agent | null
  mode: 'create' | 'edit'
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'save', agent: Agent): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref()
const saving = ref(false)
const testing = ref(false)
const previewVisible = ref(false)

const formData = ref({
  id: undefined,
  name: '',
  description: '',
  category: '',
  capabilities: [],
  systemPrompt: '',
  promptVariables: [],
  priority: 50,
  timeout: 30,
  maxRetries: 3,
  status: 'DRAFT',
  configuration: {}
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入 Agent 名称', trigger: 'blur' },
    { min: 2, max: 50, message: '名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入 Agent 描述', trigger: 'blur' },
    { max: 500, message: '描述不能超过 500 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择 Agent 类别', trigger: 'change' }
  ],
  systemPrompt: [
    { required: true, message: '请输入系统提示词', trigger: 'blur' },
    { min: 10, message: '提示词至少需要 10 个字符', trigger: 'blur' }
  ]
}

// 监听属性变化
watch(() => props.visible, (newVal) => {
  if (newVal && props.agent) {
    // 编辑模式，填充表单数据
    Object.assign(formData.value, {
      ...props.agent,
      promptVariables: props.agent.promptVariables || []
    })
  } else if (newVal && !props.agent) {
    // 创建模式，重置表单
    resetForm()
  }
})

// 方法
function resetForm() {
  formData.value = {
    id: undefined,
    name: '',
    description: '',
    category: '',
    capabilities: [],
    systemPrompt: '',
    promptVariables: [],
    priority: 50,
    timeout: 30,
    maxRetries: 3,
    status: 'DRAFT',
    configuration: {}
  }

  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

async function handleSave() {
  try {
    await formRef.value.validate()

    saving.value = true

    const agentData = {
      ...formData.value,
      promptVariables: formData.value.promptVariables.filter(v => v.name)
    }

    emit('save', agentData)
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    saving.value = false
  }
}

function handleClose() {
  emit('update:visible', false)
  emit('cancel')
}

function addVariable() {
  formData.value.promptVariables.push({
    name: '',
    description: '',
    defaultValue: ''
  })
}

function removeVariable(index: number) {
  formData.value.promptVariables.splice(index, 1)
}

function insertTemplate(type: string) {
  const templates = {
    analysis: `你是一个专业的数据分析师。请根据用户提供的数据进行深入分析，包括：

1. 数据概览和基本统计
2. 趋势分析和模式识别
3. 异常值检测
4. 关键洞察和建议

请用专业、清晰的语言回答用户的问题。`,

    processing: `你是一个数据处理专家。请根据用户需求对数据进行处理，包括：

1. 数据清洗和去重
2. 格式转换和标准化
3. 数据筛选和聚合
4. 质量检查和验证

处理过程中请保持数据的准确性和完整性。`
  }

  const template = templates[type]
  if (template) {
    formData.value.systemPrompt = template
  }
}

function previewPrompt() {
  previewVisible.value = true
}

async function testAgent() {
  try {
    await formRef.value.validate()

    testing.value = true

    // 这里可以调用测试 API
    await new Promise(resolve => setTimeout(resolve, 2000))

    ElMessage.success('Agent 测试通过')
  } catch (error) {
    ElMessage.error('Agent 测试失败')
  } finally {
    testing.value = false
  }
}
</script>

<style scoped>
.form-section {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.form-section:last-child {
  border-bottom: none;
}

.section-title {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  color: var(--text-primary);
  font-weight: 600;
}

.prompt-editor {
  position: relative;
}

.prompt-toolbar {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid var(--border-color);
}

.priority-help {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-top: 0.5rem;
}

.variables-editor {
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 1rem;
  background: var(--bg-secondary);
}

.variable-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  gap: 0.5rem;
}

.variable-item:last-child {
  margin-bottom: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}
</style>
```

### 监控仪表板

**DashboardPage.vue**:

```vue
<template>
  <div class="dashboard-page">
    <!-- 页面头部 -->
    <div class="dashboard-header">
      <h1 class="page-title">系统监控</h1>
      <div class="header-controls">
        <el-select v-model="timeRange" @change="refreshData">
          <el-option label="最近1小时" value="1h" />
          <el-option label="最近24小时" value="24h" />
          <el-option label="最近7天" value="7d" />
          <el-option label="最近30天" value="30d" />
        </el-select>
        <el-button @click="refreshData" :loading="loading">
          <i class="icon-refresh"></i>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 概览指标 -->
    <div class="metrics-overview">
      <MetricCard
        title="总执行次数"
        :value="metrics.totalExecutions"
        :trend="metrics.executionsTrend"
        icon="icon-play"
        color="primary"
      />
      <MetricCard
        title="成功率"
        :value="`${metrics.successRate}%`"
        :trend="metrics.successRateTrend"
        icon="icon-check"
        color="success"
      />
      <MetricCard
        title="平均响应时间"
        :value="`${metrics.avgResponseTime}ms`"
        :trend="metrics.responseTimeTrend"
        icon="icon-clock"
        color="warning"
      />
      <MetricCard
        title="活跃用户"
        :value="metrics.activeUsers"
        :trend="metrics.activeUsersTrend"
        icon="icon-users"
        color="info"
      />
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="chart-row">
        <!-- 执行趋势图 -->
        <div class="chart-container">
          <div class="chart-header">
            <h3 class="chart-title">执行趋势</h3>
            <div class="chart-legend">
              <span class="legend-item success">成功</span>
              <span class="legend-item error">失败</span>
            </div>
          </div>
          <div class="chart-content">
            <LineChart
              :data="executionTrendData"
              :options="executionTrendOptions"
              height="300px"
            />
          </div>
        </div>

        <!-- Agent 使用统计 -->
        <div class="chart-container">
          <div class="chart-header">
            <h3 class="chart-title">Agent 使用情况</h3>
          </div>
          <div class="chart-content">
            <PieChart
              :data="agentUsageData"
              :options="agentUsageOptions"
              height="300px"
            />
          </div>
        </div>
      </div>

      <div class="chart-row">
        <!-- 响应时间分布 -->
        <div class="chart-container">
          <div class="chart-header">
            <h3 class="chart-title">响应时间分布</h3>
          </div>
          <div class="chart-content">
            <BarChart
              :data="responseTimeData"
              :options="responseTimeOptions"
              height="300px"
            />
          </div>
        </div>

        <!-- 系统资源监控 -->
        <div class="chart-container">
          <div class="chart-header">
            <h3 class="chart-title">系统资源</h3>
          </div>
          <div class="chart-content">
            <ResourceMonitor
              :cpu-usage="systemResources.cpuUsage"
              :memory-usage="systemResources.memoryUsage"
              :disk-usage="systemResources.diskUsage"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 实时日志 -->
    <div class="logs-section">
      <div class="logs-header">
        <h3 class="logs-title">实时日志</h3>
        <div class="logs-controls">
          <el-select v-model="logLevel" size="small">
            <el-option label="全部" value="" />
            <el-option label="ERROR" value="ERROR" />
            <el-option label="WARN" value="WARN" />
            <el-option label="INFO" value="INFO" />
            <el-option label="DEBUG" value="DEBUG" />
          </el-select>
          <el-button size="small" @click="clearLogs">清空</el-button>
        </div>
      </div>

      <div class="logs-content">
        <LogViewer
          :logs="filteredLogs"
          :auto-scroll="true"
          height="300px"
        />
      </div>
    </div>

    <!-- 告警信息 -->
    <div v-if="alerts.length > 0" class="alerts-section">
      <div class="alerts-header">
        <h3 class="alerts-title">系统告警</h3>
        <el-button size="small" @click="dismissAllAlerts">全部忽略</el-button>
      </div>

      <div class="alerts-list">
        <AlertItem
          v-for="alert in alerts"
          :key="alert.id"
          :alert="alert"
          @dismiss="dismissAlert"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useMonitoringStore } from '@/store/modules/monitoring'
import MetricCard from '@/components/admin/MetricCard.vue'
import LineChart from '@/components/charts/LineChart.vue'
import PieChart from '@/components/charts/PieChart.vue'
import BarChart from '@/components/charts/BarChart.vue'
import ResourceMonitor from '@/components/admin/ResourceMonitor.vue'
import LogViewer from '@/components/admin/LogViewer.vue'
import AlertItem from '@/components/admin/AlertItem.vue'

// Store
const monitoringStore = useMonitoringStore()

// 响应式数据
const timeRange = ref('24h')
const logLevel = ref('')
const loading = ref(false)
const refreshInterval = ref<number>()

// 计算属性
const metrics = computed(() => monitoringStore.metrics)
const executionTrendData = computed(() => monitoringStore.executionTrendData)
const agentUsageData = computed(() => monitoringStore.agentUsageData)
const responseTimeData = computed(() => monitoringStore.responseTimeData)
const systemResources = computed(() => monitoringStore.systemResources)
const logs = computed(() => monitoringStore.logs)
const alerts = computed(() => monitoringStore.alerts)

const filteredLogs = computed(() => {
  if (!logLevel.value) return logs.value
  return logs.value.filter(log => log.level === logLevel.value)
})

// 图表配置
const executionTrendOptions = {
  responsive: true,
  scales: {
    y: {
      beginAtZero: true
    }
  }
}

const agentUsageOptions = {
  responsive: true,
  plugins: {
    legend: {
      position: 'bottom'
    }
  }
}

const responseTimeOptions = {
  responsive: true,
  scales: {
    y: {
      beginAtZero: true,
      title: {
        display: true,
        text: '响应时间 (ms)'
      }
    }
  }
}

// 生命周期
onMounted(async () => {
  await refreshData()
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})

// 方法
async function refreshData() {
  loading.value = true
  try {
    await monitoringStore.loadMetrics(timeRange.value)
    await monitoringStore.loadChartData(timeRange.value)
    await monitoringStore.loadSystemResources()
    await monitoringStore.loadRecentLogs()
    await monitoringStore.loadAlerts()
  } catch (error) {
    console.error('Failed to refresh dashboard data:', error)
  } finally {
    loading.value = false
  }
}

function startAutoRefresh() {
  refreshInterval.value = setInterval(() => {
    refreshData()
  }, 30000) // 30秒刷新一次
}

function stopAutoRefresh() {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
  }
}

function clearLogs() {
  monitoringStore.clearLogs()
}

function dismissAlert(alertId: string) {
  monitoringStore.dismissAlert(alertId)
}

function dismissAllAlerts() {
  monitoringStore.dismissAllAlerts()
}
</script>

<style scoped>
.dashboard-page {
  padding: 1.5rem;
  background: var(--bg-color);
  min-height: 100vh;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.page-title {
  margin: 0;
  font-size: 1.75rem;
  color: var(--text-primary);
}

.header-controls {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.metrics-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.charts-section {
  margin-bottom: 2rem;
}

.chart-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.chart-container {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--surface-secondary);
}

.chart-title {
  margin: 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.chart-legend {
  display: flex;
  gap: 1rem;
}

.legend-item {
  position: relative;
  padding-left: 1rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.legend-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.legend-item.success::before {
  background: var(--success);
}

.legend-item.error::before {
  background: var(--error);
}

.chart-content {
  padding: 1.5rem;
}

.logs-section {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  margin-bottom: 2rem;
  overflow: hidden;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--surface-secondary);
}

.logs-title {
  margin: 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.logs-controls {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.logs-content {
  padding: 1rem;
}

.alerts-section {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
}

.alerts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--error-light);
}

.alerts-title {
  margin: 0;
  font-size: 1rem;
  color: var(--error-dark);
}

.alerts-list {
  padding: 1rem;
}

@media (max-width: 1024px) {
  .chart-row {
    grid-template-columns: 1fr;
  }

  .metrics-overview {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-controls {
    justify-content: space-between;
  }

  .logs-header,
  .alerts-header {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .logs-controls {
    justify-content: space-between;
  }
}
</style>
```

## 测试策略

### 组件测试

**AgentEditDialog.test.ts**:

```typescript
import { mount } from '@vue/test-utils'
import { describe, it, expect, vi } from 'vitest'
import AgentEditDialog from '@/components/admin/AgentEditDialog.vue'

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElDialog: { template: '<div><slot /></div>' },
  ElForm: { template: '<form><slot /></form>' },
  ElFormItem: { template: '<div><slot /></div>' },
  ElInput: { template: '<input />' },
  ElSelect: { template: '<select><slot /></select>' },
  ElOption: { template: '<option />' },
  ElButton: { template: '<button><slot /></button>' },
  ElMessage: {
    success: vi.fn(),
    error: vi.fn()
  }
}))

describe('AgentEditDialog', () => {
  const mockAgent = {
    id: 'agent-001',
    name: '测试Agent',
    description: '这是一个测试Agent',
    category: 'ANALYSIS',
    capabilities: ['data_analysis'],
    systemPrompt: '你是一个测试Agent',
    status: 'ACTIVE'
  }

  it('should emit save event with form data', async () => {
    const wrapper = mount(AgentEditDialog, {
      props: {
        visible: true,
        agent: mockAgent,
        mode: 'edit'
      }
    })

    // 模拟表单验证
    const formRef = wrapper.vm.$refs.formRef
    formRef.validate = vi.fn().mockResolvedValue(true)

    await wrapper.vm.handleSave()

    expect(wrapper.emitted('save')).toBeTruthy()
    expect(wrapper.emitted('save')[0][0]).toMatchObject({
      name: '测试Agent',
      category: 'ANALYSIS'
    })
  })

  it('should validate required fields', async () => {
    const wrapper = mount(AgentEditDialog, {
      props: {
        visible: true,
        agent: null,
        mode: 'create'
      }
    })

    const formRules = wrapper.vm.formRules

    expect(formRules.name).toBeDefined()
    expect(formRules.description).toBeDefined()
    expect(formRules.category).toBeDefined()
    expect(formRules.systemPrompt).toBeDefined()
  })

  it('should handle template insertion', async () => {
    const wrapper = mount(AgentEditDialog, {
      props: {
        visible: true,
        agent: null,
        mode: 'create'
      }
    })

    wrapper.vm.insertTemplate('analysis')

    expect(wrapper.vm.formData.systemPrompt).toContain('数据分析师')
  })
})
```

### E2E 测试

**agentManagement.e2e.ts**:

```typescript
import { test, expect } from '@playwright/test'

test.describe('Agent Management', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/admin/agents')
    await page.waitForLoadState('networkidle')
  })

  test('should create new agent', async ({ page }) => {
    // 点击创建按钮
    await page.click('button:has-text("创建 Agent")')

    // 填写表单
    await page.fill('input[placeholder="请输入 Agent 名称"]', '测试Agent')
    await page.selectOption('select', 'ANALYSIS')
    await page.fill('textarea[placeholder="请描述这个 Agent 的功能和用途"]', '这是一个测试Agent')
    await page.fill('textarea[placeholder="请输入系统提示词..."]', '你是一个测试Agent')

    // 保存
    await page.click('button:has-text("创建")')

    // 验证创建成功
    await expect(page.locator('.agent-card')).toContainText('测试Agent')
  })

  test('should edit existing agent', async ({ page }) => {
    // 找到第一个Agent并编辑
    await page.click('.agent-card .edit-button')

    // 修改名称
    await page.fill('input[placeholder="请输入 Agent 名称"]', '修改后的Agent')

    // 保存
    await page.click('button:has-text("保存")')

    // 验证修改成功
    await expect(page.locator('.agent-card')).toContainText('修改后的Agent')
  })

  test('should filter agents by status', async ({ page }) => {
    // 选择状态过滤器
    await page.selectOption('.filter-select', 'ACTIVE')

    // 验证只显示启用的Agent
    const agentCards = page.locator('.agent-card')
    const count = await agentCards.count()

    for (let i = 0; i < count; i++) {
      await expect(agentCards.nth(i)).toContainText('启用')
    }
  })

  test('should search agents', async ({ page }) => {
    // 输入搜索关键词
    await page.fill('.search-input', '数据分析')

    // 验证搜索结果
    await expect(page.locator('.result-count')).toContainText('数据分析')
  })
})
```

## 定义完成 (Definition of Done)

### 功能完成

- [ ] 所有验收标准通过测试
- [ ] Agent 管理界面功能完整
- [ ] 流程配置界面正常工作
- [ ] 监控仪表板数据准确
- [ ] 权限控制有效
- [ ] 代码审查完成并批准

### 质量保证

- [ ] 组件测试覆盖率 ≥ 80%
- [ ] E2E 测试覆盖主要管理流程
- [ ] 表单验证正确有效
- [ ] 权限验证通过测试
- [ ] 性能测试满足要求

### 用户体验验证

- [ ] 界面响应流畅
- [ ] 表单操作直观
- [ ] 错误提示清晰
- [ ] 批量操作高效
- [ ] 搜索和过滤准确

### 安全验证

- [ ] 管理员权限验证
- [ ] 敏感操作确认
- [ ] 数据访问控制
- [ ] 操作审计记录

## 风险和依赖

### 风险识别

- **中风险**: 复杂表单验证和数据处理
- **中风险**: 图表和监控数据的实时性
- **低风险**: 权限管理的复杂性
- **低风险**: 大数据量的性能问题

### 缓解措施

- 实现完善的表单验证机制
- 使用高效的图表库和数据处理
- 建立清晰的权限模型
- 优化列表和图表的渲染性能

### 依赖项

- **AGENT-001**: Agent 数据库基础必须完成
- **AGENT-002**: Agent 执行引擎提供管理 API
- 现有管理后台框架和组件
- 图表库和数据可视化组件

## 后续故事

完成此故事后，下一个故事将是：

- **Story #006**: 系统配置和部署优化
- **Story #007**: 性能监控和告警系统

---

**故事状态**: Ready for Development  
**最后更新**: 2025-08-03  
**负责人**: Frontend Developer  
**预计完成**: AGENT-001, AGENT-002 完成后 3-4 工作日
