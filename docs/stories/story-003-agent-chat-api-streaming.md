# Story #003: Agent Chat API 端点和流式推送

## 故事信息

- **故事 ID**: AGENT-003
- **故事标题**: 实现 Agent Chat API 端点和 Server-Sent Events 流式推送
- **优先级**: P0 (最高)
- **估算**: 6 Story Points (2-3 工作日)
- **创建时间**: 2025-08-03
- **分配给**: Backend Developer
- **Epic**: AI Agent 对话系统 MVP
- **依赖**: AGENT-002 (Agent 执行引擎和编排服务)

## 故事描述

### 用户故事

作为一个**前端开发者**，我希望**有专用的 Agent Chat API 端点和 SSE 流式推送能力**，以便**为用户提供实时的 AI Agent 对话体验，避免长时间等待**。

### 背景和目标

基于前两个故事建立的数据基础和业务逻辑，现在需要实现用户交互的 API 层。这个故事重点实现：

1. 独立的 Agent Chat API 端点 (`/api/agent-chat/v1/*`)
2. Server-Sent Events (SSE) 流式推送
3. 与现有聊天系统的无缝协作
4. 完整的错误处理和状态管理

**业务价值**:

- 为前端提供专用的 Agent 对话 API
- 通过流式推送提升用户体验
- 确保与现有系统的完全兼容

## 验收标准

### 主要验收标准

#### ✅ AC1: Agent Chat Controller

- [ ] 实现 `AgentChatController` 控制器
- [ ] 提供 `/api/agent-chat/v1/conversations` 端点
- [ ] 支持创建和管理 Agent 对话
- [ ] 集成现有的用户认证和权限验证
- [ ] 遵循现有的 API 设计规范和错误处理模式

#### ✅ AC2: 流式对话端点

- [ ] 实现 `/api/agent-chat/v1/stream` SSE 端点
- [ ] 支持实时的 Agent 执行状态推送
- [ ] 推送思维链 (Chain of Thought) 过程
- [ ] 支持客户端断线重连
- [ ] 实现背压控制和资源管理

#### ✅ AC3: Agent 执行 API

- [ ] 实现 `/api/agent-chat/v1/execute` 端点
- [ ] 支持手动触发 Agent 执行
- [ ] 支持批量 Agent 执行
- [ ] 提供执行状态查询接口
- [ ] 支持执行的暂停和取消

#### ✅ AC4: 与现有系统集成

- [ ] 与现有 `ChatController` 端点完全隔离
- [ ] 复用现有的 conversation 管理
- [ ] 利用现有的 `ChatMessage` 存储机制
- [ ] 确保现有 `/api/chat` 端点行为不变

#### ✅ AC5: 错误处理和监控

- [ ] 实现统一的错误响应格式
- [ ] 提供详细的执行日志和审计
- [ ] 支持性能监控和指标收集
- [ ] 实现请求限流和防护机制

### 技术验收标准

#### 📋 T1: SSE 流式推送

- [ ] 支持多客户端并发连接
- [ ] 实现连接超时和心跳检测
- [ ] 支持断线重连和状态恢复
- [ ] 优化内存使用和连接管理

#### 📋 T2: API 性能

- [ ] 响应时间 < 200ms (非流式端点)
- [ ] SSE 首次响应 < 100ms
- [ ] 支持并发用户数 > 100
- [ ] 内存使用稳定，无泄漏

#### 📋 T3: 安全和权限

- [ ] JWT 令牌验证
- [ ] 用户会话管理
- [ ] API 访问频率限制
- [ ] 数据访问权限控制

## 技术实现指南

### 核心控制器架构

**AgentChatController.java**:

```java
@RestController
@RequestMapping("/api/agent-chat/v1")
@Slf4j
@Validated
@AllArgsConstructor
public class AgentChatController {

    private final AgentOrchestrationService orchestrationService;
    private final ChatService chatService;
    private final AgentExecutionService executionService;
    private final SseEmitterManager sseEmitterManager;

    /**
     * 创建 Agent 对话
     */
    @PostMapping("/conversations")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<ConversationResponse>> createConversation(
            @Valid @RequestBody CreateConversationRequest request,
            Authentication authentication) {

        log.info("Creating agent conversation for user: {}", authentication.getName());

        try {
            Long userId = getUserId(authentication);
            ConversationResponse response = chatService.createConversation(
                request.getTitle(),
                userId,
                ConversationType.AGENT_CHAT
            );

            return ResponseEntity.ok(ApiResponse.success(response));

        } catch (Exception e) {
            log.error("Failed to create agent conversation", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("创建对话失败: " + e.getMessage()));
        }
    }

    /**
     * 发送消息到 Agent 对话
     */
    @PostMapping("/conversations/{conversationId}/messages")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<MessageResponse>> sendMessage(
            @PathVariable String conversationId,
            @Valid @RequestBody SendMessageRequest request,
            Authentication authentication) {

        log.info("Sending message to agent conversation: {}", conversationId);

        try {
            Long userId = getUserId(authentication);

            // 创建编排请求
            OrchestrationRequest orchestrationRequest = OrchestrationRequest.builder()
                .userMessage(request.getContent())
                .conversationId(conversationId)
                .userId(userId)
                .messageType(request.getMessageType())
                .metadata(request.getMetadata())
                .build();

            // 异步执行编排
            Mono<OrchestrationResult> resultMono = orchestrationService.orchestrate(orchestrationRequest);

            // 立即返回消息已接收的响应
            MessageResponse response = MessageResponse.builder()
                .messageId(UUID.randomUUID().toString())
                .conversationId(conversationId)
                .status("PROCESSING")
                .timestamp(LocalDateTime.now())
                .build();

            // 异步处理结果
            resultMono.subscribe(
                result -> handleOrchestrationResult(conversationId, result),
                error -> handleOrchestrationError(conversationId, error)
            );

            return ResponseEntity.ok(ApiResponse.success(response));

        } catch (Exception e) {
            log.error("Failed to send message to agent conversation", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("发送消息失败: " + e.getMessage()));
        }
    }

    /**
     * SSE 流式推送端点
     */
    @GetMapping(value = "/conversations/{conversationId}/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @PreAuthorize("hasRole('USER')")
    public SseEmitter streamConversation(
            @PathVariable String conversationId,
            @RequestParam(defaultValue = "300000") Long timeout,
            Authentication authentication) {

        log.info("Starting SSE stream for conversation: {}", conversationId);

        try {
            Long userId = getUserId(authentication);

            // 验证用户对对话的访问权限
            validateConversationAccess(conversationId, userId);

            // 创建 SSE 连接
            SseEmitter emitter = new SseEmitter(timeout);

            // 注册连接
            sseEmitterManager.registerEmitter(conversationId, userId, emitter);

            // 设置连接事件处理
            emitter.onCompletion(() -> {
                log.info("SSE connection completed for conversation: {}", conversationId);
                sseEmitterManager.removeEmitter(conversationId, userId);
            });

            emitter.onTimeout(() -> {
                log.info("SSE connection timeout for conversation: {}", conversationId);
                sseEmitterManager.removeEmitter(conversationId, userId);
            });

            emitter.onError(ex -> {
                log.error("SSE connection error for conversation: " + conversationId, ex);
                sseEmitterManager.removeEmitter(conversationId, userId);
            });

            // 发送连接成功事件
            SseEvent connectEvent = SseEvent.builder()
                .eventType("CONNECTED")
                .conversationId(conversationId)
                .timestamp(LocalDateTime.now())
                .data(Map.of("status", "connected"))
                .build();

            emitter.send(SseEmitter.event()
                .name("agent-event")
                .data(connectEvent));

            return emitter;

        } catch (Exception e) {
            log.error("Failed to create SSE stream for conversation: " + conversationId, e);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "创建流式连接失败: " + e.getMessage());
        }
    }

    /**
     * 手动执行 Agent
     */
    @PostMapping("/conversations/{conversationId}/execute")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<ExecutionResponse>> executeAgent(
            @PathVariable String conversationId,
            @Valid @RequestBody ExecuteAgentRequest request,
            Authentication authentication) {

        log.info("Manual agent execution for conversation: {}", conversationId);

        try {
            Long userId = getUserId(authentication);

            AgentExecutionRequest executionRequest = AgentExecutionRequest.builder()
                .agentId(request.getAgentId())
                .conversationId(conversationId)
                .inputData(request.getInputData())
                .options(request.getOptions())
                .executionId(UUID.randomUUID())
                .build();

            Mono<AgentExecutionResult> result = executionService.executeAgent(executionRequest);

            ExecutionResponse response = ExecutionResponse.builder()
                .executionId(executionRequest.getExecutionId().toString())
                .status("STARTED")
                .timestamp(LocalDateTime.now())
                .build();

            return ResponseEntity.ok(ApiResponse.success(response));

        } catch (Exception e) {
            log.error("Failed to execute agent", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("执行 Agent 失败: " + e.getMessage()));
        }
    }

    /**
     * 查询执行状态
     */
    @GetMapping("/executions/{executionId}/status")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<ExecutionStatusResponse>> getExecutionStatus(
            @PathVariable String executionId,
            Authentication authentication) {

        try {
            Long userId = getUserId(authentication);

            ExecutionStatusResponse status = executionService.getExecutionStatus(
                UUID.fromString(executionId), userId);

            return ResponseEntity.ok(ApiResponse.success(status));

        } catch (Exception e) {
            log.error("Failed to get execution status", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("查询执行状态失败: " + e.getMessage()));
        }
    }
}
```

### SSE 连接管理器

**SseEmitterManager.java**:

```java
@Component
@Slf4j
public class SseEmitterManager {

    private final Map<String, Map<Long, SseEmitter>> emitters = new ConcurrentHashMap<>();
    private final ScheduledExecutorService heartbeatExecutor = Executors.newScheduledThreadPool(2);

    @PostConstruct
    public void init() {
        // 启动心跳检测
        heartbeatExecutor.scheduleAtFixedRate(this::sendHeartbeat, 30, 30, TimeUnit.SECONDS);
    }

    @PreDestroy
    public void destroy() {
        heartbeatExecutor.shutdown();
    }

    /**
     * 注册 SSE 连接
     */
    public void registerEmitter(String conversationId, Long userId, SseEmitter emitter) {
        emitters.computeIfAbsent(conversationId, k -> new ConcurrentHashMap<>())
                .put(userId, emitter);

        log.info("Registered SSE emitter for conversation: {}, user: {}", conversationId, userId);
    }

    /**
     * 移除 SSE 连接
     */
    public void removeEmitter(String conversationId, Long userId) {
        Map<Long, SseEmitter> conversationEmitters = emitters.get(conversationId);
        if (conversationEmitters != null) {
            conversationEmitters.remove(userId);
            if (conversationEmitters.isEmpty()) {
                emitters.remove(conversationId);
            }
        }

        log.info("Removed SSE emitter for conversation: {}, user: {}", conversationId, userId);
    }

    /**
     * 广播事件到对话的所有连接
     */
    public void broadcast(String conversationId, SseEvent event) {
        Map<Long, SseEmitter> conversationEmitters = emitters.get(conversationId);
        if (conversationEmitters == null || conversationEmitters.isEmpty()) {
            return;
        }

        List<Long> deadConnections = new ArrayList<>();

        conversationEmitters.forEach((userId, emitter) -> {
            try {
                emitter.send(SseEmitter.event()
                    .name("agent-event")
                    .data(event));

                log.debug("Sent SSE event to user: {} in conversation: {}", userId, conversationId);

            } catch (Exception e) {
                log.warn("Failed to send SSE event to user: {} in conversation: {}",
                    userId, conversationId, e);
                deadConnections.add(userId);
            }
        });

        // 清理死连接
        deadConnections.forEach(userId -> removeEmitter(conversationId, userId));
    }

    /**
     * 发送心跳
     */
    private void sendHeartbeat() {
        SseEvent heartbeat = SseEvent.builder()
            .eventType("HEARTBEAT")
            .timestamp(LocalDateTime.now())
            .data(Map.of("timestamp", System.currentTimeMillis()))
            .build();

        emitters.forEach((conversationId, userEmitters) -> {
            broadcast(conversationId, heartbeat);
        });
    }

    /**
     * 获取活跃连接数
     */
    public int getActiveConnectionCount() {
        return emitters.values().stream()
            .mapToInt(Map::size)
            .sum();
    }
}
```

### 事件监听器

**AgentExecutionEventListener.java**:

```java
@Component
@Slf4j
@AllArgsConstructor
public class AgentExecutionEventListener {

    private final SseEmitterManager sseEmitterManager;

    /**
     * 监听 Agent 执行事件
     */
    @EventListener
    @Async("agentExecutor")
    public void handleAgentExecutionEvent(AgentExecutionEvent event) {
        log.debug("Handling agent execution event: {}", event.getEventType());

        try {
            // 转换为 SSE 事件
            SseEvent sseEvent = convertToSseEvent(event);

            // 广播到相关对话
            if (event.getConversationId() != null) {
                sseEmitterManager.broadcast(event.getConversationId(), sseEvent);
            }

        } catch (Exception e) {
            log.error("Failed to handle agent execution event", e);
        }
    }

    /**
     * 监听编排事件
     */
    @EventListener
    @Async("agentExecutor")
    public void handleOrchestrationEvent(OrchestrationEvent event) {
        log.debug("Handling orchestration event: {}", event.getEventType());

        try {
            SseEvent sseEvent = convertOrchestrationToSseEvent(event);

            if (event.getConversationId() != null) {
                sseEmitterManager.broadcast(event.getConversationId(), sseEvent);
            }

        } catch (Exception e) {
            log.error("Failed to handle orchestration event", e);
        }
    }

    private SseEvent convertToSseEvent(AgentExecutionEvent event) {
        return SseEvent.builder()
            .eventType("AGENT_EXECUTION")
            .conversationId(event.getConversationId())
            .agentName(event.getAgentName())
            .stepId(event.getStepId())
            .timestamp(event.getTimestamp())
            .data(Map.of(
                "executionId", event.getExecutionId(),
                "status", event.getStatus(),
                "eventType", event.getEventType(),
                "data", event.getData() != null ? event.getData() : Map.of()
            ))
            .build();
    }

    private SseEvent convertOrchestrationToSseEvent(OrchestrationEvent event) {
        return SseEvent.builder()
            .eventType("ORCHESTRATION")
            .conversationId(event.getConversationId())
            .timestamp(event.getTimestamp())
            .data(Map.of(
                "orchestrationId", event.getOrchestrationId(),
                "flowName", event.getFlowName(),
                "currentStep", event.getCurrentStep(),
                "totalSteps", event.getTotalSteps(),
                "status", event.getStatus(),
                "progress", event.getProgress()
            ))
            .build();
    }
}
```

### 数据传输对象

**请求对象**:

```java
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Validated
public class SendMessageRequest {

    @NotBlank(message = "消息内容不能为空")
    @Size(max = 10000, message = "消息内容不能超过10000字符")
    private String content;

    @Builder.Default
    private MessageType messageType = MessageType.USER;

    private Map<String, Object> metadata;
}

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Validated
public class ExecuteAgentRequest {

    @NotNull(message = "Agent ID 不能为空")
    private UUID agentId;

    private Map<String, Object> inputData;

    private ExecutionOptions options;
}

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Validated
public class CreateConversationRequest {

    @Size(max = 200, message = "对话标题不能超过200字符")
    private String title;

    private Map<String, Object> metadata;
}
```

**响应对象**:

```java
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SseEvent {
    private String eventType;
    private String conversationId;
    private String agentName;
    private String stepId;
    private LocalDateTime timestamp;
    private Map<String, Object> data;
}

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExecutionStatusResponse {
    private String executionId;
    private String status;
    private Double progress;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Map<String, Object> result;
    private String errorMessage;
}
```

## 测试策略

### 单元测试

**AgentChatControllerTest.java**:

```java
@WebMvcTest(AgentChatController.class)
@MockBean({AgentOrchestrationService.class, ChatService.class,
          AgentExecutionService.class, SseEmitterManager.class})
class AgentChatControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private AgentOrchestrationService orchestrationService;

    @MockBean
    private ChatService chatService;

    @Test
    @WithMockUser(roles = "USER")
    void should_create_conversation_successfully() throws Exception {
        // Given
        CreateConversationRequest request = CreateConversationRequest.builder()
            .title("Test Conversation")
            .build();

        ConversationResponse response = ConversationResponse.builder()
            .conversationId("test-conv-001")
            .title("Test Conversation")
            .build();

        when(chatService.createConversation(any(), any(), any())).thenReturn(response);

        // When & Then
        mockMvc.perform(post("/api/agent-chat/v1/conversations")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.conversationId").value("test-conv-001"));
    }

    @Test
    @WithMockUser(roles = "USER")
    void should_send_message_successfully() throws Exception {
        // Given
        SendMessageRequest request = SendMessageRequest.builder()
            .content("分析销售数据")
            .messageType(MessageType.USER)
            .build();

        OrchestrationResult result = OrchestrationResult.builder()
            .status(OrchestrationStatus.COMPLETED)
            .build();

        when(orchestrationService.orchestrate(any())).thenReturn(Mono.just(result));

        // When & Then
        mockMvc.perform(post("/api/agent-chat/v1/conversations/test-conv-001/messages")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.status").value("PROCESSING"));
    }
}
```

### SSE 集成测试

**SseStreamingIntegrationTest.java**:

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb"
})
class SseStreamingIntegrationTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @LocalServerPort
    private int port;

    @Test
    void should_stream_agent_events() throws Exception {
        // Given
        String conversationId = "test-conv-001";
        String url = "http://localhost:" + port + "/api/agent-chat/v1/conversations/"
                    + conversationId + "/stream";

        CountDownLatch latch = new CountDownLatch(1);
        List<String> receivedEvents = new ArrayList<>();

        // When
        CompletableFuture.runAsync(() -> {
            try {
                URL sseUrl = new URL(url);
                HttpURLConnection connection = (HttpURLConnection) sseUrl.openConnection();
                connection.setRequestProperty("Accept", "text/event-stream");
                connection.setRequestProperty("Authorization", "Bearer " + generateTestToken());

                BufferedReader reader = new BufferedReader(
                    new InputStreamReader(connection.getInputStream()));

                String line;
                while ((line = reader.readLine()) != null && receivedEvents.size() < 3) {
                    if (line.startsWith("data:")) {
                        receivedEvents.add(line.substring(5));
                    }
                }

                reader.close();
                latch.countDown();

            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        // 发送一些测试事件
        sendTestAgentEvent(conversationId);

        // Then
        latch.await(10, TimeUnit.SECONDS);
        assertThat(receivedEvents).isNotEmpty();
        assertThat(receivedEvents.get(0)).contains("CONNECTED");
    }
}
```

## 定义完成 (Definition of Done)

### 功能完成

- [ ] 所有验收标准通过测试
- [ ] Agent Chat API 端点稳定运行
- [ ] SSE 流式推送正常工作
- [ ] 与现有系统完全兼容
- [ ] 代码审查完成并批准

### 质量保证

- [ ] 单元测试覆盖率 ≥ 85%
- [ ] 集成测试覆盖主要 API 场景
- [ ] SSE 连接稳定性测试通过
- [ ] 并发压力测试通过 (100+ 用户)
- [ ] 内存泄漏测试通过

### 性能验证

- [ ] API 响应时间 < 200ms
- [ ] SSE 首次响应 < 100ms
- [ ] 支持并发连接数 > 100
- [ ] 内存使用稳定

### 安全验证

- [ ] JWT 认证正常工作
- [ ] 权限控制有效
- [ ] API 访问频率限制正常
- [ ] 数据访问安全

## 风险和依赖

### 风险识别

- **高风险**: SSE 连接管理复杂性和内存泄漏
- **中风险**: 高并发下的性能问题
- **中风险**: 与现有 API 的兼容性
- **低风险**: 客户端断线重连处理

### 缓解措施

- 实现完善的连接生命周期管理
- 充分的负载测试和性能调优
- 建立完整的监控和告警机制
- 实现优雅的降级处理

### 依赖项

- **AGENT-002**: Agent 执行引擎和编排服务必须完成
- 现有 ChatService 接口稳定
- 现有认证和权限系统正常
- 前端 SSE 客户端能力

## 后续故事

完成此故事后，下一个故事将是：

- **Story #004**: 前端 Agent 对话界面和组件
- **Story #005**: Agent 管理和配置界面

---

**故事状态**: Ready for Development  
**最后更新**: 2025-08-03  
**负责人**: Backend Developer  
**预计完成**: AGENT-002 完成后 2-3 工作日
