## AI Agent 系统架构设计

### 1. 核心设计理念

- **模块化 Agent 设计**：每个 Agent 是一个独立的、可复用的智能单元，拥有特定的技能和提示词。
- **编排式协作**：通过一个明确定义的工作流（Workflow），将多个 Agent 按序或并行组合，以完成复杂任务。
- **动态与静态混合编排**：支持静态预定义的工作流，同时允许引入 **监管 Agent (Supervisor Agent)** 进行动态的、基于上下文的流程控制。
- **配置驱动**：Agent 的能力、提示词、所用模型均通过配置管理，而非硬编码，以实现敏捷调整。
- **上下文驱动的状态管理**：工作流执行期间，有一个共享的上下文（`WorkflowContext`）在 Agent 之间传递，用于状态管理和数据交换。

### 2. 系统架构图

#### 2.1 宏观架构

```
┌─────────────────────────────────────────────────────────────┐
│                    前端用户界面层 (Vue/React)                   │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐  │
│  │   工作流设计器   │ │   Agent管理界面  │ │   任务监控仪表盘  │  │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                               ↓ REST API / WebSocket
┌─────────────────────────────────────────────────────────────┐
│                 后端应用服务层 (Spring Boot)                    │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐  │
│  │  工作流编排服务  │ │  Agent管理服务   │ │   模型管理服务   │  │
│  │ WorkflowService │ │ AgentMgmtService│ │ ModelMgmtService│  │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                               ↓ 逻辑调用
┌─────────────────────────────────────────────────────────────┐
│                  Agent 执行与模型路由核心                     │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐  │
│  │  工作流编排引擎  │ │   模型服务路由   │ │   计费与鉴权     │  │
│  │ WorkflowExecutor│ │ ModelRouter     │ │ Billing/Auth    │  │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘  │
└─────────────────────────────────────────────────────────────┘
            ↓ 依赖                                  ↓ 外部调用
┌──────────────────┐                       ┌──────────────────┐
│   数据库 (Postgres) │                       │   AI 模型提供商    │
│   缓存 (Redis)    │                       │  (OpenAI, etc.)  │
└──────────────────┘                       └──────────────────┘
```

#### 2.2 动态编排流程示例 (含监管 Agent)

下图展示了监管 Agent 如何动态地协调其他 Agent 完成任务。

```mermaid
sequenceDiagram
    participant Orchestrator as 工作流编排引擎
    participant Supervisor as 监管 Agent
    participant AgentA as 数据收集 Agent
    participant AgentB as 分析总结 Agent

    Orchestrator->>Supervisor: execute(context) // 启动工作流，首先调用监管Agent
    Supervisor-->>Orchestrator: 返回执行计划 (Plan: "运行 AgentA 收集数据")

    Orchestrator->>AgentA: execute(input, context) // 根据计划执行AgentA
    AgentA-->>Orchestrator: 返回结果 (Result A: 原始数据)
    Orchestrator->>Orchestrator: 更新工作流上下文，存入 Result A

    Orchestrator->>Supervisor: execute(context) // 再次调用监管者，让其决策下一步
    Supervisor->>Supervisor: (分析上下文中的 Result A)
    Supervisor-->>Orchestrator: 返回新计划 (Plan: "使用 Result A 的数据，运行 AgentB 进行分析")

    Orchestrator->>AgentB: execute(input from Result A, context) // 根据新计划执行AgentB
    AgentB-->>Orchestrator: 返回结果 (Result B: 分析报告)
    Orchestrator->>Orchestrator: 更新工作流上下文，存入 Result B
    Orchestrator-->>User: 返回最终结果或按需继续循环
```

### 3. 核心组件代码设计

#### 3.1 Agent 与执行上下文

**Agent 接口**
为简化设计，所有 Agent (包括监管 Agent) 都实现同一个接口。流式处理是首选模式。

```java
// Agent核心接口
public interface Agent {
    /**
     * Agent 的唯一标识
     */
    String getAgentId();

    /**
     * 流式执行方法。这是Agent的核心业务逻辑。
     * @param input 当前步骤的输入数据
     * @param context 整个工作流共享的上下文，用于读取依赖数据和写入结果
     * @return 返回一个包含输出块的流。对于监管Agent，其内容是ExecutionPlan。
     */
    Stream<AgentOutputChunk> execute(AgentInput input, WorkflowContext context);

    /**
     * [可选] 同步执行方法。提供一个默认实现，通过收集流来完成。
     */
    default AgentResult executeSync(AgentInput input, WorkflowContext context) {
        List<AgentOutputChunk> chunks = execute(input, context).toList();
        // 将chunks聚合成一个完整的AgentResult
        return aggregateChunks(chunks);
    }
}

// Agent输入封装
public record AgentInput(Map<String, Object> data) {}

// 标准Agent输出结果
public record AgentResult(Object result, ModelUsage usage, String thoughtProcess) {}

// 流式输出块
public record AgentOutputChunk(String contentDelta, String thoughtProcessDelta, ModelUsage finalUsage) {}

// 新增：Agent类型枚举
public enum AgentType {
    WORKER,      // 工作Agent，负责具体任务如数据处理、分析、生成
    SUPERVISOR   // 监管Agent，负责动态规划和协调
}
```

**工作流上下文 (`WorkflowContext`)**
上下文是 Agent 间数据交换和状态管理的核心机制。

```java
public class WorkflowContext {
    private final String executionId;
    private final Map<String, Object> globalParameters; // 工作流级别的初始输入参数
    private final Map<String, AgentResult> stepResults; // 每个已完成步骤的完整结果

    public WorkflowContext(String executionId, Map<String, Object> globalParameters) {
        this.executionId = executionId;
        this.globalParameters = new ConcurrentHashMap<>(globalParameters);
        this.stepResults = new ConcurrentHashMap<>();
    }

    public void addStepResult(String stepId, AgentResult result) {
        stepResults.put(stepId, result);
    }

    /**
     * 使用类似于JSONPath的表达式从上下文中提取数据。
     * 这是实现Agent之间参数传递的关键。
     * 例如：
     *  - "${global.company_name}" -> 获取全局输入参数
     *  - "${steps.step1_fetch_news.output.summary}" -> 获取指定步骤的输出结果
     */
    public Optional<Object> getValue(String pathExpression) {
        // 实现JSONPath或类似表达式的解析逻辑
        return Optional.empty();
    }
}
```

#### 3.2 监管 Agent (Supervisor) 与动态编排

监管 Agent 是一种特殊的 Agent，其职责是控制工作流的走向。

**核心职责**:

- **动态决策**: 基于当前 `WorkflowContext` 中的信息（包括之前所有 Agent 的产出），决定下一步应该执行哪个或哪些 Agent。
- **条件分支与循环**: 实现复杂的逻辑，如"如果 A 的结果是肯定的，则执行 B；否则执行 C"，或者"重复执行 D 直到满足某个条件"。
- **参数构造**: 为下一步要执行的 Agent 动态构建输入参数。

**输出格式**:
监管 Agent 的输出是一个结构化的 **执行计划 (`ExecutionPlan`)**，而非普通数据。工作流编排引擎会解析此计划并执行。

```java
// 监管Agent的输出结构
public record ExecutionPlan(List<PlannedStep> nextSteps, String reasoning) {}

// 计划中的每一个步骤
public record PlannedStep(
    String stepId,       // 为这个动态步骤分配一个ID
    String agentId,      // 要调用的Agent
    String modelId,      // [可选] 覆盖Agent的默认模型
    Map<String, Object> input // 直接提供给下一步的输入，无需映射
) {}
```

#### 3.3 工作流与步骤定义

工作流定义支持静态步骤和监管者驱动的动态步骤。

**工作流定义 (存储在 `agent_workflows.definition` JSONB 中)**

```json
{
  "workflowId": "wf_dynamic_market_analysis_01",
  "name": "动态市场分析报告工作流",
  "parameters": ["company_name", "market_sector"],
  "steps": [
    {
      "stepId": "step1_supervisor",
      "agentId": "agent_main_supervisor",
      "isEntryPoint": true
    }
  ]
}
```

- 在这个动态工作流中，我们只定义了一个入口点——监管 Agent。它将驱动整个流程的执行。
- `inputMapping` 是实现 Agent 间参数传递的核心。任何 Agent 都可以通过此机制消费之前步骤的输出。

**`inputMapping` 参数传递示例**
一个静态工作流中的步骤，演示如何接收其他 Agent 的结果作为参数：

```json
 {
    "stepId": "step3_analyze_sentiment",
    "agentId": "agent_sentiment_analyzer",
    "dependencies": ["step1_fetch_news", "step2_fetch_reviews"],
    "inputMapping": {
        "news_content": "${steps.step1_fetch_news.output.content}",
        "review_texts": "${steps.step2_fetch_reviews.output.reviews}"
    }
}
```

### 4. AI 模型服务层设计

#### 4.1 统一模型接口与 DTO

```java
// 统一模型服务接口
public interface AIModelService {
    ChatCompletionResponse complete(ChatCompletionRequest request);
    Stream<ChatCompletionChunk> stream(ChatCompletionRequest request);
    ModelCapabilities getCapabilities(String modelId);
    boolean supports(String modelId); // 判断此实现是否支持该模型
}

// 模型调用请求
public record ChatCompletionRequest(
    String modelId,
    List<Message> messages,
    float temperature,
    int maxTokens,
    String userId // 用于计费和滥用监控
) {}

// 消息结构
public record Message(String role, String content) {}

// 同步完成的响应
public record ChatCompletionResponse(String content, ModelUsage usage, String thoughtProcess) {}

// 流式响应块
public record ChatCompletionChunk(String contentDelta, String thoughtProcessDelta, ModelUsage finalUsage) {}

// 模型Token使用与计费
public record ModelUsage(int inputTokens, int outputTokens, int totalTokens, BigDecimal cost) {}

// 模型能力信息
public record ModelCapabilities(
    String modelId,
    String provider,
    int maxTokens,
    boolean supportsStreaming,
    boolean supportsThoughtProcess,
    List<String> supportedLanguages
) {}
```

#### 4.2 模型路由与计费

**模型路由器 (`ModelRouterService`)**
此服务是所有模型调用的入口点。

```java
@Service
public class ModelRouterService implements AIModelService {
    private final List<AIModelService> modelServices; // 注入所有AIModelService的实现
    private final BillingService billingService;

    @Override
    public Stream<ChatCompletionChunk> stream(ChatCompletionRequest request) {
        // 1. 计费检查
        BigDecimal estimatedCost = billingService.estimateCost(request);
        if (!billingService.hasSufficientBalance(request.userId(), estimatedCost)) {
            throw new InsufficientBalanceException("余额不足");
        }

        // 2. 路由到正确的服务
        AIModelService service = findServiceForModel(request.modelId());

        // 3. 执行并处理计费
        return service.stream(request)
            .doOnComplete(() -> {
                // 流结束后，根据最终的 ModelUsage 进行扣费
                // 此处逻辑需要确保能获取到最终的usage
            });
    }

    private AIModelService findServiceForModel(String modelId) {
        return modelServices.stream()
            .filter(s -> s.supports(modelId))
            .findFirst()
            .orElseThrow(() -> new ModelNotFoundException("未找到支持 " + modelId + " 的模型服务"));
    }
}
```

**具体模型服务实现示例**

```java
@Service
public class OpenAIModelService implements AIModelService {
    private final OpenAIClient openAIClient;

    @Override
    public boolean supports(String modelId) {
        return modelId.startsWith("gpt-") || modelId.startsWith("o1-");
    }

    @Override
    public Stream<ChatCompletionChunk> stream(ChatCompletionRequest request) {
        // 调用OpenAI的流式API
        return openAIClient.streamChatCompletion(request)
            .map(this::convertToAgentChunk);
    }
}

@Service
public class ClaudeModelService implements AIModelService {
    private final AnthropicClient anthropicClient;

    @Override
    public boolean supports(String modelId) {
        return modelId.startsWith("claude-");
    }

    @Override
    public Stream<ChatCompletionChunk> stream(ChatCompletionRequest request) {
        // 调用Anthropic的流式API
        return anthropicClient.streamMessages(request)
            .map(this::convertToAgentChunk);
    }
}
```

### 5. 数据库设计（完整）

#### 5.1 `agent_models` 表 (重用现有字段规范)

```sql
-- 模型注册表 - 遵循现有数据库设计规范
CREATE TABLE agent_models (
    id BIGSERIAL PRIMARY KEY, -- 重用现有的自增ID模式
    model_id VARCHAR(50) UNIQUE NOT NULL, -- e.g., "gpt-4-turbo", "claude-3-opus"
    model_name VARCHAR(100) NOT NULL,
    provider VARCHAR(50) NOT NULL, -- e.g., "OpenAI", "Anthropic", "Google"
    api_endpoint VARCHAR(255), -- 可选，用于自托管模型
    input_price_per_1m_tokens DECIMAL(10, 4) NOT NULL, -- 每百万输入Token的价格
    output_price_per_1m_tokens DECIMAL(10, 4) NOT NULL, -- 每百万输出Token的价格
    supports_streaming BOOLEAN DEFAULT true,
    supports_thought_process BOOLEAN DEFAULT false,
    max_tokens INTEGER,
    status SMALLINT DEFAULT 1 NOT NULL, -- 重用现有状态字段规范: 1-启用，0-禁用
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 重用现有时间字段命名
    updated_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT, -- 重用现有创建人字段
    updated_by BIGINT,
    deleted SMALLINT NOT NULL DEFAULT 0 -- 重用现有软删除字段
);

-- 重用现有的表注释模式
COMMENT ON TABLE agent_models IS 'AI模型注册表';
COMMENT ON COLUMN agent_models.input_price_per_1m_tokens IS '以美元计价的每百万输入Token的价格';
COMMENT ON COLUMN agent_models.output_price_per_1m_tokens IS '以美元计价的每百万输出Token的价格';
COMMENT ON COLUMN agent_models.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN agent_models.deleted IS '删除标记：0-正常，1-已删除';

-- 重用现有的更新时间触发器
CREATE TRIGGER update_agent_models_updated_time
    BEFORE UPDATE ON agent_models
    FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
```

#### 5.2 `agent_agents` 表 (简化设计)

```sql
-- Agent基础信息表 - 简化设计，无租户概念
CREATE TABLE agent_agents (
    id BIGSERIAL PRIMARY KEY,
    agent_id VARCHAR(50) UNIQUE NOT NULL, -- 全局唯一
    name VARCHAR(100) NOT NULL,
    description TEXT,
    agent_type VARCHAR(50) NOT NULL, -- 'WORKER' 或 'SUPERVISOR'
    default_model_id VARCHAR(50) REFERENCES agent_models(model_id),
    status SMALLINT DEFAULT 1 NOT NULL, -- 重用现有状态规范
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT, -- 重用现有创建人字段
    updated_by BIGINT,
    deleted SMALLINT NOT NULL DEFAULT 0
);

-- 表注释
COMMENT ON TABLE agent_agents IS 'Agent基础信息表';
COMMENT ON COLUMN agent_agents.agent_id IS 'Agent标识，全局唯一';
COMMENT ON COLUMN agent_agents.agent_type IS 'Agent类型：WORKER-工作Agent，SUPERVISOR-监管Agent';

-- 创建索引
CREATE INDEX idx_agent_agents_status ON agent_agents(status);
CREATE INDEX idx_agent_agents_type ON agent_agents(agent_type);

-- 重用现有的更新时间触发器
CREATE TRIGGER update_agent_agents_updated_time
    BEFORE UPDATE ON agent_agents
    FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
```

#### 5.3 Agent 提示词模板表

```sql
-- Agent提示词模板表
CREATE TABLE agent_prompt_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_id VARCHAR(50) NOT NULL,
    template_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    content TEXT NOT NULL,
    version VARCHAR(20) DEFAULT '1.0',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    FOREIGN KEY (agent_id) REFERENCES agent_agents(agent_id)
);

-- 提示词版本历史表
CREATE TABLE agent_prompt_versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    template_id VARCHAR(50) NOT NULL,
    version VARCHAR(20) NOT NULL,
    content TEXT NOT NULL,
    change_reason TEXT,
    created_by VARCHAR(50),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    FOREIGN KEY (template_id) REFERENCES agent_prompt_templates(template_id)
);
```

#### 5.4 工作流管理表

```sql
-- 工作流定义表
CREATE TABLE agent_workflows (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    definition JSONB NOT NULL,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 工作流执行记录表
CREATE TABLE agent_workflow_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    execution_id VARCHAR(50) UNIQUE NOT NULL,
    workflow_id VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL,
    input_data JSONB,
    output_data JSONB,
    started_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    error_message TEXT,
    FOREIGN KEY (workflow_id) REFERENCES agent_workflows(workflow_id)
);

-- 步骤执行记录表
CREATE TABLE agent_step_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    execution_id VARCHAR(50) UNIQUE NOT NULL,
    workflow_execution_id VARCHAR(50) NOT NULL REFERENCES agent_workflow_executions(execution_id),
    step_id VARCHAR(50) NOT NULL,
    agent_id VARCHAR(50) NOT NULL,
    model_id_used VARCHAR(50), -- 实际使用的模型ID
    status VARCHAR(20) NOT NULL,
    input_data JSONB,
    output_data JSONB,
    thought_process JSONB, -- 模型的思维链或中间步骤
    usage_data JSONB, -- 记录本次执行的token使用和费用
    started_at TIMESTAMPTZ NOT NULL,
    completed_at TIMESTAMPTZ,
    error_message TEXT
);

CREATE INDEX idx_step_executions_workflow_exec_id ON agent_step_executions(workflow_execution_id);
CREATE INDEX idx_workflow_executions_workflow_id ON agent_workflow_executions(workflow_id);
CREATE INDEX idx_workflow_executions_status ON agent_workflow_executions(status);
```

### 6. 工作流编排引擎 (`WorkflowExecutor`) 逻辑

工作流编排引擎的逻辑需要更新，以支持动态计划。

1.  从数据库加载工作流定义。
2.  初始化一个空的执行队列和 `WorkflowContext`。
3.  找到入口步骤（`isEntryPoint: true`）并将其加入队列。
4.  开始循环执行队列中的步骤，直到队列为空：
    a. 取出一个步骤 (`currentStep`)。
    b. 解析 `inputMapping`，从 `WorkflowContext` 中准备 `AgentInput`。
    c. 调用 `Agent.execute()`。
    d. **判断 Agent 类型**：
    i. **如果是 `SUPERVISOR`**: - 将其返回的 `ExecutionPlan` 解析出来。 - 将计划中的 `PlannedStep` 加入到执行队列的**前端**，以便立即执行。
    ii. **如果是 `WORKER`**: - 将返回的 `AgentResult` 存入 `WorkflowContext`，键为 `currentStep.stepId`。 - 查找依赖于 `currentStep` 的后续静态步骤，如果其所有依赖都已完成，则将其加入队列。
5.  当队列为空时，工作流执行结束。

```java
@Service
public class WorkflowExecutorService {

    @Async
    public CompletableFuture<WorkflowExecutionResult> executeWorkflowAsync(
            String workflowId, Map<String, Object> parameters) {

        String executionId = UUID.randomUUID().toString();
        WorkflowContext context = new WorkflowContext(executionId, parameters);
        Queue<ExecutionStep> executionQueue = new LinkedList<>();

        // 加载工作流定义并找到入口点
        WorkflowDefinition workflow = workflowRepository.findByWorkflowId(workflowId);
        workflow.getSteps().stream()
            .filter(step -> step.isEntryPoint())
            .forEach(step -> executionQueue.offer(new ExecutionStep(step.getStepId(), step.getAgentId())));

        // 执行循环
        while (!executionQueue.isEmpty()) {
            ExecutionStep currentStep = executionQueue.poll();
            Agent agent = agentRegistry.getAgent(currentStep.getAgentId());

            AgentInput input = prepareAgentInput(currentStep, context);
            AgentResult result = agent.executeSync(input, context);

            if (agent.getAgentType() == AgentType.SUPERVISOR) {
                ExecutionPlan plan = (ExecutionPlan) result.result();
                // 将计划中的步骤加入队列前端
                plan.nextSteps().forEach(plannedStep ->
                    executionQueue.addFirst(new ExecutionStep(plannedStep.stepId(), plannedStep.agentId())));
            } else {
                context.addStepResult(currentStep.getStepId(), result);
                // 检查并加入依赖已满足的后续步骤
                addReadySteps(workflow, context, executionQueue);
            }
        }

        return CompletableFuture.completedFuture(new WorkflowExecutionResult(executionId, context));
    }
}
```

### 7. API & 服务层设计

#### 7.1 Agent 管理 API (集成现有认证权限体系)

**重用现有的统一 API 响应格式和权限控制机制**：

```java
@RestController
@RequestMapping("/api/v1/agents")
@RequiredArgsConstructor
@Validated
public class AgentController {

    // 创建Agent - 重用现有权限注解
        @PreAuthorize("@ss.hasPerm('agent:create')")
    @PostMapping
    public ApiResponse<AgentVO> createAgent(@Valid @RequestBody CreateAgentRequest request) {
        // 获取当前用户ID
        Long userId = SecurityUtils.getCurrentUserId();

        AgentVO agent = agentService.createAgent(userId, request);
        return ApiResponse.success(agent);
    }

    // 获取Agent详情
    @PreAuthorize("@ss.hasPerm('agent:read')")
    @GetMapping("/{agentId}")
    public ApiResponse<AgentVO> getAgent(@PathVariable String agentId) {
        AgentVO agent = agentService.getAgent(agentId);
        return ApiResponse.success(agent);
    }

    // 更新Agent
    @PreAuthorize("@ss.hasPerm('agent:update')")
    @PutMapping("/{agentId}")
    public ApiResponse<AgentVO> updateAgent(@PathVariable String agentId,
            @Valid @RequestBody UpdateAgentRequest request) {
        Long userId = SecurityUtils.getCurrentUserId();

        AgentVO agent = agentService.updateAgent(userId, agentId, request);
        return ApiResponse.success(agent);
    }
}
```

**API 端点详情**：

- `POST /api/v1/agents`: 创建 Agent (权限: `agent:create`)
- `GET /api/v1/agents/{agentId}`: 获取 Agent 详情 (权限: `agent:read`)
- `PUT /api/v1/agents/{agentId}`: 更新 Agent (权限: `agent:update`)
- `DELETE /api/v1/agents/{agentId}`: 删除 Agent (权限: `agent:delete`)
- `GET /api/v1/agents/{agentId}/prompts`: 获取提示词模板 (权限: `agent:prompt:read`)
- `POST /api/v1/agents/{agentId}/prompts`: 创建提示词模板 (权限: `agent:prompt:create`)
- `PUT /api/v1/agents/{agentId}/prompts/{templateId}`: 更新提示词模板 (权限: `agent:prompt:update`)

#### 7.2 工作流 API

- `POST /api/v1/workflows`: 创建一个新的工作流定义。
  - **Request Body**: `WorkflowDefinition` (JSON 格式)
- `GET /api/v1/workflows/{workflowId}`: 获取工作流定义。
- `PUT /api/v1/workflows/{workflowId}`: 更新工作流定义。
- `DELETE /api/v1/workflows/{workflowId}`: 删除工作流。
- `POST /api/v1/workflows/{workflowId}/execute`: 同步执行工作流。
  - **Request Body**: `ExecuteRequest { parameters: {...} }`
  - **Response Body**: `WorkflowExecutionResult`
- `POST /api/v1/workflows/{workflowId}/execute-stream`: **(核心)** 异步流式执行工作流。
  - 返回 `text/event-stream` 类型的 SSE 连接。
- `GET /api/v1/workflows/{workflowId}/executions`: 获取工作流执行历史。
- `GET /api/v1/workflows/executions/{executionId}`: 获取具体执行详情。
- `DELETE /api/v1/workflows/executions/{executionId}`: 取消正在执行的工作流。

#### 7.3 模型管理 API

- `GET /api/v1/models`: 获取所有可用的模型列表。
- `POST /api/v1/models`: 注册一个新的模型。
- `PUT /api/v1/models/{modelId}`: 更新模型信息（如价格）。
- `DELETE /api/v1/models/{modelId}`: 删除模型注册。
- `GET /api/v1/models/{modelId}/capabilities`: 获取模型能力信息。

#### 7.4 服务层设计

```java
@Service
@Transactional
public class AgentManagementService {

    // Agent注册
    public Agent registerAgent(AgentRegistrationRequest request);

    // 提示词模板管理
    public PromptTemplate createPromptTemplate(PromptTemplateRequest request);
    public PromptTemplate updatePromptTemplate(String templateId, PromptTemplateRequest request);
    public void activatePromptTemplate(String templateId);
    public void deactivatePromptTemplate(String templateId);

    // Agent配置管理
    public AgentConfig getAgentConfig(String agentId);
    public void updateAgentConfig(String agentId, AgentConfig config);
}

@Service
@Transactional
public class WorkflowOrchestrationService {

    // 工作流定义管理
    public WorkflowDefinition createWorkflow(WorkflowDefinitionRequest request);
    public WorkflowDefinition updateWorkflow(String workflowId, WorkflowDefinitionRequest request);
    public void deleteWorkflow(String workflowId);

    // 工作流执行
    public WorkflowExecution startWorkflow(String workflowId, Map<String, Object> input);
    public WorkflowExecution getWorkflowExecution(String executionId);
    public void cancelWorkflowExecution(String executionId);

    // 工作流监控
    public List<WorkflowExecution> getWorkflowExecutions(String workflowId, Pageable pageable);
    public WorkflowStatistics getWorkflowStatistics(String workflowId);
}

@Service
@Transactional
public class AgentExecutionService {

    // 单个Agent执行
    public AgentResult executeAgent(String agentId, AgentInput input);

    // 单个Agent流式执行
    public Stream<AgentOutputChunk> executeAgentStream(String agentId, AgentInput input);

    // 批量Agent执行
    public List<AgentResult> executeAgents(List<String> agentIds, AgentInput input);

    // Agent状态管理
    public AgentStatus getAgentStatus(String agentId);
    public void updateAgentStatus(String agentId, AgentStatus status);

    // 执行历史查询
    public List<AgentExecution> getAgentExecutions(String agentId, Pageable pageable);
}
```

### 8. 前端界面设计（详细设计）

#### 8.1 整体架构设计

**与现有聊天模块的核心区分**：

AI Agent 系统采用独立的前端模块设计，与现有的 `/chat` 路由完全分离：

```typescript
// 路由结构对比
// 现有聊天模块: /chat -> 单一对话界面
// AI Agent模块: /agents -> 多功能管理界面

const agentRoutes = {
  path: '/agents',
  component: () => import('@/layouts/AgentLayout.vue'),
  children: [
    {
      path: 'dashboard',     // Agent总览面板
      name: 'AgentDashboard',
      component: () => import('@/views/agents/Dashboard.vue')
    },
    {
      path: 'management',    // Agent管理
      name: 'AgentManagement',
      component: () => import('@/views/agents/Management.vue')
    },
    {
      path: 'workflows',     // 工作流设计器
      name: 'WorkflowDesigner',
      component: () => import('@/views/agents/WorkflowDesigner.vue')
    },
    {
      path: 'execution',     // 执行监控
      name: 'ExecutionMonitor',
      component: () => import('@/views/agents/ExecutionMonitor.vue')
    },
    {
      path: 'models',        // 模型管理
      name: 'ModelManagement',
      component: () => import('@/views/agents/ModelManagement.vue')
    }
  ]
}
```

**设计区别对比**：

| 功能维度     | 现有聊天模块                                   | AI Agent 模块                    |
| ------------ | ---------------------------------------------- | -------------------------------- |
| **界面布局** | FaLayoutContainer (左侧历史+右侧模板+中央对话) | 多 Tab 页面布局 (管理+设计+监控) |
| **交互模式** | 单轮对话式交互                                 | 配置+编排+监控的管理式交互       |
| **数据流向** | 用户 ↔ AI 直接对话                             | 用户 → Agent 编排 → 多步骤执行   |
| **状态管理** | 会话历史                                       | Agent 配置+工作流状态+执行历史   |
| **组件复用** | 重用 BubbleList 等对话组件                     | 新建管理类组件，避免冲突         |

#### 8.2 Agent 总览面板 (`/agents/dashboard`)

**核心功能**：系统状态概览、快速访问、实时统计

**界面设计**：

- 顶部统计卡片：活跃 Agent 数、今日执行数、成功率、本月费用
- 快速操作区：最近使用的 Agent 列表、热门工作流
- 实时执行监控：当前正在运行的任务时间线

#### 8.3 Agent 管理界面 (`/agents/management`)

**与现有聊天模块的核心区别**：

- 现有聊天：提示词模板在右侧面板，临时使用
- Agent 管理：提示词作为 Agent 的核心配置，持久存储

**主要功能**：

- **Agent 列表**：支持卡片视图和表格视图切换
- **筛选搜索**：按类型（Worker/Supervisor）、状态、模型筛选
- **Agent 卡片**：显示 Agent 基本信息、类型标识、状态指示
- **操作菜单**：编辑、测试、复制、删除、查看执行历史

**Agent 创建/编辑对话框**：

- **基础信息**：名称、类型选择、描述
- **模型配置**：默认模型选择，显示价格信息
- **提示词配置**：
  - 支持模板库选择
  - 实时预览功能
  - 变量占位符提示
  - 版本历史管理

#### 8.4 工作流可视化设计器 (`/agents/workflows`)

**设计理念**：

- 与现有聊天的线性对话不同，工作流是多步骤的有向图
- 支持拖拽式设计，可视化连线
- 实时预览和验证

**界面布局**：

- **左侧工具箱**：
  - Agent 节点：工作 Agent、监管 Agent
  - 控制节点：条件分支、并行执行、循环执行
  - 输入输出节点：数据输入、结果输出
- **中央画布**：
  - 工具栏：保存、验证、预览执行
  - 可视化画布：支持缩放、拖拽、连线
  - 小地图：全局视图导航
- **右侧属性面板**：
  - 节点属性编辑器
  - 连线条件设置
  - 工作流全局配置

**核心功能**：

- **拖拽式设计**：从工具箱拖拽节点到画布
- **智能连线**：自动检测连接点，防止无效连接
- **参数映射**：可视化配置 Agent 间的数据传递
- **实时验证**：检查工作流完整性和逻辑正确性

#### 8.5 执行监控界面 (`/agents/execution`)

**核心特色**：

- 与现有聊天的即时响应不同，Agent 执行可能是长时间的复杂流程
- 支持实时监控、日志查看、错误诊断

**主要功能**：

- **执行列表**：
  - 状态筛选：执行中、已完成、失败、已取消
  - 进度显示：步骤进度条、完成状态
  - 耗时统计：执行时长、费用统计
  - 批量操作：停止执行、重新运行
- **执行详情**：
  - 步骤时间线：每个步骤的执行状态
  - 日志流式显示：实时输出内容
  - 思维链展示：AI 推理过程可视化
  - 错误诊断：失败原因分析和建议

#### 8.6 模型管理界面 (`/agents/models`)

**设计目标**：

- 统一管理所有 AI 模型的接入和配置
- 实时监控模型可用性和性能
- 成本分析和优化建议

**主要功能**：

- **模型概览**：已接入数量、在线状态、调用统计
- **模型列表**：
  - 基本信息：名称、提供商、定价
  - 能力标识：流式支持、思维链、Token 限制
  - 健康状态：实时可用性监控
  - 使用统计：调用次数、平均延迟、成本分析
- **模型配置**：
  - 添加新模型：配置 API 密钥、端点
  - 参数调优：温度、Token 限制等
  - 健康检查：定期测试模型可用性

#### 8.7 组件复用策略

**重用现有组件**：

- `FaLayoutContainer`: 基础布局容器
- `FaCard`, `FaButton`, `FaInput` 等基础 UI 组件
- 表格、表单、模态框等通用组件
- 图表组件：用于执行统计和性能监控

**新建专用组件**：

- `AgentCard`: Agent 信息卡片展示
- `WorkflowCanvas`: 工作流可视化画布
- `ExecutionTimeline`: 执行过程时间线
- `NodePropertiesEditor`: 节点属性配置器
- `ThoughtChainViewer`: 思维链可视化组件

**避免冲突的命名规范**：

```typescript
// 现有聊天组件前缀: Chat*
// ChatMessageList, ChatSender, ChatWelcome

// Agent组件前缀: Agent*, Workflow*, Execution*
// AgentCard, AgentList, WorkflowDesigner, ExecutionMonitor
```

#### 8.8 状态管理设计

**独立的状态管理**：

```typescript
// 与现有聊天状态分离
export const useAgentStore = defineStore('agent', {
  state: () => ({
    agents: [],
    workflows: [],
    executions: [],
    models: [],
    currentExecution: null
  }),

  actions: {
    // Agent管理
    async fetchAgents() { },
    async createAgent(agentData) { },
    async updateAgent(agentId, agentData) { },

    // 工作流管理
    async saveWorkflow(workflowData) { },
    async executeWorkflow(workflowId, params) { },

    // 执行监控
    async fetchExecutions() { },
    async cancelExecution(executionId) { }
  }
})
```

#### 8.9 实时通信设计

**WebSocket 连接管理**：

```typescript
// 专用于Agent系统的WebSocket连接
export class AgentWebSocketManager {
  private ws: WebSocket

  // 订阅执行状态更新
  subscribeExecutionUpdates(executionId: string) {
    this.ws.send(JSON.stringify({
      type: 'subscribe',
      topic: `execution.${executionId}`
    }))
  }

  // 处理流式输出
  onStreamingOutput(callback: (data: any) => void) {
    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data)
      if (data.type === 'streaming_output') {
        callback(data.content)
      }
    }
  }
}
```

这样的设计确保了 AI Agent 系统与现有聊天模块的完全分离，同时保持了统一的设计语言和用户体验。Agent 系统专注于复杂的工作流管理和执行监控，而聊天模块专注于简单的对话交互，两者各司其职，互不干扰。

### 9. 扩展性设计

#### 9.1 插件化架构

- **Agent 插件接口**: 标准化的 Agent 开发接口。
- **动态加载**: 支持运行时加载新的 Agent。
- **版本管理**: Agent 版本控制和兼容性管理。

#### 9.2 多模型支持

- **模型抽象层**: 统一的 AI 模型调用接口。
- **模型路由**: 根据任务类型自动选择合适的模型。
- **成本优化**: 根据任务复杂度选择性价比最优的模型。

#### 9.3 分布式执行

- **任务分发**: 支持多实例部署和负载均衡。
- **状态同步**: 分布式环境下的状态一致性保证。
- **故障恢复**: 节点故障时的任务重试和恢复机制。

### 10. 现有系统功能重用分析

#### 10.1 认证授权系统重用

**重用现有的 Spring Security + JWT 认证系统**：

```java
// 直接重用现有的权限检查服务
@Service
public class AgentPermissionService {
    private final PermissionService existingPermissionService;

    public boolean hasAgentPermission(String agentId, String operation) {
        String requiredPerm = String.format("agent:%s:%s", agentId, operation);
        return existingPermissionService.hasPerm(requiredPerm);
    }

    public boolean hasWorkflowPermission(String workflowId, String operation) {
        String requiredPerm = String.format("workflow:%s:%s", workflowId, operation);
        return existingPermissionService.hasPerm(requiredPerm);
    }
}
```

**重用现有的用户认证信息**：

- 通过 `SecurityUtils.getCurrentUserId()` 获取当前用户
- 通过 `SecurityUtils.getCurrentTenantId()` 获取当前租户
- Agent 执行时自动记录操作用户和租户信息

#### 10.2 计费系统完全重用

**直接复用现有的完整计费体系**：

```java
// Agent 模型调用计费服务 - 重用现有计费逻辑
@Service
public class AgentBillingService {
    private final BillingService existingBillingService;
    private final UserBalanceService userBalanceService;

    public BillingUsageRecord performAgentBilling(Long userId, String agentId,
            Long inputTokens, Long outputTokens, String modelId) {
        // 重用现有计费方法，增加 Agent 类型信息
        UUID messageId = UUID.randomUUID(); // Agent执行ID
        return existingBillingService.performBilling(
            userId, messageId, inputTokens, outputTokens,
            LocalDateTime.now(), LocalDateTime.now(),
            "AGENT:" + agentId + ":" + modelId
        );
    }

    public boolean preCheckAgentBilling(Long userId) {
        // 直接重用现有的预检查逻辑
        return existingBillingService.preCheckBilling(userId, null, null);
    }
}
```

**重用现有的余额管理**：

- `UserBalanceService.getOrCreateUserBalance()` 获取用户余额
- `UserBalanceService.deductBalance()` 执行扣费
- `UserBalanceService.isBalanceNegative()` 检查余额状态

#### 10.3 简化的数据管理

**无租户概念的简化设计**：

```java
// Agent 数据表结构 - 简化设计
CREATE TABLE agent_agents (
    id BIGSERIAL PRIMARY KEY,
    agent_id VARCHAR(50) UNIQUE NOT NULL, -- 全局唯一
    name VARCHAR(100) NOT NULL,
    -- 其他字段...
);

// 工作流表 - 简化设计
CREATE TABLE agent_workflows (
    id BIGSERIAL PRIMARY KEY,
    workflow_id VARCHAR(50) UNIQUE NOT NULL, -- 全局唯一
    name VARCHAR(100) NOT NULL,
    -- 其他字段...
);
```

**简化的权限管理**：

- Agent 和工作流全局共享，无租户隔离
- 通过现有的用户权限系统控制访问
- 配额限制基于用户级别而非租户级别

#### 10.4 审计日志系统重用

**重用现有的操作日志系统**：

```java
// Agent 操作日志记录 - 扩展现有日志实体
@Component
public class AgentAuditService {
    private final SysOperationLogService operationLogService;

    public void recordAgentExecution(String agentId, String operation,
            Object params, Object result, Long costTime) {
        SysOperationLog log = new SysOperationLog();
        log.setTenantId(SecurityUtils.getCurrentTenantId());
        log.setUserId(SecurityUtils.getCurrentUserId());
        log.setUsername(SecurityUtils.getCurrentUsername());
        log.setOperationType("AGENT_EXECUTE");
        log.setModule("AI_AGENT");
        log.setBusinessType("AGENT");
        log.setBusinessId(Long.valueOf(agentId.hashCode()));
        log.setOperationDesc(String.format("执行Agent: %s, 操作: %s", agentId, operation));
        log.setRequestParams(JsonUtils.toJson(params));
        log.setResponseResult(JsonUtils.toJson(result));
        log.setCostTime(costTime);
        log.setStatus(1); // 成功

        // 异步记录日志
        operationLogService.recordOperationLogAsync(log);
    }
}
```

#### 10.5 缓存和异步系统重用

**重用现有的 Redis 缓存配置**：

```java
// Agent 配置缓存 - 使用现有 Redis 配置
@Service
public class AgentCacheService {
    private final RedisTemplate<String, Object> redisTemplate;

    private static final String AGENT_CONFIG_PREFIX = "agent:config:";
    private static final String WORKFLOW_DEF_PREFIX = "workflow:def:";

    @Cacheable(value = "agentConfig", key = "#agentId")
    public AgentConfig getAgentConfig(String agentId) {
        // 缓存Agent配置
    }

    @Cacheable(value = "workflowDef", key = "#workflowId")
    public WorkflowDefinition getWorkflowDefinition(String workflowId) {
        // 缓存工作流定义
    }
}
```

**重用现有的异步任务管理**：

```java
// Agent 异步执行 - 重用现有异步任务框架
@Service
public class AgentAsyncExecutionService {
    private final GlobalAsyncTaskService globalAsyncTaskService;

    public String executeAgentAsync(String agentId, AgentInput input) {
        CompletableFuture<AgentResult> future = CompletableFuture.supplyAsync(() -> {
            // Agent 执行逻辑
            return executeAgentSync(agentId, input);
        });

        // 重用现有的任务管理
        return globalAsyncTaskService.submitTask(
            "AI_AGENT",
            GlobalAsyncTaskService.TaskType.TEXT_ENCODE, // 可扩展新的任务类型
            "执行Agent: " + agentId,
            future
        );
    }
}
```

#### 10.6 配置管理系统重用

**重用现有的路由配置管理模式**：

```java
// 模型配置管理 - 参考现有 RouteConfig 模式
@Component
@ConfigurationProperties(prefix = "agent.models")
public class ModelConfig {
    private Map<String, ModelProvider> providers = new HashMap<>();

    @Data
    public static class ModelProvider {
        private String name;
        private String endpoint;
        private String apiKey;
        private boolean enabled = true;
        private Map<String, ModelInfo> models = new HashMap<>();
    }
}
```

#### 10.7 更新后的技术栈

基于现有系统重用，AI Agent 系统的技术栈为：

**后端核心**：

- **认证授权**: 重用现有 Spring Security + JWT + PermissionService
- **计费系统**: 完全重用现有 BillingService + UserBalanceService
- **日志审计**: 重用现有 SysOperationLogService + OperationLogAspect
- **缓存**: 重用现有 RedisTemplate + RedisCacheManager
- **异步任务**: 重用现有 GlobalAsyncTaskService + ThreadPoolTaskExecutor
- **配置管理**: 参考现有 RouteConfig 模式

**新增 Agent 专有组件**：

- **Agent 执行引擎**: 新建 AgentExecutor 和 WorkflowOrchestrator
- **模型服务层**: 新建 AIModelService 和 ModelRouterService
- **提示词管理**: 新建 PromptTemplateService
- **动态编排**: 新建 SupervisorAgent 和 ExecutionPlan

### 11. 重用现有功能的优势

#### 11.1 开发效率提升

- **减少 65% 的基础设施代码**：认证、计费、日志等直接重用
- **统一的数据模型**：用户、权限等保持一致性
- **成熟的运维体系**：监控、告警、备份等无需重复建设

#### 11.2 系统一致性保证

- **统一的权限模型**：Agent 权限与现有系统权限统一管理
- **统一的计费体系**：Agent 使用费用与其他功能费用统一结算
- **统一的审计标准**：Agent 操作日志与系统操作日志格式一致
- **简化的数据模型**：无租户概念，全局共享 Agent 资源

#### 11.3 维护成本降低

- **单一技术栈**：无需维护额外的认证、计费、日志系统
- **统一的配置管理**：Agent 配置与系统配置使用相同的管理方式
- **共享的基础设施**：数据库、缓存、消息队列等资源共享

### 12. 设计总结与开发建议

#### 12.1 现有功能重用统计

通过系统性分析，AI Agent 系统可以重用现有系统约 **65%** 的基础设施：

**完全重用的组件 (100%)**：

- 认证授权系统 (Spring Security + JWT + PermissionService)
- 计费系统 (BillingService + UserBalanceService + Token 计费逻辑)
- 日志审计系统 (SysOperationLogService + OperationLogAspect)
- 缓存系统 (RedisTemplate + RedisCacheManager)
- 异步任务管理 (GlobalAsyncTaskService + ThreadPoolTaskExecutor)

**部分重用的组件 (50-80%)**：

- 数据库表结构设计规范 (字段命名、约束、索引、触发器)
- API 设计模式 (统一响应格式、权限注解、异常处理)
- 配置管理模式 (参考现有 RouteConfig 设计)

**新增的专有组件 (30%)**：

- Agent 执行引擎 (AgentExecutor + WorkflowOrchestrator)
- 模型服务路由层 (AIModelService + ModelRouterService)
- 提示词模板管理 (PromptTemplateService)
- 监管 Agent 协调机制 (SupervisorAgent + ExecutionPlan)

#### 12.2 开发优先级建议

**第一阶段 - 基础集成 (2-3 周)**：

1. 创建 Agent 相关数据库表（重用现有规范）
2. 实现 AgentManagementService（集成现有认证权限）
3. 实现基础的 Agent CRUD API（重用现有 API 模式）
4. 集成现有计费系统进行 Agent 使用计费

**第二阶段 - 核心功能 (3-4 周)**：

1. 实现 AIModelService 模型服务抽象层
2. 实现基础的 WorkerAgent 执行逻辑
3. 集成现有异步任务系统支持 Agent 异步执行
4. 实现提示词模板管理功能

**第三阶段 - 高级功能 (4-5 周)**：

1. 实现工作流编排引擎（静态工作流）
2. 实现 SupervisorAgent 和动态编排能力
3. 完善流式输出和思维链展示
4. 实现前端 Agent 管理界面

#### 12.3 风险控制建议

**数据安全**：

- 通过现有的用户权限系统控制 Agent 访问
- Agent 配置和执行结果按用户权限进行访问控制
- 重用现有的操作审计日志，确保完整的执行链路追踪

**成本控制**：

- 复用现有的 Token 计费和余额管理机制
- 实现 Agent 执行的成本预估和实时监控
- 设置合理的 Agent 调用频率限制和资源配额

**系统稳定性**：

- 重用现有的异步任务管理，避免 Agent 执行阻塞主线程
- 实现 Agent 执行超时机制和错误重试策略
- 建立完善的 Agent 执行性能监控体系

#### 12.4 技术债务最小化

通过重用现有成熟组件，本设计避免了以下技术债务：

- **重复造轮子**：认证、计费、日志等基础功能直接复用
- **架构不一致**：AI Agent 系统与现有系统保持技术栈和设计模式一致
- **维护复杂性**：避免维护多套相似的基础设施代码
- **学习成本**：开发团队无需学习新的技术栈和设计模式

这个优化后的架构设计最大化地重用了现有系统功能，确保了 AI Agent 系统与现有业务系统的无缝集成，同时大幅降低了开发和维护成本。
