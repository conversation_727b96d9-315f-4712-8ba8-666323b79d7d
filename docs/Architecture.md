# DIPS Pro 系统架构设计

## 最新架构变更 (2025-08-06)

### AI Agent 链式工作流架构完整实现 (2025-08-06 17:45:31)

**变更概述**：

完成了 AI Agent 链式工作流架构的完整实现，包括后端执行框架、前端实时体验组件、SSE 流式服务等核心功能。这是一个完全解耦的设计，Java 代码只做通用执行框架，业务逻辑完全由 Agent 的 system_prompt 决定。

**核心架构特点**：

1. **完全解耦的 AI Agent 架构**：
   - Java 代码只做通用执行框架，不包含具体业务逻辑
   - 业务逻辑完全由 Agent 的 system_prompt 决定
   - 支持思维链（Chain of Thought）和实时流式响应
   - 标准化 JSON 输入输出，确保 Agent 间数据传递的一致性

2. **智能意图识别与任务分发**：
   - 意图识别 AI 自主决策链路构建
   - 基于用户输入自动选择最适合的 Agent 组合
   - 支持串行、并行、条件分支等多种执行模式

3. **实时流式响应系统**：
   - SSE（Server-Sent Events）实时推送执行过程给前端
   - 支持思维过程可视化和执行日志实时显示
   - 前端实时体验组件提供完整的用户交互界面

**后端架构实现**：

1. **核心服务组件**：
   ```
   GenericAiAgentEngine         - AI Agent 执行引擎
   GenericChainExecutor         - 链式执行器
   ChainExecutionStreamingService - SSE 流式服务
   AgentExecutionEngine         - 执行引擎包装器
   AiAgentOrchestrationService  - 多 Agent 编排服务
   ```

2. **数据流转设计**：
   ```
   用户输入 → 意图识别Agent → 链路构建 → Agent执行 → 结果返回
            ↓
   实时推送 ← SSE服务 ← 执行状态 ← 思维过程记录
   ```

3. **实体关系优化**：
   ```java
   AgentFlow        - 执行流程定义
   AgentFlowStep    - 流程步骤定义
   AgentDefinition  - Agent 定义和配置
   ```

**前端组件架构**：

1. **核心组件**：
   ```
   ChainExecutionView    - AI Agent 链式执行实时体验组件
   FlowOverview         - 流程概览和可视化组件
   SseService          - SSE 实时连接服务
   AgentChatStore      - 状态管理 Store
   ```

2. **实时体验特性**：
   - 思维链实时显示和可视化
   - 执行进度实时更新
   - 并行执行状态监控
   - 错误处理和重试机制
   - 执行结果导出和分享

3. **用户交互优化**：
   - 全屏模式支持
   - 自动滚动和手动控制
   - 执行暂停/恢复/停止
   - 详细日志和思维过程查看

**技术实现亮点**：

1. **标准化 JSON 协议**：
   ```json
   {
     "response_type": "chain_execution",
     "thinking_process": {
       "analysis": "对用户请求的详细分析",
       "confidence": 0.95
     },
     "chain_instruction": {
       "agent_chain": [...],
       "execution_mode": "sequential"
     }
   }
   ```

2. **SSE 实时推送**：
   - 支持多种事件类型（execution_started, step_progress, thinking_process 等）
   - 自动重连和心跳检测
   - 连接状态管理和错误恢复

3. **类型安全设计**：
   - 完整的 TypeScript 类型定义
   - 前后端数据结构一致性保证
   - 编译时类型检查和运行时数据验证

**文件结构变更**：

```
dp-server/src/main/java/com/dipspro/modules/agent/
├── entity/
│   ├── AgentFlow.java                 [新增] 执行流程实体
│   └── AgentFlowStep.java            [新增] 流程步骤实体
├── service/
│   ├── GenericAiAgentEngine.java     [修改] 增强AI执行引擎
│   ├── GenericChainExecutor.java     [修改] 完善链式执行器
│   ├── ChainExecutionStreamingService.java [新增] SSE流式服务
│   ├── AgentExecutionEngine.java     [新增] 执行引擎包装器
│   └── AiAgentOrchestrationService.java [新增] 多Agent编排
├── dto/
│   ├── AgentExecutionResult.java     [修改] 增加链路指令字段
│   ├── OrchestrationRequest.java     [新增] 编排请求DTO
│   ├── OrchestrationResult.java      [新增] 编排结果DTO
│   └── OrchestrationEvent.java       [新增] 编排事件DTO
└── repository/
    └── AgentFlowStepRepository.java   [修改] 增加查询方法

dp-web-fa/src/
├── components/agent-chat/
│   └── ChainExecutionView.vue        [新增] 链式执行实时体验组件
├── services/
│   └── sseService.ts                 [新增] SSE实时连接服务
├── stores/modules/
│   └── agentChat.ts                  [新增] Agent对话状态管理
├── api/
│   └── agentChat.ts                  [新增] Agent对话API接口
└── types/
    └── agent.ts                      [新增] 完整类型定义
```

**配置和部署**：

1. **意图识别 Agent 配置**：
   - 完整的 system_prompt 设计，包含意图识别策略
   - 业务 Agent 能力矩阵和分发规则
   - 标准 JSON 输出协议和质量控制机制

2. **数据库迁移脚本**：
   - 创建初始意图识别 Agent 配置
   - 支持多个示例业务 Agent（数据分析、内容撰写等）

这次架构变更标志着 DIPS Pro 系统在 AI Agent 协作方面达到了企业级应用的完整功能，为用户提供了智能、直观、实时的 AI 协作体验。

## 历史架构变更

### Agent字段映射优化 - AgentRole自动赋值机制 (2025-08-06 11:16:06)

**变更概述**：

优化了Agent实体的字段设计，解决了创建和更新Agent时agentRole字段未赋值的问题。通过建立category与agentRole的映射关系，实现了用户友好的业务分类与系统架构角色的自动对应。

**架构设计变更**：

1. **双字段设计原则明确**：

   - **`category`**：面向用户的业务分类（字符串类型）
     - 用途：前端展示、用户理解、业务分类
     - 示例：`ANALYSIS`、`PROCESSING`、`REPORTING`、`VISUALIZATION`
   
   - **`agentRole`**：面向系统的架构角色（枚举类型）
     - 用途：系统内部调度、AI Agent架构分工
     - 示例：`GENERAL_ASSISTANT`、`RESPONSE_GENERATION`、`CUSTOM`

2. **自动映射机制**：

   ```java
   private AgentDefinition.AgentRole mapCategoryToAgentRole(String category) {
       switch (category.toUpperCase()) {
           case "INTENT_RECOGNITION":
               return AgentDefinition.AgentRole.INTENT_RECOGNITION;
           case "ANALYSIS":
           case "PROCESSING": 
           case "INFERENCE":
               return AgentDefinition.AgentRole.GENERAL_ASSISTANT;
           case "REPORTING":
           case "VISUALIZATION":
               return AgentDefinition.AgentRole.RESPONSE_GENERATION;
           default:
               return AgentDefinition.AgentRole.CUSTOM;
       }
   }
   ```

3. **服务层增强**：

   - **创建时自动赋值**：如果agentRole为空且category不为空，自动设置对应角色
   - **更新时智能更新**：当category发生变化时，自动重新映射agentRole
   - **向后兼容**：保持前端现有category字段的使用方式不变

**技术实现细节**：

1. **AgentDefinitionServiceImpl.createAgent()增强**：
   ```java
   // 根据category自动设置agentRole
   if (agentDefinition.getAgentRole() == null && agentDefinition.getCategory() != null) {
       agentDefinition.setAgentRole(mapCategoryToAgentRole(agentDefinition.getCategory()));
   }
   ```

2. **AgentDefinitionServiceImpl.updateAgent()增强**：
   ```java
   // 根据category自动设置agentRole（如果category发生变化或agentRole为空）
   if (agentDefinition.getCategory() != null && 
       (agentDefinition.getAgentRole() == null || 
        !agentDefinition.getCategory().equals(existingAgent.getCategory()))) {
       agentDefinition.setAgentRole(mapCategoryToAgentRole(agentDefinition.getCategory()));
   }
   ```

**用户体验优化**：

- **前端无感知变更**：用户继续使用熟悉的业务分类界面
- **系统智能增强**：后端自动为Agent分配合适的系统角色
- **架构一致性**：确保全AI Agent架构中的角色调度机制正常工作

**扩展性考虑**：

- **映射关系可配置化**：未来可将映射关系提取到配置文件
- **多对多映射支持**：为复杂业务场景预留架构空间
- **角色动态扩展**：新增AgentRole时可轻松扩展映射逻辑

## 历史架构变更 (2025-08-05)

### 全AI Agent架构重构 - 统一AI Agent执行引擎和提示词管理 (2025-08-05 15:06:56)

**变更概述**：

将原有的混合架构（Java代码实现的意图识别服务 + AI Agent业务逻辑）重构为完全基于AI Agent的架构，所有智能决策都由AI Agent完成，并实现了统一的Agent执行引擎和可编辑的提示词管理系统。

**架构设计变更**：

1. **全AI Agent架构核心组件**：

   - **`UnifiedAiAgentEngine`**：统一的AI Agent执行引擎，支持所有类型的AI Agent
   - **`AiAgentOrchestrationService`**：全AI Agent编排服务，替代原有的混合编排
   - **`AgentPromptService`**：提示词管理服务，支持动态编辑和版本管理
   - **`AgentPromptTemplate`**：提示词模板实体，支持变量替换和缓存

2. **AI Agent角色体系**（MVP版本）：

   - **意图识别Agent** (`INTENT_RECOGNITION`)：分析用户消息并识别意图
   - **流程编排Agent** (`ORCHESTRATION`)：根据意图设计执行流程
   - **结果聚合Agent** (`RESULT_AGGREGATION`)：整合多个执行结果
   - **响应生成Agent** (`RESPONSE_GENERATION`)：生成友好专业的最终回复
   - **工具调用Agent** (`TOOL_INVOCATION`)：处理工具和函数调用
   - **通用助手Agent** (`GENERAL_ASSISTANT`)：处理通用对话和问答

3. **AgentDefinition实体扩展**：

   ```java
   // 新增字段
   private AgentType agentType;           // SYSTEM/BUSINESS/TOOL/WORKFLOW
   private AgentRole agentRole;           // 具体角色类型
   private Boolean isSystemAgent;         // 是否为系统内置Agent
   ```

4. **提示词管理系统**：

   - **动态编辑**：支持在线编辑Agent提示词，实时生效
   - **变量替换**：支持 `${variableName}` 格式的变量替换
   - **版本管理**：提示词模板支持版本控制和历史回溯
   - **分类管理**：系统提示词、用户提示词模板、混合类型
   - **缓存机制**：Redis缓存提示词渲染结果，提升性能

5. **执行流程重设计**：

   ```
   用户消息 → 意图识别Agent → 流程编排Agent → 通用助手Agent → 结果聚合Agent → 响应生成Agent → 用户响应
   ```

**技术实现亮点**：

```java
// 统一AI Agent执行接口
public Mono<AgentExecutionResult> executeAgent(AgentExecutionRequest request);

// 按角色执行Agent
public Mono<AgentExecutionResult> executeAgentByRole(AgentRole agentRole, AgentExecutionRequest request);

// 提示词动态渲染
public String renderPrompt(Long templateId, Map<String, Object> variables);
```

**遵循MVP原则**：

- 移除了客服Agent、销售Agent、技术支持Agent等业务型Agent
- 专注于核心系统Agent，确保架构简洁高效
- 保留扩展性，后续可根据需求添加新的Agent角色

**性能优化**：

- 提示词模板缓存机制，避免重复渲染
- 响应式编程模型，支持异步非阻塞执行
- 统一的错误处理和降级策略

### 数据库连接池优化 - 增加心跳检查和重连机制 (2025-08-05 09:54:20)

**变更概述**：

针对生产环境中频繁出现的数据库连接断开问题（HikariDataSource has been closed），实施了完整的数据库连接池优化方案，包括心跳检查、自动重连机制、连接池监控和故障自动恢复功能。

**架构设计变更**：

1. **HikariCP 连接池配置优化**：

   - **心跳检查**：添加 `connection-test-query: SELECT 1` 验证连接有效性
   - **连接验证**：设置 `validation-timeout: 5000` (5秒) 验证超时时间
   - **泄漏检测**：启用 `leak-detection-threshold: 30000` (30秒) 连接泄漏检测
   - **连接池监控**：为生产环境启用 `register-mbeans: true` JMX 监控

2. **数据库 URL 参数优化**：

   - **PostgreSQL**：添加 `autoReconnect=true&tcpKeepAlive=true&socketTimeout=30&loginTimeout=30`
   - **MySQL**：添加 `autoReconnect=true&socketTimeout=30000&connectTimeout=20000`
   - **重连机制**：设置 `maxReconnects=3&initialTimeout=2` 自动重连参数

3. **连接池参数调整**：

   - **开发环境**：`maximum-pool-size: 15`, `minimum-idle: 5`
   - **生产环境**：`maximum-pool-size: 20`, `minimum-idle: 8`
   - **连接超时**：统一调整为 `connection-timeout: 20000` (20秒)
   - **生命周期**：调整为 `max-lifetime: 1500000` (25分钟)

4. **新增监控和管理组件**：

   - **`DatabaseConnectionUtil`**：数据库连接监控和恢复工具类
   - **`DatabaseMonitoringConfig`**：定时健康检查和自动恢复服务
   - **`DatabaseMonitorController`**：RESTful API 监控接口

**技术实现**：

```yaml
# 优化后的连接池配置示例
spring:
  datasource:
    primary:
      hikari:
        # 连接验证查询 - 心跳检查
        connection-test-query: SELECT 1
        # 验证超时时间 (5秒)
        validation-timeout: 5000
        # 连接泄漏检测 (30秒)
        leak-detection-threshold: 30000
        # 连接池名称，便于监控
        pool-name: DipsPro-Primary-Pool
        # 生产环境启用JMX管理监控
        register-mbeans: true
```

```java
// 新增监控功能
@Scheduled(fixedRate = 300000) // 5分钟定时检查
public void scheduledHealthCheck() {
    boolean primaryHealthy = DatabaseConnectionUtil.isConnectionHealthy(primaryDataSource);
    if (!primaryHealthy) {
        DatabaseConnectionUtil.checkAndRecoverConnection(primaryDataSource);
    }
}
```

**监控 API 接口**：

- `GET /api/system/database/health` - 获取数据库整体健康状态
- `GET /api/system/database/primary/status` - 主数据库连接池状态
- `GET /api/system/database/mysql/status` - MySQL 数据库连接池状态
- `POST /api/system/database/primary/recover` - 手动恢复主数据库连接
- `POST /api/system/database/mysql/recover` - 手动恢复 MySQL 数据库连接
- `POST /api/system/database/test` - 测试所有数据库连接

**影响分析**：

- **可靠性提升**：自动检测并恢复断开的数据库连接，减少服务中断
- **监控能力**：提供实时连接池状态监控和 JMX 管理接口
- **运维效率**：支持手动触发连接恢复，便于故障排查和处理
- **性能优化**：通过连接池参数调优，提高并发处理能力

---

## 历史架构变更

### 路由守卫逻辑优化 - 修复用户协议页面无限循环 (2025-07-25 14:22:49)

**变更概述**：

修复了前端路由守卫中用户协议页面和引导页面之间的无限循环问题。通过优化 `checkOnboardingFlow` 函数的逻辑流程，确保用户协议优先级高于引导页面，避免在两个页面之间的循环跳转。

**架构设计变更**：

1. **路由守卫逻辑优化**：

   - 在 `checkOnboardingFlow` 函数中添加特殊处理逻辑
   - 确保用户在协议页面时不会进行引导页面检查
   - 明确用户协议检查和引导页面检查的优先级关系

2. **状态检查流程优化**：

   ```javascript
   // 新增逻辑：用户协议页面特殊处理
   if (needsUserAgreement && isUserAgreementPage) {
     console.log('✅ [路由守卫] 在用户协议页面，允许访问')
     next()
     return  // 直接返回，不进行引导页面检查
   }
   ```

3. **用户状态管理改进**：

   - **协议阶段**：`user_agreement_accepted: false` → 强制进入协议页面
   - **引导阶段**：协议同意后 + `first_login_completed: false` → 进入引导页面
   - **正常使用**：两个状态都为 `true` → 正常访问应用

**技术实现**：

```javascript
// 修复前的问题流程
用户协议页面 → 检查引导需求 → 重定向到引导页面 → 检查协议状态 → 回到协议页面 → 无限循环

// 修复后的正确流程
用户协议页面 → 检查是否在协议页面 → 直接允许访问 → 用户完成协议 → 检查引导需求 → 正常进入引导
```

**影响分析**：

- **用户体验**：新用户可以正常完成协议 → 引导 → 使用的完整流程
- **系统稳定性**：消除了路由守卫的无限循环问题
- **代码维护性**：逻辑更清晰，优先级更明确
- **调试友好性**：增加了详细的控制台日志

**相关文件**：

- `dp-web-fa/src/router/guards.ts` - 路由守卫逻辑修复

---

## 最新架构变更 (2025-01-19)

### 菜单系统架构增强 - 描述字段支持 (2025-01-19)

**变更概述**：

为 sys_menu 表和相关前后端代码添加了描述字段（description），增强菜单系统的可用性。由于菜单名称可能重复，通过描述字段可以更好地区分各个菜单的具体作用，特别是在角色权限分配时能够提供更明确的上下文信息。

**架构设计变更**：

1. **数据库层变更**：

   - sys_menu 表新增 description 字段（varchar(512)）
   - 提供数据库迁移脚本，支持已存在的表结构升级

2. **后端架构变更**：

   - Menu 实体类增加 description 属性
   - MenuForm 表单对象增加 description 字段
   - MenuVO 视图对象增加 description 字段
   - 保持现有 API 接口兼容性

3. **前端架构变更**：

   - TypeScript 接口定义更新（MenuVO、MenuForm）
   - 菜单管理页面增加描述字段编辑功能
   - 角色权限分配页面显示菜单描述信息

4. **用户体验改进**：
   - 管理员在创建/编辑菜单时可以添加详细描述
   - 角色权限分配时可以看到每个菜单的描述信息
   - 提高菜单管理的直观性和易用性

**技术实现**：

```sql
-- 数据库变更
ALTER TABLE sys_menu ADD COLUMN description varchar(512);
COMMENT ON COLUMN sys_menu.description IS '菜单描述信息';
```

```java
// 后端实体变更
@Schema(description = "菜单描述信息")
private String description;
```

```typescript
// 前端接口变更
interface MenuVO {
  // ... 其他字段
  /** 菜单描述信息 */
  description?: string;
}
```

**影响分析**：

- 向后兼容：现有代码和数据不受影响
- 数据迁移：自动为现有菜单添加描述字段，初始值为 NULL
- API 兼容：所有现有 API 保持兼容
- 用户界面：菜单管理界面增加描述编辑功能

## 最新架构变更 (2025-07-14)

### 二维码生成 Controller 接口模块架构设计 (2025-07-14 11:29:47)

**变更概述**：

新增了二维码生成 Controller 接口模块，为二维码生成工具类提供完整的 REST API 接口。该模块采用标准的 Spring Boot Controller 架构，提供单个生成、批量生成、快速生成等多种 API 接口，支持参数验证、错误处理和 Swagger 文档。

**架构设计**：

1. **核心 Controller 类**：

   - `QrCodeController`: 二维码生成 REST API 控制器
   - `QrCodeGenerateRequestDto`: 单个二维码生成请求 DTO
   - `QrCodeBatchGenerateRequestDto`: 批量二维码生成请求 DTO

2. **API 接口设计**：

   ```java
   // 单个二维码生成
   POST /api/qrcode/generate

   // 批量二维码生成
   POST /api/qrcode/batch-generate

   // 快速生成（GET请求）
   GET /api/qrcode/quick-generate?content=xxx&width=300&height=300

   // 内容验证
   GET /api/qrcode/validate?content=xxx

   // 获取推荐尺寸
   GET /api/qrcode/recommended-size?contentLength=100

   // 获取容量信息
   GET /api/qrcode/capacity-info?level=M
   ```

3. **请求参数验证**：

   ```java
   // 单个生成请求
   {
     "content": "必填，最大2000字符",
     "width": "可选，50-2000像素，默认300",
     "height": "可选，50-2000像素，默认300",
     "fileName": "可选，最大100字符",
     "description": "可选，最大200字符"
   }

   // 批量生成请求
   {
     "contents": ["必填，最大100个，每个最大2000字符"],
     "width": "可选，50-2000像素，默认300",
     "height": "可选，50-2000像素，默认300",
     "description": "可选，最大200字符"
   }
   ```

4. **响应格式统一**：

   ```java
   // 成功响应
   {
     "success": true,
     "data": QrCodeResult,
     "message": "二维码生成成功"
   }

   // 错误响应
   {
     "success": false,
     "message": "错误信息"
   }
   ```

**技术特性**：

1. **参数验证**：使用 Jakarta Validation 进行请求参数验证
2. **错误处理**：统一的异常处理和错误响应格式
3. **日志记录**：详细的操作日志记录，便于调试和监控
4. **API 文档**：集成 Swagger 注解，自动生成 API 文档
5. **多种接口**：支持 POST 和 GET 请求，适应不同使用场景

**使用场景**：

1. **前端集成**：为前端应用提供标准的 REST API 接口
2. **第三方调用**：支持其他系统通过 HTTP 接口调用二维码生成功能
3. **批量处理**：支持批量生成多个二维码，提高处理效率
4. **快速集成**：提供简单的 GET 接口，便于快速集成和测试

### 二维码生成工具类模块架构设计 (2025-07-14 11:10:16)

**变更概述**：

新增了二维码生成工具类模块，提供完整的二维码生成、上传七牛云和获取加密链接功能。该模块采用静态工具类设计，集成了 ZXing 二维码库和现有的七牛云上传服务。

**架构设计**：

1. **核心工具类**：

   - `QrCodeUtil`: 二维码生成静态工具类
   - `QrCodeResult`: 二维码生成结果 DTO
   - `QrCodeUtilExample`: 使用示例类
   - `QrCodeUtilTest`: 单元测试类

2. **技术栈集成**：

   ```xml
   <!-- ZXing 二维码生成库 -->
   <dependency>
       <groupId>com.google.zxing</groupId>
       <artifactId>core</artifactId>
       <version>3.5.3</version>
   </dependency>
   <dependency>
       <groupId>com.google.zxing</groupId>
       <artifactId>javase</artifactId>
       <version>3.5.3</version>
   </dependency>
   ```

3. **核心功能模块**：

   ```java
   // 基础二维码生成
   QrCodeResult generateQrCode(String content)
   QrCodeResult generateQrCode(String content, int width, int height)
   QrCodeResult generateQrCode(String content, int width, int height, String fileName)

   // 批量生成
   List<QrCodeResult> generateQrCodeBatch(List<String> contents, int width, int height)

   // 工具方法
   boolean validateContent(String content)
   boolean isValidUrl(String content)
   int[] getRecommendedSize(int contentLength)
   String getCapacityInfo(ErrorCorrectionLevel level)
   ```

4. **七牛云集成架构**：
   ```
   用户调用 → QrCodeUtil.generateQrCode() →
   生成二维码图片 → 转换为字节数组 →
   QiniuService.uploadFile() → 七牛云存储 →
   返回加密访问链接
   ```

**技术特性**：

1. **静态工具类设计**：无状态、线程安全，符合工具类设计原则
2. **多种生成方式**：支持默认尺寸、自定义尺寸、批量生成
3. **完整的参数验证**：内容验证、尺寸限制、URL 格式检查
4. **错误处理机制**：统一的错误返回格式，详细的错误信息
5. **七牛云深度集成**：自动上传、文件命名、访问链接生成

**应用场景**：

1. **用户分享功能**：生成用户分享链接的二维码
2. **活动报名**：生成活动报名页面的二维码
3. **联系方式**：生成包含联系信息的二维码
4. **系统链接**：为系统各功能模块生成快速访问二维码

### 发票下载功能架构优化 (2025-07-11 18:22:16)

**变更概述**：

优化了发票下载功能的架构设计，从重定向模式改为直接返回 URL 模式，解决了前后端接口不匹配的问题，提升了用户体验和系统稳定性。

**架构设计变更**：

1. **后端接口架构调整**：

   ```java
   // 原设计：返回重定向响应
   public ResponseEntity<Resource> downloadInvoiceFile(Long invoiceId, Long userId)

   // 新设计：返回私有访问URL
   public String downloadInvoiceFile(Long invoiceId, Long userId)
   ```

2. **前端调用架构简化**：

   ```typescript
   // 原设计：期望 Blob 响应
   downloadInvoiceFile(invoiceId: number): Promise<Blob>

   // 新设计：获取 URL 字符串
   downloadInvoiceFile(invoiceId: number): Promise<ApiResponse<string>>
   ```

3. **文件访问流程优化**：

   ```
   原流程：前端 → 后端重定向 → 七牛云文件
   新流程：前端 → 后端获取URL → 前端直接访问七牛云文件
   ```

**技术优势**：

1. **接口一致性**：统一了 API 返回格式，符合 RESTful 设计原则
2. **用户体验提升**：支持新窗口打开，避免页面跳转
3. **错误处理改进**：前端可以更好地处理错误情况和加载状态
4. **缓存友好**：URL 可以被前端缓存，减少重复请求

### 七牛云上传附件功能模块架构设计 (2025-07-11 14:29:19)

**变更概述**：

设计并实现了七牛云上传附件的独立功能模块，该模块采用标准的服务层架构设计，提供了完整的文件上传、管理和访问功能。模块具有高度的可复用性和扩展性，可以被项目中的其他业务模块调用。

**核心架构特性**：

1. **模块独立性**：

   - 独立的服务模块，位于 `com.dipspro.modules.common` 包下
   - 与业务逻辑解耦，可被多个业务模块复用
   - 统一的配置管理和错误处理机制

2. **服务层架构设计**：

   ```
   com.dipspro.modules.common/
   ├── service/
   │   ├── QiniuService.java           # 七牛云服务接口
   │   └── impl/
   │       └── QiniuServiceImpl.java   # 七牛云服务实现
   ├── dto/
   │   └── QiniuUploadResult.java      # 上传结果数据传输对象
   └── config/
       └── QiniuConfig.java            # 七牛云配置管理
   ```

3. **配置架构设计**：

   - **配置类**: `QiniuConfig` 使用 Spring Boot 配置绑定
   - **环境变量支持**: 支持通过环境变量覆盖配置参数
   - **Bean 管理**: 自动配置七牛云 SDK 核心组件

4. **服务接口设计**：

   - **多种上传方式**: 支持 MultipartFile、InputStream、字节数组
   - **文件管理**: 提供删除、存在性检查、URL 生成等功能
   - **访问控制**: 支持公开和私有文件访问模式
   - **Token 管理**: 支持上传 Token 生成和管理

5. **文件存储策略**：

   - **路径生成**: 基于时间戳和业务标识的文件路径生成策略
   - **文件验证**: 支持文件类型、大小等多维度验证
   - **命名规范**: 统一的文件命名和存储规范

**技术实现架构**：

1. **依赖管理**:

   ```xml
   <dependency>
       <groupId>com.qiniu</groupId>
       <artifactId>qiniu-java-sdk</artifactId>
       <version>7.15.1</version>
   </dependency>
   ```

2. **配置结构**:

   ```yaml
   qiniu:
     access-key: ${QINIU_ACCESS_KEY:your_access_key}
     secret-key: ${QINIU_SECRET_KEY:your_secret_key}
     bucket: ${QINIU_BUCKET:dips-pro-dev}
     domain: ${QINIU_DOMAIN:http://your-domain.com}
     region: huadong
     max-file-size: 10485760  # 10MB
     allowed-file-types: [jpg, jpeg, png, gif, pdf, doc, docx, xls, xlsx]
   ```

3. **错误处理架构**:

   - 统一的异常处理机制
   - 详细的错误信息和用户友好的提示
   - 上传结果的标准化封装

**发票上传功能集成架构**：

1. **业务集成设计**：

   - 在发票服务中注入七牛云服务
   - 发票文件上传与发票记录的事务性关联
   - 文件上传成功后自动更新发票状态

2. **API 接口设计**：

   ```
   POST /api/billing/admin/invoice/{id}/upload
   ├── 参数: MultipartFile file, InvoiceUploadDto uploadDto
   ├── 返回: ApiResponse<InvoiceAdminDto>
   └── 功能: 上传发票文件并关联到发票记录
   ```

3. **前端集成架构**：

   - Vue 3 + Ant Design Vue Upload 组件
   - 自定义上传逻辑和进度监控
   - 文件验证和错误处理机制

**安全架构设计**：

1. **访问控制**:

   - 基于角色的文件访问权限控制
   - 私有文件的签名访问机制
   - 上传 Token 的时效性控制

2. **文件安全**:

   - 文件类型白名单验证
   - 文件大小限制
   - 恶意文件检测预留接口

**扩展性设计**：

- **多云支持**: 预留接口支持其他云存储服务
- **插件化**: 支持文件处理插件扩展
- **监控集成**: 预留监控和统计接口
- **CDN 集成**: 支持 CDN 加速访问

**性能优化架构**：

- **异步上传**: 支持异步文件上传处理
- **批量操作**: 预留批量文件操作接口
- **缓存策略**: URL 生成和 Token 缓存优化
- **连接池**: 七牛云 SDK 连接池优化

## 历史架构变更 (2025-07-10)

### 自有发票管理业务模块架构设计调整 (2025-07-10 17:44:16)

**变更概述**：

根据项目结构优化需求，将自有发票管理业务模块调整到计费系统（billing）包下，简化数据库约束和字段设计，提高系统的统一性和维护性。

**主要架构调整**：

1. **模块路径统一**：

   - 从独立的 `com.dipspro.modules.invoice` 调整为 `com.dipspro.modules.billing` 子模块
   - API 路径从 `/api/invoice` 调整为 `/api/billing/invoice`
   - 与现有计费系统深度集成，便于统一管理

2. **数据库设计简化**：

   - **外键约束**：移除所有数据库外键约束，通过应用层保证数据一致性
   - **字段简化**：`b_billing_transactions` 表只添加两个字段：
     - `self_invoice_id` - 关联的发票 ID
     - `self_invoice_status` - 发票状态（NULL/PENDING/COMPLETED/CANCELLED）
   - **性能优化**：减少数据库约束检查，提高写入性能

3. **权限体系调整**：
   - 权限代码从 `invoice:*` 调整为 `billing:invoice:*`
   - 在现有计费系统权限体系下统一管理
   - 保持角色权限的一致性和可维护性

### 自有发票管理业务模块架构设计 (2025-07-10 17:31:34)

**变更概述**：

设计完成全新的自有发票管理业务模块，该模块与现有的微信发票系统完全独立，拥有独立的数据结构、业务逻辑和管理流程。模块采用标准的分层架构设计，支持普通用户和财务人员的不同角色需求。

**核心架构特性**：

1. **模块独立性**：

   - 完全独立于微信发票模块 (`com.dipspro.modules.pay.wechat`)
   - 使用独立的数据表 `b_billing_invoice` 和 `b_billing_invoice_transactions`
   - 独立的业务逻辑和 API 接口

2. **分层架构设计**：

   ```
   com.dipspro.modules.invoice/
   ├── controller/          # REST API控制器层
   ├── service/            # 业务逻辑层
   ├── repository/         # 数据访问层
   ├── entity/            # JPA实体层
   ├── dto/               # 数据传输对象
   ├── enums/             # 枚举定义
   └── exception/         # 异常处理
   ```

3. **数据库架构设计**：

   - **主表**: `b_billing_invoice` - 发票核心信息存储
   - **关联表**: `b_billing_invoice_transactions` - 发票与交易关联关系
   - **扩展表**: `b_billing_transactions` 添加自有发票相关字段

4. **业务流程架构**：

   - **用户端流程**: 查看历史发票 → 选择未开票交易 → 提交开票申请
   - **财务端流程**: 接收通知 → 处理开票申请 → 上传发票文件 → 完成开票

5. **权限架构设计**：

   - **用户权限**: `invoice:user:view`, `invoice:user:apply`, `invoice:user:download`
   - **财务权限**: `invoice:admin:manage`, `invoice:admin:process`, `invoice:admin:upload`
   - **菜单分离**: 不同角色显示不同的发票管理菜单

6. **通知系统架构**：
   - **邮件通知**: 支持向多个财务人员发送邮件
   - **短信通知**: 支持向多个财务人员发送短信
   - **异步处理**: 使用 `@Async` 注解实现异步通知

**技术实现要点**：

1. **状态管理**:

   - 发票状态：`PENDING`(开票中) → `COMPLETED`(已完成) / `CANCELLED`(已取消)
   - 状态流转控制和业务规则验证

2. **文件管理**:

   - 支持 PDF 文件上传和下载
   - 文件安全性检查和存储策略
   - 文件访问权限控制

3. **数据完整性**:

   - 外键约束确保数据关联完整性
   - 事务管理确保操作原子性
   - 乐观锁防止并发冲突

4. **安全架构**:
   - 基于角色的访问控制(RBAC)
   - API 接口权限验证
   - 数据访问权限隔离

**前端架构设计**：

1. **路由设计**:

   ```
   /invoice/
   ├── user/    # 用户发票界面
   └── admin/   # 财务管理界面
   ```

2. **组件架构**:

   - 发票列表组件 (`InvoiceList.vue`)
   - 开票申请对话框 (`InvoiceApplicationDialog.vue`)
   - 财务处理组件 (`InvoiceProcessDialog.vue`)

3. **状态管理**:
   - 发票数据状态管理
   - 用户权限状态管理
   - 界面交互状态管理

**扩展性设计**：

- 支持多种发票类型扩展（普通发票、专用发票）
- 支持多种通知方式扩展
- 支持工作流引擎集成
- 支持多租户隔离

**性能优化**：

- 数据库索引优化查询性能
- 分页查询减少数据传输
- 文件异步处理提升用户体验
- 缓存策略优化响应速度

## 历史架构变更 (2025-07-08)

### 计费交易表发票字段扩展 (2025-07-08 10:05:26)

**变更概述**：

为支持电子发票功能，对 `b_billing_transactions` 表进行了结构扩展，添加了发票相关字段。采用 ALTER TABLE 方式进行表结构变更，确保数据库升级的安全性和向后兼容性。

**主要变更**：

1. **新增字段**：

   - `invoice_eligible` - 是否可开发票（布尔型）
   - `invoice_applied` - 是否已申请发票（布尔型）
   - `invoice_id` - 关联的发票 ID（外键）
   - `invoice_status` - 发票状态（枚举）
   - `invoice_applied_at` - 发票申请时间
   - `invoice_issued_at` - 发票开具时间

2. **数据完整性**：

   - 发票状态检查约束
   - 申请发票逻辑约束
   - 字段注释和文档

3. **性能优化**：

   - 发票相关索引
   - 复合索引优化多条件查询
   - 统计视图简化复杂查询

4. **数据迁移**：
   - 自动更新现有记录
   - 成功充值记录标记为可开发票

**架构影响**：

- 计费系统与发票系统的数据关联
- 支持发票全生命周期管理
- 提供发票统计和分析能力
- 保持向后兼容性

### 微信支付电子发票功能架构 (2025-07-08 10:01:36)

**变更概述**：

实现了完整的微信支付电子发票功能模块，采用标准的分层架构设计，支持普通发票和专用发票的申请、管理和下载。该模块遵循项目现有的架构规范，与微信支付系统和计费模块深度集成，提供完整的发票生命周期管理。

**架构设计原则**：

1. **分层架构**：遵循 Controller → Service → Repository → Entity 的标准分层
2. **模块化设计**：独立的发票模块，与其他模块松耦合
3. **数据完整性**：完整的发票信息存储和状态管理
4. **API 标准化**：统一的 REST API 设计规范
5. **扩展性**：预留微信 API 集成接口，支持未来功能扩展

**核心架构组件**：

```
微信支付电子发票模块架构
├── 表现层 (Presentation Layer)
│   └── WechatInvoiceController
│       ├── 发票申请接口 (/api/wechat/invoice/apply)
│       ├── 发票查询接口 (/api/wechat/invoice/{invoiceNo})
│       ├── 发票列表接口 (/api/wechat/invoice/list)
│       ├── 重新申请接口 (/api/wechat/invoice/{invoiceNo}/reapply)
│       ├── 取消申请接口 (/api/wechat/invoice/{invoiceNo}/cancel)
│       ├── 发票下载接口 (/api/wechat/invoice/{invoiceNo}/download)
│       ├── 微信回调接口 (/api/wechat/invoice/callback)
│       ├── 配置管理接口 (/api/wechat/invoice/config)
│       └── 统计查询接口 (/api/wechat/invoice/statistics)
├── 业务逻辑层 (Business Logic Layer)
│   ├── WechatInvoiceService (接口)
│   └── WechatInvoiceServiceImpl (实现)
│       ├── 发票申请业务逻辑
│       │   ├── 参数验证（专用发票字段验证）
│       │   ├── 支付记录检查
│       │   ├── 重复申请检查
│       │   └── 用户权限验证
│       ├── 发票状态管理
│       │   ├── 状态流转控制
│       │   ├── 异步状态更新
│       │   └── 失败重试机制
│       ├── 微信 API 集成（预留）
│       │   ├── 获取抬头填写链接
│       │   ├── 创建发票卡券模板
│       │   ├── 上传发票文件
│       │   └── 处理微信回调
│       └── 发票统计分析
│           ├── 用户发票统计
│           ├── 状态分布统计
│           └── 金额汇总统计
├── 数据访问层 (Data Access Layer)
│   └── WechatInvoiceRepository
│       ├── 基础 CRUD 操作
│       ├── 条件查询方法
│       │   ├── findByInvoiceNoAndDeletedFalse
│       │   ├── findByOutTradeNoAndDeletedFalse
│       │   ├── findByUserIdAndStatusAndDeletedFalse
│       │   ├── findByCardIdAndDeletedFalse
│       │   └── findPendingInvoices
│       ├── 分页查询支持
│       ├── 统计查询方法
│       │   ├── countByUserId
│       │   ├── countByUserIdAndStatus
│       │   └── existsByOutTradeNoAndDeletedFalse
│       └── 软删除支持
└── 数据模型层 (Data Model Layer)
    ├── WechatInvoice (实体类)
    │   ├── 基础信息字段
    │   │   ├── 发票申请单号 (invoiceNo)
    │   │   ├── 支付记录关联 (paymentRecordId)
    │   │   ├── 商户订单号 (outTradeNo)
    │   │   ├── 微信支付订单号 (transactionId)
    │   │   └── 用户ID (userId)
    │   ├── 发票信息字段
    │   │   ├── 发票金额 (amount)
    │   │   ├── 发票类型 (invoiceType)
    │   │   ├── 发票抬头 (invoiceTitle)
    │   │   ├── 纳税人识别号 (taxNumber)
    │   │   └── 企业注册信息
    │   ├── 收件人信息字段
    │   │   ├── 收件人姓名 (recipientName)
    │   │   ├── 收件人手机号 (recipientPhone)
    │   │   └── 收件人邮箱 (recipientEmail)
    │   ├── 微信卡券字段
    │   │   ├── 卡券模板ID (cardTemplateId)
    │   │   └── 卡券ID (cardId)
    │   ├── 发票文件字段
    │   │   ├── 文件URL (invoiceFileUrl)
    │   │   ├── 文件大小 (invoiceFileSize)
    │   │   └── 文件MD5 (invoiceFileMd5)
    │   ├── 发票详情字段
    │   │   ├── 发票代码 (invoiceCode)
    │   │   ├── 发票号码 (invoiceNumber)
    │   │   ├── 开票日期 (invoiceDate)
    │   │   └── 校验码 (checkCode)
    │   └── 状态管理字段
    │       ├── 发票状态 (status)
    │       ├── 失败原因 (failureReason)
    │       ├── 微信回调数据 (callbackData)
    │       └── 软删除标记 (deleted)
    ├── InvoiceRequestDto (请求DTO)
    │   ├── 参数验证注解
    │   ├── 专用发票字段验证
    │   └── 业务逻辑验证方法
    └── InvoiceResponseDto (响应DTO)
        ├── 状态显示转换
        ├── 金额格式化
        ├── 操作权限判断
        └── 便捷查询方法
```

**数据库架构设计**：

```
数据库表设计 (wechat_invoice)
├── 主键和索引设计
│   ├── 主键：id (BIGSERIAL)
│   ├── 唯一索引：invoice_no
│   ├── 业务索引：payment_record_id, out_trade_no
│   ├── 查询索引：user_id, status, card_id
│   └── 复合索引：user_id + status, user_id + created_at
├── 字段约束设计
│   ├── 非空约束：invoice_no, payment_record_id, user_id, amount
│   ├── 检查约束：amount >= 0
│   ├── 枚举约束：invoice_type, status
│   └── 长度约束：各字符串字段
├── 软删除设计
│   ├── deleted 字段标记
│   ├── 查询时过滤已删除记录
│   └── 支持数据恢复
└── 审计字段设计
    ├── created_at 创建时间
    ├── updated_at 更新时间
    └── 自动更新触发器
```

**状态管理架构**：

```
发票状态流转架构
├── 状态定义
│   ├── PENDING - 待开票
│   ├── PROCESSING - 开票中
│   ├── SUCCESS - 开票成功
│   ├── FAILED - 开票失败
│   └── CANCELLED - 已取消
├── 状态流转规则
│   ├── PENDING → PROCESSING (开始开票)
│   ├── PROCESSING → SUCCESS (开票成功)
│   ├── PROCESSING → FAILED (开票失败)
│   ├── PENDING → CANCELLED (用户取消)
│   └── FAILED → PENDING (重新申请)
├── 状态管理方法
│   ├── markAsProcessing() - 标记为开票中
│   ├── markAsSuccess() - 标记为成功
│   ├── markAsFailed() - 标记为失败
│   └── 自动更新时间戳
└── 状态验证机制
    ├── 业务规则验证
    ├── 权限检查
    └── 状态一致性保证
```

**API 设计架构**：

```
REST API 设计规范
├── 统一响应格式
│   ├── ApiResponse<T> 封装
│   ├── 成功响应 (status: 1)
│   ├── 错误响应 (status: 0)
│   └── 错误信息国际化
├── 参数验证架构
│   ├── @Valid 注解验证
│   ├── 自定义验证规则
│   ├── 业务逻辑验证
│   └── 错误信息收集
├── 权限控制架构
│   ├── 用户身份验证
│   ├── 数据权限控制
│   ├── 操作权限验证
│   └── 跨租户隔离
└── 分页查询架构
    ├── Pageable 参数支持
    ├── Page<T> 响应格式
    ├── 排序参数支持
    └── 性能优化策略
```

**集成架构设计**：

```
系统集成架构
├── 微信支付集成
│   ├── 支付记录关联
│   ├── 订单信息获取
│   ├── 支付状态验证
│   └── 金额一致性检查
├── 计费模块集成
│   ├── 支付记录查询
│   ├── 用户权限验证
│   ├── 金额验证
│   └── 业务状态检查
├── 用户模块集成
│   ├── 用户身份验证
│   ├── 用户信息获取
│   ├── 权限验证
│   └── 多租户支持
└── 微信 API 集成（预留）
    ├── 发票抬头链接获取
    ├── 卡券模板创建
    ├── 发票文件上传
    ├── 开票状态查询
    └── 回调处理机制
```

**扩展性架构设计**：

```
功能扩展架构
├── 发票类型扩展
│   ├── 电子普通发票
│   ├── 电子专用发票
│   ├── 纸质发票支持
│   └── 区块链发票
├── 支付方式扩展
│   ├── 微信支付
│   ├── 支付宝支付
│   ├── 银行卡支付
│   └── 企业转账
├── 功能模块扩展
│   ├── 发票验真
│   ├── 发票归档
│   ├── 发票统计分析
│   └── 发票导出功能
└── 第三方集成扩展
    ├── 税务系统对接
    ├── 财务系统集成
    ├── ERP系统对接
    └── 电子签章集成
```

## 历史架构变更 (2025-06-27)

### 引导页面 Notion 风格重构 (2025-06-27 11:25:59)

**变更概述**：

对引导页面系统进行了全面重构，采用 Notion 定价页面的现代化设计理念，提供更专业、简洁的用户体验。此次重构包括布局组件、定价展示、卡片设计等多个层面的优化。

**架构设计原则**：

1. **现代化设计**：参考 Notion 的简洁设计风格
2. **组件化架构**：模块化的组件设计，便于维护和扩展
3. **响应式布局**：适配多种屏幕尺寸和设备
4. **类型安全**：完整的 TypeScript 类型定义
5. **可配置性**：支持多种主题和配置选项

**核心架构组件**：

```
Notion风格引导页面架构
├── 布局组件 (OnboardingLayout)
│   ├── 顶部导航栏 (Header)
│   │   ├── 品牌标识区域
│   │   ├── 步骤指示器
│   │   └── 圆点样式进度条
│   ├── 主内容区域 (Main)
│   │   ├── 居中布局设计
│   │   ├── 页面标题和描述
│   │   └── 动态内容展示
│   └── 底部操作栏 (Footer)
│       ├── 固定底部定位
│       ├── 主要操作按钮
│       └── 次要操作按钮
├── 定价展示组件 (NotionPricingSection)
│   ├── 主标题区域
│   │   ├── "One tool for your whole company"
│   │   ├── "Free for teams to try"
│   │   └── 合作伙伴标识
│   ├── 定价周期切换器
│   │   ├── 月付/年付切换
│   │   ├── 折扣提示
│   │   └── 货币标识
│   └── 定价卡片网格
│       ├── 4列网格布局
│       ├── 推荐套餐突出显示
│       └── 功能列表展示
├── 定价卡片组件 (NotionPricingCard)
│   ├── 卡片头部
│   │   ├── 套餐名称
│   │   ├── 价格信息
│   │   └── 套餐描述
│   ├── 选择按钮
│   │   ├── 主要按钮样式
│   │   ├── 次要按钮样式
│   │   └── 悬停效果
│   └── 功能列表
│       ├── 基础功能
│       ├── AI功能分组
│       └── 特色功能标识
└── 周期切换组件 (NotionPeriodToggle)
    ├── 切换按钮组
    │   ├── 月付选项
    │   ├── 年付选项
    │   └── 激活状态样式
    ├── 折扣提示
    └── 平滑切换动画
```

**设计系统架构**：

```
Notion设计系统
├── 色彩方案 (Color Scheme)
│   ├── 主色调：#000000 (黑色文字)
│   ├── 强调色：#2563eb (蓝色按钮)
│   ├── 辅助色：#6b7280 (灰色文字)
│   ├── 背景色：#ffffff (白色背景)
│   └── 边框色：#e5e7eb (浅灰边框)
├── 字体系统 (Typography)
│   ├── 字体族：-apple-system, BlinkMacSystemFont, 'Segoe UI'
│   ├── 主标题：64px, 700字重
│   ├── 副标题：24px, 600字重
│   ├── 正文：16px, 400字重
│   └── 小文字：14px, 500字重
├── 间距系统 (Spacing)
│   ├── 大间距：64px, 48px
│   ├── 中间距：32px, 24px
│   ├── 小间距：16px, 12px
│   └── 微间距：8px, 4px
├── 圆角系统 (Border Radius)
│   ├── 大圆角：12px (卡片)
│   ├── 中圆角：8px (按钮)
│   └── 小圆角：6px (小元素)
└── 阴影系统 (Box Shadow)
    ├── 卡片阴影：0 1px 3px rgba(0,0,0,0.1)
    ├── 悬停阴影：0 4px 6px rgba(0,0,0,0.1)
    └── 按钮阴影：0 1px 2px rgba(0,0,0,0.05)
```

**响应式设计架构**：

```
响应式布局系统
├── 桌面端 (Desktop: >1024px)
│   ├── 4列网格布局
│   ├── 64px主标题
│   ├── 完整功能展示
│   └── 大间距设计
├── 平板端 (Tablet: 768px-1024px)
│   ├── 2列网格布局
│   ├── 48px主标题
│   ├── 适中间距
│   └── 简化功能列表
└── 移动端 (Mobile: <768px)
    ├── 单列布局
    ├── 36px主标题
    ├── 紧凑间距
    └── 隐藏复杂表格
```

**组件状态管理架构**：

```
状态管理系统
├── 定价周期状态 (billingCycle)
│   ├── 'monthly' - 月付模式
│   ├── 'yearly' - 年付模式
│   └── 响应式切换
├── 选择状态管理
│   ├── 单选模式
│   ├── 多选模式
│   └── 状态持久化
├── 加载状态管理
│   ├── 初始加载
│   ├── 数据获取
│   └── 操作反馈
└── 主题状态管理
    ├── 医美主题
    ├── 地产主题
    └── 默认主题
```

**技术实现架构**：

```
技术栈架构
├── 前端框架
│   ├── Vue 3 Composition API
│   ├── TypeScript 类型系统
│   ├── 响应式数据绑定
│   └── 组件生命周期管理
├── 样式系统
│   ├── SCSS 预处理器
│   ├── CSS 变量系统
│   ├── 响应式断点
│   └── 动画过渡效果
├── 构建系统
│   ├── Vite 构建工具
│   ├── 热重载开发
│   ├── 代码分割
│   └── 资源优化
└── 类型定义
    ├── 组件Props类型
    ├── 事件Emit类型
    ├── 状态管理类型
    └── API接口类型
```

## 历史架构变更 (2025-06-26)

### 聊天界面页面引导功能架构 (2025-06-26 10:05:38)

**变更概述**：

为聊天界面 container.vue 实现了完整的页面引导系统，使用 Driver.js 提供四个关键功能区域的用户引导。此功能采用组件化设计，完全独立于现有聊天功能，通过智能高亮和交互式引导帮助用户快速了解界面功能。

**架构设计原则**：

1. **组件独立性**：引导组件完全独立，不影响现有功能
2. **用户体验优先**：直观的视觉引导和中文界面
3. **响应式设计**：支持不同屏幕尺寸和布局适配
4. **可配置性**：支持自动开始、手动触发等多种模式
5. **可扩展性**：易于添加新的引导步骤和自定义样式

**核心架构组件**：

```
页面引导架构
├── 引导组件 (PageGuide Component)
│   ├── 引导逻辑层 (Guide Logic)
│   │   ├── Driver.js 集成
│   │   ├── 步骤配置管理
│   │   └── 事件回调处理
│   ├── 用户界面层 (UI Layer)
│   │   ├── 引导按钮
│   │   ├── 弹窗样式
│   │   └── 高亮效果
│   └── 状态管理层 (State Management)
│       ├── 引导状态
│       ├── 配置选项
│       └── 交互控制
└── 宿主页面集成 (Host Page Integration)
    ├── 元素标识 (Element Identification)
    │   ├── #chat-sender
    │   ├── #conversation-history
    │   ├── #prompt-templates
    │   └── #chat-welcome
    ├── 组件引入 (Component Import)
    └── 模板集成 (Template Integration)
```

**引导步骤设计架构**：

```
引导步骤配置
├── 步骤一：消息输入区域 (#chat-sender)
│   ├── 位置：底部输入区域
│   ├── 弹窗位置：top
│   ├── 说明内容：输入框和模板功能介绍
│   └── 操作按钮：[下一步, 关闭]
├── 步骤二：历史会话 (#conversation-history)
│   ├── 位置：左侧边栏
│   ├── 弹窗位置：right
│   ├── 说明内容：会话管理功能介绍
│   └── 操作按钮：[下一步, 上一步, 关闭]
├── 步骤三：提示词模板 (#prompt-templates)
│   ├── 位置：右侧边栏
│   ├── 弹窗位置：left
│   ├── 说明内容：模板选择功能介绍
│   └── 操作按钮：[下一步, 上一步, 关闭]
└── 步骤四：建议示例 (#chat-welcome)
    ├── 位置：中央欢迎区域
    ├── 弹窗位置：top
    ├── 说明内容：示例问题功能介绍
    └── 操作按钮：[上一步, 关闭]
```

**组件技术架构**：

```
PageGuide 组件架构
├── 依赖管理 (Dependencies)
│   ├── driver.js - 核心引导库
│   ├── driver.js/dist/driver.css - 样式文件
│   └── Vue 3 Composition API
├── 属性接口 (Props Interface)
│   ├── autoStart: boolean - 自动开始引导
│   └── showGuideButton: boolean - 显示引导按钮
├── 事件系统 (Event System)
│   ├── guideStarted - 引导开始事件
│   ├── guideCompleted - 引导完成事件
│   └── guideCancelled - 引导取消事件
├── 状态管理 (State Management)
│   ├── driverInstance - Driver.js 实例
│   └── isGuideActive - 引导激活状态
└── 方法接口 (Methods Interface)
    ├── startGuide() - 开始引导
    ├── stopGuide() - 停止引导
    └── resetGuide() - 重置引导
```

**样式设计架构**：

```
自定义样式系统
├── 弹窗样式 (Popover Styling)
│   ├── 背景色：白色/暗色模式适配
│   ├── 圆角：8px 现代化设计
│   ├── 阴影：层次感阴影效果
│   └── 最大宽度：320px 适中尺寸
├── 按钮样式 (Button Styling)
│   ├── 主要按钮：蓝色主题色
│   ├── 次要按钮：灰色辅助色
│   ├── 悬停效果：颜色渐变过渡
│   └── 间距布局：8px 按钮间距
├── 文字样式 (Typography)
│   ├── 标题：16px 粗体
│   ├── 描述：14px 常规
│   ├── 进度：12px 辅助色
│   └── 行高：1.5 可读性优化
└── 高亮效果 (Highlight Effects)
    ├── 边框高亮：蓝色光圈效果
    ├── 圆角：4px 柔和边角
    ├── 透明度：30% 半透明效果
    └── 动画：平滑过渡动画
```

**用户交互流程架构**：

```
交互流程设计
├── 引导触发 (Guide Trigger)
│   ├── 手动触发：点击引导按钮
│   ├── 自动触发：页面加载后延迟
│   └── 条件检查：元素存在性验证
├── 引导过程 (Guide Process)
│   ├── 步骤高亮：目标元素视觉突出
│   ├── 弹窗显示：说明内容展示
│   ├── 进度显示：当前步骤/总步骤
│   └── 操作控制：前进/后退/关闭
├── 状态管理 (State Management)
│   ├── 引导状态：激活/非激活
│   ├── 按钮状态：显示/隐藏
│   └── 事件回调：状态变更通知
└── 引导结束 (Guide Completion)
    ├── 正常完成：用户完成所有步骤
    ├── 用户取消：中途关闭引导
    └── 状态重置：恢复初始状态
```

**集成架构设计**：

```
容器页面集成架构
├── 元素标识策略 (Element Identification)
│   ├── ID 属性添加：为目标元素添加唯一ID
│   ├── 选择器映射：CSS选择器与功能区域映射
│   ├── 动态检测：运行时元素存在性检查
│   └── 容错处理：缺失元素的优雅降级
├── 组件导入策略 (Component Import)
│   ├── 静态导入：编译时组件引入
│   ├── 类型安全：TypeScript 类型检查
│   └── 依赖管理：确保 Driver.js 可用性
├── 模板集成策略 (Template Integration)
│   ├── 非侵入式：不影响现有布局
│   ├── 固定定位：引导按钮固定位置
│   ├── 层级管理：适当的 z-index 设置
│   └── 响应式布局：适配不同屏幕尺寸
└── 生命周期管理 (Lifecycle Management)
    ├── 组件挂载：自动初始化引导实例
    ├── 配置应用：根据属性配置行为
    ├── 事件监听：响应用户交互
    └── 资源清理：组件卸载时清理资源
```

**性能优化架构**：

1. **加载优化**：

   - Driver.js 按需加载
   - 样式文件懒加载
   - 组件延迟初始化

2. **内存管理**：

   - 引导实例及时销毁
   - 事件监听器清理
   - DOM 引用释放

3. **渲染优化**：
   - 条件渲染减少 DOM 节点
   - CSS 变换优化动画性能
   - 避免不必要的重渲染

**扩展性架构**：

1. **步骤扩展**：

   - 新增引导步骤配置
   - 支持动态步骤生成
   - 自定义步骤内容

2. **样式扩展**：

   - 主题色彩定制
   - 动画效果配置
   - 布局适配扩展

3. **功能扩展**：
   - 多语言支持
   - 引导进度保存
   - 个性化引导路径

**安全性考虑**：

1. **DOM 安全**：

   - 元素选择器验证
   - XSS 攻击防护
   - 安全的 innerHTML 使用

2. **用户隐私**：
   - 引导行为本地化
   - 不收集用户操作数据
   - 尊重用户选择权

---

### 租户配置查看功能架构 (2025-06-24 17:34:51)

**变更概述**：

在租户管理系统中新增了租户配置查看功能，实现了从租户列表直接查看 sys_tenant_config 键值对信息的能力。此功能采用前后端分离架构，通过模态对话框展示租户的系统配置、业务配置和界面配置信息。

**架构设计原则**：

1. **RESTful API 设计**：提供标准的租户配置查询接口
2. **分层架构模式**：Controller → Service → Repository 标准分层
3. **组件化前端设计**：独立的配置查看组件，支持复用
4. **类型安全传输**：使用 TypeScript 类型定义确保数据安全
5. **用户体验优化**：模态对话框交互，配置分类展示

**核心架构组件**：

```
租户配置查看架构
├── 后端架构 (Backend Architecture)
│   ├── 数据传输层 (DTO Layer)
│   │   └── TenantConfigResponseDto - 租户配置响应数据结构
│   ├── 数据访问层 (Repository Layer)
│   │   └── TenantConfigRepository - 租户配置数据查询
│   ├── 业务服务层 (Service Layer)
│   │   └── TenantService - 租户配置业务逻辑
│   └── 控制器层 (Controller Layer)
│       └── TenantController - 租户配置查询接口
└── 前端架构 (Frontend Architecture)
    ├── API 接口层 (API Layer)
    │   └── sys_tenantConfig.ts - 配置查询接口定义
    ├── 组件层 (Component Layer)
    │   └── TenantConfigModal - 配置查看组件
    └── 页面层 (Page Layer)
        └── tenant/list.vue - 租户列表页面集成
```

**数据流处理架构**：

```
配置查看数据流程
├── 用户交互触发
│   ├── 租户列表操作菜单点击
│   ├── 传递租户ID和名称参数
│   └── 打开配置查看模态框
├── 前端数据请求
│   ├── 调用 getTenantConfigs API
│   ├── 传递 tenantId 参数
│   └── 处理加载状态
├── 后端数据处理
│   ├── 控制器接收请求参数
│   ├── 服务层查询配置数据
│   ├── 仓库层数据库查询
│   └── DTO 转换返回响应
└── 前端数据展示
    ├── 按配置类型分组显示
    ├── 长文本展开/收起处理
    ├── 时间格式化显示
    └── 空值友好提示
```

**后端接口架构设计**：

```
API 接口设计
├── 接口路径：GET /api/sys/tenant/configs
├── 请求参数：
│   └── tenantId: Long (查询参数)
├── 响应结构：ApiResponse<List<TenantConfigResponseDto>>
│   ├── 数据字段：id, tenantId, configKey, configValue
│   ├── 类型字段：configType, description
│   └── 时间字段：createdTime, updatedTime
├── 数据排序：按 configType ASC, configKey ASC
└── 错误处理：
    ├── 参数验证错误
    ├── 数据库查询异常
    └── 系统处理错误
```

**前端组件架构设计**：

```
TenantConfigModal 组件架构
├── 组件属性 (Props)
│   ├── visible: boolean - 显示状态
│   ├── tenantId: number - 租户ID
│   └── tenantName: string - 租户名称
├── 组件事件 (Emits)
│   └── update:visible - 显示状态更新
├── 数据管理 (Data Management)
│   ├── loading: 加载状态管理
│   ├── configList: 配置数据列表
│   └── localVisible: 本地显示状态
├── 计算属性 (Computed Properties)
│   └── configGroups: 按类型分组的配置
├── 交互功能 (Interaction Features)
│   ├── 配置分类展示 (system/business/ui)
│   ├── 长文本展开收起切换
│   ├── 时间格式化显示
│   └── 空值友好提示
└── 样式设计 (Styling)
    ├── 响应式布局设计
    ├── 配置类型标签颜色
    └── 内容卡片化显示
```

**配置数据分类架构**：

```
配置类型分类系统
├── 系统配置 (system)
│   ├── 显示标签：系统配置
│   ├── 标签颜色：danger (红色)
│   ├── 显示优先级：1 (最高)
│   └── 典型配置：系统版本、系统名称等
├── 业务配置 (business)
│   ├── 显示标签：业务配置
│   ├── 标签颜色：primary (蓝色)
│   ├── 显示优先级：2 (中等)
│   └── 典型配置：公司名称、地址等
└── 界面配置 (ui)
    ├── 显示标签：界面配置
    ├── 标签颜色：success (绿色)
    ├── 显示优先级：3 (较低)
    └── 典型配置：主题色、Logo等
```

**用户体验优化架构**：

1. **交互设计优化**：

   - 模态对话框非阻塞式操作
   - 配置内容按类型分组展示
   - 长文本自动截断与展开功能
   - 加载状态友好提示

2. **数据展示优化**：
   - 配置类型使用不同颜色标签区分
   - 时间戳格式化为可读时间
   - 空值显示为友好的 "-" 符号
   - 配置描述信息辅助理解

**扩展性架构设计**：

1. **组件复用性**：

   - TenantConfigModal 组件可在其他页面复用
   - 支持通过 props 传递不同租户信息
   - 组件内部状态完全独立管理

2. **功能扩展性**：
   - 预留配置编辑功能扩展接口
   - 支持新增配置类型的显示
   - 可扩展配置搜索和过滤功能

**性能优化架构**：

1. **前端性能优化**：

   - 组件按需加载，减少初始包大小
   - 配置数据懒加载，仅在弹窗显示时请求
   - 使用 computed 属性缓存分组计算结果

2. **后端性能优化**：
   - 数据库查询使用索引优化
   - 按租户 ID 精确查询，避免全表扫描
   - DTO 转换使用 BeanUtils 提高效率

**数据库查询优化**：

```
查询优化策略
├── 索引利用
│   ├── 使用 tenant_id 索引快速定位
│   ├── 复合索引支持排序优化
│   └── 避免全表扫描操作
├── 查询方法命名
│   ├── findByTenantIdOrderByConfigTypeAscConfigKeyAsc
│   ├── Spring Data JPA 方法名推导
│   └── 自动生成优化查询语句
└── 结果排序
    ├── 数据库层面排序，减少应用层处理
    ├── 先按配置类型排序
    └── 同类型内按配置键排序
```

**安全性架构考虑**：

1. **权限控制**：

   - 继承现有租户管理权限体系
   - API 接口使用相同的安全拦截器
   - 租户数据隔离查询

2. **数据安全**：
   - 参数验证防止 SQL 注入
   - 敏感配置信息显示控制
   - 错误信息安全处理

**业务价值**：

1. **运营效率提升**：管理员可快速查看租户配置，无需直接操作数据库
2. **问题排查能力**：通过配置查看快速定位租户配置问题
3. **系统透明度**：提供租户配置的可视化查看能力
4. **用户体验**：友好的界面设计，提升管理操作体验

**兼容性影响**：

- **现有功能**：完全兼容现有租户管理功能
- **数据结构**：只读查询，无数据修改操作
- **API 接口**：新增接口，不影响现有 API 使用
- **前端集成**：无缝集成到现有租户列表界面

## 最新架构变更 (2025-06-24)

### 租户 CSV 批量导入架构 (2025-06-24 14:47:58)

**变更概述**：

实现了租户数据的 CSV 批量导入功能架构，专门为医美机构提供高效的数据批量录入能力。此架构采用混合设计模式，既支持 API 接口调用，也提供工具类直接使用，确保了功能的灵活性和扩展性。

**架构设计原则**：

1. **混合实现模式**：核心逻辑封装在工具类中，同时提供 RESTful API 接口
2. **批量事务处理**：每 50 条记录为一个事务批次，平衡性能与数据安全
3. **错误即停策略**：遇到任何验证错误立即停止，确保数据完整性
4. **自动编码生成**：标准化租户编码格式（YMO + 七位数字）
5. **分层数据验证**：CSV 格式、业务规则、数据库约束三层验证体系

**核心架构组件**：

```
CSV导入架构
├── 数据访问层 (Repository Layer)
│   ├── TenantConfigRepository - 租户配置数据访问
│   └── TenantRepository (扩展) - 添加编码查询方法
├── 数据传输层 (DTO Layer)
│   ├── TenantCsvImportDto - CSV导入数据结构
│   └── TenantImportResultDto - 导入结果数据结构
├── 工具服务层 (Utility Layer)
│   ├── TenantCodeGenerator - 租户编码生成器
│   └── TenantCsvImportUtil - CSV导入核心处理器
├── 业务服务层 (Service Layer)
│   └── TenantService (扩展) - 添加导入业务方法
└── 控制器层 (Controller Layer)
    └── TenantController (扩展) - 提供导入API接口
```

**数据处理流水线架构**：

```
CSV导入处理流程
├── 文件接收和验证
│   ├── 文件格式验证 (.csv扩展名检查)
│   ├── 文件大小限制 (Spring Boot默认配置)
│   └── 编码格式处理 (UTF-8)
├── CSV解析和结构验证
│   ├── 表头验证 (name, contactPhone, profileId)
│   ├── 数据行解析 (逗号分隔符处理)
│   └── 列数匹配检查
├── 业务数据验证
│   ├── 字段格式验证 (JSR-303注解)
│   ├── 业务规则验证 (长度、格式等)
│   └── 重复性检查 (租户名称去重)
├── 批量数据处理
│   ├── 租户编码生成 (YMO + 递增数字)
│   ├── 租户实体创建 (设置默认分类)
│   ├── 批量事务保存 (50条/批次)
│   └── 租户配置创建 (微信小程序ID)
└── 结果统计和反馈
    ├── 成功数量统计
    ├── 失败原因收集
    ├── 处理时间记录
    └── 完整结果返回
```

**租户编码生成架构**：

```
编码生成策略
├── 编码格式定义
│   ├── 行业前缀 - YM (医美MEDICAL_BEAUTY)
│   ├── 类型前缀 - O (机构ORGANIZATION)
│   └── 序号部分 - 七位数字 (0000001-9999999)
├── 编码生成逻辑
│   ├── 数据库查询最大编码 (findMaxTenantCodeByPrefix)
│   ├── 数字部分提取和递增
│   ├── 零填充格式化 (String.format)
│   └── 完整编码拼接
└── 编码验证机制
    ├── 格式正则验证 (^YMO\\d{7}$)
    ├── 唯一性数据库验证
    └── 业务规则一致性检查
```

**事务管理架构**：

```
事务处理策略
├── 方法级事务注解
│   ├── @Transactional(rollbackFor = Exception.class)
│   ├── 任何异常都触发回滚
│   └── 确保数据一致性
├── 批量处理事务
│   ├── 每50条记录为一个处理批次
│   ├── 批次内部作为一个事务单元
│   └── 批次间相互独立
└── 错误处理策略
    ├── 验证错误 - 立即停止，全量回滚
    ├── 保存异常 - 立即停止，已保存数据回滚
    └── 系统异常 - 记录详细错误日志
```

**API 接口设计架构**：

```
接口架构设计
├── 接口路径：POST /api/sys/tenant/import-csv
├── 请求参数：
│   └── file: MultipartFile (CSV文件)
├── 响应结构：ApiResponse<TenantImportResultDto>
│   ├── 成功响应：返回详细导入统计
│   ├── 失败响应：返回具体错误信息
│   └── 部分失败：返回错误详情和成功数量
└── 错误处理：
    ├── 文件验证错误 (空文件、格式错误)
    ├── 数据验证错误 (格式、业务规则)
    └── 系统处理错误 (数据库、事务异常)
```

**数据模型扩展**：

1. **租户配置表关联**：

   - 每个导入的租户自动创建配置记录
   - 配置键：微信小程序 ID
   - 配置类型：business
   - 配置值：来自 CSV 的 profileId 字段

2. **租户分类自动设置**：
   - industryType：固定为 MEDICAL_BEAUTY
   - tenantType：固定为 ORGANIZATION
   - status：默认为启用状态(1)

**性能优化架构**：

1. **批量处理优化**：

   - 使用 saveAll()批量保存，减少数据库交互
   - 50 条记录为最优批次大小，平衡内存和性能
   - 避免逐条处理的性能损耗

2. **内存管理优化**：
   - 流式处理大文件，避免全量加载到内存
   - 及时清理批次数据，防止内存溢出
   - 使用 StringBuilder 优化字符串操作

**扩展性架构设计**：

1. **多行业支持预留**：

   - 编码生成器支持不同行业前缀配置
   - 导入工具类支持参数化配置
   - 为地产(RE)等其他行业预留接口

2. **多文件格式支持**：
   - 当前实现 CSV 解析，架构支持扩展 Excel 等格式
   - 解析逻辑抽象化，便于新增格式支持
   - 统一的数据验证和处理流程

**安全性架构考虑**：

1. **数据验证安全**：

   - 多层次数据验证，防止恶意数据注入
   - 文件大小限制，防止 DoS 攻击
   - 字符编码验证，防止编码攻击

2. **操作权限控制**：
   - 继承现有的租户管理权限体系
   - API 接口使用相同的安全拦截器
   - 操作日志记录，便于审计追踪

**监控和日志架构**：

1. **详细操作日志**：

   - 导入开始和结束时间记录
   - 每个批次处理结果记录
   - 错误详情和堆栈信息记录

2. **性能监控指标**：
   - 处理时间统计
   - 成功率统计
   - 批次处理效率监控

**业务价值**：

1. **运营效率提升**：大幅减少手动录入工作量，支持批量数据迁移
2. **数据质量保障**：多层验证确保数据完整性和一致性
3. **系统可扩展性**：为其他类型租户导入提供基础架构
4. **用户体验优化**：提供直观的导入结果反馈和错误提示

**兼容性影响**：

- **现有功能**：完全兼容现有租户管理功能
- **数据结构**：利用现有表结构，无破坏性变更
- **API 接口**：新增接口，不影响现有 API 使用
- **前端集成**：可无缝集成到现有租户管理界面

## 最新架构变更 (2025-06-24)

### 租户系统分类功能扩展 (2025-06-24 10:19:30)

**变更概述**：

实现了租户系统的行业和类型分类功能，支持医美、地产行业区分和机构、厂家、消费者类型区分。此变更为租户管理提供了更细粒度的分类能力，便于系统针对不同行业和类型提供差异化服务。

**架构影响**：

1. **数据模型扩展**：

   - **表结构变更**： 表新增 和 字段
   - **数据约束**：添加 CHECK 约束确保字段值的有效性
   - **查询优化**：新增索引提升按分类查询的性能

2. **业务模型扩展**：

   - **实体类更新**： 新增分类字段
   - **DTO 扩展**：所有租户相关 DTO 支持分类字段
   - **常量定义**：新增 类统一管理分类枚举值

3. **系统功能增强**：

   - **创建租户**：支持指定行业类型和租户类型
   - **租户查询**：支持按行业和类型筛选租户
   - **前端展示**：提供分类的中文描述显示

**分类定义**：

- **行业类型**：MEDICAL_BEAUTY（医美）、REAL_ESTATE（地产）
- **租户类型**：ORGANIZATION（机构）、MANUFACTURER（厂家）、CONSUMER（消费者）

## 最新架构变更 (2025-06-20)

### 用户创建时自动套餐绑定架构 (2025-06-20 09:13:10)

**变更概述**：

实现了用户创建时自动绑定默认套餐的完整架构，确保所有新用户都拥有基础的计费配置和免费 Token 配额。此变更加强了用户注册流程的完整性，为后续计费功能的正常使用提供了基础保障。

**架构影响**：

1. **用户创建流程增强**：

   - **文件路径**：`UserServiceImpl.java`、`AuthService.java`
   - **功能增强**：用户创建成功后自动绑定默认套餐（ID 为 1）
   - **数据完整性**：确保新用户拥有完整的计费配置和余额记录

2. **套餐绑定机制优化**：

   - **文件路径**：`UserBalanceServiceImpl.java`
   - **核心功能**：`initializeUserBalance()` 方法支持根据套餐设置免费 Token
   - **数据流转**：从套餐表 `freeTokens` 字段自动写入用户余额表 `free_tokens` 字段

3. **错误处理和容错机制**：
   - **异常隔离**：套餐绑定失败不影响用户创建的主流程
   - **详细日志**：记录用户 ID、套餐 ID、免费 Token 数量等关键信息
   - **优雅降级**：绑定失败时使用默认值，后续可通过其他方式补充

**技术实现架构**：

```
用户创建流程架构
├── 用户注册入口 (AuthService.register)
│   ├── 租户验证和创建
│   ├── 用户信息验证和创建
│   ├── 密码加密和存储
│   └── 【新增】套餐自动绑定
│       ├── 调用 userBalanceService.initializeUserBalance(userId, 1L)
│       ├── 异常捕获和日志记录
│       └── 不影响主流程的容错机制
├── 管理员创建用户 (UserService.createUser)
│   ├── 用户信息验证和创建
│   ├── 密码生成和加密
│   └── 【新增】套餐自动绑定
│       ├── 调用 userBalanceService.initializeUserBalance(userId, 1L)
│       ├── 异常捕获和日志记录
│       └── 不影响主流程的容错机制
└── 套餐绑定核心逻辑 (UserBalanceService.initializeUserBalance)
    ├── 【增强】套餐信息查询
    │   ├── 根据套餐ID查询套餐详情
    │   ├── 获取套餐的 freeTokens 配置
    │   └── 套餐不存在时的异常处理
    ├── 用户余额记录创建
    │   ├── 设置基础余额信息（充值余额、赠送余额）
    │   ├── 【核心】设置免费Token数量（来自套餐配置）
    │   └── 设置初始状态和时间戳
    └── 数据持久化和日志记录
        ├── 保存用户余额记录到数据库
        ├── 记录详细的操作日志
        └── 返回完整的余额记录信息
```

**数据流转架构**：

```
套餐配置 → 用户余额表
b_billing_packages.free_tokens → b_user_balances.free_tokens

具体数据流：
1. 查询套餐 (id=1) → 获取 free_tokens 值
2. 创建用户余额记录 → 设置 free_tokens 字段
3. 保存余额记录 → 持久化到数据库
4. 记录操作日志 → 便于审计和问题排查
```

**业务价值**：

1. **用户体验提升**：新用户立即拥有免费 Token，可以直接体验核心功能
2. **运营效率提升**：减少手动配置工作，降低客服支持成本
3. **数据一致性保障**：确保所有新用户都有完整的计费配置
4. **系统稳定性增强**：避免用户余额记录缺失导致的异常情况

**兼容性影响**：

- **现有用户**：不受影响，现有的余额记录和套餐绑定保持不变
- **现有 API**：完全兼容，API 接口和响应格式无变化
- **数据库架构**：无变化，使用现有的表结构和字段
- **前端功能**：无需修改，自动继承新的后端功能

## 最新架构变更 (2025-06-19)

### 前端 Token 计算逻辑完全统一 (2025-06-19 22:22:50)

**变更概述**：

完成前端 Token 计算逻辑的彻底统一，修改了最后一个前端计算函数 `PackageAPI.previewTokenCost()`，现在所有 Token 相关计算都统一由后端 API 处理。这标志着 DIPS Pro 前后端分离架构的进一步完善。

**具体修改**：

1. **PackageAPI.previewTokenCost() 函数重构**：
   - **文件路径**：`dp-web-fa/src/api/packages.ts`
   - **修改前**：前端获取套餐详情后手动计算 Token 费用
   - **修改后**：直接调用后端 `/api/billing/calculate-cost` API
   - **代码优化**：删除 19 行前端计算逻辑，新增 11 行 API 调用

**技术架构提升**：

1. **计算逻辑完全后端化**：

   - 所有 Token 费用计算统一在后端处理
   - 前端专注于数据展示和用户交互
   - 消除了前后端计算逻辑不一致的隐患

2. **安全性和可靠性增强**：

   - 计费逻辑无法被前端篡改
   - 数据计算结果始终一致
   - 降低了计费系统的安全风险

3. **代码维护性提升**：
   - 业务规则变更只需修改后端
   - 前端代码更加简洁和专注
   - 符合单一职责原则

**影响范围评估**：

- **直接影响**：`PackageAPI.previewTokenCost()` 函数
- **间接影响**：通过该 API 的所有前端功能（自动适配）
- **兼容性**：现有接口和类型定义保持不变
- **缓存机制**：现有防抖和缓存策略继续有效

### 前端 Token 计算逻辑统一删除 (2025-06-19 21:44:53)

**变更时间**：2025-06-19 21:44:53

**变更概述**：

完全删除前端的 Token 计算逻辑，统一使用后端 API 进行 Token 费用计算，确保计算逻辑的一致性和安全性。此变更强化了前后端分离架构中的职责划分，将所有业务计算逻辑集中在后端处理。

**架构影响**：

1. **计算职责重新划分**：

   - 前端：仅负责数据展示、格式化、状态映射等纯 UI 逻辑
   - 后端：统一负责所有 Token 计算、价格计算、费用预估等业务逻辑

2. **API 调用架构优化**：

   - 所有计算相关功能都通过后端 API 实现
   - 前端 Store 层正确调用 `/api/billing/calculate-cost` 等接口
   - 增强了数据一致性和计算准确性

3. **代码清理和简化**：
   - 删除 `dp-web-fa/src/utils/priceCalculator.ts` 整个价格计算模块
   - 删除 `dp-web-fa/src/components/billing/PricePreview.vue` 依赖前端计算的组件
   - 简化 `dp-web-fa/src/utils/billing.ts` 只保留格式化和状态映射函数

**删除的前端计算函数**：

```typescript
// 已删除的复杂计算函数
- calculateTokenCostWithQualification()  // Token费用计算含资格状态
- calculateActualTokenPrice()           // 实际Token价格计算
- estimateMonthlyFee()                  // 月费预估
- calculateRealTimeCostPreview()        // 实时费用预览
- calculatePackageDiscount()            // 套餐优惠计算
- validatePackageSwitch()               // 套餐切换验证
- generateSwitchWarnings()              // 切换警告生成
- getPackageRecommendationReason()      // 套餐推荐原因
```

**保留的纯展示函数**：

```typescript
// 保留的格式化和状态映射函数
- formatSubscriptionStatus()            // 订阅状态格式化
- formatQualificationStatus()           // 资格状态格式化
- getBalanceStatus()                   // 余额状态获取
- calculateProgressPercentage()         // 进度百分比计算
- formatPackagePrice()                 // 套餐价格格式化
```

**技术收益**：

1. **安全性提升**：防止客户端篡改计算逻辑
2. **一致性保障**：避免前后端计算逻辑不同步
3. **维护简化**：业务规则统一在后端管理
4. **性能优化**：减少前端代码体积和复杂度

### 预付费阶梯定价系统 - 阶段四完整实现

**变更时间**：2025-06-19 21:09:11 (阶段四最终完成)

**功能概述**：

全面完成预付费阶梯定价系统的企业级实现，涵盖后端核心业务逻辑、前端交互界面、API 集成体系和数据可视化等全技术栈。系统已具备完整的生产环境部署能力，实现了"达标享优惠，未达标用标准价"的核心商业模式，并建立了完善的数据监控和智能分析体系。

**阶段四总体成果**：

- 后端实现：8 个 DTO 类、3 个服务接口、定时任务配置、Repository 增强
- 前端实现：价格计算工具、统计监控界面、API 集成、路由配置
- 技术栈整合：Spring Boot + Vue 3 + TypeScript + ECharts 全栈解决方案
- 企业级特性：数据分析、实时监控、异常检测、智能报表

**业务逻辑架构核心**：

1. **预付费阶梯定价模型**：根据用户月消费达标状态动态调整 Token 价格
2. **智能定时任务调度**：8 个核心定时任务覆盖资格检查、月度重置、自动降级等全生命周期
3. **全方位数据监控**：从用户行为到系统健康的完整统计分析体系
4. **风险控制机制**：异常切换检测、频繁操作监控、风险等级评估
5. **企业级稳定性**：事务安全、异步执行、状态跟踪、错误恢复机制

**阶段四：业务逻辑完善架构**：

### 一、前端价格计算系统优化

**核心功能模块**：

1. **动态价格计算引擎** (`calculateActualTokenPrice`)：

   - 输入：套餐数据、资格状态、回退套餐
   - 输出：实际价格、达标状态、价格类型（qualified/standard/fallback）
   - 核心逻辑：达标用户享受套餐优惠价，未达标用户使用标准价

2. **智能费用预览系统** (`calculateTokenCostWithQualification`)：

   - 实时计算考虑资格状态的 Token 费用
   - 自动计算达标后可节省的金额
   - 提供"达标后可节省 ¥xx.xx"的友好提示

3. **价格显示信息生成器** (`getPriceDisplayInfo`)：

   - 生成动态价格标签（优惠价/标准价）
   - 计算节省百分比和具体金额
   - 根据月度进度确定紧急程度（高/中/低/无）

4. **实时费用预览引擎** (`calculateRealTimeCostPreview`)：
   - 对比当前费用与达标后费用
   - 生成个性化充值建议和紧急提醒
   - 支持剩余天数和进度百分比的智能分析

### 二、定时任务调度系统

**BillingScheduleService 架构**：

```
定时任务调度中心
├── 资格检查任务 (每日 02:00)
│   ├── 重新计算月消费达标状态
│   ├── 更新用户订阅资格状态
│   └── 记录任务执行状态和影响记录数
├── 月度重置任务 (每月1号 01:00)
│   ├── 初始化新月份消费记录
│   ├── 重置所有用户资格状态为未达标
│   └── 为活跃订阅用户创建月度跟踪
├── 自动降级任务 (每月2号 03:00)
│   ├── 识别上月未达标用户
│   ├── 自动降级到入门套餐
│   └── 记录降级原因和操作日志
├── 消费提醒系统 (每日 09:00)
│   ├── 三级紧急程度策略
│   │   ├── 紧急：剩3天且进度<80%
│   │   ├── 高级：剩7天且进度<60%
│   │   └── 中级：剩15天且进度<40%
│   └── 个性化消费建议生成
├── 达标通知任务 (每日 10:00)
│   ├── 识别新达标用户
│   ├── 发送优惠价格启用通知
│   └── 跟踪通知发送状态
├── 异常监控任务 (每日 04:00)
│   ├── 检测频繁切换行为（一天>3次）
│   ├── 分析切换模式和风险等级
│   └── 自动风控措施触发
├── 数据清理任务 (每周日 01:00)
│   ├── 清理6个月前历史数据
│   ├── 优化数据库存储空间
│   └── 保持系统性能稳定
└── 报告生成任务 (每日 05:00)
    ├── 生成套餐使用分布报告
    ├── 计算关键业务指标
    └── 为管理后台提供数据支持
```

**任务执行监控机制**：

- **TaskExecutionStatus**：记录任务名称、执行时间、状态、耗时、影响记录数
- **ConcurrentHashMap 缓存**：线程安全的任务状态存储
- **手动触发支持**：管理员可手动执行特定任务
- **异常恢复机制**：任务失败时的错误记录和恢复策略

### 三、数据统计监控体系

**BillingAnalyticsService 架构**：

1. **套餐切换分析系统**：

   - **PackageSwitchRateStats**：总体切换率、方向分析（升级/降级/平级）
   - **PackageSwitchFlow**：切换流向统计，识别热门切换路径
   - **平均切换频率**：用户切换行为模式分析

2. **用户达标率监控**：

   - **QualificationRateAnalysis**：月度达标率趋势分析
   - **QualificationByPackage**：各套餐达标情况对比
   - **DailyQualificationTrend**：每日新增达标用户趋势

3. **收入分层统计引擎**：

   - **RevenueLayerStats**：收入分布层级分析
   - **基尼系数计算**：衡量收入分配不平等程度
   - **套餐收入贡献**：各套餐对总收入的贡献占比

4. **异常行为监控系统**：

   - **AbnormalSwitchMonitor**：异常切换模式检测
   - **AbnormalSwitchCase**：具体异常案例记录
   - **风险等级评估**：高/中/低风险分类

5. **用户价值分析**：

   - **UserLifetimeValue**：生命周期价值预测
   - **PackageUsageTrend**：套餐使用趋势分析
   - **PackageROIAnalysis**：套餐投资回报率计算

6. **实时监控面板**：
   - **RealTimeMetrics**：系统健康度实时指标
   - **活跃用户统计**：当日新增、活跃用户数
   - **收入监控**：实时收入和异常切换监控

### 四、技术架构特色

**Spring Boot 企业级特性**：

- **@Scheduled + Cron 表达式**：精确的定时任务调度
- **@Async 异步执行**：非阻塞的任务处理机制
- **@Transactional 事务安全**：数据一致性保障
- **@Service 服务分层**：清晰的业务逻辑分离

**高可用性设计**：

- **并发安全**：ConcurrentHashMap 确保多线程访问安全
- **失败重试**：任务执行失败时的自动重试机制
- **状态跟踪**：完整的任务执行历史和状态记录
- **监控告警**：异常情况的实时检测和通知

**性能优化策略**：

- **批量处理**：用户资格检查和数据更新的批量操作
- **索引优化**：基于查询模式的数据库索引设计
- **缓存策略**：任务状态和统计数据的内存缓存
- **数据清理**：定期清理历史数据，保持系统性能

### 五、系统集成与部署架构

**完整技术栈整合**：

```
预付费阶梯定价系统架构图
┌─────────────────────────────────────────────────────────────┐
│                    前端价格计算层                             │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐  │
│  │ 动态价格计算引擎  │ │ 智能费用预览系统  │ │ 实时监控面板     │  │
│  │ calculateActual │ │ costWithQualify │ │ getRealTime     │  │
│  │ TokenPrice()   │ │ cation()       │ │ CostPreview()   │  │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                               ↓ API调用
┌─────────────────────────────────────────────────────────────┐
│                    后端业务逻辑层                             │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐  │
│  │   定时任务调度   │ │   套餐管理服务   │ │   数据统计监控   │  │
│  │ BillingSchedule │ │ PackageSwitch   │ │ BillingAnalytics│  │
│  │ Service        │ │ Service        │ │ Service        │  │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                               ↓ 数据持久化
┌─────────────────────────────────────────────────────────────┐
│                     数据存储层                               │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐  │
│  │   用户月消费表   │ │   套餐订阅表     │ │   计费使用记录表  │  │
│  │ UserMonthly     │ │ UserPackage     │ │ BillingUsage    │  │
│  │ Spending       │ │ Subscription   │ │ Record         │  │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### 六、前端数据可视化系统

**BillingAnalytics.vue 统计监控界面**：

1. **数据概览面板**：

   - 总收入、活跃用户、套餐订阅、达标率四大核心指标
   - 实时趋势显示（上升/下降/平稳）
   - 动态百分比变化展示

2. **交互式图表系统**：

   - **收入趋势分析**：支持日/周/月维度切换，ECharts 渐变面积图
   - **用户分层分析**：按消费金额分层的饼图展示，支持 hover 交互
   - **套餐 ROI 分析**：柱状图显示，支持收入/利润率视图切换
   - **资格达标分析**：达标率趋势线图和消费分布柱状图

3. **实时监控面板**：

   - 今日收入对比、在线用户峰值、系统负载监控
   - 异常告警计数，支持点击查看详情
   - 30 秒自动刷新机制

4. **数据表格功能**：
   - 用户详细信息列表（ID、姓名、套餐、消费、达标状态、生命周期价值）
   - 智能搜索和筛选（按用户名、套餐类型）
   - 分页显示，支持 20/50/100/200 条切换
   - 操作功能：查看用户详情、导出个人数据

**API 集成架构 (billingAnalyticsApi)**：

```typescript
// 8个核心分析接口
billingAnalyticsApi {
  getOverviewData()          // 概览数据获取
  getRevenueTrend()          // 收入趋势数据
  getUserLayerData()         // 用户分层分析
  getPackageROIAnalysis()    // 套餐ROI分析
  getQualificationAnalysis() // 资格达标分析
  getRealTimeMetrics()       // 实时监控指标
  getUserList()              // 用户详细列表
  exportAnalyticsReport()    // 分析报告导出
}

// 完整TypeScript类型体系
- OverviewData, TrendData, RevenueTrendData
- UserLayerData, PackageROIData, QualificationAnalysisData
- RealTimeMetricsData, UserListParams, PaginatedUserData
- UserDetailData, AlertData (10+接口类型)
```

**Vue Router 路由集成**：

- 路由配置：`/billing/analytics`
- 组件懒加载：`() => import('@/views/billing/BillingAnalytics.vue')`
- 权限控制：`auth: ['billing:analytics:view']`
- 图标设置：`icon: 'ri:bar-chart-line'`

### 七、企业级功能特性

**数据导出和报表**：

- 支持 Excel/PDF 格式导出
- 自定义时间范围报告生成
- 用户级别数据导出功能

**响应式设计**：

- 移动端适配（768px 断点）
- Grid 布局自动适应屏幕尺寸
- 图表容器动态调整

**用户体验优化**：

- Loading 状态管理
- 错误处理和友好提示
- 数据刷新和实时更新
- 工具提示和帮助信息

**系统能力总览**：

1. **核心商业能力**：

   - ✅ 预付费阶梯定价模型完整实现
   - ✅ 达标享优惠，未达标用标准价的核心逻辑
   - ✅ 完整的数据可视化和监控体系
   - ✅ 企业级统计分析和报表功能
   - ✅ 智能月消费跟踪和资格状态管理
   - ✅ 自动降级和升级建议系统

2. **运营管理能力**：

   - ✅ 8 个核心定时任务的全自动化运营
   - ✅ 三级紧急程度的智能提醒策略
   - ✅ 异常行为检测和风控机制
   - ✅ 完整的数据统计和分析体系

3. **用户体验能力**：

   - ✅ 实时价格预览和节省金额提示
   - ✅ 个性化充值建议和进度跟踪
   - ✅ 动态价格标签和紧急程度显示
   - ✅ 套餐切换的智能引导和确认

4. **技术保障能力**：
   - ✅ Spring Boot 企业级架构
   - ✅ 事务安全和并发控制
   - ✅ 任务执行监控和异常恢复
   - ✅ 性能优化和数据清理机制

**部署就绪特性**：

- **生产环境配置**：完整的 application-prod.yml 配置
- **监控告警集成**：与现有监控系统的集成接口
- **数据库迁移脚本**：完整的 DDL 和初始数据脚本
- **API 文档完整**：Swagger 集成的完整 API 文档
- **测试覆盖**：单元测试和集成测试的完整覆盖

---

## 前期架构变更记录

### 前端套餐组件系统架构设计 (2025-06-19 19:53:52)

1. **套餐选择页面** (`PackageSelection.vue`)：

   - 三栏式套餐对比展示，支持价格、特性、限制对比
   - 当前套餐高亮、推荐套餐标识
   - 集成套餐切换功能，支持预览和确认
   - 响应式栅格布局，移动端优化

2. **当前套餐信息组件** (`CurrentPackageInfo.vue`)：

   - 套餐基本信息展示（名称、价格、状态）
   - 月消费进度可视化（进度条、百分比、剩余天数）
   - 资格状态判断和显示
   - 多尺寸支持（small/default/large）

3. **月消费进度组件** (`MonthlyProgressCard.vue`)：

   - 详细进度统计（已消费/最低要求/剩余天数）
   - 智能充值建议系统（high/medium/low 三级紧急程度）
   - 可视化进度条（颜色编码状态）
   - 一键充值和详情查看

4. **套餐切换弹窗** (`PackageSwitchModal.vue`)：

   - 切换前后详细对比表格
   - 影响分析（生效时间、本月影响、下月影响、长期影响）
   - 智能警告系统（价格变动、功能限制、额度变化）
   - 建议信息和切换原因填写

**状态管理架构**：

- **扩展 packages store**：新增用户订阅状态、月度进度、套餐切换方法
- **响应式数据流**：组件 ↔ store ↔ API 的双向数据绑定
- **错误边界处理**：统一的错误捕获和用户友好提示
- **加载状态管理**：细粒度的 loading 状态控制

**工具函数库架构**：

创建了 `billing.ts` 工具函数库，包含 18 个核心函数：

- **价格计算类**：套餐优惠计算、费用预估、价格格式化
- **状态判断类**：余额状态、订阅状态、资格状态格式化
- **进度管理类**：进度百分比计算、剩余消费计算、充值建议
- **套餐对比类**：升级判断、特性对比、切换验证
- **智能分析类**：警告生成、推荐原因分析

**样式系统架构**：

设计了完整的 `billing.scss` 样式系统：

- **CSS 变量系统**：套餐主题色、状态色、进度条色定义
- **组件样式库**：卡片、表格、进度条、状态标签、余额显示
- **响应式设计**：768px/480px 断点适配
- **主题支持**：亮色/暗色主题自动切换
- **动画效果**：脉冲动画、发光效果、平滑过渡

**组件集成架构**：

1. **BalanceDisplay 扩展**：

   - 新增 `showPackageInfo` 属性控制套餐信息显示
   - 集成 `CurrentPackageInfo` 组件
   - 保持向后兼容性

2. **Recharge 页面扩展**：

   - 独立套餐信息卡片展示
   - 避免重复显示，优化页面布局
   - 保持充值流程完整性

**类型定义架构**：

扩展 `packages.ts` 类型文件，新增：

- `UserSubscription`：用户订阅信息
- `MonthlyProgress`：月消费进度
- `PackageSwitchRequest/Preview/Result`：套餐切换相关
- `PackageComparison`：套餐对比数据
- `SubscriptionStatus/QualificationStatus`：状态枚举

**API 接口架构**：

扩展 `packages.ts` API 文件，新增：

- `getUserSubscription()`：获取用户订阅
- `getMonthlyProgress()`：获取月度进度
- `switchPackage()`：执行套餐切换
- `previewPackageSwitch()`：预览切换效果

**技术优势**：

- **高内聚低耦合**：组件独立开发、测试、维护
- **可扩展性**：基于接口设计，便于功能扩展
- **性能优化**：懒加载、计算属性、条件渲染
- **可测试性**：纯函数设计，易于单元测试
- **可维护性**：清晰的代码结构和文档

**组件文件清单**：

1. `views/billing/PackageSelection.vue` - 套餐选择页面
2. `components/billing/CurrentPackageInfo.vue` - 当前套餐信息
3. `components/billing/MonthlyProgressCard.vue` - 月消费进度
4. `components/billing/PackageSwitchModal.vue` - 套餐切换弹窗
5. `stores/packages.ts` - 套餐状态管理（扩展）
6. `utils/billing.ts` - billing 工具函数
7. `assets/styles/billing.scss` - billing 样式系统

---

### 前端路由修复：余额组件详情跳转

**变更时间**：2025-06-19 13:53:14

**问题描述**：余额组件中的"查看详情"按钮跳转到不存在的路由 `/billing/history`

**修复内容**：

- 文件：`dp-web-fa/src/components/billing/BalanceDisplay.vue`
- 方法：`handleViewHistory()`
- 修改：将路由从 `/billing/history` 改为 `/billing/usage-history`
- 目标：正确跳转到消费记录页面

**前端路由映射**：

- `/billing/overview` → 计费概览页面
- `/billing/recharge` → 账户充值页面
- `/billing/usage-history` → 消费记录页面
- `/billing/appeal` → 费用申诉页面

---

## 最新架构变更 (2025-06-19)

### 计费系统不计费功能架构设计

**变更时间**：2025-06-19 09:59:50

**功能概述**：

为计费系统增加智能不计费判断功能，针对特定场景（余额不足、无查询结果、模板无数据等）的消息提供免费记录机制，既保证数据完整性又满足业务需求。

**架构设计**：

1. **配置化设计**：

   - 新增 `NoChargeConfig` 配置类，支持通过 application.yml 配置不计费规则
   - 支持关键词匹配、功能开关、记录开关等灵活配置

2. **服务分层设计**：

   - `NoChargeDetectionService`: 不计费检测服务接口
   - `NoChargeDetectionServiceImpl`: 基于关键词匹配的检测实现
   - `NoChargeResult`: 封装检测结果的数据传输对象

3. **条件计费机制**：
   - 在 `BillingService` 中新增 `performConditionalBilling` 方法
   - 集成不计费检测逻辑，根据消息内容决定计费行为
   - 保持现有计费流程兼容，仅在需要时调用条件计费

**核心组件**：

- **NoChargeConfig**: 不计费配置类，支持以下配置项：

  - `enabled`: 功能开关
  - `recordEnabled`: 是否记录不计费请求
  - `insufficientBalanceKeywords`: 余额不足消息关键词
  - `noResultKeywords`: 无查询结果关键词
  - `templateNoDataKeywords`: 模板无数据关键词
  - `systemMessageKeywords`: 系统消息关键词

- **NoChargeDetectionService**: 不计费检测服务，提供：

  - `shouldNotCharge()`: 主要检测方法
  - `getNoChargeType()`: 获取不计费类型
  - 分类检测方法（余额不足、无结果、模板无数据、系统消息）

- **BillingType 扩展**: 新增计费类型：
  - `NO_CHARGE_INSUFFICIENT_BALANCE`: 余额不足免费
  - `NO_CHARGE_NO_RESULT`: 无查询结果免费
  - `NO_CHARGE_TEMPLATE_NO_DATA`: 模板无数据免费
  - `NO_CHARGE_SYSTEM_MESSAGE`: 系统消息免费

**数据库变更**：

- 扩展 `billing_type` 字段长度至 50 字符
- 更新字段注释，支持新的计费类型枚举值
- 新增 `NO_CHARGE` 计费状态

**前端适配**：

- 扩展 TypeScript 类型定义，支持新的计费类型
- 在消费记录页面增加计费类型列显示
- 提供计费类型的本地化显示和颜色标识

**配置示例**：

```yaml
billing:
  no-charge:
    enabled: true
    record-enabled: true
    insufficient-balance-keywords:
      - "账户余额不足"
      - "请先充值后再发起对话"
    no-result-keywords:
      - "未找到"
      - "没有查询到预期结果"
    template-no-data-keywords:
      - "模板数据为空"
      - "无模板数据"
```

**技术优势**：

- 配置化：支持灵活的关键词配置和规则调整
- 可扩展：基于接口设计，便于扩展新的检测规则
- 数据完整：所有请求都记录，不计费消息单独标识
- 向后兼容：不影响现有计费流程和数据结构

---

## 最新架构变更 (2025-06-13)

### nginx CSP 配置优化 - 修复 HTTP 图片协议转换问题

**变更时间**：2025-06-13 21:35:00

**问题背景**：

- 聊天消息中的图片链接为 HTTP 协议，在本地开发环境显示正常
- 生产环境通过 HTTPS 访问时，浏览器自动将 HTTP 图片升级为 HTTPS 导致加载失败
- 根本原因：nginx CSP 配置中 `connect-src 'self' https:` 限制了只能使用 HTTPS 连接

**解决方案**：

修改 Content Security Policy (CSP) 配置，将 `connect-src` 指令从仅支持 HTTPS 改为同时支持 HTTP 和 HTTPS

**配置变更**：

```
修改前：connect-src 'self' https:;
修改后：connect-src 'self' http: https:;
```

**影响文件**：

1. `dp-landingpage/nginx.conf`：
   - HTTP server 块 CSP 策略
   - HTTPS server 块 CSP 策略
2. `dp-web-fa/nginx.conf`：
   - HTTP server 块 CSP 策略
   - HTTPS server 块 CSP 策略

**安全考虑**：

- 仅允许已在 `img-src` 中配置的 `*.fg-china.cn` 域名图片资源
- 保持其他 CSP 指令的安全限制不变
- 图片资源仍然受到域名白名单限制

**部署要求**：

- 需要重启相关 nginx 容器使配置生效
- 建议后续将外部图片源升级为 HTTPS 或实施图片代理方案

### 前端计费系统类型安全改进

**变更背景**：修复前端计费模块的 TypeScript 编译错误，提升代码类型安全性

**核心改进**：

1. **类型定义层完善**：

   - 补充 `PaymentMethod`、`PaginationConfig`、`AttachmentInfo` 等缺失类型
   - 扩展 `AppealRecord` 支持前端组件需要的字段（title、contact、priority）
   - 统一分页响应数据结构类型定义

2. **Store 层方法补全**：

   - 新增支付方式、申诉管理、文件下载等业务方法
   - 统一分页对象结构，确保类型一致性
   - 完善错误处理和状态管理机制

3. **API 层接口对齐**：
   - 补充后端控制器对应的前端 API 方法
   - 修复下载类方法的返回类型包装
   - 统一 mock 数据结构与实际接口响应格式

**技术指标**：

- TypeScript 编译错误从 91 个减少至 70 个（修复进度 23%）
- 计费模块核心功能类型安全性显著提升
- API 接口与后端控制器完全对齐

**影响范围**：

- 前端：`src/types/billing.ts`、`src/stores/billing.ts`、`src/api/billing.ts`
- 后端对应：BillingController、BillingAppealController、UserBalanceController 等

---

## 系统概述

本文档描述了 dips-pro 应用的整体架构设计。

## 最新架构更新 (2025-06-12)

### 前端计费系统架构设计

#### 前端计费模块架构

```
dp-web-fa/src/
├── types/billing.ts           # 计费系统类型定义
├── api/billing.ts            # 计费API服务层
├── stores/billing.ts         # Pinia状态管理
├── utils/format.ts           # 格式化工具函数
└── components/billing/       # 计费组件库
    ├── BalanceDisplay.vue    # 余额显示组件
    ├── TokenCounter.vue      # Token计数器组件
    ├── RechargeForm.vue      # 充值表单组件（规划中）
    ├── UsageHistory.vue      # 消费记录组件（规划中）
    └── AppealForm.vue        # 申诉表单组件（规划中）
```

#### 技术特性

- **完整 TypeScript 支持**：类型安全的 API 调用和状态管理
- **响应式设计**：支持移动端和桌面端的自适应布局
- **暗色主题**：完整的暗色主题支持和主题切换
- **实时监控**：余额状态实时监控和自动刷新机制
- **性能优化**：防抖处理、懒加载、状态缓存
- **用户体验**：动画效果、加载状态、错误提示

#### 状态管理设计

- **账户状态**：余额、货币、账户状态实时同步
- **配置缓存**：计费配置本地缓存和自动更新
- **记录管理**：使用记录、充值记录、申诉记录的分页和缓存
- **错误处理**：统一的错误处理和用户提示机制

#### 组件特性详解

**BalanceDisplay 组件**：

- 实时余额显示和状态指示
- 支持多种尺寸（small/default/large）
- 自动刷新和手动刷新功能
- 余额变动动画和通知提示
- 快速充值和详情查看入口

**TokenCounter 组件**：

- 实时 Token 数量计算和费用预估
- 防抖优化，避免频繁 API 调用
- 余额充足性检查和警告提示
- 详细计费信息展开/收起
- 支持多模型配置和切换

#### 开发规划

- [x] 基础设施搭建（类型定义、API、状态管理）
- [x] 核心组件开发（余额显示、Token 计数器）
- [x] 页面功能开发（充值、消费记录、申诉）
- [x] 计费概览页面开发
- [x] 路由配置和导航集成
- [x] 聊天组件集成（TokenCounter 集成到 BaseSender）
- [ ] 余额不足时的发送限制逻辑
- [ ] 完善测试和性能优化

## 1. 整体模块划分

应用主要包含以下模块：

- **前端 (Frontend - `dips-pro-web-fa`)**: 用户交互界面，使用 Vue 3 + TypeScript + Vite 和 Ant Design X Vue。负责展示信息、接收用户输入、发送请求给后端。包含以下重要组件：
  - **ChatMessageList**: 聊天消息列表组件，负责渲染用户和助手的消息气泡。
  - **ChatThoughtChain**: 思维链组件，负责解析和展示 AI 助手的中间思考步骤和工具调用过程，提升透明度和可解释性。
  - **ChatSender**: 聊天输入组件，负责接收用户输入并发送消息。
  - **ChatFunctionButtons**: 功能按钮组件，提供新建会话、查看历史会话等功能。
  - **ConversationHistory**: 会话历史组件，展示并管理历史会话列表。
- **后端 (Backend - `dips-pro-server`)**: 应用核心，使用 Java 23 + Spring Boot。处理业务逻辑、管理数据、与外部服务（AI, n8n）交互。包含以下内部模块：
  - **智能问答核心 (Q&A Core)**: 处理用户提问，选择模型，整合信息，生成回答。
  - **智能体管理器 (Agent Manager)**: 通过与 n8n 交互，管理 AI 智能体的配置。
  - **n8n 接口 (n8n Interface)**: 封装与 n8n 通信的逻辑 (使用 Spring WebClient)。
- **数据库 (Database)**:
  - **PostgreSQL**: 主要关系型数据库，存储用户信息、聊天记录、智能体基础信息（可选缓存）。
  - **PGVector (PostgreSQL Extension)**: 用于 RAG 的向量存储和检索。
  - **Redis**: 用于缓存，例如缓存 n8n 获取的 Agent 配置、用户会话信息等。
  - **MySQL (查询历史数据)**: 用于查询历史表数据并构建索引。
  - **MongoDB (可选)**: 未来可用于存储结构更灵活的数据。
- **消息队列 (Message Queue - 可选)**: 如 RabbitMQ 或 Kafka。用于处理耗时任务（调用 AI、复杂 n8n 工作流），提升用户体验。按需引入。
- **知识库/向量数据库 (Knowledge Base/Vector DB)**: 即使用 PostgreSQL + PGVector 实现。

## 2. 关键技术选型

- **前端**:
  - **主应用 (dp-web-fa)**: Vue 3 + TypeScript + Vite + pnpm，使用 Ant Design Vue 和 Element Plus
  - **落地页 (dips-pro-landingpage)**: React 应用
- **后端**: Java 23, Spring Boot 3.x
- **数据库**: PostgreSQL, PGVector, Redis, MySQL
- **工作流/智能体配置**: n8n (外部系统)
- **部署**: Docker, Docker Compose
- **通信协议**: RESTful API
- **数据格式**: JSON
- **身份认证**: JWT (JSON Web Tokens)
- **API 文档**: OpenAPI v3 (Swagger)

## 2.2 后端架构重构 (2025-06-10 更新)

### 楼盘画像服务重构

为解决 `ProjectProfileServiceImpl` 类代码过长（1561 行）的问题，实施了分层架构重构：

#### 新增 DAO 层

- **ProjectAnalyticsDao**: 楼盘分析数据访问接口

  - `queryIndexScore()`: 查询指标得分
  - `queryIndexScoreWithOwnerType()`: 查询带业主类型的指标得分
  - `batchQueryIndexScore()`: 批量查询指标得分
  - `queryTotalSample()`: 查询样框数据
  - `batchQueryTotalSample()`: 批量查询样框数据
  - `queryAgeGroup()`: 查询年龄段数据
  - `queryAna()`: 查询分析数据

- **ProjectEnrichmentDao**: 楼盘数据丰富化访问接口
  - `queryLjLouPan()`: 查询链家楼盘数据
  - `queryLjLouPanLayout()`: 查询链家楼盘户型数据

#### 新增业务处理器层

- **ProjectProfileProcessor**: 楼盘画像业务处理器
  - `processProjectQuery()`: 处理楼盘查询逻辑（精确查询 → 模糊查询 → 选择选项）
  - `enrichProjectProfileWithDetail()`: 丰富楼盘画像基础数据
  - `enrichProjectProfileWithData()`: 丰富楼盘画像完整数据
  - `enrichSalesInfo()`: 丰富销售信息相关数据
  - `enrichAnalysisData()`: 丰富分析数据
  - 私有方法：`enrichIndexScore()`, `enrichTotalSample()`, `enrichLjLouPan()` 等

#### 重构后的服务层

- **ProjectProfileServiceImplRefactored**: 重构后的服务实现
  - 使用依赖注入的 `ProjectProfileProcessor` 和 DAO 组件
  - 专注于流程编排和异常处理
  - 每个主要方法的代码行数大幅减少
  - 提高了代码的可读性和可维护性

#### 重构优势

- **单一职责原则**: 每个类的职责更加明确
- **代码复用**: 消除了大量重复代码
- **可测试性**: 各层组件可以独立测试
- **可维护性**: 代码结构更清晰，便于后续维护和扩展
- **性能优化**: 支持批量查询，减少数据库访问次数

## 2.1 Docker 容器化部署架构

### 反向代理部署架构 (2025-05-28 更新)

从 2025-05-28 开始，前端应用采用反向代理部署架构，通过 dp-landingpage 的 nginx 反向代理访问 dp-web-fa：

#### dp-landingpage (官网/落地页 + 反向代理)

- **域名**: `dipsai.cn`
- **端口**: HTTP (80), HTTPS (443)
- **容器名**: `dp-landingpage`
- **技术栈**: React + TypeScript + Vite
- **构建配置**:
  - 基于 `node:20-alpine` 的多阶段构建
  - 使用 npm 包管理器
  - Nginx 静态文件服务 + 反向代理
- **反向代理配置**:
  - `/app/` → 代理到 `dp-web-fa:8080/`
  - `/server/` → 代理到 `dips-pro-server:8080/api/`
- **部署文件**:
  - `dp-landingpage/Dockerfile`: 独立构建配置
  - `dp-landingpage/nginx.conf`: 包含反向代理配置
  - `deploy/docker-compose.landingpage.prod.yml`: 部署编排
  - `deploy/jenkinsfile.landingpage`: CI/CD 流水线

#### dp-web-fa (主应用)

- **访问路径**: `dipsai.cn/app`
- **端口**: 内部端口 8080/8443 (不对外暴露)
- **容器名**: `dp-web-fa`
- **技术栈**: Vue 3 + TypeScript + Vite + pnpm
- **构建配置**:
  - 基于 `node:20-alpine` 的多阶段构建
  - 使用 pnpm 包管理器，确保依赖版本一致性
  - Nginx 静态文件服务
  - API 基础路径: `/server`
- **部署文件**:
  - `dp-web-fa/Dockerfile`: 独立构建配置，API 路径更新为 `/server`
  - `dp-web-fa/nginx.conf`: 移除独立域名配置，适配反向代理
  - `deploy/docker-compose.webapp.prod.yml`: 移除端口映射
  - `deploy/jenkinsfile.webapp`: CI/CD 流水线

#### 共同特性

- **配置挂载**:
  - SSL 证书目录挂载: `/mnt/{项目名}/certs:/etc/nginx/certs:ro`
  - Nginx 配置文件挂载: `/mnt/{项目名}/nginx.conf:/etc/nginx/conf.d/default.conf:ro`
  - **配置文件同步**: Jenkins 构建过程中自动从 Git 仓库复制最新的 nginx.conf 文件到服务器挂载目录
- **网络**: 共享 `dips-pro-network` 外部网络
- **SSL 支持**: 支持 TLS 1.2/1.3，需要为各自域名准备证书
- **健康检查**: 提供 `/health` 端点
- **生产优化**: gzip 压缩、静态资源缓存、安全头配置

#### 部署优势

- **独立部署**: 两个应用可以独立构建、部署和扩展
- **统一域名**: 通过路径区分不同应用，便于管理和访问
- **资源隔离**: 各自独立的资源配置和监控
- **CI/CD 分离**: 独立的构建流水线，减少相互影响
- **反向代理**: 通过 nginx 反向代理实现路径路由，保持架构灵活性

## 2.5 预付费阶梯定价系统

### 概述

预付费阶梯定价系统是 DIPS Pro 计费模块的核心功能，实现基于用户月消费金额的套餐优惠机制。用户根据当月实际消费金额享受不同的 Token 价格优惠。

### 核心理念

- **无 Token 数量限制**：所有套餐都不限制 Token 使用数量
- **无模型限制**：支持所有 AI 模型类型
- **月消费达标制**：只要达到套餐的最低月消费要求，即可享受优惠价格
- **实时价格切换**：根据当月消费状态实时选择计费价格

### 数据库架构

#### 套餐表扩展 (b_billing_packages)

**新增字段**：

- `monthly_minimum_spend`：最低月消费要求（DECIMAL(10,2)）
- `package_level`：套餐等级标识（VARCHAR(20)），支持 BASIC/STANDARD/PREMIUM

#### 月消费统计表 (b_user_monthly_spending)

**核心字段**：

- `user_id`：用户 ID
- `package_id`：用户当前套餐 ID
- `month_year`：月份标识（格式：2024-01）
- `total_spend`：当月总消费金额
- `minimum_required`：当月最低消费要求
- `qualification_status`：达标状态（QUALIFIED/UNQUALIFIED）
- `package_switched_at`：套餐切换时间

**索引设计**：

- 唯一索引：`(user_id, month_year)` 防止重复记录
- 复合索引：`(package_id, month_year)` 支持套餐级别分析
- 单字段索引：`qualification_status`、`month_year` 支持统计查询

### 服务架构

#### MonthlySpendingService

**核心方法**：

- `updateMonthlySpending()`: 实时更新用户月消费金额
- `isQualifiedForMonth()`: 检查用户当月是否达标
- `handlePackageSwitch()`: 处理套餐切换的月消费影响
- `getCurrentMonthYear()`: 获取当前月份字符串

**业务逻辑**：

- 每次计费后自动更新月消费统计
- 达标状态自动计算和更新
- 支持月中套餐切换的复杂场景

#### PackageSwitchService

**功能特性**：

- 套餐切换前置检查，防止恶意频繁切换
- 批量升级达标用户的自动化处理
- 套餐升级建议算法
- 24 小时内切换撤销功能

**切换限制**：

- 每月只允许切换一次套餐
- 切换后立即重新计算月消费要求
- 支持切换撤销（24 小时内）

#### BillingService 集成

**阶梯定价核心逻辑**：

```java
// 1. 获取用户当前套餐
BillingPackage billingPackage = getBillingPackageForUser(userBalance);

// 2. 根据月消费状态选择有效价格
BillingPackage effectivePackage = getEffectivePackageForUser(userId, billingPackage);

// 3. 使用有效价格计算费用
return calculateTokenCostWithPackage(effectivePackage, inputTokens, outputTokens);
```

**月消费更新集成**：

```java
// 计费完成后自动更新月消费统计
String currentMonth = monthlySpendingService.getCurrentMonthYear();
monthlySpendingService.updateMonthlySpending(userId, packageId, totalCost, currentMonth);
```

### 套餐级别设计

#### 入门套餐 (BASIC)

- **月消费要求**：0 元（无要求）
- **Token 价格**：输入 0.008，输出 0.012
- **适用场景**：轻度使用用户，标准价格

#### 标准套餐 (STANDARD)

- **月消费要求**：200 元
- **Token 价格**：输入 0.005，输出 0.008
- **适用场景**：中度使用用户，优惠价格

#### 高级套餐 (PREMIUM)

- **月消费要求**：1000 元
- **Token 价格**：输入 0.003，输出 0.005
- **适用场景**：重度使用用户，最优价格

### 技术实现特性

#### 实时价格切换

- 每次计费时实时检查用户月消费状态
- 达标用户自动享受套餐优惠价格
- 未达标用户使用入门套餐标准价格
- 无需用户手动操作，系统自动处理

#### 数据完整性保障

- 外键约束确保数据关联完整性
- 唯一索引防止重复统计记录
- 触发器自动维护时间戳
- 完善的事务处理机制

#### 异常处理机制

- 月消费统计更新失败不影响主要计费流程
- 完善的错误日志记录
- 优雅的降级处理策略
- 数据一致性保障

### 业务流程

#### 用户计费流程

1. 用户发起 AI 对话请求
2. 系统获取用户当前套餐信息
3. 检查用户当月消费达标状态
4. 选择对应的 Token 价格（达标用优惠价，未达标用标准价）
5. 执行计费并扣除费用
6. 更新用户月消费统计
7. 重新计算达标状态

#### 套餐切换流程

1. 用户或系统发起套餐切换请求
2. 验证切换资格（是否已切换、套餐是否存在等）
3. 更新用户套餐信息
4. 重新计算月消费要求和达标状态
5. 记录切换时间
6. 应用新的计费价格

#### 自动升级流程

1. 系统定期扫描达标用户
2. 分析用户消费模式和套餐匹配度
3. 生成升级建议
4. 批量执行符合条件的用户升级
5. 记录升级日志和统计信息

### 监控与统计

#### 关键指标

- 各套餐级别用户分布
- 月消费达标率统计
- 套餐切换频率分析
- 价格优惠效果评估

#### 数据分析支持

- 按套餐级别的消费分布统计
- 用户消费趋势分析
- 套餐升级转化率跟踪
- 系统收入影响评估

### 扩展性设计

#### 灵活的套餐配置

- 支持动态调整套餐价格和消费要求
- 支持新增套餐级别
- 支持季度、年度等更长周期的优惠机制

#### 个性化优惠

- 为特定用户群体定制专属套餐
- 支持基于使用行为的动态定价
- 支持促销活动和临时优惠

这个阶梯定价系统通过技术手段实现了灵活的商业模式，既保证了用户的使用体验，又优化了平台的收入结构，为业务发展提供了强有力的支撑。

## 3. 前后端数据交互

- **方式**: 基于 HTTP/HTTPS 的 RESTful API。
- **数据格式**: 请求体和响应体主要使用 JSON。
- **身份认证**: 用户登录后，后端生成 JWT 返回给前端。前端在后续请求的 `Authorization` Header 中携带 `Bearer <token>`。后端通过过滤器/拦截器验证 JWT。
- **API 安全**: 所有 API 接口通过 JWT 认证保护，使用 Bearer Token 方式。配置位于 `SecurityConfig` 类和 `application.yml` 的相关部分。生产环境通过环境变量配置 Redis 连接和数据库连接信息。
  - **JWT 认证传递链路**: 实现了完整的用户身份认证传递机制，支持 n8n 工作流调用需要认证的 dp-server 接口：
    - **认证获取**: `ChatServiceImpl.processAndSaveMessage` 方法通过 `RequestContextHolder` 获取当前 HTTP 请求上下文
    - **Token 提取**: 从 `Authorization` 头提取 Bearer token，从请求属性获取用户信息（userId、username）
    - **信息传递**: 将 `userToken`、`userId`、`username` 添加到 n8n 请求体中
    - **n8n 使用**: n8n 工作流可使用传递的 `userToken` 作为 `Authorization: Bearer <userToken>` 调用 dp-server 接口
    - **应用场景**: 支持 n8n 调用如 `http://47.99.92.35:5681/api/data-query/userProfile/{{ $json.body.slots.customer_feature }}` 等需要认证的接口
    - **技术实现**:
      - `getCurrentUserToken()`: 从 HTTP 请求头获取 JWT token
      - `getCurrentUserId()`: 从请求属性获取用户 ID
      - `getCurrentUsername()`: 从 Spring Security 上下文或请求属性获取用户名
      - 完善的异常处理和详细的日志记录机制
- **敏感数据掩码**: 系统实现了敏感数据掩码功能，通过 `MaskUtil` 工具类对日志中的敏感信息进行脱敏处理：
  - 手机号掩码：只显示前 2 位和后 2 位，中间使用星号(\*)替代，如 `13*******45`
  - 应用场景：所有涉及手机号的日志输出，包括但不限于：
    - `DataMigrationServiceImpl` 中的手机号查询、结果统计和数据处理日志
    - `DataMigrationController` 中的 API 调用日志
    - `DataQueryExample` 中的查询示例日志
    - `DataQueryController` 和 `DataQueryServiceImpl` 等类中的手机号相关日志
  - 实现机制：静态工具方法 `MaskUtil.maskMobile(String mobile)` 处理字符串替换
  - 边界处理：对长度不足 4 位的手机号保持原样，防止掩码过程中出现异常
  - 安全保障：确保业务数据处理过程中原始手机号不受影响，仅在日志输出时应用掩码
- **用户画像标签分析系统**: 基于用户历史购买记录自动分析生成用户标签的模块：
  - **分析器组件架构**:
    - `UserProfileAnalyzer`: 抽象基类，定义标签分析的基础框架和公共方法
      - 提供`addTag`方法：统一添加标签功能，避免重复标签
      - 提供`hasTag`方法：检查标签是否已存在
      - 定义`analyzeAllTags`方法：执行所有标签分析的入口方法
      - 定义`doAnalyze`抽象方法：由子类实现具体分析逻辑
      - 定义`createAnalyzers`方法：由子类实现分析器创建逻辑
    - `MainUserProfileAnalyzer`: 具体实现类，继承 UserProfileAnalyzer，负责协调各专用分析器
      - 创建并管理所有子分析器的实例(basicAnalyzer, propertyAnalyzer, brandAnalyzer, preferenceAnalyzer)
      - 通过 doAnalyze 方法调用各子分析器的 doAnalyze 方法，充分利用多态特性
      - 汇总所有分析结果到统一的标签集合
    - 子分析器类(均继承 UserProfileAnalyzer)：
      - `BasicTagAnalyzer`: 分析基础标签，如多次置业、投资客等购买行为
      - `BrandTagAnalyzer`: 分析品牌相关标签，如品牌忠实、单一品牌持有等
      - `PropertyTagAnalyzer`: 分析房产特性标签，如装修类型、户型偏好等
      - `PreferenceTagAnalyzer`: 分析用户偏好标签，如配套设施、设计规划等
        - `analyzeFacilityPreferenceTag`: 分析配套设施偏好，比较项目设施数量与城市均值
        - `analyzeRawDataBasedPreferenceTag`: 通用偏好分析方法，接受评分字段名和关键词数组作为参数
          - 支持多个评分字段和多个关键词的偏好分析
          - 实现两类分析通道：评分分析和开放题文本关键词匹配
          - 汇总所有项目的 rawData 后一次性分析，避免数据分散
          - 动态参数化标签代码、标签名称和标签描述，提高代码复用性
        - `analyzeDesignPreferenceTag`: 分析设计偏好，调用通用方法检测"rk0a5"评分和"设计"关键词
        - `analyzeQualityPreferenceTag`: 分析质量偏好，调用通用方法检测"re0a5"评分和"质量/做工/工艺/材料/用料"等关键词
        - `analyzeDecorationPreferenceTag`: 分析装修偏好，匹配装修相关关键词
        - `analyzePropertyServicePreferenceTag`: 分析物业服务偏好，匹配服务相关关键词
        - `analyzePromiseFulfillmentTag`: 分析兑现承诺偏好，匹配信用相关关键词
        - `analyzeHighStandardPreferenceTag`: 分析高标准偏好，检测 ra1a5 低分评价
      - 每个子分析器在自己的 doAnalyze 方法中实现对内部各具体分析方法的调用
  - **标签分析流程**:
    1. 用户查询时，`UserProfileDto`调用`analyzeUserProfileTags`方法
    2. 创建`MainUserProfileAnalyzer`实例，传入项目列表
    3. 调用`analyzeAllTags`方法开始分析
    4. `MainUserProfileAnalyzer`在 createAnalyzers 方法中初始化各个专用分析器
    5. `MainUserProfileAnalyzer`的 doAnalyze 方法调用各子分析器的 doAnalyze 方法
    6. 各子分析器在各自的 doAnalyze 方法中执行自己的具体分析方法
    7. 子分析器通过继承的`addTag`方法，向统一的标签集合添加标签
    8. 分析完成后，标签集合被返回并保存在`UserProfileDto`中
  - **标签分类体系**:
    - 购买行为类标签: 多次置业、投资客、跨城市置业等
    - 客户价值类标签: 高净值客户等
    - 品牌偏好类标签: 品牌忠实、单一品牌持有等
    - 居住偏好类标签: 配套设施偏好、设计规划偏好、大/小户型偏好、高标准偏好等
    - 购买决策类标签: 质量做工偏好、物业服务偏好等
  - **标签分析算法**:
    - 基于阈值的标签识别: 使用 PREFERRED_CATEGORY_THRESHOLD(0.5)判断特征是否占主导
    - 基于项目比例的标签生成: 根据符合条件的项目占比判断用户倾向
    - 多维度交叉分析: 综合考虑多个维度数据，如评分、好评文本等
    - **设计偏好标签分析算法**：
      - 实现三种并行评估通道，满足任一条件即可触发标签：
        1. **评分数据分析**：
           - 从`project.getRawData()`中提取`rk0a5`字段值
           - 计算有评分的记录数和低分(1-3 分)记录数
           - 当低分占比`≥50%`时，项目符合设计偏好条件
        2. **好评文本分析**：
           - 从`project.getTextGood()`中提取好评文本
           - 匹配设计关键词"设计"
           - 当好评文本中包含关键词"设计"时，项目符合设计偏好条件
        3. **差评文本分析**：
           - 从`project.getTextBad()`中提取差评文本
           - 匹配设计关键词"设计"
           - 当差评文本中提及关键词"设计"时，项目符合设计偏好条件
      - 标签触发条件：
        - 只要有一个符合条件的项目，即可添加标签
  - **标签数据模型**:
    - `UserProfileTag`: 封装标签基本信息，包括代码、名称、描述、类型、置信度等
    - 匹配项目追踪: 记录每个标签关联的具体项目，支持标签验证和解释
- **API 设计**: 遵循 RESTful 风格，使用 OpenAPI 规范定义接口，便于生成文档和客户端代码。
- **核心聊天 API**:
  - `POST /api/chat`: 发送聊天消息。
    - 请求体 (`ChatRequest`): `{ \"message\": \"用户输入\", \"conversationId\": \"可选的会话ID\", \"templateId\": \"可选的模板ID\", \"slots\": \"可选的槽位值对象\" }`
    - 响应体 (`ChatResponse`): 统一使用 list 格式的消息内容：
      - **统一格式**: `{ \"list\": [{\"type\": \"text\", \"content\": \"文本内容\"}, {\"type\": \"suggestion\", \"content\": [\"建议1\", \"建议2\"]}, {\"type\": \"file\", \"content\": {\"name\": \"文件名\", \"url\": \"下载链接\"}}], \"conversationId\": \"会话ID\", \"intermediateSteps\": \"思维链数据\", \"agentType\": \"智能体类型\" }`
    - **统一消息格式架构** (2025-05-29 重构)：
      - **唯一返回格式**：只返回 list 字段，移除 response 字段，简化前端处理逻辑
      - **数据存储策略**：content 字段存储 JSON 序列化的 MessageContent 列表
      - **支持的消息类型**：
        - `text`: 文本内容，支持 Markdown 格式渲染（包含增强的链接处理：所有链接默认在新窗口打开，具备安全属性 rel="noopener noreferrer"）
        - `suggestion`: 建议选项数组，用于快速操作和引导用户
        - `file`: 文件信息对象，支持文件展示、下载和预览
        - `image`: 图片信息对象，支持图片展示和缩放
        - `chart`: 图表数据对象，支持数据可视化
        - `table`: 表格数据对象，支持结构化数据展示
      - **向后兼容性**：
        - 单一文本消息自动包装为 list 格式：`[{\"type\":\"text\",\"content\":\"文本内容\"}]`
        - 现有数据通过解析逻辑正确处理
        - 错误响应也使用统一的 list 格式
      - **处理逻辑简化**：
        - 移除双重处理逻辑（content vs list）
        - 统一的序列化/反序列化机制
        - 简化的错误处理和响应构造
  - `GET /api/chat/history/{conversationId}`: 获取指定会话的聊天记录。
    - 路径参数: `conversationId`
    - 响应体: `List<ChatMessageDto>` (数组，包含 `id`, `role`, `content`, `intermediateSteps`, `createdAt`)
    - 逻辑: 调用 `ChatService` 查询数据库中对应 `conversationId` 的所有消息，按时间升序返回。
- **提示词模板管理 API**:
  - `GET /api/prompt/templates`: 获取所有提示词模板。
    - 查询参数: `agentType` (可选，按类型过滤)
    - 响应体: `List<PromptTemplateDto>` (数组，包含模板信息)
    - 逻辑: 调用 `PromptTemplateService` 查询并返回模板列表。
  - `GET /api/prompt/templates/{id}`: 获取单个提示词模板详情。
    - 路径参数: `id` (模板 ID)
    - 响应体: `PromptTemplateDto` (包含完整的模板信息，包括槽位定义)
    - 逻辑: 调用 `PromptTemplateService` 查询指定 ID 的模板。
  - `POST /api/prompt/templates`: 创建新的提示词模板。
    - 请求体: `PromptTemplateDto` (不含 ID)
    - 响应体: `PromptTemplateDto` (包含生成的 ID)
    - 逻辑: 调用 `PromptTemplateService` 创建并保存新模板。
  - `PUT /api/prompt/templates/{id}`: 更新现有提示词模板。
    - 路径参数: `id` (模板 ID)
    - 请求体: `PromptTemplateDto` (含更新后的内容)
    - 响应体: `PromptTemplateDto` (更新后的模板对象)
    - 逻辑: 调用 `PromptTemplateService` 更新指定 ID 的模板。
  - `DELETE /api/prompt/templates/{id}`: 删除提示词模板。
    - 路径参数: `id` (模板 ID)
    - 响应: HTTP 204 No Content
    - 逻辑: 调用 `PromptTemplateService` 删除指定 ID 的模板。
- **数据查询 API**:
  - `GET /api/data-migration/query-name-list/{mobile}`: 根据手机号查询名单数据。
    - 路径参数: `mobile` (手机号码)
    - 响应体: `List<NameListDataDto>` (数组，包含表名、ID 和完整数据内容)
    - 逻辑: 调用 `DataMigrationService` 查询与手机号关联的所有名单数据（表名以 data*namelist*开头的数据）。
  - `GET /api/data-migration/query-dataset/{mobile}`: 根据手机号查询调研数据。
    - 路径参数: `mobile` (手机号码)
    - 响应体: `List<NameListDataDto>` (数组，包含表名、ID 和完整数据内容)
    - 逻辑: 调用 `DataMigrationService` 查询与手机号关联的所有调研数据（表名以 dataset\_开头的数据）。
  - `GET /api/data-migration/query-name-list-statistics/{mobile}`: 根据手机号查询名单数据并按项目统计。
    - 路径参数: `mobile` (手机号码)
    - 响应体: `DataStatisticsResultDto` (包含原始数据列表和按项目分组的统计结果)
    - 逻辑: 调用 `DataMigrationService` 查询与手机号关联的名单数据，并按 customer_project 字段进行分组统计。
  - `GET /api/data-migration/query-dataset-statistics/{mobile}`: 根据手机号查询调研数据并按项目统计。
    - 路径参数: `mobile` (手机号码)
    - 响应体: `DataStatisticsResultDto` (包含原始数据列表和按项目分组的统计结果)
    - 逻辑: 调用 `DataMigrationService` 查询与手机号关联的调研数据，并按 customer_project 字段进行分组统计。
  - `GET /api/data-query/history-project-stats/{mobile}`: 查询历史购买楼盘信息和用户画像标签。
    - 路径参数: `mobile` (手机号码)
    - 响应体: `HistoryProjectStatsDto` (包含项目列表、用户画像标签和详细指标分析)
    - 逻辑: 调用 `DataQueryService` 查询历史项目统计信息，包含基础信息、链家楼盘匹配数据，并自动生成用户画像标签。
  - `GET /api/data-migration/query-top-mobiles`: 查询记录数满足条件的手机号列表。
    - 查询参数:
    - `type` (查询类型: ALL、NAMELIST、DATASET，默认 ALL)
    - `threshold` (阈值，查询记录数大于该值的手机号，默认 5)
    - `limit` (限制返回条数，默认 20)
    - 响应体: `MobileStatsResultDto` (包含手机号列表、对应记录数、查询条件和总记录数)
    - 逻辑: 调用 `DataMigrationService` 查询 Redis 中记录数超过阈值的手机号，按记录数降序排序，并限制返回条数。

## 4. n8n 工作流集成

- **核心思想**: n8n 不仅执行自动化任务，还作为"智能体配置中心"。
- **调用方式**: 后端使用 Spring Boot 的 `WebClient` 向 n8n 的 Webhook URL 或 REST API 发送 HTTP 请求。
  - **超时配置**: `WebClient` 配置了 5 分钟的超时时间，以适应复杂查询和大型语言模型可能需要的长时间处理。
  - **服务器异步配置**: 在 `application.yml` 中设置 `server.servlet.async.request-timeout=300000` (5 分钟)，确保 Spring MVC 异步请求处理器的超时与 WebClient 一致。
  - **内存配置**: 设置 `maxInMemorySize=16MB`，以处理可能的大型响应数据。
- **触发时机**: 用户操作触发、AI 判断需要调用时触发。
- **数据传递**: 后端将上下文信息（用户输入、`conversationId`/`sessionId`、Agent ID 等）作为参数传递给 n8n。
- **获取结果**:
  - **同步 (不推荐用于长任务)**: n8n 直接返回结果。
  - **异步回调**: n8n 完成后调用后端预设的 Webhook URL 通知结果。
  - **轮询 (次选)**: 后端定时查询 n8n 工作流状态。
- **安全**: 保护 n8n Webhook URL，考虑使用 API Key 或其他认证机制。
- **后端职责**: 封装调用 n8n 的细节，提供简洁的内部服务接口。

## 5. 核心的智能问答流程和智能体管理机制 (n8n 中心化)

1.  **n8n 定义**:
    - 创建 "Agent Registry" 工作流，维护 Agent 列表及其配置 (ID, name, description, systemPrompt, model, associatedWorkflows - n8n 任务流 URL 映射)。
    - 提供 Webhook 端点，供后端查询 Agent 配置 (查询所有或按 ID 查询)。
    - 创建具体的任务执行工作流 (e.g., 查询订单, 创建工单)。
2.  **后端交互流程 (结合聊天记录保存)**:

    - 前端发送消息 (`message`) + 可选的 `conversationId` 给后端 `POST /api/chat`。
    - 后端判断 `conversationId`：若无则生成新的，若有则使用。
    - 后端保存用户消息 (`role=user`, `content=message`) 到 `chat_messages` 表，关联 `conversationId`。
    - 后端调用 n8n "Agent Registry" Webhook 获取 `agentId` (如果前端指定或有默认) 对应的配置。
    - 后端准备上下文 (System Prompt, 历史记录 - 从 `chat_messages` 表获取, 用户消息)。
    - 后端调用配置中指定的 AI 大模型 (`model`)。
    - AI 返回回答 (`assistantReplyContent`)。
    - 后端保存助手回复 (`role=assistant`, `content=assistantReplyContent`) 到 `chat_messages` 表，关联 `conversationId`。
    - 后端解析助手回复，判断是否需要调用 n8n 任务 (基于 AI 指令或特定模式)。
    - **(条件性)** 若需调用 n8n 任务:
      - 从 `associatedWorkflows` 找到对应任务的 URL。
      - 提取所需参数。
      - 调用 n8n 任务工作流 Webhook (可携带 `conversationId`)。
      - n8n 执行任务，通过回调将结果发送给后端。
      - 后端处理 n8n 结果（可能再次调用 AI 格式化，或直接使用，并可能需要更新/追加 `chat_messages` 表）。
    - 后端将最终回答/结果 (`ChatResponse` 包含 `conversationId`) 发送给前端。
    - 前端可通过 `GET /api/chat/history/{conversationId}` 获取完整或更新后的聊天记录。

3.  **提示词模板处理流程**:

    - 前端发送带有模板请求的消息，包含 `templateId` 和用户填写的 `slots` 值对象。
    - 后端 `ChatService` 在 `processAndSaveMessage` 方法中检测到提供了 `templateId`，执行以下步骤：
      - 调用 `PromptTemplateService.getTemplateById` 查询模板对象
      - 从模板对象中提取 `templateContent` 作为基础提示词
      - 遍历用户提供的 `slots` 键值对(Map<String, Object>)，对每个 key-value 对执行替换：
        - 构造占位符格式 `{{key}}`
        - 将 value 转换为字符串(使用 toString()方法)，处理 null 值为空字符串
        - 使用 String.replace()方法，将模板内容中的占位符替换为实际值
        - 在替换过程中使用日志记录每个占位符的替换情况，便于调试
      - 创建完整的用户消息内容，包含模板使用信息和填充后的内容
      - 保存用户消息至数据库，同时将填充好的完整提示词(processedTemplate)发送给 n8n webhook 的`chatInput`字段
      - 继续后续的处理步骤，获取和保存 AI 回复
    - 如果模板未找到，会生成一个错误消息并使用基本的模板信息替代，同时设置 userMessageForAI 确保与 n8n 正确通信
    - 这种实现确保了模板的灵活性和可维护性，管理员只需更新模板内容和槽位定义，无需修改代码

4.  **JSON/JSONB 数据处理配置**:
    - 系统使用 `JacksonConfig` 配置类统一管理 JSON 序列化/反序列化，特别是 JSONB 类型的数据处理：
      - 采用 Spring 推荐的 `Jackson2ObjectMapperBuilder` 构建全局 `ObjectMapper` Bean
      - 通过 builder 以声明式方式配置特性和模块，减少手动配置错误
      - 启用 `JavaTimeModule`，确保日期/时间类型(如 `Instant`)正确处理
      - 开启大小写不敏感的枚举处理，支持前端传入不同大小写格式的枚举值
      - 配置 JSON 解析容错性：允许单引号、无引号字段名，忽略未知属性和空对象
      - 设置忽略 null 值特性，减少 JSON 响应体积
      - 将 `slotDefinitions` 等 JSONB 字段处理委托给专用的序列化/反序列化方法
      - 日志输出配置过程，确保问题可追踪
    - `PromptTemplateServiceImpl` 中使用两个辅助方法处理 JSONB 数据:
      - `serializeSlotDefinitions`: 将 Java 对象列表(`List<SlotDefinition>`)序列化为 JSON 字符串
      - `deserializeSlotDefinitions`: 将 JSON 字符串反序列化为 Java 对象列表

## 6. 数据库设计

### 6.1 主要表设计

- **`conversations` 表**: 存储会话的基本信息。
  - `id` (UUID, PK): 会话唯一标识符。
  - `label` (VARCHAR): 会话标签/名称（可为空）。
  - `created_at` (TIMESTAMPTZ): 创建时间。
  - `updated_at` (TIMESTAMPTZ): 更新时间。
- **`chat_messages` 表**: 存储具体的聊天消息。
  - `id` (UUID, PK): 消息唯一标识符。
  - `conversation_id` (UUID, FK -> conversations.id): 所属会话 ID。
  - `role` (VARCHAR): 消息角色 ('USER', 'ASSISTANT')。
  - `content` (TEXT): 消息内容。
  - `intermediate_steps_json` (JSONB): 存储助手思考过程/工具调用的 JSON 字符串。
  - `created_at` (TIMESTAMPTZ): 创建时间。
  - `updated_at` (TIMESTAMPTZ): 更新时间。
- **`prompt_templates` 表**: 存储可复用的提示词模板。
  - `id` (UUID, PK): 模板唯一标识符。
  - `name` (VARCHAR, NOT NULL): 模板名称。
  - `description` (TEXT): 模板描述。
  - `agent_type` (VARCHAR): 适用的 Agent 类型。
  - `template_content` (TEXT): 模板内容（带槽位标记，例如 `{{meeting_date}}`）。
  - `slot_definitions` (JSONB, NOT NULL): 槽位定义列表 (包含 key, name, type, required, options 等)。
  - `created_at` (TIMESTAMPTZ): 创建时间。
  - `updated_at` (TIMESTAMPTZ): 更新时间。

### 6.2 多数据源配置

系统采用多数据源设计，用于支持不同的数据访问需求：

1. **主数据源 (PostgreSQL)**

   - 用途：存储系统的主要业务数据，包括会话、消息、用户等
   - 框架支持：作为 JPA 的主数据源，用于实体映射和 ORM 操作
   - 配置要点：
     - 使用@Primary 注解标记为默认数据源
     - 配置 HikariCP 连接池参数优化性能
     - 配置 EntityManagerFactoryBean 绑定此数据源
     - 设置 PostgreSQL 方言确保 SQL 生成正确

2. **MySQL 数据源**

   - 用途：主要用于查询历史业务数据，构建手机号索引
   - 访问方式：主要通过 JdbcTemplate 进行原生 SQL 查询
   - 数据迁移：负责将历史数据表中的记录索引到 Redis 中
   - 配置要点：
     - 使用独立的 HikariCP 连接池配置
     - 不参与 JPA 实体映射，仅用于 JDBC 操作

3. **多数据源实现细节**
   - **DataSourceConfig 类**: 集中管理所有数据源配置
     - primaryDataSource: 主 PostgreSQL 数据源
     - mysqlDataSource: MySQL 历史数据源
     - entityManagerFactory: 配置 JPA 使用主数据源
     - transactionManager: 事务管理器配置
   - **@Qualifier 使用**: 在 service 层使用@Qualifier("mysqlJdbcTemplate")注解指定使用 MySQL 数据源
   - **连接池配置**: 两个数据源使用不同的连接池配置，针对不同使用场景优化
   - **错误处理**: 配置详细的数据库连接日志，便于诊断连接问题

### 6.3 Redis 索引设计与优化

系统使用 Redis 作为手机号索引的存储和快速查询工具：

1. **键值结构设计**

   - 键格式: `{手机号}` (直接使用手机号作为键，不带前缀)
   - 值格式: 字符串数组，格式为 `tableName:id`，如: `["data_namelist_1:123", "data_namelist_1_24:456"]`
   - 优势:
     - 简化存储结构，减少 Redis 空间占用和序列化/反序列化开销
     - 降低网络传输量，提高查询性能
     - 更直观的数据格式，易于调试和排查问题

2. **批量导入策略**

   - **批次大小控制**: 动态调整的批次大小，初始批次最大为 5000 条记录
   - **Pipeline 优化**: 使用 Redis pipeline 批量执行命令，减少网络往返
   - **批次分解**: 大量数据被分解为多个小批次，防止超大批次导致超时
   - **动态批次调整**: 超时后自动减半批次大小进行重试，提高成功率
   - **并行控制**: 设置最大并行任务数(10)，避免 Redis 连接过载
   - **指数退避重试**: 当超时发生时，使用指数退避算法进行重试，最多重试 3 次

3. **并行任务处理机制**

   - **任务拆分**: 将表按 ID 范围拆分成多个任务，每个任务处理 30,000 条记录的范围
   - **CompletableFuture 异步处理**: 每个 ID 范围的处理任务封装为 CompletableFuture，实现异步并行执行
   - **自适应线程数**: 根据处理器核心数量动态调整并行任务数，公式为`min(处理器核心数*2, 8, 任务总数)`
   - **任务完成判定**: 使用 waitForPartialTasksWithRetry 方法，通过以下机制确保任务可靠完成：
     - **全量检查前置**: 在进入等待循环前，首先执行一次全量完成状态检查，避免所有任务已完成时仍进入等待
     - **部分完成策略**: 当超过一半任务完成时，允许提前返回处理下一批任务，提高整体吞吐量
     - **增量检查**: 每轮等待后，计算已完成任务数量，并在所有任务完成时立即返回
     - **超时保护**: 设置最大等待时间(5 分钟)和最大重试次数(3 次)，防止任务永久挂起
     - **递增等待**: 采用递增的等待时间(500ms、1000ms、1500ms...)，减轻轮询压力
     - **最终检查**: 在 processTable 方法结束时，使用 CompletableFuture.allOf 进行最终的任务完成等待
   - **全局超时保护**: 在整个导入流程设置最大运行时间(4 小时)，防止无限运行
   - **任务状态监控**: 详细记录任务完成状态、完成任务数量和进度百分比

4. **连接池与超时配置**

   - **连接池参数**:
     - max-active: 16 (最大活跃连接数)
     - max-idle: 8 (最大空闲连接数)
     - min-idle: 1 (最小空闲连接数)
     - max-wait: 3000ms (连接获取最大等待时间)
     - time-between-eviction-runs: 60000ms (定期清理空闲连接的间隔)
   - **超时设置**:
     - timeout: 10000ms (Redis 操作超时时间)
   - **客户端配置**: 使用 Lettuce 客户端，通过 LettuceClientConfiguration 配置
   - **序列化**: 统一使用 StringRedisSerializer 处理键，GenericJackson2JsonRedisSerializer 处理值

5. **错误处理机制**

   - **Pipeline 异常处理**: 捕获 RedisPipelineException，实现批次降级处理
   - **超时异常处理**: 捕获 QueryTimeoutException，触发重试机制
   - **连接异常处理**: 捕获 RedisConnectionFailureException，提供友好错误信息
   - **批处理降级**: 当批量操作失败时，自动降级为单键处理，确保数据完整性
   - **异常恢复**: 实现 waitForPartialTasksWithRetry 方法，健壮地等待未完成任务
   - **进度状态保护**: 确保在各种异常情况下也能正确更新导入进度状态，防止任务卡住

6. **监控与统计**
   - 记录每个批次的处理时间、新增键数、更新键数和错误数
   - 详细记录 Redis 操作耗时，便于性能分析和调优
   - 对批量获取、设置和处理过程分别统计耗时，识别瓶颈
   - 提供全面的任务完成状态日志，包括完成率、耗时和异常情况

### 6.4 POI 数据查询与地理匹配

系统实现了基于地理坐标的 POI 数据查询功能，用于丰富项目周边环境描述：

1. **数据源与表结构**

   - 数据表: `inf_poi` (存储在 MySQL 数据库中)
   - 主要字段:
     - `id`: POI 唯一标识
     - `name`: POI 名称
     - `type`: 兴趣点类型(大类;中类;小类)，如 "餐饮服务;中餐厅;特色/地方风味餐厅"
     - `typecode`: 兴趣点类型编码，如 "050118"
     - `address`: 详细地址
     - `lng`/`lat`: 经纬度坐标
     - 其他丰富的信息字段：电话、标签、城市、区域等

2. **地理距离计算**

   - **Haversine 公式**: 在 SQL 中使用 Haversine 公式计算地球表面两点间的球面距离
   - **距离计算 SQL**:
     ```sql
     ROUND(6371000 * ACOS(COS(RADIANS(?)) * COS(RADIANS(lat)) * COS(RADIANS(lng) - RADIANS(?)) + SIN(RADIANS(?)) * SIN(RADIANS(lat))), 2) as distance
     ```
   - **参数化配置**:
     - 搜索半径：默认 2 公里，可通过常量配置修改
     - 最大返回数：默认每个项目最多返回 20 个 POI

3. **数据处理与展示优化**

   - **类型分组统计**: 对查询结果按 POI 类型进行分组，展示 Top 3 类型及其数量
   - **距离排序**: 按距离升序排序，优先展示最近的 POI
   - **结果格式化**: 对 POI 结果进行结构化展示
     - 输出格式示例: `餐饮服务(5)，购物服务(3)，生活服务(2)`
     - POI 具体列表示例: `肯德基(120米)，星巴克(230米)，沃尔玛(450米)`

4. **实现机制**
   - **查询流程**:
     - 在`historyProjectStats`方法中，通过`enrichProjectsWithNearbyPois`调用 POI 查询逻辑
     - 对每个有地理坐标的项目，使用`enrichProjectWithNearbyPois`进行 POI 数据查询
     - 将查询结果包装为`PointOfInterestDto`对象列表，存储在项目详情中
   - **异常处理**: 完善的错误处理和日志记录，确保查询过程的稳定性
   - **降级策略**: 当项目无经纬度或查询出错时，返回空列表，确保不影响主流程

## 7. 前端组件设计

项目基于 Ant Design X，用于创建现代化的对话式用户界面。

### 核心组件

- **App.jsx**: 应用根组件，管理全局状态和路由。
- **MainLayout.jsx**: 应用的主布局组件，提供整体页面结构和路由渲染区域。
- **IndexPage.jsx**: 网站首页组件，直接渲染 ChatInterface，提供一个不依赖路由参数的聊天入口。
- **ChatPage.jsx**: 历史会话页面，支持切换/管理不同的会话，使用 conversationId 参数。
- **HomePage.jsx**: 简化的主页面组件，显示应用介绍和功能导航。
- **ChatInterface.jsx**: 主聊天界面容器，协调各聊天子组件间的交互。
- **ChatMessageList.jsx**: 渲染聊天消息列表，包括用户和助手的消息气泡。
- **ChatWelcome.jsx**: 显示欢迎界面，包含热门话题和提示词模板快捷方式。
- **ChatSender.jsx**: 聊天输入组件，使用固定布局策略将输入框定位在聊天区域底部。

### 聊天界面布局优化

聊天界面采用了固定输入框的设计模式，确保用户在任何时候都能方便地输入消息：

- **固定布局结构**: 在 `src/views/chat/container.vue` 中，使用 Flex 布局和相对/绝对定位相结合的方式组织界面结构。
- **输入框固定策略**:
  - 使用 `absolute bottom-0 left-0 right-0` 将输入框容器固定在父容器底部
  - 添加 `z-10` 确保输入框始终位于其他内容上层，避免被滚动内容覆盖
  - 设置 `bg-white dark:bg-gray-900` 适配明暗两种主题模式
  - 添加 `border-t border-gray-200 dark:border-gray-700` 创建清晰的视觉分隔
  - 设置适当的内边距 `py-2`，提升视觉舒适度
- **内容区域滚动**:
  - 中央内容区域使用 `flex-grow overflow-auto` 实现可滚动布局
  - 添加 `pb-24` 底部内边距，确保滚动到底部时内容不被固定输入框遮挡
- **自适应响应**: 整体布局支持各种屏幕尺寸，在不同设备上都能保持良好的交互体验。

这种布局设计确保了用户在浏览长聊天记录时，输入框始终保持可见和可用，大幅提升了使用体验。

### 着陆页设计 (Landing Page)

dips-pro-landingpage 采用了现代化的视觉设计，主要配色方案为：

- **主色调**: 紫色系 (#5661f6) - 代表创新与科技
- **辅助色**: 更丰富的紫色 (#9061f9) - 增强视觉层次
- **强调色**: 醒目的粉色 (#ff5f93) - 用于突出重要元素和按钮
- **文本色**: 深蓝黑色 (#1a1b32) 和蓝灰色 (#5b6084) - 提供良好的对比度和可读性
- **背景色**: 纯白色 (#ffffff) 和偏紫蓝色的浅灰 (#f5f7ff) - 创造层次感
- **渐变**: 主色到辅助色、辅助色到强调色的渐变 - 增加视觉吸引力

配色实现采用多层次策略：

1. **Tailwind 配置**: 自定义颜色变量和渐变样式
2. **HeroUI 主题**: 修改 light/dark 主题配色
3. **CSS 变量**: 在全局 CSS 中定义配色变量，确保全局一致性
4. **公共组件样式类**: 如 `text-gradient-primary`, `btn-gradient-primary` 等

设计同时保留了响应式布局和浅色/深色模式的支持，确保在各种设备和环境下都有最佳的用户体验。

### 全屏分页滚动实现

Landing Page 采用了自定义的全屏分页滚动设计，为用户提供类似 PPT 演示的浏览体验：

- **技术实现**:

  - 使用自定义滚动解决方案，摒弃了之前尝试的`@fullpage/react-fullpage`库
  - 自定义 CSS 样式(`styles.css`)实现平滑过渡和导航样式
  - 自定义 React hooks 处理滚动检测和页面切换
  - 将每个主要内容区域封装为独立的 section

- **核心功能**:
  - 丝滑的页面过渡: 使用 scroll-behavior: smooth 实现平滑滚动
  - 阻尼感: 使用自定义的滚动检测和延迟激活逻辑
  - 自动导航点: 右侧固定位置的导航点随滚动位置更新
  - 键盘导航: 支持通过上下箭头键切换页面
  - 内容过渡动画: 切换页面时内容淡入
  - 滚动提示器: 用动画指示用户可以继续滚动
  - 保留背景视差效果: 使用 react-scroll-parallax 实现背景层次感

### 视觉增强组件

- **GlowingCard**: 提供动态发光效果的卡片容器，跟随鼠标移动调整发光位置和强度。
- **FlowingBorderCard**: 实现鼠标悬停时显示流动渐变边框效果和上浮动画的卡片组件，通过 CSS 变量实现动态配置边框样式（颜色、宽度、圆角、模糊度等），仅在鼠标悬停时触发流动动画和上浮效果，支持自定义渐变色数组、上浮像素值和动画速度参数，为用户提供直观的交互反馈。
- **RadiantText**: 带有闪耀动画效果的文字显示组件，增强标题的视觉冲击力。
- **ParallaxBanner**: 使用 react-scroll-parallax 库实现的视差背景组件，创造页面深度感。

### 响应式设计策略

Landing Page 采用全面的响应式设计策略，确保在从大屏幕桌面到小屏幕移动设备的所有环境中都能提供良好的用户体验：

- **Bento Grid 系统**:

  - 大屏幕(>1024px): 采用 12 列网格，充分利用宽屏空间
  - 中等屏幕(768px-1024px): 减少每个元素的跨度，调整为 6 列或 12 列
  - 小屏幕(<640px): 转为单列布局，确保内容垂直流动，避免挤压
  - 使用 CSS Grid 和媒体查询实现，而非传统 Bootstrap 行列系统

- **响应式组件设计**:

  - **Features 卡片**: 在小屏幕上从水平排列转为垂直排列，内容居中
  - **GlowingCard**: 针对触摸设备优化，显示静态发光效果，不依赖鼠标跟踪
  - **图标大小**: 根据屏幕宽度自动调整(如 text-6xl md:text-8xl)
  - **间距和边距**: 在小屏幕上适当减小，避免浪费宝贵的屏幕空间

- **Tailwind 实践**:

  - 充分利用 Tailwind 的响应式前缀(sm:, md:, lg:)
  - 默认为移动视图设计(Mobile First)，再针对更大屏幕调整
  - 容器类采用 px-4 sm:px-6 md:px-8 等模式，响应式调整内边距
  - flex-col sm:flex-row 模式用于布局方向响应式变化

- **视差效果优化**:
  - 在移动设备上减少视差移动幅度，避免内容过度移动
  - 保留基本动效，但减弱滚动相关的位移量
  - 确保装饰元素在小屏幕上不会遮挡核心内容

### 模板相关组件

- **SlotEditor.jsx**: 单个槽位编辑器，根据槽位类型（文本、日期、选择列表等）显示不同的编辑控件，使用 Popover 浮层进行编辑。
- **InlineTemplateEditor.jsx**: 行内模板编辑器，解析模板文本中的槽位标记（如`{{meeting_date}}`），将其转换为可交互的 SlotEditor 组件。
- **TemplateDrawer.jsx**: 模板选择抽屉，显示可用模板列表。
- **TemplateFormModalWrapper.jsx**: 模板表单模态框，用于表单式编辑模板槽位（作为备选方案）。
- **TemplateManagePage.jsx**: 模板管理页面，显示所有可用模板的列表，支持搜索、编辑和删除操作，提供创建新模板的入口。
- **TemplateEditPage.jsx**: 模板编辑/创建页面，提供完整的模板创建和编辑表单，包括基本信息设置、模板内容编辑和槽位定义管理。

### 发送组件架构

发送组件采用分层设计，提高代码可维护性和扩展性：

- **BaseSender.jsx**: 基础消息发送组件，封装发送功能和交互逻辑。
- **TextSender.jsx**: 基于 BaseSender 的纯文本输入组件。
- **TemplateSender.jsx**: 基于 BaseSender 的模板消息发送组件，集成 InlineTemplateEditor。
- **ChatSender.jsx**: 控制器组件，根据是否有模板选择渲染 TextSender 或 TemplateSender。

### Landing Page 组件设计

Landing Page 为 DIPS Pro 平台提供引人入胜的首页展示，突出平台的核心价值和功能。组件设计注重视觉吸引力、响应式布局和用户引导。

- **LandingPage.jsx**: 主容器组件，负责整体布局和组织所有 Landing Page 子组件。

  - 使用 Ant Design 的 Layout 组件提供整体结构（Header, Content, Footer）
  - 实现固定顶部导航栏，包含 Logo、导航链接和行动按钮
  - 组织各内容区块的布局和顺序
  - 实现页脚设计，包含 Logo、链接分组和版权信息
  - 设置全局的响应式断点和样式变量

- **HeroSection.jsx**: 引人注目的英雄区域，是访问者的第一印象。

  - 设计渐变背景和神经网络动画效果
  - 呈现主标题"DIPS Pro: 释放 AI 群体智能，驱动未来工作流"
  - 添加浮动卡片元素，展示平台核心功能（数据分析、智能对话、流程自动化）
  - 使用 CSS 动画实现浮动效果和视差滚动
  - 提供明确的行动按钮引导用户探索产品

- **ProblemSolution.jsx**: 阐述用户痛点和 DIPS Pro 的解决方案。

  - 使用 Ant Design 的 Card 和 Row/Col 组件创建响应式布局
  - 展示三个主要痛点卡片，每个配有图标、标题和描述
  - 提供清晰的解决方案说明，强调多智能体协作模式的价值
  - 使用简洁的列表展示主要优势点
  - 实现卡片悬停动画，增强交互体验

- **Features.jsx**: 展示平台的四大核心优势。

  - 使用 GlowingCard 组件实现特性卡片的发光效果
  - 为每个特性卡片设计独特的图标和渐变色
  - 添加统计数据展示（工作效率提升、人为错误降低、流程时间缩短）
  - 实现卡片的悬停动画和渐变背景
  - 保持一致的视觉语言和布局结构

- **UseCases.jsx**: 通过标签切换展示四个主要应用场景。

  - 使用 Ant Design 的 Tabs 组件实现场景切换
  - 每个场景包含详细描述、效果说明和工作流程示意图
  - 使用简化的工作流程图展示智能体协作的步骤（数据收集 → 分析处理 → 内容生成 → 人工确认）
  - 添加场景相关的图片和视觉效果
  - 实现标签和内容的动画过渡效果

- **Testimonials.jsx**: 展示客户评价和合作品牌。

  - 使用 Ant Design 的 Carousel 组件实现评价轮播
  - 设计评价卡片包含头像、姓名、职位、公司和评分
  - 添加引号装饰和卡片阴影效果
  - 展示"受到众多企业的信赖"的品牌展示区域
  - 实现响应式布局，确保在移动设备上合理显示

- **CallToAction.jsx**: 引导用户采取下一步行动。
  - 设计突出的行动按钮和引人注目的标题
  - 展示产品核心优势（立即启动、数据安全、全程支持、按需付费）
  - 添加常见问题解答部分，解决潜在用户疑虑
  - 使用渐变背景和卡片阴影增强视觉吸引力
  - 实现响应式布局，确保移动设备上的良好显示效果

所有 Landing Page 组件都使用 CSS 模块或独立的样式文件，实现响应式设计，确保在不同设备（桌面、平板、手机）上都有良好的显示效果。设计风格保持与主应用一致，使用相同的色彩方案和设计语言。

## 8. 日志配置

- **框架**: 使用 Logback (通过 `logback-spring.xml`)，这是 Spring Boot 的默认日志框架。
- **配置文件**: `dips-pro-server/src/main/resources/logback-spring.xml`
- **日志路径**:
  - 在 `logback-spring.xml` 中，文件输出路径由 `${LOG_PATH}` 变量控制 (默认值为 `logs`)。
  - 该变量通过 Docker Compose 文件中的环境变量进行设置。
- **环境区分与持久化 (通过 Docker Compose)**:
  - **`dev` (开发环境)**:
    - 日志级别: `INFO` (根级别), `DEBUG` (对于 `com.dipspro` 包)。
    - 输出: 控制台 (`CONSOLE`)。
    - `docker-compose.dev.yml` 设置 `LOG_PATH=/var/log/dips-pro/dev` 并将宿主机 `./logs/dev` 挂载到此路径。
  - **`test` (测试环境)**:
    - 日志级别: `INFO`。
    - 输出: 控制台 (`CONSOLE`)。 (注意: 当前配置下，测试环境不输出到文件，如需文件输出，需调整 `logback-spring.xml` 中 `test` profile 并配置 Docker Compose)
  - **`prod` (生产环境)**:
    - 日志级别: `INFO`。
    - 输出: 滚动文件 (`FILE`)。
    - `docker-compose.prod.yml` 设置 `LOG_PATH=/var/log/dips-pro/prod` 并将宿主机 `./logs/prod` 挂载到此路径。
- **默认配置**: 如果没有激活特定 profile 或未在 Docker 环境中运行 (即 `LOG_PATH` 环境变量未设置)，文件日志将默认写入项目根目录下的 `logs` 文件夹。控制台日志仍按默认配置输出。

## 9. 部署架构

应用的部署基于 Docker 和 Docker Compose，并可通过 Jenkins 实现自动化。

- **容器化**:
  - 后端 Spring Boot 应用通过 `dips-pro-server/Dockerfile`

## 前端组件结构

### 通用 UI 组件

- **HyperText**: 一个交互式文本组件，当用户悬停时会显示字符变换动画效果，增强用户体验和页面视觉吸引力。位于 `dips-pro-landingpage/src/components/ui/hyper-text.tsx`。

## 数据缓存与索引设计

### MySQL 数据表索引至 Redis

在本系统中，我们实现了一个将 MySQL 数据表数据索引至 Redis 的功能，以提高查询性能。

#### 索引数据源

- 多个历史数据表：`data_namelist_1`、`data_namelist_1_24`、`data_namelist_1_23`、`data_namelist_1_22`、`data_namelist_1_21`、`data_namelist_1_20`
- 每张表包含数百万条记录
- 数据库来源为 MySQL，系统采用多数据源配置，包括：
  - 主数据源(PostgreSQL)：存储系统业务数据
  - MySQL 数据源：专门用于访问历史数据表

#### 数据源配置设计

- **多数据源配置**：

  ```java
  @Configuration
  public class DataSourceConfig {
      @Primary
      @Bean(name = "primaryDataSource")
      @ConfigurationProperties(prefix = "spring.datasource.primary")
      public DataSource primaryDataSource() {...}

      @Bean(name = "mysqlDataSource")
      @ConfigurationProperties(prefix = "spring.datasource.mysql")
      public DataSource mysqlDataSource() {...}

      @Primary
      @Bean(name = "primaryJdbcTemplate")
      public JdbcTemplate primaryJdbcTemplate(...) {...}

      @Bean(name = "mysqlJdbcTemplate")
      public JdbcTemplate mysqlJdbcTemplate(...) {...}
  }
  ```

- **YAML 配置**：
  ```yaml
  spring:
    datasource:
      # 主数据源 (PostgreSQL)
      primary:
        driver-class-name: org.postgresql.Driver
        url: *******************************
        username: xxx
        password: xxx
      # MySQL数据源 (用于data_namelist表)
      mysql:
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: *********************************
        username: xxx
        password: xxx
        hikari:
          maximum-pool-size: 10
    # Redis配置
    data:
      redis:
        host: xxx
        port: 6379
        password: xxx
        database: 1
  ```

#### Redis 键值设计

- **键(Key)**: `{手机号}` (直接使用手机号作为键，不带前缀)
- **值(Value)**: 字符串数组，格式为 `tableName:id`，如: `["data_namelist_1:123", "data_namelist_1_24:456"]`
- **优势**:
  - 简化存储结构，减少 Redis 空间占用和序列化/反序列化开销
  - 降低网络传输量，提高查询性能
  - 更直观的数据格式，易于调试和排查问题

#### 索引流程

1. 分批查询 MySQL 数据，每批 10000 条记录
2. 查询条件：
   ```sql
   d_mobile is not null and d_mobile<>'' and d_mobile<>'0' and d_mobile<>'****'
   AND LEFT(d_mobile, 4) <> '0085'
   AND LEFT(d_mobile, 2) NOT IN ('01','02','03','04','05','06','07','08','09')
   ```
3. 使用异步任务执行导入，避免阻塞 Web 请求
4. 对于已存在的 mobile 键，增量追加新数据而非覆盖
5. 详细记录导入进度和日志

#### 数据库查询优化

1. **基于索引的两阶段查询**：
   - 第一阶段：通过手机号在 Redis 中快速查找对应的表名和 ID
   - 第二阶段：根据表名和 ID 直接在 MySQL 中查询完整记录
2. **批量查询支持**：支持一次查询多个手机号对应的数据
3. **增量更新机制**：当数据库中有新数据时，可以增量添加到 Redis 索引中

#### API 接口

1. **启动导入**

   - 路径: `/api/data-migration/import-to-redis`
   - 方法: POST
   - 参数: `batchSize` (默认 10000)
   - 响应: 导入进程启动状态

2. **查询进度**

   - 路径: `/api/data-migration/import-progress`
   - 方法: GET
   - 响应: 导入进度信息，包括总记录数、已处理记录数、进度百分比、预计剩余时间等

3. **按手机号查询索引记录**

   - 路径: `/api/data-migration/query/{mobile}`
   - 方法: GET
   - 参数: `mobile` (路径参数)
   - 响应: 该手机号对应的数据索引记录列表（仅包含表名和 ID）

4. **按手机号查询完整数据**
   - 路径: `/api/data-migration/query-name-list/{mobile}` - 方法: GET
   - 参数: `mobile` (路径参数)
   - 响应: 该手机号对应的完整数据内容，包括表名、ID 以及实际记录的所有字段数据
   - 特点: 该接口先通过 Redis 索引获取记录位置信息，再从 MySQL 数据库中查询完整记录内容

#### 示例用法

```java
// 1. 查询索引记录（仅包含表名和ID）
List<MobileDataRecord> records = dataMigrationService.queryByMobile("13812345678");

// 2. 查询完整数据内容（包含表中全部字段）
List<NameListDataDto> completeData = dataMigrationService.queryNameList("13812345678");

// 使用示例工具类
DataQueryExample queryExample = new DataQueryExample();

// 3. 只查询索引记录
List<MobileDataRecord> indexRecords = queryExample.findDataByMobile("13812345678");

// 4. 查询完整数据
List<Map<String, Object>> completeData = queryExample.findCompleteDataByMobile("13812345678");

// 5. 批量查询
List<String> mobileList = Arrays.asList("13812345678", "13987654321");
Map<String, List<MobileDataRecord>> batchResult = queryExample.batchQueryMobileData(mobileList);
```

## 数据导入组件

### 主要功能

- 将 MySQL 中的名单数据批量导入到 Redis
- 支持多线程并行导入
- 支持断点续传
- 保存导入进度
- 支持手机号查询

### 数据处理流程

1. 扫描数据表，获取表的 ID 范围
2. 将大表按 ID 范围分割成多个小任务
3. 使用线程池并行处理小任务
4. 任务处理时，从 MySQL 读取数据，然后写入 Redis
5. 记录导入进度和统计信息

### Redis 数据批量操作优化

为解决 Redis 批量获取操作可能出现的超时问题，我们采用以下策略：

1. 数据分批处理：将大批量的键集合拆分为较小的批次（默认每批 5000 个键）分别处理
2. 重试机制：对于 Redis 超时操作实现指数退避重试，最多重试 3 次
3. 降级策略：当批量获取多次失败后，降级为单键逐个获取模式
4. 完善的日志记录：记录每个批次的处理情况，包括成功、失败和重试信息

## 数据查询优化

### 大表分批查询策略

系统采用分批次处理策略优化大数据量表的查询性能，特别是针对可能导致超时的全表扫描操作：

1. **基于 ID 范围的分割处理**

   - 首先使用`getTableIdRange`方法获取表的最小和最大 ID 范围
   - 根据 ID 范围和配置的批次大小(通常为 10 万条记录)计算总批次数
   - 将整个 ID 范围分割为多个不重叠的批次，每个批次独立处理
   - 使用`MinMaxIds`内部类封装 ID 范围信息，提供验证和范围计算功能

2. **批次处理实现技术**

   - 在 SQL 查询中使用`id >= startId AND id <= endId`约束条件限定每批次查询范围
   - 使用 JDBC 标准查询方式将结果集映射为 Java 对象集合
   - 批次处理流程完全顺序化，按照批次计数逐个处理，避免并发带来的复杂性
   - 每批次处理后记录详细进度日志，包括已处理记录数、累计获取的数据量和耗时

3. **提前终止机制**

   - 实现两级提前终止策略：批次内终止和全局终止
   - 批次内终止：当单个批次处理后已找到足够多满足条件的记录(如超过 MAX_RESULTS)时，提前终止该批次处理
   - 全局终止：当所有批次累计找到的满足条件记录超过阈值时，提前终止后续表的处理
   - 终止条件参数化配置，可根据业务需求和系统性能调整

4. **优化查询示例 - queryMobilesByProjectCount 方法**

   - 该方法用于查询在多个项目中出现次数超过阈值的手机号
   - 优化前：一次性查询整张表，容易导致查询超时
   - 优化后：
     - 对每个表获取 ID 范围，将查询分解为多个批次(每批 10 万条)
     - 每批次独立计算满足条件的记录，累计到全局统计中
     - 实现动态阈值判断，找到足够多结果后提前终止
     - 详细记录处理进度，便于监控和调优
     - 增强错误处理，即使单批次查询出错也不影响整体流程

5. **性能收益**

   - 避免单次大数据量查询导致的超时问题
   - 提高系统响应速度，改善用户体验
   - 降低数据库负载，减轻服务器压力
   - 提供更精确的进度反馈和更健壮的错误处理
   - 满足条件时提前终止，避免不必要的全表扫描

6. **适用场景**
   - 大型历史数据表查询和统计
   - 需要进行跨表数据关联和聚合的复杂查询
   - 预计查询结果较小但需要扫描大量记录的场景
   - 后台批处理任务和报表生成

## 7. 工具类设计

系统设计遵循"不依赖 Spring 上下文的功能类型代码都提取到静态工具类中"的原则，主要工具类包括：

- **MapUtils**: 提供安全地从 Map 获取值的方法
  - `safeGetInteger(Map<String, Object> map, String key)`: 安全获取 Integer 值，处理 null 值和类型转换
  - `safeGetDouble(Map<String, Object> map, String key)`: 安全获取 Double 值，处理 null 值和类型转换
  - `safeGetString(Map<String, Object> map, String key)`: 安全获取 String 值，处理 null 值情况
- **更多工具类将根据需要添加**

工具类设计原则：

1. 使用 `final` 类和私有构造函数防止实例化
2. 所有方法都是静态的，不维护状态
3. 方法参数强制非空检查，确保健壮性
4. 提供详细的 Javadoc 文档，说明每个方法的用途、参数和返回值
5. 遵循单一职责原则，每个工具类专注于特定领域的功能

## XIII. 指标缓存设计

### 1. 概述

指标缓存模块提供对系统各项指标的统一管理和查询功能，支持按指标代码查询指标定义、获取指标层级关系等操作。指标数据通过配置文件定义，加载到内存中缓存，不需要数据库持久化。

### 2. 核心组件

- **IndexDefinition**：指标定义实体类，包含指标名称、代码、层级关系等信息
- **IndexService**：指标服务接口，提供指标查询的核心功能
- **IndexServiceImpl**：指标服务实现类，负责从配置文件加载指标定义并提供缓存查询
- **IndexUtils**：指标工具类，提供处理指标代码的静态工具方法
- **IndexController**：指标控制器，提供 REST API 用于查询指标数据
- **IndexHierarchyDto**：指标层级结构 DTO，用于展示主指标与子指标的层级关系

### 3. 数据结构设计

指标定义包含以下关键属性：

- **indexGridName**：指标名称
- **legacyIndexCode**：遗留指标代码（原始系统代码，如 `ra1a5`）
- **planarIndexCode**：planar 指标代码（新系统代码，如 `001`）
- **parentIndexCode**：父指标代码（如子指标 `101003001` 的父指标为 `101003`）
- **isMainIndex**：是否是主指标

### 4. 指标层级关系

系统中的指标分为两个层级：主指标和子指标。主指标如"房屋设计"（`101005`），子指标如"户型设计"（`101005001`）。子指标代码通常以主指标代码为前缀。

主要指标类型包括：

- 满意度指标：`001`-`003`
- 房屋类指标：`101***`
- 服务类指标：`201***`
- 其他指标：`301***`, `302***`

### 5. 配置文件

指标定义存储在 `index-definitions.yml` 配置文件中，采用 YAML 格式，可以通过修改此文件增减指标，而无需修改代码。

示例配置：

```yaml
indexes:
  - indexGridName: 房屋设计
    legacyIndexCode: rk0a5
    planarIndexCode: '101005'
    isMainIndex: true

  - indexGridName: 户型设计
    legacyIndexCode: rk03a5
    planarIndexCode: '101005001'
    parentIndexCode: '101005'
    isMainIndex: false
```

### 6. 缓存实现

指标缓存使用 Spring 应用启动时初始化，数据加载后存储在内存中，支持多种查询方式：

- 按 planar 指标代码查询
- 按遗留指标代码查询
- 获取所有主指标
- 获取指定主指标的子指标
- 获取完整的指标层级结构

### 7. API 接口

系统提供以下 REST API 用于查询指标数据：

- `GET /api/indexes`：获取所有指标定义
- `GET /api/indexes/main`：获取所有主指标
- `GET /api/indexes/planar/{code}`：根据 planar 指标代码查询
- `GET /api/indexes/legacy/{code}`：根据遗留指标代码查询
- `GET /api/indexes/main/{mainCode}/sub`：获取指定主指标的子指标
- `GET /api/indexes/mapping`：获取主指标与子指标的映射关系
- `GET /api/indexes/hierarchy`：获取所有指标的层级结构
- `GET /api/indexes/hierarchy/{mainCode}`：获取指定主指标的层级结构

### 8. 扩展性

- 可以通过修改 `index-definitions.yml` 配置文件轻松添加、修改或删除指标
- 支持未来添加更多指标类型和层级
- 系统设计支持后续与数据分析、报表生成等模块集成

### 7.6 用户画像标签分析功能

HistoryProjectStatsDto 实现了基于历史购买项目数据自动分析生成用户画像标签的功能：

1. **标签系统设计**:

   - 采用灵活的设计模式，使用内部类`UserProfileTag`封装标签属性
   - 每个标签包含代码、名称、描述、类型、置信度和匹配的项目列表
   - 标签按类型分组展示，如购买行为、客户价值、品牌偏好等
   - 通过匹配项目关系，可追踪每个标签的证据来源

2. **标签分析逻辑**:

   - 通过`analyzeUserProfileTags()`方法统一调用各类标签分析函数
   - 实现 17 种标签规则，包括多次置业经历、高净值客户、投资客等
   - 每个标签都有明确的规则定义，基于历史数据生成
   - 分析过程自动执行，集成到 toString()方法中确保 API 返回结果包含标签信息

3. **特色标签类型**:

   - **购买行为标签**: 多次置业经历、投资客、跨城市置业经历等
   - **客户价值标签**: 高净值客户、成长型客户等
   - **品牌偏好标签**: 品牌忠实、单一品牌持有、品牌首购、头部品牌偏好等
   - **居住偏好标签**: 配套设施偏好、设计规划偏好、质量做工偏好、装修品质偏好等

4. **技术实现**:
   - 利用 Java 流式处理 API 高效处理集合数据
   - 采用函数式编程思想，单一职责原则设计标签分析函数
   - 通过缓存优化分析性能，避免重复计算
   - 结构化输出格式，便于前端展示和进一步分析

该功能为业务分析提供了额外价值，通过将原始数据转化为有意义的用户标签，帮助业务人员更好地了解客户特征和偏好。

## 项目架构设计

### 历史楼盘信息模块架构

#### 1. 整体架构

历史楼盘信息模块采用了分层架构设计，将数据表示、业务逻辑、格式化输出等功能进行了清晰的职责划分。主要分为以下几层：

- **数据传输层（DTO）**：负责数据的封装和传输
- **业务逻辑层（Analyzer）**：负责数据分析和处理
- **格式化层（Formatter）**：负责数据的展示格式化
- **工具层（Util）**：提供通用工具方法
- **常量层（Constant）**：存放系统常量定义

#### 2. 核心类说明

##### 2.1 数据传输层

- **HistoryProjectStatsDto**：历史楼盘信息 DTO，包含项目列表和用户标签列表
- **ProjectDetail**：楼盘详情数据，包含楼盘基本信息、评分、配套设施等
- **IndexWithScore**：带分值的指标数据，用于存储各项评分指标
- **UserProfileTag**：用户画像标签，用于描述用户特征和偏好
- **PointOfInterestDto**：兴趣点数据，用于描述楼盘周边设施

##### 2.2 业务逻辑层

- **UserProfileAnalyzer**：用户画像分析总控类，协调各种标签分析
- **BasicTagAnalyzer**：基础标签分析器，处理多次置业、投资客等基础标签
- **BrandTagAnalyzer**：品牌标签分析器，处理品牌忠实度等品牌相关标签
- **PreferenceTagAnalyzer**：偏好标签分析器，处理用户居住偏好相关标签

##### 2.3 格式化层

- **ProjectDetailFormatter**：项目详情格式化器，负责将数据转换为结构化文本输出

##### 2.4 工具与常量

- **ProjectStatsUtil**：提供项目统计相关的通用工具方法
  - 格式化价格(`formatPrice`)：将价格数字转换为不同单位(万元、亿元)的字符串
  - 配套设施追加(`appendFacilityIfNotNull`)：处理项目配套设施文本拼接逻辑
  - 距离计算(`calculateDistance`)：使用 Haversine 公式计算两点间地球表面距离
  - 获取头部开发商(`getTop20Developers`)：获取预定义的头部 TOP20 房地产开发商列表
  - 获取旅游养老目的地(`getTourismRetirementDestinations`)：获取预定义的文旅养老热门目的地列表
  - 提取物业费(`extractPropertyFee`)：从物业费描述文本中提取数值，支持范围格式(如"7~18")
  - 楼盘档次权重(`getBuildingGradeWeight`)：根据楼盘档次返回对应的权重值
  - 查找最早/最晚签约项目(`findEarliestProject`/`findLatestProject`)：在项目列表中查找签约时间最早/最晚的项目
  - 日期比较(`isMoreRecentDate`)：比较两个日期字符串，判断前者是否比后者更近
- **ProjectStatsConstants**：定义项目统计相关的常量值

#### 3. 类关系图

```
                           +-----------------+
                           | HistoryProject  |
                           |    StatsDto     |
                           +-----------------+
                                   |
                 +------------------+------------------+
                 |                  |                  |
        +----------------+  +----------------+  +----------------+
        | ProjectDetail  |  | UserProfileTag |  |UserProfileAnalyzer
        +----------------+  +----------------+  +----------------+
                 |                                      |
        +----------------+                     +--------+--------+--------+
        |IndexWithScore  |                     |BasicTagAnalyzer| |Brand..| |Pref...|
        +----------------+            +---------------+ +-------+ +-------+
               |                      |BasicTagAnalyzer| |Brand..| |Pref...|
        +-------------+               +---------------+ +-------+ +-------+
        |   Formatter |
        +-------------+
```

#### 4. 设计原则应用

- **单一职责原则**：每个类只负责一项职责，如标签分析与数据结构分离
- **开闭原则**：扩展新标签分析只需添加新的分析器，无需修改现有代码
- **依赖倒置原则**：高层模块不依赖于底层模块的实现细节
- **接口隔离原则**：各模块通过精确定义的接口通信
- **组合优于继承**：使用组合方式实现功能扩展

#### 5. 未来优化方向

- 考虑引入策略模式优化标签分析器的实现
- 增加缓存机制提高频繁访问数据的性能
- 引入事件机制处理分析过程中的状态通知
- 优化文本格式化的模板机制，增强灵活性

## XIV. 前端部署架构

### 1. HTTPS 证书配置

系统采用了安全的证书管理方式，将 HTTPS 证书直接打包在 Docker 镜像中：

#### 1.1 证书存储结构

- **证书目录**：证书文件可以通过两种方式提供：

  1. 开发环境：通过 Docker 卷挂载从主机提供
  2. 生产环境：通过 Jenkins 构建脚本处理

- **证书文件**：
  - `dipsai.cn.pem`：完整的证书链文件
  - `dipsai.cn.key`：证书私钥文件

#### 1.2 Docker 集成

- Docker 镜像中只创建了证书目录`/etc/nginx/certs/`
- 在部署时通过卷挂载将实际证书文件挂载到容器中
- Nginx 配置直接从容器内路径读取证书文件
- 实现了证书与容器的解耦，增强了安全性和灵活性

#### 1.3 部署优势

- **灵活部署**：支持多种证书提供方式，适应不同环境需求
- **安全性增强**：证书不包含在镜像中，降低了证书泄露风险
- **CI/CD 友好**：支持 Jenkins 等自动化部署流程，通过挂载提供证书
- **证书更新便捷**：无需重新构建镜像即可更新证书，只需替换挂载的证书文件

## DIPS Pro 架构设计

### 前端架构

#### 技术栈

- React + TypeScript
- Ant Design X（UI 组件库）
- UmiJS（企业级 React 应用框架）
- Mako（基于 Rust 的快速打包工具，对 umi 进行了深度优化）

#### 代码结构

```
dp-web/
├── src/                # 源代码
│   ├── pages/          # 页面组件
│   ├── components/     # 公共组件
│   ├── services/       # API服务
│   ├── models/         # 数据模型
│   ├── utils/          # 工具函数
│   └── layouts/        # 页面布局
├── public/             # 静态资源
├── .umirc.ts           # UmiJS配置
└── package.json        # 依赖管理
```

#### 页面组件

##### 1. 智能体页面(Agents)

智能体展示页面允许用户浏览和选择可用的智能体：

- **路径**: `/agents`
- **组件**: `src/pages/Agents/index.tsx`
- **功能**:
  - 以卡片式布局展示所有可用智能体
  - 每个智能体卡片显示名称、描述、标签、类型、状态和能力值
  - 使用 Ant Design 的 Card、Avatar 和 Tag 等组件构建统一 UI 风格
  - 支持响应式布局，根据屏幕尺寸自适应展示列数
- **数据来源**:
  - 开发环境: 从 mock 数据获取智能体列表
  - 生产环境: 通过 API 从后端获取智能体配置
- **交互设计**:
  - 卡片悬停效果增强用户体验
  - 状态徽标(Badge)直观显示智能体活跃状态
  - 标签系统便于用户理解智能体功能域

##### 2. 欢迎页面(Welcome)

系统入口页面，提供导航和快速访问功能：

- **路径**: `/welcome`
- **组件**: `src/pages/Welcome.tsx`

##### 3. 管理页面(Admin)

管理员功能页面，提供系统管理功能：

- **路径**: `/admin/sub-page`
- **组件**: `src/pages/Admin.tsx`

##### 4. 查询表格(TableList)

数据查询和展示页面：

- **路径**: `/list`
- **组件**: `src/pages/TableList/index.tsx`

#### 最佳实践与解决方案

##### React 严格模式兼容性

在 React 严格模式(Strict Mode)下，`findDOMNode`方法已被弃用，并将在未来版本中移除。这会导致使用 Ant Design 组件时在控制台显示警告信息。为解决此问题，我们采用以下方案：

1. **使用 React.forwardRef**：
   - 将组件改写为使用`forwardRef`和`useImperativeHandle`的形式
   - 示例: `dp-web/src/pages/Table/components/UpdateForm.tsx`
2. **实现步骤**：

   ```typescript
   // 改写前
   const MyComponent: React.FC<Props> = (props) => {
     // 组件逻辑...
     return <div>...</div>;
   };

   // 改写后
   const MyComponent: React.FC<Props> = forwardRef((props, ref) => {
     // 可选：暴露组件方法给父组件
     useImperativeHandle(ref, () => ({
       // 可以在这里暴露方法
     }));

     // 组件逻辑...
     return <div>...</div>;
   });
   ```

3. **适用场景**：

   - 任何使用了 Tooltip、Popconfirm 等弹出组件的自定义组件
   - 包含 Modal、Drawer 等需要 DOM 引用的组件
   - 使用 Form.Item 包装的自定义表单组件

4. **验证方法**：
   - 在开启 React 严格模式的情况下，控制台不再显示`findDOMNode is deprecated`警告

### 后端架构

#### 技术栈

- Spring Boot
- JPA / Hibernate
- PostgreSQL（主数据库）
- Redis（缓存和索引）
- MySQL（数据采集和分析）

#### 代码结构

```
dips-pro-server/
├── src/main/java/com/dipspro/
│   ├── config/         # 配置类
│   ├── controller/     # REST API控制器
│   ├── service/        # 业务逻辑层
│   ├── repository/     # 数据访问层
│   ├── entity/         # JPA实体类
│   ├── dto/            # 数据传输对象
│   ├── exception/      # 异常处理
│   └── util/           # 工具类
└── src/main/resources/
    └── application.yml # 应用配置
```

#### 数据流

1. 前端通过 REST API 与后端交互
2. 控制器层接收请求并委托给服务层处理
3. 服务层实现业务逻辑，通过仓库层访问数据
4. 仓库层负责与数据库的交互

#### 缓存策略

- 使用 Redis 缓存频繁访问的数据
- 实现多级缓存机制，减轻数据库负担

### 部署架构

#### 开发环境

- 本地开发: Docker Compose
- 测试环境: Kubernetes 集群

#### 生产环境

- Kubernetes 集群
- NGINX 作为反向代理和静态资源服务器
- PostgreSQL 集群（主从复制）
- Redis 集群（哨兵模式）

#### CI/CD 流程

- 使用 Jenkins 实现持续集成和部署
- 自动化测试包括单元测试和集成测试
- 代码质量检查使用 SonarQube

### 安全架构

#### 认证与授权

- 使用 JWT 实现无状态认证
- 基于角色的访问控制(RBAC)
- 敏感 API 使用 API 密钥保护

#### 数据安全

- 所有 API 通过 HTTPS 访问
- 敏感数据加密存储
- 定期数据备份和恢复演练

### 监控与日志

#### 监控

- 使用 Prometheus 收集指标
- Grafana 用于可视化监控数据
- 设置关键指标的告警阈值

#### 日志

- 集中式日志收集(ELK Stack)
- 结构化日志格式，便于检索和分析
- 日志分级: DEBUG, INFO, WARN, ERROR

### 性能优化

#### 前端性能

- 代码分割和懒加载
- 静态资源 CDN 加速
- 组件级别的缓存策略

#### 后端性能

- 合理使用索引
- 查询优化和 SQL 调优
- 采用异步处理框架处理耗时操作

### 扩展性设计

#### 水平扩展

- 无状态设计，支持多实例部署
- 数据分片策略
- 负载均衡

#### 垂直扩展

- 模块化设计，便于功能扩展
- 插件系统支持第三方功能集成
- 配置驱动的功能开关

## 模板系统

### 模板插槽处理流程

模板槽位数据处理是 DIPS Pro 中的核心功能之一，它允许用户创建带有动态内容的提示词模板。整个处理流程经过了全面的优化，以确保数据在 UI 到 API 的整个过程中保持一致性。

#### 数据结构

模板槽位定义支持两种格式：

1. **数组格式**：后端 API 返回的格式，每个槽位是一个带有 name、type、defaultValue 等属性的对象

   ```typescript
   SlotDefinition[] = [
     { name: 'name1', type: 'text', defaultValue: 'default1', required: true },
     { name: 'name2', type: 'select', options: [...], defaultValue: 'option1' }
   ]
   ```

2. **对象格式**：前端组件内部处理的格式，key 为槽位名称，value 为槽位定义
   ```typescript
   Record<string, SlotDefinition> = {
     'name1': { type: 'text', defaultValue: 'default1', required: true },
     'name2': { type: 'select', options: [...], defaultValue: 'option1' }
   }
   ```

系统会在多个组件中自动转换这两种格式，确保与后端 API 和前端组件都能正确交互。

#### 数据流

模板槽位数据在系统中的流动路径：

1. **模板选择**：`container.vue` 中的 `handleSelectTemplate` 函数负责初始化槽位定义和默认值
2. **槽位编辑**：`SlotEditor.vue` 组件处理用户输入，并通过 `handleSlotChange` 事件向上传递
3. **数据汇总**：`InlineTemplateEditor.vue` 收集所有槽位的值，并在提交时传递给 `TemplateSender.vue`
4. **消息构建**：`TemplateSender.vue` 和 `ChatSender.vue` 使用模板内容和槽位值构建完整的消息
5. **API 调用**：使用 `chatApi.sendChatMessage` 发送数据到后端，同时传递模板 ID、填充后的消息内容和槽位值

### 模板消息填充机制

为确保模板消息能够被正确处理，系统实现了双重填充保障机制：

1. **前端填充**：在 `ChatSender.vue` 中，使用 `fillTemplate` 函数将模板内容和槽位值合并，生成完整的消息内容，在发送 API 请求时作为 `message` 参数传递。

2. **API 层填充**：在 `chatApi.sendChatMessage` 函数中，如果检测到 `message` 为空但使用了模板，会自动获取模板内容并使用槽位值进行填充，确保发送到后端的请求中 `message` 字段始终有值。

3. **模板缓存**：为提高性能，API 层实现了模板内容缓存机制，避免重复请求相同的模板内容。

这种双重机制确保了即使在某一层填充失败的情况下，模板消息也能被正确处理和发送。

## 消息格式化架构

### MessageContent 统一消息内容类

为了支持丰富的聊天消息内容类型，系统设计了统一的 `MessageContent` 类：

```java
// 位置：com.dipspro.modules.chat.dto.MessageContent
public class MessageContent {
    private String type;    // 内容类型
    private Object content; // 内容数据
}
```

#### 支持的内容类型

- **text**: 文本内容，支持 Markdown 格式
- **suggestion**: 建议选项数组，用于快速操作
- **file**: 文件信息对象，支持文件展示和下载
- **collapsible**: 可折叠内容，支持展开/折叠交互
- **image**: 图片信息对象，支持图片展示
- **chart**: 图表数据对象，支持数据可视化
- **table**: 表格数据对象，支持结构化数据展示

#### 静态工厂方法

```java
MessageContent.text("文本内容")
MessageContent.suggestion(new String[]{"选项1", "选项2"})
MessageContent.collapsible(collapsibleData)
MessageContent.file(fileData)
```

### 格式化器增强

`ProjectDetailFormatter` 类新增结构化方法：

- `formatStructured()`: 返回完整的结构化消息内容列表
- `formatSimpleStructured()`: 返回简化的结构化消息内容列表

支持多种内容组合：

- 项目列表文本
- 可折叠的用户画像分析
- 可折叠的项目详情信息
- 交互式建议选项
- 文件附件下载

### 数据流转

```
UserProfileDto → ProjectDetailFormatter → List<MessageContent> → ChatResponse → 前端渲染
```

这种架构设计确保了：

- 统一的消息内容格式
- 灵活的内容类型扩展
- 前后端数据结构一致性
- 丰富的用户交互体验

### 数据查询模块 (normalize)

#### API 接口

- **用户画像查询 API**:

  - `GET /api/data-query/userProfile/{mobile}`: 根据手机号查询用户画像。

    - 路径参数: `mobile` (手机号码)
    - 响应体: `List<MessageContent>` (用户画像内容列表)
    - 逻辑: 调用 `DataQueryService` 查询用户画像信息，包含项目详情、用户标签等。

  - `POST /api/data-query/userProfile/batch`: 批量查询用户画像。
    - 请求体: `BatchUserProfileRequest` (包含手机号列表和显示详情标志)
      ```json
      {
        "mobiles": ["13800138000", "13800138001"],
        "showDetail": false
      }
      ```
    - 响应体: `List<MessageContent>` (与单个查询接口格式一致的消息内容列表)
      - 包含统计信息、手机号标识、用户画像内容和分隔符
      - 格式示例：
        ```
        批量查询统计: 总数 2, 成功 1, 失败 1, 耗时 1500ms
        ==================================================
        手机号: 138****8000
        [用户画像内容...]
        ---
        手机号: 138****8001 - 未找到用户画像数据
        ```
    - 限制: 单次查询最多支持 100 个手机号
    - 逻辑: 使用并行处理提高查询效率，将所有结果合并到统一的消息格式中

## 计费模块架构

### 核心计费流程

计费系统采用**同步计费**模式，确保事务一致性：

1. **同步计费流程**：

   - `ChatServiceImpl.processAndSaveMessage()` 调用 `performBillingForMessage()`
   - `performBillingForMessage()` 直接调用同步的 `performBilling()` 方法
   - 在同一事务中完成：计费记录创建 → 余额扣除 → 交易记录创建
   - 设置 `conversationId` 并保存到 `BillingUsageRecord`

2. **事务管理**：

   - `UserBalanceServiceImpl.deductBalance()` 使用 `@Transactional(rollbackFor = Exception.class)`
   - 确保余额扣除和计费记录在同一事务中
   - 避免异步操作导致的事务上下文丢失

3. **计费记录结构**：
   - 包含完整的 Token 统计信息
   - 保存 `conversationId` 用于轮次关联
   - 区分成功记录（TOKEN_BASED）和失败记录（FAILED）

---

## 充值模块架构重构 [2025-06-18]

### 重构背景

在系统运行过程中发现充值接口存在重复记录问题，即同一笔充值操作会产生两条相同的交易记录。经过深入分析，发现问题根源在于充值流程中存在双重创建交易记录的设计缺陷。

### 问题分析

#### 原始架构问题

```
RechargeService.processPaymentSuccess()
  ├─ UserBalanceService.recharge() -> 创建交易记录1
  └─ BillingTransactionService.createRechargeTransaction() -> 创建交易记录2
```

#### 核心问题

1. **职责重复**：UserBalanceService 和 BillingTransactionService 都在创建交易记录
2. **缺乏防重复机制**：没有基于支付单号的幂等性控制
3. **架构不清晰**：交易记录创建的入口不统一

### 重构方案

#### 设计原则

1. **单一职责原则**：每个服务类只负责自己领域的核心功能
2. **统一入口原则**：所有交易记录创建统一由 BillingTransactionService 处理
3. **职责分离原则**：UserBalanceService 只负责余额计算，不创建交易记录
4. **向后兼容原则**：确保 API 接口不变，对外部调用方透明

#### 新架构设计

```
RechargeService.processPaymentSuccess()
  ├─ UserBalanceService.recharge() -> 返回 BalanceOperationResult
  └─ BillingTransactionService.createRechargeTransactionWithBalanceInfo() -> 统一创建交易记录（含防重复）
```

#### 核心改进

##### 1. 余额操作结果封装

新增 `BalanceOperationResult` DTO，封装余额操作的详细结果：

- 操作前后余额
- 操作金额和类型
- 操作是否成功
- 详细消息

##### 2. UserBalanceService 职责调整

- **原职责**：余额管理 + 交易记录创建
- **新职责**：仅负责余额管理（计算、更新、验证）
- **方法变更**：recharge() 方法返回 BalanceOperationResult 而非 boolean

##### 3. BillingTransactionService 职责强化

- **原职责**：交易记录创建
- **新职责**：所有交易记录的统一创建和管理
- **新方法**：createRechargeTransactionWithBalanceInfo() - 支持防重复检测

##### 4. 防重复机制

- 基于支付单号进行重复检测
- 数据库层面的唯一约束保护
- 业务层面的幂等性控制

### 实施细节

#### 1. 数据流改进

```
原流程：
支付成功 -> 更新余额(创建记录1) -> 创建记录(记录2)

新流程：
支付成功 -> 更新余额 -> 统一创建交易记录（防重复）
```

#### 2. 关键组件

##### BalanceOperationResult

```java
public class BalanceOperationResult {
    private BigDecimal balanceBefore;   // 操作前余额
    private BigDecimal balanceAfter;    // 操作后余额
    private BigDecimal operationAmount; // 操作金额
    private String operationType;       // 操作类型
    private boolean success;            // 操作是否成功
    private String message;             // 操作结果消息
    private Long userId;                // 用户ID
}
```

##### 防重复检测方法

```java
// 基于支付单号检查重复
```

---

## 最新架构变更 (2025-06-19)

### 预付费阶梯定价架构设计

**变更时间**：2025-06-19 16:43:10（更新）

**功能概述**：

实现基于月消费金额的预付费阶梯定价功能，不同套餐设置不同的最低月消费要求，达到要求后享受更低的 Token 单价，支持无 Token 数量和模型限制的灵活计费模式。

**核心设计理念**：

- **阶梯定价机制**：根据月消费金额享受不同优惠级别
- **无使用限制**：取消 Token 数量和模型类型限制
- **预付费模式**：用户充值后按消费计费，余额不足停止服务
- **月度达标判定**：月底统计消费金额，达标用户下月享受优惠价格

**数据库架构设计**：

1. **套餐表扩展** (`b_billing_packages`)：

   - `monthly_minimum_spend`：最低月消费要求（人民币）
   - `package_level`：套餐等级标识（BASIC/STANDARD/PREMIUM）
   - 支持现有字段的完全兼容

2. **用户月消费统计表** (`b_user_monthly_spending`)：

   ```sql
   CREATE TABLE b_user_monthly_spending (
       id BIGSERIAL PRIMARY KEY,
       user_id BIGINT NOT NULL,
       package_id BIGINT NOT NULL,
       month_year VARCHAR(7) NOT NULL, -- 格式: 2024-01
       total_spend DECIMAL(10,2) DEFAULT 0,
       minimum_required DECIMAL(10,2) DEFAULT 0,
       qualification_status VARCHAR(20) DEFAULT 'UNQUALIFIED',
       package_switched_at TIMESTAMP WITH TIME ZONE NULL,
       created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
       updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
       CONSTRAINT uk_user_month UNIQUE (user_id, month_year)
   );
   ```

3. **预设套餐配置**：

   - **入门套餐**：无最低消费，标准价格（0.008/0.012）
   - **标准套餐**：月消费 200 元，优惠价格（0.005/0.008）
   - **高级套餐**：月消费 1000 元，最优价格（0.003/0.005）

**架构优势**：

1. **渐进式升级**：用户可根据使用量自然升级到更优惠套餐
2. **消费激励**：提供明确的消费目标和对应的价格优惠
3. **数据统计**：完整的月度消费统计支持业务分析
4. **灵活扩展**：支持更多套餐级别和定价策略

**技术实现要点**：

- 使用 PostgreSQL 约束确保数据一致性
- 复合索引优化查询性能
- 外键关联保证数据完整性
- 触发器自动更新时间戳

**业务流程**：

1. 用户注册时默认分配入门套餐
2. 按实际 Token 使用量和当前套餐价格计费
3. 月末统计用户消费金额，判定是否达标
4. 达标用户自动升级套餐级别，享受下月优惠价格
5. 支持用户主动降级或升级套餐选择

---

## 最新架构变更 (2025-06-19)

// ... existing code ...

## 计费系统设计

### 核心功能

- 用户余额管理
- Token 使用计费
- 套餐管理
- 消费统计
- 月度资格检查
- 可视化 Agent 折扣优化

### 套餐管理

- 支持多套餐配置
- 动态价格调整
- 免费 Token 配额
- 使用限制设置
- 可视化 Agent 输出 Token 折扣系数

### 可视化 Agent 折扣机制

为了优化用户体验，系统支持对可视化 Agent 的输出 Token 应用折扣系数：

**实现原理：**

- 数据库表 `b_billing_packages` 添加 `visual_discount_factor` 字段
- 字段类型：DECIMAL(4,3)，范围 0.001-1.000
- 默认值：1.000（表示无折扣）
- 0.100 表示 90%折扣，0.500 表示 50%折扣

**应用逻辑：**

- 在计费时根据 agentType 参数判断是否为可视化 Agent
- 如果是可视化 Agent（agentType = "visual"），对输出 Token 应用折扣系数
- 输入 Token 费用不受影响，只对输出 Token 应用折扣
- 最终费用 = 输入 Token 费用 + (输出 Token 费用 × 可视化折扣系数)

**配置管理：**

- 管理员可在套餐编辑界面配置可视化折扣系数
- 支持表单验证，确保系数在有效范围内
- 前端显示友好的折扣说明（如"0.100 表示 90%折扣"）

### 计费优化

**折扣应用优先级：**

1. 月度最低消费达标检查
2. 套餐基础价格应用
3. 可视化 Agent 折扣系数应用
4. 最终费用计算和扣费

## 引导页面系统 (Onboarding)

我们设计并实现了一个高度可配置、可扩展的引导页面系统，旨在为不同行业和类型的用户提供定制化的首次登录体验。

### 核心设计原则

- **配置驱动**: 整个引导流程由可读性强的配置文件驱动，而非硬编码在业务逻辑中。这使得添加或修改引导流程变得非常简单，无需改动核心代码。
- **组件化**: 引导页面被拆分为多个可复用的 Vue 组件（如布局、定价卡片），遵循 DRY 原则，提高了代码的可维护性。
- **类型安全**: 全程使用 TypeScript，并为配置、状态和组件属性提供了严格的类型定义，确保了代码的健壮性。

### 系统架构

1.  **分发器 (`onboarding/index.vue`)**: 作为所有引导流程的统一入口。当用户被路由到 `/onboarding` 时，此组件会根据 `userStore` 中的用户类型（行业、租户类型）查询配置文件，并立即将用户重定向到为其量身定制的具体引导页面。
2.  **配置文件**:
    - `onboardingConfig.ts`: 定义了从用户类型到具体引导页面的映射关系。
    - `moduleConfigs.ts`: 定义了所有可用的业务模块及其详细信息（如标题、功能列表、定价等）。
3.  **动态引导页面**: 具体的引导页面（如 `MedicalBeautyOrgOnboarding.vue`）是动态的。它们从配置文件中获取需要展示的模块列表，并使用 `v-for` 和通用组件 (`PricingSection.vue`) 来渲染内容。
4.  **状态管理 (`userStore`)**: 用户的引导状态 (`firstLoginCompleted`) 由 Pinia 统一管理，并在登录时从后端获取。
5.  **路由守卫 (`guards.ts`)**: 全局路由守卫会检查用户的登录状态和引导完成状态，确保所有未完成引导的用户在首次登录时被强制带到引导流程。
6.  **后端支持**: 后端提供了在 `LoginResponse` 中返回用户类型和引导状态的能力，并提供了一个 `POST /api/user/complete-onboarding` 接口来持久化用户的引导完成状态。

### 视觉设计

我们严格遵循了 Notion 定价页面的视觉风格，特别是在 `PricingSection.vue` 组件中，以确保提供一个美观、专业的界面。

### 医美消费者研究页面架构

#### 页面结构设计

- **页面路径**: `/ym/pages/consumer-research/YmConsumerResearchPage.tsx`
- **组件化设计**: 采用可复用的 `ResearchChartCard` 组件统一管理图表和表格展示
- **数据分离**: 图表配置和表格数据存储在独立的 JSON 文件中，便于维护和更新

#### 技术栈

- **图表库**: ECharts + echarts-for-react
- **动画库**: Framer Motion
- **样式**: Tailwind CSS + 自定义 CSS 变量
- **数据格式**: JSON 配置文件

#### 数据架构

```
consumer-research/
├── data/
│   ├── satisfaction-analysis.json      # 满意度分析图表配置
│   ├── consumer-behavior.json          # 消费者行为饼图配置
│   ├── market-trends.json              # 市场趋势折线图配置
│   └── consumer-profile-table.json     # 消费者画像表格数据
├── components/
│   ├── ResearchChartCard.tsx           # 可复用图表卡片组件
│   └── ConsumerResearchNavbar.tsx      # 页面导航组件
└── YmConsumerResearchPage.tsx          # 主页面组件
```

#### 组件设计模式

- **ResearchChartCard**: 支持图表和表格两种展示模式，内置数据洞察和业务建议模块
- **响应式布局**: 支持桌面端和移动端自适应
- **动画体验**: 基于 Framer Motion 的页面加载和滚动动画

#### 数据解读架构

每个分析模块包含：

- **数据可视化**: ECharts 图表或 HTML 表格
- **数据洞察**: 基于数据的深度分析和发现
- **业务建议**: 可执行的商业策略指导
- **布局控制**: 支持左右布局切换（isReversed 属性）

## 前端架构

### 医美消费者研究页面架构

#### 页面结构设计

- **页面路径**: `/ym/pages/consumer-research/YmConsumerResearchPage.tsx`
- **组件化设计**: 采用可复用的 `ResearchChartCard` 组件统一管理图表和表格展示
- **数据分离**: 图表配置和表格数据存储在独立的 JSON 文件中，便于维护和更新

#### 技术栈

- **图表库**: ECharts + echarts-for-react
- **动画库**: Framer Motion
- **样式**: Tailwind CSS + 自定义 CSS 变量
- **数据格式**: JSON 配置文件

#### 数据架构

```
consumer-research/
├── data/
│   ├── satisfaction-analysis.json      # 满意度分析图表配置
│   ├── consumer-behavior.json          # 消费者行为饼图配置
│   ├── market-trends.json              # 市场趋势折线图配置
│   └── consumer-profile-table.json     # 消费者画像表格数据
├── components/
│   ├── ResearchChartCard.tsx           # 可复用图表卡片组件
│   └── ConsumerResearchNavbar.tsx      # 页面导航组件
└── YmConsumerResearchPage.tsx          # 主页面组件
```

#### 组件设计模式

- **ResearchChartCard**: 支持图表和表格两种展示模式，内置数据洞察和业务建议模块
- **响应式布局**: 支持桌面端和移动端自适应
- **动画体验**: 基于 Framer Motion 的页面加载和滚动动画

#### 数据解读架构

每个分析模块包含：

- **数据可视化**: ECharts 图表或 HTML 表格
- **数据洞察**: 基于数据的深度分析和发现
- **业务建议**: 可执行的商业策略指导
- **布局控制**: 支持左右布局切换（isReversed 属性）

### 行业 AI 分析页面架构

#### 页面结构设计

- **页面路径**: `/ym/pages/industry-ai-analysis/YmIndustryAIAnalysisPage.tsx`
- **组件化设计**: 采用可复用的 `AIAnalysisChartCard` 组件统一管理 AI 分析图表展示
- **数据分离**: AI 分析配置和数据存储在独立的 JSON 文件中，支持 AI 模型输出数据格式

#### 技术栈

- **图表库**: ECharts + echarts-for-react
- **动画库**: Framer Motion
- **样式**: Tailwind CSS + 自定义 CSS 变量
- **数据格式**: JSON 配置文件
- **AI 集成**: 支持 AI 模型数据输出格式

#### 数据架构

```
industry-ai-analysis/
├── data/
│   ├── market-prediction.json          # 市场趋势预测折线图配置
│   ├── competitor-analysis.json        # 竞争对手分析雷达图配置
│   ├── risk-assessment.json            # 风险评估饼图配置
│   └── ai-capability-table.json        # AI能力评估表格数据
├── components/
│   ├── AIAnalysisChartCard.tsx         # AI分析专用图表卡片组件
│   └── IndustryAIAnalysisNavbar.tsx    # 页面导航组件
└── YmIndustryAIAnalysisPage.tsx        # 主页面组件
```

#### AI 分析组件设计

- **AIAnalysisChartCard**: 专为 AI 分析设计的图表组件，支持复杂的 AI 数据可视化
- **智能加载**: AI 数据加载动画和状态管理
- **多图表支持**: 折线图、雷达图、饼图、表格等多种展示形式
- **AI 洞察模块**: 深度的 AI 分析结果解读和策略建议

#### AI 数据解读架构

每个 AI 分析模块包含：

- **AI 预测模型**: 基于机器学习的趋势预测和分析
- **智能洞察**: AI 驱动的数据深度分析和发现
- **策略建议**: AI 生成的可执行商业决策指导
- **技术评估**: AI 模型成熟度和准确率评估
- **风险预警**: AI 智能识别的风险因素和预警机制

#### AI 技术特性

- **实时 AI 分析**: 支持 AI 模型实时数据处理和分析
- **多维度评估**: 技术、市场、竞争、风险等多维度 AI 分析
- **预测建模**: 基于历史数据的 AI 趋势预测
- **智能决策支持**: AI 辅助的商业决策建议系统

## 后端架构

### 核心架构原则
