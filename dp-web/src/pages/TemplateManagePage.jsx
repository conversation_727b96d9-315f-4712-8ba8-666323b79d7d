import { useNavigate } from 'react-router-dom';
import React, { useState, useEffect } from 'react';
import { 
  Typography, 
  Button, 
  Table, 
  Space, 
  Card, 
  Tooltip, 
  Popconfirm, 
  Tag,
  Input,
  App,
  Empty
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  SearchOutlined,
  ArrowRightOutlined
} from '@ant-design/icons';
import { createStyles } from 'antd-style';
import { getPromptTemplates, deleteTemplate } from '../services/chatApi';

const { Title } = Typography;
const { Search } = Input;

const useStyle = createStyles(({ css }) => {
  return {
    container: css`
      width: 100%;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    `,
    header: css`
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    `,
    card: css`
      margin-bottom: 20px;
    `,
    searchContainer: css`
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;
    `,
    empty: css`
      padding: 48px 0;
    `,
    tag: css`
      margin: 2px;
    `
  };
});

const TemplateManagePage = () => {
  const { styles } = useStyle();
  const navigate = useNavigate();
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  // 使用App.useApp获取消息实例，避免静态函数警告
  const { message: messageApi } = App.useApp();
  
  // 获取所有模板
  useEffect(() => {
    fetchTemplates();
  }, []);
  
  // 获取模板列表
  const fetchTemplates = async () => {
    setLoading(true);
    try {
      const data = await getPromptTemplates();
      setTemplates(data);
    } catch (error) {
      messageApi.error(`获取模板列表失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };
  
  // 处理删除模板
  const handleDelete = async (id) => {
    try {
      await deleteTemplate(id);
      messageApi.success('模板删除成功');
      fetchTemplates(); // 重新加载列表
    } catch (error) {
      messageApi.error(`删除模板失败: ${error.message}`);
    }
  };
  
  // 根据搜索文本过滤模板
  const filteredTemplates = templates.filter(template => {
    const searchLower = searchText.toLowerCase();
    return (
      template.name?.toLowerCase().includes(searchLower) || 
      template.description?.toLowerCase().includes(searchLower) || 
      template.agentType?.toLowerCase().includes(searchLower)
    );
  });
  
  // 表格列定义
  const columns = [
    {
      title: '模板名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <a 
          onClick={() => navigate(`/template/edit/${record.id}`)}
          style={{ fontWeight: 'bold' }}
        >
          {text}
        </a>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: {
        showTitle: false,
      },
      render: description => (
        <Tooltip title={description}>
          <span>{description || '-'}</span>
        </Tooltip>
      ),
    },
    {
      title: '类型',
      dataIndex: 'agentType',
      key: 'agentType',
      render: text => text ? <Tag color="blue">{text}</Tag> : '-',
    },
    {
      title: '槽位数量',
      dataIndex: 'slotDefinitions',
      key: 'slotDefinitions',
      align: 'center',
      render: (slotDefinitions) => {
        const count = slotDefinitions?.length || 0;
        return <Tag color={count > 0 ? 'green' : 'default'}>{count}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              onClick={() => navigate(`/template/edit/${record.id}`)}
            />
          </Tooltip>
          <Popconfirm
            title="确定删除此模板吗？"
            description="删除后不可恢复。"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button 
                type="text" 
                danger 
                icon={<DeleteOutlined />} 
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];
  
  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Title level={2}>提示词模板管理</Title>
        <Button 
          type="primary" 
          icon={<PlusOutlined />} 
          onClick={() => navigate('/template/new')}
        >
          新建模板
        </Button>
      </div>
      
      <Card className={styles.card}>
        <div className={styles.searchContainer}>
          <Search
            placeholder="搜索模板名称或描述"
            allowClear
            style={{ width: 300 }}
            value={searchText}
            onChange={e => setSearchText(e.target.value)}
          />
          
          <Button 
            type="default" 
            icon={<ArrowRightOutlined />}
            onClick={() => navigate('/chat')}
          >
            去聊天界面
          </Button>
        </div>
        
        <Table 
          columns={columns} 
          dataSource={filteredTemplates} 
          rowKey="id"
          loading={loading}
          pagination={{ 
            defaultPageSize: 10, 
            showSizeChanger: true, 
            pageSizeOptions: ['10', '20', '50'],
            showTotal: (total) => `共 ${total} 个模板` 
          }}
          locale={{
            emptyText: (
              <Empty
                className={styles.empty}
                description="暂无模板数据"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            )
          }}
        />
      </Card>
    </div>
  );
};

export default TemplateManagePage; 