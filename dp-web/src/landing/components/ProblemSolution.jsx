import React from 'react';
import { Row, Col, Card } from 'antd';
import { ClockCircleOutlined, DisconnectOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import '../styles/ProblemSolution.css';

const ProblemSolution = () => {
  // 痛点数据
  const painPoints = [
    {
      icon: <ClockCircleOutlined className="pain-icon" />,
      title: '重复性任务耗费大量宝贵时间',
      description: '员工深陷数据整理、邮件分类、信息查找的泥潭，无法专注于高价值工作。'
    },
    {
      icon: <DisconnectOutlined className="pain-icon" />,
      title: '跨部门协作流程复杂低效',
      description: '信息传递不畅，任务交接困难，项目延期风险高，团队间协作障碍重重。'
    },
    {
      icon: <QuestionCircleOutlined className="pain-icon" />,
      title: '单一AI工具能力有限',
      description: '现有AI工具缺乏联动能力，无法处理需要多步骤、多角度思考的复杂场景。'
    }
  ];

  return (
    <section className="problem-solution" id="problems">
      <div className="container">
        <div className="section-header">
          <h2 className="section-title">您的团队是否正被这些问题困扰？</h2>
          <p className="section-subtitle">这些常见痛点正在消耗您团队的宝贵时间和资源</p>
        </div>

        <Row gutter={[32, 32]} className="pain-points-row">
          {painPoints.map((point, index) => (
            <Col xs={24} md={8} key={index}>
              <Card className="pain-card">
                <div className="pain-icon-container">{point.icon}</div>
                <h3 className="pain-title">{point.title}</h3>
                <p className="pain-description">{point.description}</p>
              </Card>
            </Col>
          ))}
        </Row>

        <div className="solution-container">
          <div className="solution-content">
            <div className="solution-header">
              <div className="solution-tag">解决方案</div>
              <h2 className="solution-title">DIPS Pro，为您而来，化繁为简</h2>
            </div>
            <p className="solution-description">
              DIPS Pro 引入革命性的<strong>多智能体协作模式</strong>。我们不再依赖单个 AI，而是构建一个智能体"团队"，每个智能体专注于特定任务，它们无缝协作，如同经验丰富的专业团队，高效、精准地完成您设定好的复杂工作流。
            </p>
            <div className="solution-points">
              <div className="solution-point">
                <div className="point-icon">⚡</div>
                <div className="point-text">解放人力资源，专注高价值决策</div>
              </div>
              <div className="solution-point">
                <div className="point-icon">🔄</div>
                <div className="point-text">无缝协作，自动处理跨领域任务</div>
              </div>
              <div className="solution-point">
                <div className="point-icon">📈</div>
                <div className="point-text">提高效率，降低出错率</div>
              </div>
            </div>
          </div>
          <div className="solution-image">
            <img src="/img/solution-illustration.png" alt="DIPS Pro 解决方案" />
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProblemSolution; 