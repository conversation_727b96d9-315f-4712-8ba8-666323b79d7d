import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import * as antd from 'antd';
import dayjs from 'dayjs';

const { Input, Select, DatePicker, Popover, Button, Space, Cascader, ConfigProvider } = antd;

/**
 * SlotEditor组件 - 用于编辑模板中的单个槽位
 * 支持文本、日期和选择列表等不同类型
 */
const SlotEditor = ({
  slotName,
  slotDefinitions,
  values,
  onChange,
  onClose
}) => {
  const slotDefinition = slotDefinitions[slotName];
  const value = values[slotName];
  // 处理类型的大小写不一致问题，统一转为小写
  const type = slotDefinition?.type ? String(slotDefinition.type).toLowerCase() : 'text';
  const required = slotDefinition?.required || false;
  const options = slotDefinition?.options || [];
  const defaultValue = slotDefinition?.defaultValue;
  const disabled = slotDefinition?.disabled || false;

  // 为了在用户取消时不影响外部状态，使用内部状态暂存编辑中的值
  // 如果value存在使用value，否则检查是否有默认值
  const [tempValue, setTempValue] = useState(value !== null && value !== undefined ? value : defaultValue);
  // 处理确认按钮点击
  const handleConfirm = () => {
    onChange(tempValue);
    onClose();
    // 清空tempValue
    setTempValue(null);
  };

  // 处理取消按钮点击
  const handleCancel = () => {
    onClose();
    // 清空tempValue
    setTempValue(value);
  };

  // 每次渲染时，把tempValue重置为value
  useEffect(() => {
    setTempValue(value !== null && value !== undefined ? value : defaultValue);
  }, [value]);

  // 处理回车键按下事件
  const handleKeyDown = (e) => {
    // 如果按下的是回车键（且没有同时按下Shift键，以支持多行文本的换行）
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault(); // 防止默认行为
      handleConfirm();
    }
  };

  // 根据槽位类型渲染不同的编辑控件
  const renderEditor = () => {
    switch (type) {
      case 'date':
        return (
          <div onKeyDown={handleKeyDown}>
            <DatePicker
              value={tempValue ? dayjs(tempValue) : null}
              onChange={(date) => setTempValue(date ? date.toDate() : null)}
              style={{ width: '100%' }}
            />
          </div>
        );

      case 'select':
        return (
          <div onKeyDown={handleKeyDown}>
            <Select
              showSearch
              disabled={disabled}
              value={tempValue}
              onChange={(val) => setTempValue(val)}
              style={{ minWidth: '100%' }}
              popupMatchSelectWidth={false}
              options={options.map(opt => ({
                label: opt.label || opt.value,
                value: opt.value
              }))}
            />
          </div>
        );
      case 'textarea':
        return (
          <Input.TextArea
            value={tempValue}
            onChange={(e) => setTempValue(e.target.value)}
            placeholder={`请输入${slotDefinition?.label || slotName}`}
            onKeyDown={(e) => {
              // 对于多行文本，只有在按下Ctrl+Enter时才提交
              if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();
                handleConfirm();
              }
            }}
            autoSize={{ minRows: 2, maxRows: 6 }}
          />
        );
      case 'cascader':
        const displayRender = labels => labels[labels.length - 1];
        return (
          <div onKeyDown={handleKeyDown}>
            {/* 配置 Cascader 的主题 */}
            <ConfigProvider
              theme={{
                components: {
                  Cascader: {
                    dropdownHeight: 256,
                    optionSelectedBg: 'rgba(0, 0, 0, 0.04)',
                  },
                },
              }}
            >
              <Cascader
                options={options}
                expandTrigger="hover"
                displayRender={displayRender}
                style={{ width: '100%' }}
                onChange={(value, selectedOptions) => {
                  const lastValue = value && value.length > 0 ? value[value.length - 1] : null;
                  setTempValue(lastValue);
                }}
              />
            </ConfigProvider>
          </div>
        );
      case 'text':
      default:
        return (
          <Input
            value={tempValue}
            onChange={(e) => setTempValue(e.target.value)}
            placeholder={`请输入${slotDefinition?.label || slotName}`}
            onPressEnter={handleConfirm}
          />
        );
    }
  };

  // 底部按钮
  const footer = (
    <div style={{ marginTop: 8, textAlign: 'right' }}>
      <Space>
        <Button size="small" onClick={handleCancel}>取消</Button>
        <Button
          size="small"
          type="primary"
          onClick={handleConfirm}
        >
          确认
        </Button>
      </Space>
    </div>
  );

  return (
    <div className="slot-editor" onKeyDown={handleKeyDown}>
      <div style={{ marginBottom: 8 }}>
        <strong>{slotDefinition?.label || slotName}</strong>
        {required && <span style={{ color: '#ff4d4f', marginLeft: 4 }}>*</span>}
      </div>
      {renderEditor()}
      {footer}
    </div>
  );
};

SlotEditor.propTypes = {
  slotName: PropTypes.string.isRequired,
  slotDefinition: PropTypes.shape({
    type: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.oneOf(['text', 'textarea', 'date', 'select', 'cascader', 'TEXT', 'TEXTAREA', 'DATE', 'SELECT', 'CASCADER'])
    ]),
    label: PropTypes.string,
    required: PropTypes.bool,
    defaultValue: PropTypes.any,
    options: PropTypes.arrayOf(PropTypes.shape({
      label: PropTypes.string,
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      children: PropTypes.array // 为 Cascader 添加子选项支持
    }))
  }),
  value: PropTypes.any,
  onChange: PropTypes.func.isRequired,
  onClose: PropTypes.func.isRequired
};

SlotEditor.defaultProps = {
  slotDefinition: { type: 'text', required: false },
  value: null
};

export default SlotEditor;