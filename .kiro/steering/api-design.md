# API Design Guidelines

## REST API Standards

### URL Conventions
- Use **kebab-case** for URL paths: `/api/user-profiles`
- Use **plural nouns** for resource collections: `/api/users`
- Use **nested resources** for relationships: `/api/tenants/{id}/users`
- Version APIs with prefix: `/api/v1/users`

### HTTP Methods
- **GET**: Retrieve resources (idempotent)
- **POST**: Create new resources
- **PUT**: Update entire resource (idempotent)
- **PATCH**: Partial resource updates
- **DELETE**: Remove resources (idempotent)

### Response Format
```json
{
  "success": true,
  "code": 200,
  "message": "Success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### Error Response Format
```json
{
  "success": false,
  "code": 400,
  "message": "Validation failed",
  "errors": [
    {
      "field": "email",
      "message": "Invalid email format"
    }
  ],
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## Status Code Usage

### Success Codes
- **200 OK**: Successful GET, PUT, PATCH
- **201 Created**: Successful POST with resource creation
- **204 No Content**: Successful DELETE or PUT with no response body

### Client Error Codes
- **400 Bad Request**: Invalid request format or validation errors
- **401 Unauthorized**: Missing or invalid authentication
- **403 Forbidden**: Valid authentication but insufficient permissions
- **404 Not Found**: Resource doesn't exist
- **409 Conflict**: Resource conflict (duplicate creation)
- **422 Unprocessable Entity**: Valid format but business logic errors

### Server Error Codes
- **500 Internal Server Error**: Unexpected server errors
- **503 Service Unavailable**: Temporary service issues

## Request/Response Patterns

### Pagination
```json
{
  "content": [],
  "page": {
    "number": 0,
    "size": 20,
    "totalElements": 100,
    "totalPages": 5
  }
}
```

### Filtering & Sorting
- Use query parameters: `?status=active&sort=createTime,desc`
- Support multiple sort fields: `?sort=name,asc&sort=createTime,desc`
- Use standard operators: `?createTime>=2024-01-01&createTime<2024-02-01`

### Bulk Operations
```json
{
  "operations": [
    {"action": "create", "data": {}},
    {"action": "update", "id": 1, "data": {}},
    {"action": "delete", "id": 2}
  ]
}
```

## Multi-tenant API Design

### Tenant Context
- Include tenant information in JWT token
- Automatically filter data by tenant in repositories
- Validate tenant access in controllers
- Support tenant switching for admin users

### Tenant-specific Endpoints
```
/api/v1/tenants/{tenantId}/users
/api/v1/tenants/{tenantId}/billing/packages
/api/v1/tenants/{tenantId}/chat/conversations
```

## Security Headers

### Required Headers
- `Authorization: Bearer {jwt-token}`
- `Content-Type: application/json`
- `Accept: application/json`

### Response Headers
- `X-Request-ID`: Unique request identifier for tracing
- `X-Rate-Limit-Remaining`: API rate limit information
- `Cache-Control`: Caching directives

## API Versioning Strategy

### Version in URL Path
- Current: `/api/v1/users`
- Future: `/api/v2/users`

### Backward Compatibility
- Maintain previous versions for at least 6 months
- Provide migration guides for breaking changes
- Use deprecation warnings in responses

### Version Lifecycle
1. **Alpha**: Internal testing only
2. **Beta**: Limited external testing
3. **Stable**: Production ready
4. **Deprecated**: Scheduled for removal
5. **Retired**: No longer supported

## Rate Limiting

### Default Limits
- **Authenticated users**: 1000 requests/hour
- **Anonymous users**: 100 requests/hour
- **Admin operations**: 10000 requests/hour

### Rate Limit Headers
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## API Documentation

### OpenAPI Specification
- Use Swagger/OpenAPI 3.0 annotations
- Include request/response examples
- Document all error scenarios
- Provide interactive API explorer

### Endpoint Documentation Template
```java
@Operation(summary = "Create new user", description = "Creates a new user in the system")
@ApiResponses(value = {
    @ApiResponse(responseCode = "201", description = "User created successfully"),
    @ApiResponse(responseCode = "400", description = "Invalid input data"),
    @ApiResponse(responseCode = "409", description = "User already exists")
})
```