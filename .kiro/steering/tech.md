# Technology Stack

## Core Framework
- **Spring Boot**: 3.3.2
- **Java**: 23
- **Build Tool**: Maven 3.8+
- **Application Server**: Embedded Tomcat

## Database & Persistence
- **Primary Database**: PostgreSQL 12+ (with pgvector extension)
- **Secondary Database**: MySQL 8.0+ (compatibility)
- **ORM**: Spring Data JPA + Hibernate 6.5.2
- **Cache**: Redis 6+ (Lettuce client)
- **Connection Pool**: HikariCP

## AI & Vector Processing
- **Spring AI**: 1.0.0-M6 (OpenAI integration)
- **Vector Database**: PostgreSQL pgvector
- **Text Embedding**: ONNX Runtime + text2vec-base-chinese model
- **Token Calculation**: JTokkit library

## Security & Authentication
- **Authentication**: JWT (JSON Web Tokens)
- **Authorization**: Spring Security + RBAC
- **Password Encoding**: BCrypt
- **Session Management**: Stateless

## Key Libraries
- **Lombok**: Code generation and boilerplate reduction
- **MapStruct**: Bean mapping and conversion
- **Hypersistence Utils**: PostgreSQL advanced type support
- **Apache Commons Lang3**: Utility functions
- **Google Guava**: Core libraries
- **Hutool**: Chinese utility toolkit
- **FastJSON**: JSON processing

## Payment & External Services
- **WeChat Pay**: Payment processing
- **Qiniu Cloud**: File storage and CDN
- **ZXing**: QR code generation

## Common Commands

### Development
```bash
# Start development server
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# Start with specific profile
mvn spring-boot:run -Dspring-boot.run.profiles=local
```

### Build & Package
```bash
# Clean and compile
mvn clean compile

# Run tests
mvn test

# Package application
mvn clean package

# Skip tests during packaging
mvn clean package -DskipTests
```

### Docker
```bash
# Build Docker image
docker build -t dips-pro-server .

# Run with Docker
docker run -d --name dips-pro-server -p 8080:8080 dips-pro-server
```

### Database
```bash
# Initialize PostgreSQL database
createdb dips_pro
psql -d dips_pro -f sql/sys_postgresql.sql
```

## Environment Profiles
- **dev**: Development environment (default)
- **test**: Testing environment
- **prod**: Production environment
- **local**: Local development
- **route**: Route service configuration
- **embedding**: AI embedding configuration
- **springai**: Spring AI configuration