# Project Structure & Architecture

## Root Directory Layout
```
dp-server/
├── src/main/java/com/dipspro/     # Main Java source code
├── src/main/resources/            # Configuration files and resources
├── src/test/java/                 # Test code
├── sql/                          # Database scripts
├── pom.xml                       # Maven configuration
├── Dockerfile                    # Docker build configuration
└── README.md                     # Project documentation
```

## Java Package Structure
```
com.dipspro/
├── DipsProApp.java               # Spring Boot main class
├── aspect/                       # AOP aspects (logging, etc.)
├── common/                       # Shared components
│   ├── dto/                     # Common DTOs (ApiResponse, PageResponse)
│   ├── exception/               # Global exception handling
│   ├── interceptor/             # Request/response interceptors
│   ├── result/                  # Result wrapper classes
│   └── asyncTask/               # Async task processing
├── config/                       # Configuration classes
├── security/                     # Security components (JWT, filters)
├── util/                        # Utility classes
├── constant/                    # Application constants
└── modules/                     # Business modules
```

## Standard Module Structure
Each business module follows this pattern:
```
modules/{module-name}/
├── controller/                   # REST controllers
├── service/                     # Business logic layer
│   └── impl/                   # Service implementations
├── repository/                  # Data access layer
├── entity/                      # JPA entities
├── dto/                        # Data transfer objects
├── mapper/                     # MapStruct mappers (if needed)
├── enums/                      # Module-specific enums
└── exception/                  # Module-specific exceptions
```

## Key Business Modules
- **auth/**: Authentication and authorization
- **user/**: User management
- **tenant/**: Multi-tenant management
- **chat/**: AI chat functionality
- **profile/**: User profiling and analytics
- **billing/**: Billing and payment processing
- **system/**: System administration
- **embedding/**: Vector embedding processing
- **normalize/**: Data normalization

## Configuration Structure
```
src/main/resources/
├── application.yml               # Main configuration
├── application-{profile}.yml     # Environment-specific configs
├── logback-spring.xml           # Logging configuration
├── index-definitions.yml        # Search index definitions
└── mapper/                      # MyBatis XML mappers (if used)
```

## Architectural Patterns

### Layered Architecture
- **Controller**: HTTP request handling, validation, response formatting
- **Service**: Business logic, transaction management, exception handling
- **Repository**: Data access, query optimization
- **Entity**: Data models, database mapping

### Multi-tenant Design
- All business entities include `tenantId` field
- Automatic tenant data filtering in repositories
- Tenant-level configuration isolation

### Base Classes
- **BaseEntity**: Common audit fields (createBy, updateBy, createTime, updateTime)
- **BaseDTO**: Common DTO fields for data transfer
- **BaseMapper**: Common mapping operations
- **BasePageQuery**: Standardized pagination

### Response Patterns
- **ApiResponse<T>**: Unified API response wrapper
- **Result<T>**: Alternative result wrapper
- **PageResponse<T>**: Paginated response format

### Security Architecture
- JWT stateless authentication
- Spring Security filter chain
- Method-level authorization with `@PreAuthorize`
- Tenant-aware security context

## Naming Conventions
- **Entities**: Remove `sys_` prefix from table names (e.g., `sys_user` → `User`)
- **DTOs**: Suffix with purpose (`UserCreateDto`, `UserResponseDto`)
- **Services**: Interface + Impl pattern (`UserService` + `UserServiceImpl`)
- **Controllers**: Suffix with `Controller` (`UserController`)
- **Repositories**: Suffix with `Repository` (`UserRepository`)

## Database Design Principles
- All business tables include tenant isolation
- Audit fields in all entities (create/update time and user)
- Use PostgreSQL JSONB for flexible data storage
- Vector columns for AI/ML features using pgvector extension