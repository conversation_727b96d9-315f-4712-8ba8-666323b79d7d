# Product Overview

DIPS Pro is an enterprise-grade multi-tenant backend service built on Spring Boot 3.3.2, designed for data intelligence processing and analysis scenarios. 

## Core Features

- **Multi-tenant Architecture**: Complete tenant isolation with tenant-level configuration management
- **RBAC Permission System**: User, role, and permission three-tier management with role inheritance
- **AI Assistant Chat**: Conversational AI with template matching and embedding-based responses
- **User Profiling**: Data analysis, user tagging, and project analytics
- **Billing Management**: Package management, balance control, usage statistics, and payment processing
- **Security**: JWT authentication with Spring Security, password encryption, audit logging

## Business Domains

- Authentication & Authorization
- User & Tenant Management  
- AI Chat & Template Matching
- User Profile Analytics
- Billing & Payment Processing
- System Administration & Audit

## Target Use Cases

- Enterprise data intelligence platforms
- Multi-tenant SaaS applications
- AI-powered analytics services
- Customer data platforms with billing