# Testing Guidelines

## Testing Strategy

### Testing Pyramid
- **Unit Tests (70%)**: Fast, isolated, focused on single components
- **Integration Tests (20%)**: Test component interactions and database operations
- **End-to-End Tests (10%)**: Full application workflow testing

### Test Coverage Goals
- **Minimum**: 80% line coverage
- **Target**: 90% line coverage for critical business logic
- **Services**: 95% coverage for service layer
- **Controllers**: 85% coverage for API endpoints

## Unit Testing Standards

### Test Structure (AAA Pattern)
```java
@Test
void shouldCreateUserWhenValidDataProvided() {
    // Arrange
    UserCreateDto createDto = UserCreateDto.builder()
        .username("testuser")
        .email("<EMAIL>")
        .build();
    
    // Act
    UserResponseDto result = userService.createUser(createDto);
    
    // Assert
    assertThat(result.getUsername()).isEqualTo("testuser");
    assertThat(result.getEmail()).isEqualTo("<EMAIL>");
}
```

### Mocking Guidelines
```java
@ExtendWith(MockitoExtension.class)
class UserServiceTest {
    
    @Mock
    private UserRepository userRepository;
    
    @Mock
    private PasswordEncoder passwordEncoder;
    
    @InjectMocks
    private UserServiceImpl userService;
    
    @Test
    void shouldThrowExceptionWhenUserAlreadyExists() {
        // Given
        when(userRepository.existsByTenantIdAndUsername(1L, "testuser"))
            .thenReturn(true);
        
        // When & Then
        assertThatThrownBy(() -> userService.createUser(createDto))
            .isInstanceOf(EntityExistException.class)
            .hasMessage("User already exists");
    }
}
```

### Test Data Builders
```java
public class UserTestDataBuilder {
    
    public static User.UserBuilder defaultUser() {
        return User.builder()
            .id(1L)
            .tenantId(1L)
            .username("testuser")
            .email("<EMAIL>")
            .status(UserStatus.ACTIVE)
            .createTime(LocalDateTime.now())
            .deleted(false);
    }
    
    public static User activeUser() {
        return defaultUser().build();
    }
    
    public static User inactiveUser() {
        return defaultUser()
            .status(UserStatus.INACTIVE)
            .build();
    }
}
```

## Integration Testing

### Repository Testing
```java
@DataJpaTest
@TestPropertySource(properties = {
    "spring.jpa.hibernate.ddl-auto=create-drop",
    "spring.datasource.url=jdbc:h2:mem:testdb"
})
class UserRepositoryTest {
    
    @Autowired
    private TestEntityManager entityManager;
    
    @Autowired
    private UserRepository userRepository;
    
    @Test
    void shouldFindUsersByTenantId() {
        // Given
        User user = UserTestDataBuilder.activeUser();
        entityManager.persistAndFlush(user);
        
        // When
        List<User> users = userRepository.findByTenantId(1L);
        
        // Then
        assertThat(users).hasSize(1);
        assertThat(users.get(0).getUsername()).isEqualTo("testuser");
    }
}
```

### Service Integration Testing
```java
@SpringBootTest
@Transactional
@TestPropertySource(locations = "classpath:application-test.yml")
class UserServiceIntegrationTest {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private UserRepository userRepository;
    
    @Test
    void shouldCreateUserWithEncryptedPassword() {
        // Given
        UserCreateDto createDto = UserCreateDto.builder()
            .username("testuser")
            .email("<EMAIL>")
            .password("password123")
            .build();
        
        // When
        UserResponseDto result = userService.createUser(createDto);
        
        // Then
        User savedUser = userRepository.findById(result.getId()).orElseThrow();
        assertThat(savedUser.getPasswordHash()).isNotEqualTo("password123");
        assertThat(savedUser.getPasswordHash()).startsWith("$2a$");
    }
}
```

## Controller Testing

### Web Layer Testing
```java
@WebMvcTest(UserController.class)
@Import(SecurityConfig.class)
class UserControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private UserService userService;
    
    @Test
    @WithMockUser(roles = "ADMIN")
    void shouldCreateUserWhenValidRequest() throws Exception {
        // Given
        UserCreateDto createDto = UserCreateDto.builder()
            .username("testuser")
            .email("<EMAIL>")
            .build();
        
        UserResponseDto responseDto = UserResponseDto.builder()
            .id(1L)
            .username("testuser")
            .email("<EMAIL>")
            .build();
        
        when(userService.createUser(any(UserCreateDto.class)))
            .thenReturn(responseDto);
        
        // When & Then
        mockMvc.perform(post("/api/v1/users")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.username").value("testuser"));
    }
}
```

## Multi-tenant Testing

### Tenant Isolation Testing
```java
@SpringBootTest
@Transactional
class TenantIsolationTest {
    
    @Autowired
    private UserService userService;
    
    @Test
    void shouldIsolateUsersByTenant() {
        // Given - Create users in different tenants
        SecurityUtil.setCurrentTenantId(1L);
        UserResponseDto tenant1User = userService.createUser(createUserDto("user1"));
        
        SecurityUtil.setCurrentTenantId(2L);
        UserResponseDto tenant2User = userService.createUser(createUserDto("user2"));
        
        // When - Query from tenant 1
        SecurityUtil.setCurrentTenantId(1L);
        List<UserResponseDto> tenant1Users = userService.getAllUsers();
        
        // Then - Should only see tenant 1 users
        assertThat(tenant1Users).hasSize(1);
        assertThat(tenant1Users.get(0).getUsername()).isEqualTo("user1");
    }
}
```

## Test Configuration

### Test Properties
```yaml
# application-test.yml
spring:
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
  redis:
    host: localhost
    port: 6379
    database: 1

logging:
  level:
    com.dipspro: DEBUG
    org.springframework.security: DEBUG
```

### Test Containers (Optional)
```java
@Testcontainers
@SpringBootTest
class DatabaseIntegrationTest {
    
    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:13")
            .withDatabaseName("testdb")
            .withUsername("test")
            .withPassword("test");
    
    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
    }
}
```

## Performance Testing

### Load Testing Guidelines
- Test critical API endpoints under load
- Verify database connection pool behavior
- Test concurrent user scenarios
- Monitor memory usage and garbage collection

### Benchmark Testing
```java
@BenchmarkMode(Mode.AverageTime)
@OutputTimeUnit(TimeUnit.MILLISECONDS)
@State(Scope.Benchmark)
public class UserServiceBenchmark {
    
    @Benchmark
    public void testUserCreation() {
        // Benchmark user creation performance
    }
}
```

## Test Maintenance

### Test Data Management
- Use test data builders for consistent test data
- Clean up test data after each test
- Use transactions for automatic rollback
- Avoid shared mutable state between tests

### Continuous Integration
- Run all tests on every commit
- Fail builds on test failures
- Generate test coverage reports
- Monitor test execution time and optimize slow tests

### Test Documentation
- Document complex test scenarios
- Maintain test data setup instructions
- Document testing environment requirements
- Keep test documentation up to date with code changes