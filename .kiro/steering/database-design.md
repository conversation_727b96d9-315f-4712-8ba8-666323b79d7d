# Database Design Guidelines

## Schema Design Principles

### Multi-tenant Architecture
- All business tables MUST include `tenant_id` column
- Create composite indexes including `tenant_id` as first column
- Implement row-level security policies for tenant isolation
- Use tenant-aware base repository for automatic filtering

### Naming Conventions
- Use **snake_case** for table and column names
- Use **plural nouns** for table names: `users`, `chat_messages`
- Use descriptive names: `user_balance_history` not `ubh`
- Prefix system tables with `sys_`: `sys_menu`, `sys_role`

### Primary Keys
- Use `BIGINT` auto-increment for primary keys
- Name primary key column as `id`
- Consider UUID for distributed systems or external references
- Never expose internal IDs in public APIs

## Standard Table Structure

### Base Audit Columns
All business tables should include:
```sql
CREATE TABLE example_table (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    tenant_id BIGINT NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted BOOLEAN DEFAULT FALSE,
    version INTEGER DEFAULT 0
);
```

### Indexes Strategy
```sql
-- Tenant isolation index (always first)
CREATE INDEX idx_example_tenant_id ON example_table(tenant_id);

-- Composite indexes for common queries
CREATE INDEX idx_example_tenant_status ON example_table(tenant_id, status);

-- Partial indexes for soft deletes
CREATE INDEX idx_example_active ON example_table(tenant_id) WHERE deleted = FALSE;
```

## Data Types Guidelines

### Common Field Types
- **IDs**: `BIGINT` for internal IDs, `VARCHAR(64)` for external references
- **Timestamps**: `TIMESTAMP` with timezone awareness
- **Money/Decimal**: `DECIMAL(19,4)` for financial calculations
- **Status/Enum**: `VARCHAR(32)` with CHECK constraints
- **JSON Data**: `JSONB` for flexible schema data
- **Text Content**: `TEXT` for unlimited text, `VARCHAR(n)` for limited

### PostgreSQL Specific Types
- **UUID**: Use `UUID` type for globally unique identifiers
- **Arrays**: `TEXT[]` for simple lists, avoid for complex data
- **JSONB**: For semi-structured data with indexing needs
- **Vector**: `VECTOR(1536)` for AI embeddings using pgvector

## Relationship Design

### Foreign Key Constraints
```sql
-- Always include tenant_id in foreign key relationships
ALTER TABLE user_profiles 
ADD CONSTRAINT fk_user_profiles_user 
FOREIGN KEY (tenant_id, user_id) 
REFERENCES users(tenant_id, id);
```

### Many-to-Many Relationships
```sql
CREATE TABLE user_role_relations (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    tenant_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by BIGINT,
    UNIQUE(tenant_id, user_id, role_id)
);
```

## Performance Optimization

### Indexing Strategy
- Create indexes for all foreign keys
- Index frequently queried columns
- Use partial indexes for filtered queries
- Monitor and optimize slow queries regularly

### Query Optimization
- Always include `tenant_id` in WHERE clauses
- Use LIMIT for large result sets
- Implement proper pagination with OFFSET/LIMIT
- Consider materialized views for complex aggregations

### Connection Management
- Use HikariCP connection pool
- Configure appropriate pool sizes
- Monitor connection usage and leaks
- Implement connection timeout settings

## Data Migration Guidelines

### Flyway Migration Scripts
- Use sequential versioning: `V1__Initial_schema.sql`
- Include rollback scripts when possible
- Test migrations on production-like data
- Keep migrations idempotent and reversible

### Migration Best Practices
```sql
-- V1__Create_users_table.sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    tenant_id BIGINT NOT NULL,
    username VARCHAR(64) NOT NULL,
    email VARCHAR(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    status VARCHAR(32) DEFAULT 'ACTIVE',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted BOOLEAN DEFAULT FALSE,
    version INTEGER DEFAULT 0
);

-- Indexes
CREATE UNIQUE INDEX uk_users_tenant_username ON users(tenant_id, username) WHERE deleted = FALSE;
CREATE UNIQUE INDEX uk_users_tenant_email ON users(tenant_id, email) WHERE deleted = FALSE;
CREATE INDEX idx_users_tenant_status ON users(tenant_id, status);
```

## Security Considerations

### Data Protection
- Encrypt sensitive data at column level
- Use hashed passwords with salt
- Implement audit logging for sensitive operations
- Regular security audits and penetration testing

### Access Control
- Implement row-level security (RLS) policies
- Use database roles for different access levels
- Limit database user permissions
- Regular access review and cleanup

## Backup and Recovery

### Backup Strategy
- Daily full backups with point-in-time recovery
- Test backup restoration procedures regularly
- Implement cross-region backup replication
- Document recovery procedures and RTO/RPO targets

### Data Retention
- Implement data archiving for old records
- Define retention policies for different data types
- Comply with data protection regulations (GDPR, etc.)
- Secure deletion of sensitive data when required

## Monitoring and Maintenance

### Performance Monitoring
- Monitor query performance and slow queries
- Track database connection usage
- Monitor disk space and growth trends
- Set up alerts for critical metrics

### Regular Maintenance
- Update table statistics regularly
- Rebuild indexes when necessary
- Monitor and clean up unused indexes
- Regular database health checks