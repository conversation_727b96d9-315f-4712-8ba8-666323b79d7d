# 全棧架構指導規則

## 項目整體架構

### 系統組成
- **DP Server**: Spring Boot 3.3.2 後端服務（多租戶企業級後端）
- **DP Web FA**: 前端應用（管理後台/用戶界面）
- **DP Landing Page**: 產品落地頁（營銷展示頁面）

### 架構原則
- **前後端分離**: 清晰的API邊界，獨立部署和擴展
- **多租戶支持**: 所有組件都需要考慮租戶隔離
- **統一認證**: 跨應用的單點登錄和權限管理
- **響應式設計**: 支持多設備訪問
- **微服務就緒**: 為未來拆分做好準備

## 前後端集成規範

### API 通信標準
```typescript
// 統一的API響應格式
interface ApiResponse<T> {
  success: boolean;
  code: number;
  message: string;
  data: T;
  timestamp: string;
}

// 分頁響應格式
interface PageResponse<T> {
  content: T[];
  page: {
    number: number;
    size: number;
    totalElements: number;
    totalPages: number;
  };
}
```

### 認證集成
- **JWT Token**: 前端存儲在 localStorage 或 httpOnly cookie
- **Token 刷新**: 自動刷新機制，無感知續期
- **權限控制**: 前端路由守衛 + 後端方法級權限
- **多租戶上下文**: Token 中包含租戶信息

### 錯誤處理統一
```typescript
// 前端統一錯誤處理
interface ErrorResponse {
  success: false;
  code: number;
  message: string;
  errors?: Array<{
    field: string;
    message: string;
  }>;
  timestamp: string;
}
```

## 前端開發規範

### 技術棧建議
- **框架**: React 18+ / Vue 3+ / Angular 15+
- **狀態管理**: Redux Toolkit / Zustand / Pinia
- **UI 組件庫**: Ant Design / Element Plus / Material-UI
- **HTTP 客戶端**: Axios / Fetch API
- **路由**: React Router / Vue Router / Angular Router
- **構建工具**: Vite / Webpack 5
- **類型檢查**: TypeScript 4.9+

### 項目結構
```
dp-web-fa/
├── src/
│   ├── components/          # 通用組件
│   ├── pages/              # 頁面組件
│   ├── services/           # API 服務層
│   ├── stores/             # 狀態管理
│   ├── utils/              # 工具函數
│   ├── hooks/              # 自定義 Hooks
│   ├── types/              # TypeScript 類型定義
│   ├── constants/          # 常量定義
│   └── assets/             # 靜態資源
├── public/                 # 公共資源
└── tests/                  # 測試文件
```

### 代碼規範
- **命名約定**: camelCase 變量，PascalCase 組件
- **文件命名**: kebab-case 文件名
- **組件設計**: 單一職責，可復用性
- **狀態管理**: 集中管理，避免 prop drilling
- **性能優化**: 懶加載，代碼分割，緩存策略

## 落地頁開發規範

### DP Landing Page 特點
- **靜態生成**: 使用 Next.js / Nuxt.js / Gatsby
- **SEO 優化**: 服務端渲染，meta 標籤優化
- **性能優先**: 圖片優化，CDN 加速
- **響應式設計**: 移動端優先
- **轉化追蹤**: 埋點統計，A/B 測試

### 內容管理
- **多語言支持**: i18n 國際化
- **內容更新**: 無需重新部署的內容管理
- **品牌一致性**: 與主應用保持視覺統一

## 部署和運維

### 容器化部署
```dockerfile
# DP Server Dockerfile
FROM openjdk:21-jdk-slim
COPY target/dp-server.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]

# DP Web FA Dockerfile  
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
```

### 環境配置
```yaml
# docker-compose.yml
version: '3.8'
services:
  dp-server:
    build: ./dp-server
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DATABASE_URL=postgresql://db:5432/dips_pro
    depends_on:
      - db
      - redis

  dp-web-fa:
    build: ./dp-web-fa
    ports:
      - "3000:80"
    environment:
      - REACT_APP_API_URL=http://dp-server:8080/api

  dp-landing:
    build: ./dp-landing-page
    ports:
      - "3001:80"

  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=dips_pro
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password

  redis:
    image: redis:6-alpine
```
###
 CI/CD 流水線
```yaml
# .github/workflows/deploy.yml
name: Deploy Full Stack
on:
  push:
    branches: [main]

jobs:
  test-backend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          java-version: '21'
      - name: Run Backend Tests
        run: |
          cd dp-server
          mvn test

  test-frontend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Run Frontend Tests
        run: |
          cd dp-web-fa
          npm ci
          npm run test

  deploy:
    needs: [test-backend, test-frontend]
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Production
        run: |
          docker-compose up -d
```

## 安全考慮

### 跨域配置
```java
// 後端 CORS 配置
@Configuration
public class CorsConfig {
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(Arrays.asList(
            "https://*.yourdomain.com",
            "http://localhost:3000"
        ));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/api/**", configuration);
        return source;
    }
}
```

### 前端安全
- **XSS 防護**: 輸入驗證，輸出編碼
- **CSRF 防護**: CSRF Token 驗證
- **內容安全策略**: CSP 頭部配置
- **敏感信息**: 不在前端存儲敏感數據

## 監控和日誌

### 全鏈路監控
- **後端監控**: Spring Boot Actuator + Micrometer
- **前端監控**: 錯誤追蹤，性能監控
- **業務監控**: 用戶行為分析，轉化漏斗

### 日誌聚合
```yaml
# 日誌配置
logging:
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
  level:
    com.dipspro: INFO
    org.springframework.security: DEBUG
```

## 開發工作流

### 分支策略
- **main**: 生產環境分支
- **develop**: 開發環境分支
- **feature/***: 功能開發分支
- **hotfix/***: 緊急修復分支

### 代碼審查
- **Pull Request**: 必須經過代碼審查
- **自動化檢查**: 代碼質量，安全掃描
- **測試覆蓋率**: 前後端都要求 80% 以上

### 版本發布
- **語義化版本**: major.minor.patch
- **發布說明**: 詳細的變更日誌
- **回滾計劃**: 快速回滾機制

## 數據流和狀態管理

### 前端狀態管理策略
```typescript
// 全局狀態結構
interface AppState {
  auth: {
    user: User | null;
    token: string | null;
    permissions: string[];
    currentTenant: Tenant | null;
  };
  ui: {
    loading: boolean;
    theme: 'light' | 'dark';
    language: string;
  };
  business: {
    // 業務相關狀態
  };
}
```

### 數據同步策略
- **實時同步**: WebSocket 連接用於實時數據
- **緩存策略**: 前端緩存 + 後端 Redis 緩存
- **離線支持**: Service Worker 實現離線功能
- **樂觀更新**: 前端先更新，後端確認

## 性能優化

### 前端性能
- **代碼分割**: 路由級別和組件級別分割
- **懶加載**: 圖片和組件懶加載
- **緩存策略**: HTTP 緩存 + 瀏覽器緩存
- **CDN 加速**: 靜態資源 CDN 分發

### 後端性能
- **數據庫優化**: 索引優化，查詢優化
- **緩存層**: Redis 多級緩存
- **連接池**: 數據庫連接池優化
- **異步處理**: 耗時操作異步化

## 國際化和本地化

### 多語言支持
```typescript
// 前端國際化配置
const i18nConfig = {
  locales: ['zh-CN', 'zh-TW', 'en-US'],
  defaultLocale: 'zh-CN',
  fallbackLocale: 'en-US'
};
```

### 後端國際化
```java
// Spring Boot 國際化配置
@Configuration
public class I18nConfig {
    @Bean
    public MessageSource messageSource() {
        ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
        messageSource.setBasenames("messages/messages");
        messageSource.setDefaultEncoding("UTF-8");
        return messageSource;
    }
}
```

## 測試策略

### 前端測試
- **單元測試**: Jest + Testing Library
- **組件測試**: Storybook 組件文檔
- **E2E 測試**: Cypress / Playwright
- **視覺回歸測試**: Percy / Chromatic

### 後端測試
- **單元測試**: JUnit 5 + Mockito
- **集成測試**: Spring Boot Test
- **API 測試**: RestAssured
- **性能測試**: JMeter / Gatling

### 測試環境
- **本地測試**: Docker Compose 測試環境
- **CI 測試**: GitHub Actions 自動化測試
- **預發布測試**: 生產環境鏡像測試