# Coding Standards & Best Practices

## Code Style Guidelines

### Java Conventions
- Use **camelCase** for variables and methods
- Use **PascalCase** for classes and interfaces
- Use **UPPER_SNAKE_CASE** for constants
- Maximum line length: 120 characters
- Use 4 spaces for indentation (no tabs)

### Lombok Usage
- Use `@Data` for simple DTOs
- Use `@Builder` for complex object construction
- Use `@Slf4j` for logging instead of manual logger creation
- Prefer `@RequiredArgsConstructor` over `@Autowired` field injection

### Exception Handling
- Create specific business exceptions extending `BusinessException`
- Use `@ControllerAdvice` for global exception handling
- Always log exceptions with appropriate level (ERROR for system issues, WARN for business logic)
- Return meaningful error messages to clients

## Code Organization

### Service Layer
- Keep services focused on single responsibility
- Use interfaces for service contracts
- Implement transaction boundaries at service level with `@Transactional`
- Validate input parameters at service entry points

### Repository Layer
- Extend `JpaRepository<Entity, ID>` for basic CRUD operations
- Use `@Query` annotations for complex queries
- Implement custom repository methods in separate interfaces
- Always include tenant filtering in multi-tenant queries

### Controller Layer
- Use `@RestController` for REST APIs
- Implement proper HTTP status codes
- Use `@Valid` for request validation
- Return consistent response format using `ApiResponse<T>`

## Security Practices

### Authentication & Authorization
- Always validate JWT tokens in protected endpoints
- Use `@PreAuthorize` for method-level security
- Include tenant context in security checks
- Log security-related events for audit

### Data Protection
- Never log sensitive data (passwords, tokens, personal info)
- Use `@JsonIgnore` for sensitive fields in DTOs
- Implement proper data masking in logs
- Validate all user inputs to prevent injection attacks

## Performance Guidelines

### Database Operations
- Use pagination for large result sets
- Implement proper indexing strategies
- Use batch operations for bulk data processing
- Monitor and optimize N+1 query problems

### Caching Strategy
- Cache frequently accessed reference data
- Use Redis for session and temporary data
- Implement cache invalidation strategies
- Monitor cache hit rates and performance

## Testing Standards

### Unit Testing
- Aim for 80%+ code coverage
- Use `@MockBean` for Spring context testing
- Test both positive and negative scenarios
- Mock external dependencies

### Integration Testing
- Use `@SpringBootTest` for full context testing
- Test database operations with `@DataJpaTest`
- Verify API contracts with `@WebMvcTest`
- Include tenant isolation testing

## Documentation Requirements

### Code Documentation
- Document all public APIs with JavaDoc
- Include parameter descriptions and return values
- Document business logic and complex algorithms
- Maintain README files for each module

### API Documentation
- Use OpenAPI/Swagger annotations
- Document all request/response models
- Include example requests and responses
- Maintain API versioning documentation