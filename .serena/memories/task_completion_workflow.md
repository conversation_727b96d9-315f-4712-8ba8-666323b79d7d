# DIPS Pro 任务完成工作流

## 代码开发完成后的标准流程

### 1. 代码质量检查
```bash
# 编译检查
mvn compile

# 运行单元测试
mvn test

# 打包验证
mvn package -DskipTests
```

### 2. 代码风格检查
- 确保Lombok注解正确使用
- 检查JavaDoc注释完整性
- 验证命名规范是否符合约定
- 确保数据库字段映射正确

### 3. 功能测试
```bash
# 启动应用(开发环境)
mvn spring-boot:run

# 或者运行JAR包
java -jar target/dp-server-0.0.1-SNAPSHOT.jar --spring.profiles.active=dev
```

### 4. API测试验证
- 使用Postman或curl测试API接口
- 验证统一响应格式ApiResponse
- 检查权限控制是否生效
- 测试多租户数据隔离

### 5. 数据库验证
```bash
# 连接数据库检查
psql -h *********** -p 5433 -U dips -d dips

# 验证数据表结构
\d+ sys_user
\d+ sys_tenant
```

### 6. 日志检查
- 检查application.log是否有ERROR级别日志
- 验证业务操作日志记录是否完整
- 确认敏感信息已正确脱敏

### 7. 代码提交
```bash
# 添加变更文件
git add .

# 提交代码(使用规范的commit message)
git commit -m "feat: 添加用户管理功能
- 实现用户CRUD操作
- 添加用户角色关联
- 完善用户权限验证"

# 推送到远程仓库
git push origin master
```

### 8. 文档更新
- 更新API文档(如有新接口)
- 更新数据库设计文档(如有表结构变更)
- 更新部署文档(如有配置变更)

### 9. 部署验证(如需要)
- 验证打包后的JAR文件完整
- 确认环境配置正确
- 测试生产环境兼容性

## 常见问题排查

### 编译错误
- 检查Lombok是否正确配置
- 确认MapStruct注解处理器路径
- 验证Java版本兼容性(需要Java 23)

### 运行时错误
- 检查数据库连接配置
- 验证Redis连接状态
- 确认JWT密钥配置正确

### 性能问题
- 检查数据库查询性能
- 监控JVM堆内存使用
- 分析慢查询日志

## 质量标准
- 单元测试覆盖率 > 80%
- 代码无编译警告
- 关键业务逻辑有完整注释
- API响应时间 < 500ms
- 数据库查询优化(避免N+1问题)
- 内存泄漏检查通过