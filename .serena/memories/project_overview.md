# DIPS Pro Web前端项目概述

这是一个基于 Vue 3 + TypeScript + Vite 构建的现代化前端应用程序。

## 技术栈
- **框架**: Vue 3.5.13
- **开发语言**: TypeScript
- **构建工具**: Vite 6.2.7
- **状态管理**: Pinia 3.0.2
- **路由**: Vue Router 4.5.1
- **UI组件库**: Element Plus 2.9.9 + Ant Design Vue 4.2.6
- **样式**: UnoCSS + SCSS
- **包管理器**: pnpm

## 项目结构
- `src/api/` - API接口定义
- `src/components/` - 通用组件
- `src/views/` - 页面视图
- `src/router/` - 路由配置
- `src/store/` - 状态管理
- `src/utils/` - 工具函数
- `src/types/` - 类型定义

## 核心功能
- 用户管理系统
- 租户管理系统
- 多租户权限控制
- 响应式布局设计