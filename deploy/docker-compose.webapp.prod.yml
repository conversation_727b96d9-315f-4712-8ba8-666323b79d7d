version: "3.8"

services:
  webapp:
    build:
      context: ../
      dockerfile: dp-web-fa/Dockerfile
    container_name: dp-web-fa
    # ports:
    # 通过反向代理访问，不需要直接端口映射
    # - "8080:8080"
    # - "8443:8443"
    volumes:
      # 挂载证书目录
      - /mnt/dp-web-fa/certs:/etc/nginx/certs:ro
      # 挂载 nginx 配置文件
      - /mnt/dp-web-fa/nginx.conf:/etc/nginx/conf.d/default.conf:ro
      # 挂载静态文件目录 - 支持直接文件替换
      - /mnt/dp-web-fa/html:/etc/nginx/html/app:rw
    networks:
      - dips-pro-network
    restart: unless-stopped # Restart policy for production

networks:
  dips-pro-network:
    external: true # 标记为外部
    # If deploying separately and the network is created elsewhere (e.g., by the backend compose or manually),
    # you might need to declare it as external:
    # name: dips-pro-network # Ensure the name matches
    # external: true
