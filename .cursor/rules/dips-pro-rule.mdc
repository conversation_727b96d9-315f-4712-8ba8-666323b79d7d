---
description: 
globs: 
alwaysApply: true
---

## java 后端编译问题

Java 后端始终使用 Lombok 来生成 getter/setter 方法、构造方法、输出日志
如果过程当中生成 Lombok 相关的编译错误可以忽略,不需要尝试修复。

## 过程记录

- 文档需求：
  - 在 /Users/<USER>/800_Code/BeyondDeep/dips_pro/docs 目录维护一个架构设计文件 Architecture.md，每次设计架构变更的修改，都需要在架构设计文件中同步变更
  - 在 /Users/<USER>/800_Code/BeyondDeep/dips_pro/docs 目录维护一个开发日志文件 dev_log.md，每次代码修改，都需要在日志文件中同步变更，使用 shell 工具 date "+%Y-%m-%d %H:%M:%S"获取当前时间 在每个日志行前面增加[yyyy-mm-dd HH:mm:ss]时间戳，最新的日志放在文档最前面，不要使用 echo 命令操作
- 始终使用中文书写文档
