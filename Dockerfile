# === Stage 1: Build the Main Web Application ===
FROM node:20-alpine as web-builder
WORKDIR /app-web

# 安装pnpm（缓存层）
RUN npm install -g pnpm

# 复制依赖文件（缓存层）
COPY dp-web-fa/package.json dp-web-fa/pnpm-lock.yaml ./

# 配置pnpm镜像源（不安装依赖，避免状态不一致）
RUN pnpm config set registry https://registry.npmmirror.com/ || \
    pnpm config set registry https://registry.npmjs.org/

# 复制源码并构建（变更频繁层）
COPY dp-web-fa/ ./

# 清理并安装依赖（确保状态一致）
RUN rm -rf node_modules package-lock.json yarn.lock && \
    echo "Starting dependency installation..." && \
    CI=true NODE_ENV=production pnpm install --frozen-lockfile --prefer-offline || \
    (echo "Frozen lockfile installation failed, falling back to regular install..." && \
     rm -f pnpm-lock.yaml && \
     CI=true NODE_ENV=production pnpm install --prefer-offline)

# 构建应用并验证输出
RUN echo "Starting application build..." && \
    CI=true NODE_ENV=production \
    VITE_APP_API_BASEURL=/server \
    VITE_OPEN_PROXY=false \
    pnpm run build && \
    echo "Build completed successfully!" && \
    ls -la dist/ && \
    echo "Dist directory contents:" && \
    find dist -type f | head -10 && \
    echo "Build verification completed."

# === Stage 2: Serve the web application with Nginx ===
FROM nginx:stable-alpine

# 复制自定义 Nginx 配置
COPY dp-web-fa/nginx.conf /etc/nginx/conf.d/default.conf

# 创建证书目录 (用于 SSL 证书挂载)
RUN mkdir -p /etc/nginx/certs

# 创建 app 目录并复制构建的 Web 应用静态文件
RUN mkdir -p /etc/nginx/html/app
COPY --from=web-builder /app-web/dist /etc/nginx/html/app/

# 暴露端口
EXPOSE 8080
EXPOSE 8443

# 启动 Nginx
CMD ["nginx", "-g", "daemon off;"]
