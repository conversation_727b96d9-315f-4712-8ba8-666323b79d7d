import { request } from '@/api'

// ============ 类型定义 ============

export interface SystemConfig {
  // 基础系统配置
  applicationName: string
  applicationVersion: string
  environment: 'development' | 'production' | 'test'
  debugMode: boolean

  // 服务器配置
  serverPort: number
  maxConnections: number
  requestTimeout: number

  // 数据库配置
  database: {
    poolSize: number
    connectionTimeout: number
    queryTimeout: number
    autoReconnect: boolean
  }

  // 时区和地区设置
  timezone: string
  locale: string
  dateFormat: string
  timeFormat: string
}

export interface AIConfig {
  // 默认 AI 提供商
  defaultProvider: 'openai' | 'deepseek' | 'azure' | 'anthropic'

  // OpenAI 配置
  openai: {
    apiKey: string
    baseUrl: string
    model: string
    temperature: number
    maxTokens: number
    timeout: number
  }

  // DeepSeek 配置
  deepseek: {
    apiKey: string
    baseUrl: string
    model: string
    temperature: number
    maxTokens: number
    timeout: number
  }

  // Azure OpenAI 配置
  azure: {
    apiKey: string
    endpoint: string
    deploymentId: string
    apiVersion: string
    timeout: number
  }

  // Anthropic 配置
  anthropic: {
    apiKey: string
    model: string
    maxTokens: number
    timeout: number
  }

  // 通用 AI 配置
  fallbackProvider?: string
  retryAttempts: number
  rateLimitPerMinute: number
  enableLogging: boolean
}

export interface PerformanceConfig {
  // Redis 缓存配置
  redis: {
    host: string
    port: number
    password: string
    database: number
    maxConnections: number
    connectionTimeout: number
    commandTimeout: number
    keyPrefix: string
  }

  // 缓存策略
  cache: {
    defaultTTL: number
    maxSize: number
    enableCompression: boolean
    enableMetrics: boolean
  }

  // 线程池配置
  threading: {
    corePoolSize: number
    maxPoolSize: number
    queueCapacity: number
    keepAliveTime: number
  }

  // 异步处理配置
  async: {
    enableAsync: boolean
    taskTimeout: number
    maxConcurrentTasks: number
    retryAttempts: number
  }
}

export interface MonitoringConfig {
  // 日志配置
  logging: {
    level: 'ERROR' | 'WARN' | 'INFO' | 'DEBUG' | 'TRACE'
    enableConsoleOutput: boolean
    enableFileOutput: boolean
    maxFileSize: number
    maxFiles: number
    logFormat: string
    logPath: string
  }

  // 监控配置
  monitoring: {
    enableMetrics: boolean
    metricsPort: number
    enableHealthCheck: boolean
    healthCheckInterval: number
    enableAlerts: boolean
    alertEmail: string
  }

  // 性能监控
  performance: {
    enableProfiling: boolean
    sampleRate: number
    enableMemoryMonitoring: boolean
    memoryThreshold: number
    enableCpuMonitoring: boolean
    cpuThreshold: number
  }

  // 审计配置
  audit: {
    enableAudit: boolean
    auditLevel: 'ALL' | 'ADMIN' | 'SENSITIVE'
    retentionDays: number
    enableEncryption: boolean
  }
}

export interface SecurityConfig {
  // JWT 配置
  jwt: {
    secret: string
    expirationTime: number
    refreshTokenExpiration: number
    issuer: string
    enableRefreshToken: boolean
  }

  // 密码策略
  password: {
    minLength: number
    requireUppercase: boolean
    requireLowercase: boolean
    requireNumbers: boolean
    requireSpecialChars: boolean
    maxAge: number
    preventReuse: number
  }

  // API 安全
  api: {
    enableRateLimit: boolean
    rateLimitPerMinute: number
    enableCors: boolean
    allowedOrigins: string[]
    enableApiKey: boolean
    apiKeyExpiration: number
  }

  // 加密配置
  encryption: {
    algorithm: string
    keySize: number
    enableAtRest: boolean
    enableInTransit: boolean
  }

  // 会话配置
  session: {
    timeout: number
    maxSessions: number
    enableSessionTracking: boolean
    enableConcurrentLogin: boolean
  }
}

export interface BackupConfig {
  id: string
  name: string
  description?: string
  createdAt: string
  createdBy: string
  size: number
  checksum: string
  status: 'COMPLETED' | 'IN_PROGRESS' | 'FAILED'
  errorMessage?: string
}

export interface BackupSchedule {
  enabled: boolean
  frequency: 'daily' | 'weekly' | 'monthly'
  time: string // HH:mm format
  dayOfWeek?: number // 0-6, only for weekly
  dayOfMonth?: number // 1-31, only for monthly
  retentionDays: number
  enableCompression: boolean
  enableEncryption: boolean
}

export interface TestResult {
  service: string
  status: 'SUCCESS' | 'FAILURE' | 'WARNING'
  message: string
  details?: Record<string, any>
  responseTime?: number
  timestamp: string
}

export interface ConfigValidationResult {
  valid: boolean
  errors: string[]
  warnings: string[]
}

// ============ API 服务类 ============

export class ConfigManagementService {
  // ============ 系统配置 ============

  static async getSystemConfig(): Promise<SystemConfig> {
    const response = await request.get('/api/admin/v1/config/system')
    return response.data
  }

  static async updateSystemConfig(config: SystemConfig): Promise<SystemConfig> {
    const response = await request.put('/api/admin/v1/config/system', config)
    return response.data
  }

  static async resetSystemConfig(): Promise<SystemConfig> {
    const response = await request.post('/api/admin/v1/config/system/reset')
    return response.data
  }

  static async validateSystemConfig(config: SystemConfig): Promise<ConfigValidationResult> {
    const response = await request.post('/api/admin/v1/config/system/validate', config)
    return response.data
  }

  // ============ AI 配置 ============

  static async getAIConfig(): Promise<AIConfig> {
    const response = await request.get('/api/admin/v1/config/ai')
    return response.data
  }

  static async updateAIConfig(config: AIConfig): Promise<AIConfig> {
    const response = await request.put('/api/admin/v1/config/ai', config)
    return response.data
  }

  static async testAIConnection(provider: string): Promise<TestResult[]> {
    const response = await request.post(`/api/admin/v1/config/ai/test/${provider}`)
    return response.data
  }

  static async resetAIConfig(provider?: string): Promise<AIConfig> {
    const url = provider
      ? `/api/admin/v1/config/ai/reset/${provider}`
      : '/api/admin/v1/config/ai/reset'
    const response = await request.post(url)
    return response.data
  }

  // ============ 性能配置 ============

  static async getPerformanceConfig(): Promise<PerformanceConfig> {
    const response = await request.get('/api/admin/v1/config/performance')
    return response.data
  }

  static async updatePerformanceConfig(config: PerformanceConfig): Promise<PerformanceConfig> {
    const response = await request.put('/api/admin/v1/config/performance', config)
    return response.data
  }

  static async clearCache(type?: string): Promise<void> {
    const url = type
      ? `/api/admin/v1/config/performance/cache/clear/${type}`
      : '/api/admin/v1/config/performance/cache/clear'
    await request.post(url)
  }

  static async getCacheStats(): Promise<Record<string, any>> {
    const response = await request.get('/api/admin/v1/config/performance/cache/stats')
    return response.data
  }

  static async testRedisConnection(): Promise<TestResult> {
    const response = await request.post('/api/admin/v1/config/performance/redis/test')
    return response.data
  }

  // ============ 监控配置 ============

  static async getMonitoringConfig(): Promise<MonitoringConfig> {
    const response = await request.get('/api/admin/v1/config/monitoring')
    return response.data
  }

  static async updateMonitoringConfig(config: MonitoringConfig): Promise<MonitoringConfig> {
    const response = await request.put('/api/admin/v1/config/monitoring', config)
    return response.data
  }

  static async testLogging(): Promise<TestResult[]> {
    const response = await request.post('/api/admin/v1/config/monitoring/test-logging')
    return response.data
  }

  static async getLogFiles(): Promise<string[]> {
    const response = await request.get('/api/admin/v1/config/monitoring/log-files')
    return response.data
  }

  static async downloadLogFile(filename: string): Promise<Blob> {
    const response = await request.get(`/api/admin/v1/config/monitoring/log-files/${filename}`, {
      responseType: 'blob'
    })
    return response.data
  }

  // ============ 安全配置 ============

  static async getSecurityConfig(): Promise<SecurityConfig> {
    const response = await request.get('/api/admin/v1/config/security')
    return response.data
  }

  static async updateSecurityConfig(config: SecurityConfig): Promise<SecurityConfig> {
    const response = await request.put('/api/admin/v1/config/security', config)
    return response.data
  }

  static async regenerateJWTSecret(): Promise<{ secret: string }> {
    const response = await request.post('/api/admin/v1/config/security/jwt/regenerate')
    return response.data
  }

  static async regenerateAPIKeys(): Promise<Record<string, string>> {
    const response = await request.post('/api/admin/v1/config/security/api-keys/regenerate')
    return response.data
  }

  static async testSecurity(): Promise<TestResult[]> {
    const response = await request.post('/api/admin/v1/config/security/test')
    return response.data
  }

  // ============ 备份管理 ============

  static async getBackupList(): Promise<BackupConfig[]> {
    const response = await request.get('/api/admin/v1/config/backup')
    return response.data
  }

  static async createBackup(name: string, description?: string): Promise<BackupConfig> {
    const response = await request.post('/api/admin/v1/config/backup', {
      name,
      description
    })
    return response.data
  }

  static async restoreBackup(backupId: string): Promise<void> {
    await request.post(`/api/admin/v1/config/backup/${backupId}/restore`)
  }

  static async deleteBackup(backupId: string): Promise<void> {
    await request.delete(`/api/admin/v1/config/backup/${backupId}`)
  }

  static async downloadBackup(backupId: string): Promise<Blob> {
    const response = await request.get(`/api/admin/v1/config/backup/${backupId}/download`, {
      responseType: 'blob'
    })
    return response.data
  }

  static async getBackupSchedule(): Promise<BackupSchedule> {
    const response = await request.get('/api/admin/v1/config/backup/schedule')
    return response.data
  }

  static async updateBackupSchedule(schedule: BackupSchedule): Promise<BackupSchedule> {
    const response = await request.put('/api/admin/v1/config/backup/schedule', schedule)
    return response.data
  }

  // ============ 配置导入导出 ============

  static async exportConfiguration(): Promise<Blob> {
    const response = await request.get('/api/admin/v1/config/export', {
      responseType: 'blob'
    })
    return response.data
  }

  static async importConfiguration(configFile: File): Promise<void> {
    const formData = new FormData()
    formData.append('file', configFile)

    await request.post('/api/admin/v1/config/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  static async validateConfiguration(configData: any): Promise<ConfigValidationResult> {
    const response = await request.post('/api/admin/v1/config/validate', configData)
    return response.data
  }

  // ============ 批量操作 ============

  static async saveAllConfigs(configs: {
    system?: SystemConfig
    ai?: AIConfig
    performance?: PerformanceConfig
    monitoring?: MonitoringConfig
    security?: SecurityConfig
  }): Promise<void> {
    await request.post('/api/admin/v1/config/save-all', configs)
  }

  static async resetAllConfigs(): Promise<void> {
    await request.post('/api/admin/v1/config/reset-all')
  }

  static async getConfigHistory(type: string, limit = 10): Promise<any[]> {
    const response = await request.get(`/api/admin/v1/config/history/${type}`, {
      params: { limit }
    })
    return response.data
  }

  // ============ 系统信息 ============

  static async getSystemInfo(): Promise<Record<string, any>> {
    const response = await request.get('/api/admin/v1/config/system-info')
    return response.data
  }

  static async getConfigStatus(): Promise<Record<string, any>> {
    const response = await request.get('/api/admin/v1/config/status')
    return response.data
  }
}
