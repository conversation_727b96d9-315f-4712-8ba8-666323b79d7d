import type {
  ApiResponse,
  AppealRecord,
  AppealRecordQueryParams,
  AppealRequest,
  BalanceCheckResponse,
  BalanceWarningConfig,
  BillingAccount,
  BillingConfig,
  BillingStatistics,
  PagedResponse,
  PaymentMethod,
  PaymentStatusResponse,
  RechargePresetDto,
  RechargeRecord,
  RechargeRecordQueryParams,
  RechargeRequest,
  TokenCalculationRequest,
  TokenCalculationResponse,
  UsageRecord,
  UsageRecordQueryParams,
  WechatPaymentResponse,
  InvoiceRequest,
  InvoiceResponse,
  InvoiceQueryParams,
  InvoiceStatistics,
  IndustryTenantPricing,
} from '@/types/billing'
import { request } from '@/api'

// 导入套餐API
export { PackageAPI } from './packages'

/**
 * 计费 API 类 - 与后端 BillingController 接口路径对齐
 */
export class BillingAPI {
  /**
   * 获取用户计费账户信息
   */
  static async getAccount(): Promise<ApiResponse<BillingAccount>> {
    return request.get('/api/billing/account')
  }

  /**
   * 获取计费配置信息 - 对应后端 /api/billing/configuration
   */
  static async getConfiguration(): Promise<ApiResponse<BillingConfig>> {
    return request.get('/api/billing/configuration')
  }

  /**
   * 计算Token费用 - 对应后端 /api/billing/calculate-cost
   * @param data Token计算请求数据
   */
  static async calculateTokenCost(data: TokenCalculationRequest): Promise<ApiResponse<TokenCalculationResponse>> {
    return request.post('/api/billing/calculate-cost', data)
  }

  /**
   * 获取使用记录列表 - 对应后端 /api/billing/usage
   * @param params 查询参数
   */
  static async getUsageRecords(params?: UsageRecordQueryParams): Promise<ApiResponse<PagedResponse<UsageRecord>>> {
    return request.get('/api/billing/usage', { params })
  }

  /**
   * 获取指定使用记录详情 - 对应后端 /api/billing/usage/{id}
   * @param id 使用记录ID
   */
  static async getUsageRecord(id: string): Promise<ApiResponse<UsageRecord>> {
    return request.get(`/api/billing/usage/${id}`)
  }

  /**
   * 获取使用统计数据 - 对应后端 /api/billing/usage/statistics
   * @param period 统计周期
   */
  static async getUsageStatistics(period = 'month'): Promise<ApiResponse<BillingStatistics>> {
    return request.get('/api/billing/usage/statistics', { params: { period } })
  }

  /**
   * 预检查计费 - 对应后端 /api/billing/precheck
   * @param inputTokens 输入Token数量
   * @param outputTokens 输出Token数量
   */
  static async precheckBilling(inputTokens: number, outputTokens: number): Promise<ApiResponse<boolean>> {
    return request.get('/api/billing/precheck', { params: { inputTokens, outputTokens } })
  }

  /**
   * 获取充值记录列表
   * @param params 查询参数
   */
  static async getRechargeRecords(params?: RechargeRecordQueryParams): Promise<ApiResponse<PagedResponse<RechargeRecord>>> {
    return request.get('/api/billing/recharge-records', { params })
  }

  /**
   * 获取指定充值记录详情
   * @param id 充值记录ID
   */
  static async getRechargeRecord(id: string): Promise<ApiResponse<RechargeRecord>> {
    return request.get(`/api/billing/recharge-records/${id}`)
  }

  /**
   * 提交充值申请
   * @param data 充值请求数据
   */
  static async submitRecharge(data: RechargeRequest): Promise<ApiResponse<RechargeRecord>> {
    return request.post('/api/billing/recharge', data)
  }

  /**
   * 获取申诉记录列表
   * @param params 查询参数
   */
  static async getAppealRecords(params?: AppealRecordQueryParams): Promise<ApiResponse<PagedResponse<AppealRecord>>> {
    return request.get('/api/billing/appeals', { params })
  }

  /**
   * 获取指定申诉记录详情
   * @param id 申诉记录ID
   */
  static async getAppealRecord(id: string): Promise<ApiResponse<AppealRecord>> {
    return request.get(`/api/billing/appeals/${id}`)
  }

  /**
   * 提交申诉申请
   * @param data 申诉请求数据
   */
  static async submitAppeal(data: AppealRequest): Promise<ApiResponse<AppealRecord>> {
    return request.post('/api/billing/appeals', data)
  }

  /**
   * 更新申诉记录
   * @param id 申诉记录ID
   * @param data 更新数据
   */
  static async updateAppeal(id: string, data: Partial<AppealRequest>): Promise<ApiResponse<AppealRecord>> {
    return request.put(`/api/billing/appeals/${id}`, data)
  }

  /**
   * 获取余额警告配置
   */
  static async getBalanceWarningConfig(): Promise<ApiResponse<BalanceWarningConfig>> {
    return request.get('/api/billing/balance-warning-config')
  }

  /**
   * 更新余额警告配置
   * @param config 警告配置
   */
  static async updateBalanceWarningConfig(config: BalanceWarningConfig): Promise<ApiResponse<BalanceWarningConfig>> {
    return request.put('/api/billing/balance-warning-config', config)
  }

  /**
   * 刷新账户余额
   */
  static async refreshBalance(): Promise<ApiResponse<BillingAccount>> {
    return request.post('/api/billing/account/refresh')
  }

  /**
   * 导出使用记录
   * @param params 查询参数
   * @param format 导出格式 (excel, csv)
   */
  static async exportUsageRecords(params?: UsageRecordQueryParams, format: 'excel' | 'csv' = 'excel'): Promise<Blob> {
    const response = await request.get('/api/billing/usage/export', {
      params: { ...params, format },
      responseType: 'blob',
    })
    return response.data
  }

  /**
   * 导出充值记录
   * @param params 查询参数
   * @param format 导出格式 (excel, csv)
   */
  static async exportRechargeRecords(params?: RechargeRecordQueryParams, format: 'excel' | 'csv' = 'excel'): Promise<Blob> {
    const response = await request.get('/api/billing/recharge-records/export', {
      params: { ...params, format },
      responseType: 'blob',
    })
    return response.data
  }

  /**
   * 获取支付方式列表
   */
  static async getPaymentMethods(): Promise<ApiResponse<PaymentMethod[]>> {
    return request.get('/api/billing/payment-methods')
  }

  /**
   * 验证支付状态
   * @param rechargeId 充值记录ID
   */
  static async verifyPaymentStatus(rechargeId: string): Promise<ApiResponse<RechargeRecord>> {
    return request.post(`/api/billing/recharge/${rechargeId}/verify`)
  }

  /**
   * 取消充值申请
   * @param rechargeId 充值记录ID
   */
  static async cancelRecharge(rechargeId: string): Promise<ApiResponse<RechargeRecord>> {
    return request.post(`/api/billing/recharge/${rechargeId}/cancel`)
  }

  /**
   * 检查余额是否充足
   * @param amount 需要检查的金额
   */
  static async checkBalance(amount: number): Promise<ApiResponse<BalanceCheckResponse>> {
    return request.get('/api/billing/check-balance', { params: { amount } })
  }

  /**
   * 获取预设充值金额选项
   */
  static async getRechargePresets(): Promise<ApiResponse<RechargePresetDto[]>> {
    return request.get('/api/billing/recharge-presets')
  }

  /**
   * 取消申诉
   * @param appealId 申诉记录ID
   */
  static async cancelAppeal(appealId: string): Promise<ApiResponse<AppealRecord>> {
    return request.post(`/api/billing/appeals/${appealId}/cancel`)
  }

  /**
   * 下载附件
   * @param attachmentId 附件ID
   */
  static async downloadAttachment(attachmentId: string): Promise<Blob> {
    const response = await request.get(`/api/billing/attachments/${attachmentId}`, {
      responseType: 'blob',
    })
    return response.data
  }

  /**
   * 下载收据
   * @param recordId 记录ID
   */
  static async downloadReceipt(recordId: string): Promise<Blob> {
    const response = await request.get(`/api/billing/receipts/${recordId}`, {
      responseType: 'blob',
    })
    return response.data
  }

  /**
   * 获取微信支付二维码
   * @param data 充值请求数据
   */
  static async getWechatPaymentQrcode(data: RechargeRequest): Promise<ApiResponse<WechatPaymentResponse>> {
    return request.post('/api/billing/recharge', data)
  }

  /**
   * 查询支付状态
   * @param outTradeNo 商户订单号
   */
  static async getPaymentStatus(outTradeNo: string): Promise<ApiResponse<any>> {
    return request.get('/api/pay/wechat/query', { params: { outTradeNo } })
  }

  /**
   * 获取当前用户对应的套餐定价列表
   */
  static async getCurrentUserPricing(): Promise<ApiResponse<IndustryTenantPricing[]>> {
    return request.get('/api/billing/pricing/current')
  }

  /**
   * 根据行业类型和租户类型获取套餐定价列表
   * @param industryType 行业类型
   * @param tenantType 租户类型
   */
  static async getPricingByType(industryType: string, tenantType: string): Promise<ApiResponse<IndustryTenantPricing[]>> {
    return request.get('/api/billing/pricing/by-type', {
      params: { industryType, tenantType }
    })
  }

  /**
   * 获取所有可用的套餐定价列表
   */
  static async getAllAvailablePricing(): Promise<ApiResponse<IndustryTenantPricing[]>> {
    return request.get('/api/billing/pricing/available')
  }

  /**
   * 获取所有套餐定价列表（包括不可用的）
   */
  static async getAllPricing(): Promise<ApiResponse<IndustryTenantPricing[]>> {
    return request.get('/api/billing/pricing/all')
  }

  /**
   * 根据ID获取套餐定价详情
   * @param id 定价ID
   */
  static async getPricingById(id: number): Promise<ApiResponse<IndustryTenantPricing>> {
    return request.get(`/api/billing/pricing/${id}`)
  }

  /**
   * 创建套餐定价
   * @param data 定价数据
   */
  static async createPricing(data: Partial<IndustryTenantPricing>): Promise<ApiResponse<IndustryTenantPricing>> {
    return request.post('/api/billing/pricing', data)
  }

  /**
   * 更新套餐定价
   * @param id 定价ID
   * @param data 定价数据
   */
  static async updatePricing(id: number, data: Partial<IndustryTenantPricing>): Promise<ApiResponse<IndustryTenantPricing>> {
    return request.put(`/api/billing/pricing/${id}`, data)
  }

  /**
   * 删除套餐定价
   * @param id 定价ID
   */
  static async deletePricing(id: number): Promise<ApiResponse<void>> {
    return request.delete(`/api/billing/pricing/${id}`)
  }
}

/**
 * 发票 API 类
 */
export class InvoiceAPI {
  /**
   * 申请电子发票
   * @param data 发票申请数据
   */
  static async applyInvoice(data: InvoiceRequest): Promise<ApiResponse<InvoiceResponse>> {
    return request.post('/api/pay/wechat/invoice/apply', data)
  }

  /**
   * 查询发票详情
   * @param invoiceNo 发票申请单号
   */
  static async getInvoiceDetail(invoiceNo: string): Promise<ApiResponse<InvoiceResponse>> {
    return request.get(`/api/pay/wechat/invoice/${invoiceNo}`)
  }

  /**
   * 查询用户发票列表
   * @param params 查询参数
   */
  static async getUserInvoices(params?: InvoiceQueryParams): Promise<ApiResponse<PagedResponse<InvoiceResponse>>> {
    return request.get('/api/pay/wechat/invoice/list', { params })
  }

  /**
   * 重新申请发票
   * @param invoiceNo 发票申请单号
   * @param data 新的发票申请信息
   */
  static async reapplyInvoice(invoiceNo: string, data: InvoiceRequest): Promise<ApiResponse<InvoiceResponse>> {
    return request.put(`/api/pay/wechat/invoice/${invoiceNo}/reapply`, data)
  }

  /**
   * 取消发票申请
   * @param invoiceNo 发票申请单号
   */
  static async cancelInvoice(invoiceNo: string): Promise<ApiResponse<string>> {
    return request.put(`/api/pay/wechat/invoice/${invoiceNo}/cancel`)
  }

  /**
   * 下载发票文件
   * @param invoiceNo 发票申请单号
   */
  static async downloadInvoice(invoiceNo: string): Promise<ApiResponse<string>> {
    return request.get(`/api/pay/wechat/invoice/${invoiceNo}/download`)
  }

  /**
   * 获取发票抬头填写链接
   * @param outTradeNo 商户订单号
   */
  static async getInvoiceTitleUrl(outTradeNo: string): Promise<ApiResponse<string>> {
    return request.get('/api/pay/wechat/invoice/title-url', { params: { outTradeNo } })
  }

  /**
   * 查询用户发票统计信息
   */
  static async getUserInvoiceStatistics(): Promise<ApiResponse<InvoiceStatistics>> {
    return request.get('/api/pay/wechat/invoice/statistics')
  }
}

