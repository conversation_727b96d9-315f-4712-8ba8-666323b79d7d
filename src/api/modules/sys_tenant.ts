import api from '../index'

const API_BASE_URL = '/api'

// 租户相关类型定义
export interface TenantCreateDto {
  tenantCode: string
  tenantName: string
  domain?: string
  status: number

  expireTime?: string
  contactName?: string
  contactPhone?: string
  contactEmail?: string
  logoUrl?: string
  description?: string
  industryType?: string // 行业类型：MEDICAL_BEAUTY-医美，REAL_ESTATE-地产
  tenantType?: string // 租户类型：ORGANIZATION-机构，ORGANIZATION_GROUP-机构集团，MANUFACTURER-厂家，CONSUMER-消费者
}

export interface TenantUpdateDto {
  tenantCode?: string
  tenantName?: string
  domain?: string
  status?: number
  packageType?: string
  expireTime?: string
  contactName?: string
  contactPhone?: string
  contactEmail?: string
  logoUrl?: string
  description?: string
  industryType?: string // 行业类型：MEDICAL_BEAUTY-医美，REAL_ESTATE-地产
  tenantType?: string // 租户类型：ORGANIZATION-机构，ORGANIZATION_GROUP-机构集团，MANUFACTURER-厂家，CONSUMER-消费者
}

export interface TenantQueryDto {
  page?: number
  size?: number
  tenantCode?: string
  tenantName?: string
  status?: number
  packageType?: string
  contactName?: string
  contactPhone?: string
  contactEmail?: string
  industryType?: string // 行业类型筛选
  tenantType?: string // 租户类型筛选
  sortField?: string
  sortOrder?: string
}

export interface TenantResponseDto {
  id: number
  tenantCode: string
  tenantName: string
  domain?: string
  status: number
  statusText: string


  expireTime?: string
  contactName?: string
  contactPhone?: string
  contactEmail?: string
  logoUrl?: string
  description?: string
  industryType?: string // 行业类型
  industryTypeText?: string // 行业类型描述
  tenantType?: string // 租户类型
  tenantTypeText?: string // 租户类型描述
  createdTime: string
  updatedTime: string
  createdBy?: number
  updatedBy?: number
  expired: boolean
  remainingDays?: number
}

// 租户海报数据类型定义
export interface TenantPosterDto {
  name: string
  qiniuTemplateUrl: string // 模板图片完整URL（带token）
  qiniuUrl: string // 组合二维码后的图片完整URL（带token）
  x: number
  y: number
  width: number
  height: number
  description?: string
}

// 租户管理API
export default {
  // 分页查询租户列表
  getTenantList: (data: TenantQueryDto) => api.get(`${API_BASE_URL}/sys/tenant/list`, {
    params: data,
  }),

  // 获取租户详情
  getTenantDetail: (id: number | string) => api.get(`${API_BASE_URL}/sys/tenant/detail`, {
    params: { id },
  }),

  // 创建租户
  createTenant: (data: TenantCreateDto) => api.post(`${API_BASE_URL}/sys/tenant/create`, data),

  // 更新租户
  updateTenant: (data: TenantUpdateDto & { id: number | string }) => api.post(`${API_BASE_URL}/sys/tenant/edit`, data, {
    params: { id: data.id },
  }),

  // 删除租户
  deleteTenant: (id: number | string) => api.post(`${API_BASE_URL}/sys/tenant/delete`, null, {
    params: { id },
  }),

  // 批量删除租户
  deleteTenants: (ids: (number | string)[]) => api.post(`${API_BASE_URL}/sys/tenant/batch-delete`, ids),

  // 更新租户状态
  updateTenantStatus: (id: number | string, status: number) => api.post(`${API_BASE_URL}/sys/tenant/update-status`, null, {
    params: { id, status },
  }),

  // 检查租户编码是否存在
  checkTenantCode: (tenantCode: string, excludeId?: number | string) => {
    const params: any = { tenantCode }
    if (excludeId) {
      params.excludeId = excludeId
    }
    return api.get(`${API_BASE_URL}/sys/tenant/check-tenant-code`, { params })
  },

  // 检查租户名称是否存在
  checkTenantName: (tenantName: string, excludeId?: number | string) => {
    const params: any = { tenantName }
    if (excludeId) {
      params.excludeId = excludeId
    }
    return api.get(`${API_BASE_URL}/sys/tenant/check-tenant-name`, { params })
  },

  // 检查域名是否存在
  checkDomain: (domain: string, excludeId?: number | string) => {
    const params: any = { domain }
    if (excludeId) {
      params.excludeId = excludeId
    }
    return api.get(`${API_BASE_URL}/sys/tenant/check-domain`, { params })
  },

  // 获取所有启用的租户
  getActiveTenants: () => api.get(`${API_BASE_URL}/sys/tenant/active`),

  // 根据套餐类型获取租户列表
  // 根据套餐类型获取租户列表
  getTenantsByPackageType: (packageType: string) => api.get(`${API_BASE_URL}/sys/tenant/by-package-type`, {
    params: { packageType },
  }),

  // 刷新租户缓存
  refreshTenantCache: () => api.post(`${API_BASE_URL}/sys/tenant/refresh-cache`),

  // 获取租户缓存状态
  getTenantCacheStatus: () => api.get(`${API_BASE_URL}/sys/tenant/cache-status`),

  // 获取租户二维码访问链接
  getTenantQrCodeUrl: (tenantId: number | string) => api.get(`${API_BASE_URL}/sys/tenant/qrcode-url`, {
    params: { tenantId },
  }),

  // 生成租户二维码
  generateTenantQrCode: (tenantId: number | string) => api.post(`${API_BASE_URL}/sys/tenant/generate-qrcode`, null, {
    params: { tenantId },
  }),

  // 获取租户海报列表
  getTenantPosters: (tenantId: number | string) => api.get(`${API_BASE_URL}/sys/tenant/posters`, {
    params: { tenantId },
  }),
}
