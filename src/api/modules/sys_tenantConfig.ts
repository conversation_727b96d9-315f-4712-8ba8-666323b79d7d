import api from '../index'

const API_BASE_URL = '/api'

// 租户配置类型定义
export interface TenantConfigItem {
  id: number
  tenantId: number
  configKey: string
  configValue: string
  configType: string
  description?: string
  createdTime: string
  updatedTime: string
}

export default {
  // 获取租户配置列表
  getTenantConfigs: (tenantId: number | string) => api.get(`${API_BASE_URL}/sys/tenant/configs`, {
    params: { tenantId },
  }),

  list: (data: {
    title?: string
    from: number
    limit: number
  }) => api.get('sys/tenant_config/list', {
    params: data,
    baseURL: '/mock/',
  }),

  detail: (id: number | string) => api.get('sys/tenant_config/detail', {
    params: {
      id,
    },
    baseURL: '/mock/',
  }),

  create: (data: any) => api.post('sys/tenant_config/create', data, {
    baseURL: '/mock/',
  }),

  edit: (data: any) => api.post('sys/tenant_config/edit', data, {
    baseURL: '/mock/',
  }),

  delete: (id: number | string) => api.post('sys/tenant_config/delete', {
    id,
  }, {
    baseURL: '/mock/',
  }),
}
