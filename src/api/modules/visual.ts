import {request} from "@/api";

export async function getRhVisual(
  mapId: string,
  shotId: string,
  viewPointId: string,
  nodeId: string,
  visualId: string
) {
  try {
    let url = `/api/route/itgvk/rh/visualJson?mapId=${mapId}&shotId=${shotId}&viewPointId=${viewPointId}&nodeId=${nodeId}&visualId=${visualId}`;
    let response = await request({
      url: url,
      method: 'get'
    });
    return response.data
  } catch (error: any) {
    console.error(`getRhVisual error: `, error)
  }
}

export async function getGptVisual(
  stencilId: string,
  shotId: string,
  vertexId: string,
  indexId: string,
  visualStencilId: string,
  dataPeriod: string
) {
  try {
    let url = `/api/route/sp/gpt/findVisualJson?stencilId=${stencilId}&shotId=${shotId}&vertexId=${vertexId}&indexId=${indexId}&visualStencilId=${visualStencilId}&dataPeriod=${dataPeriod}`;
    let response = await request({
      url: url,
      method: 'get'
    });
    return response.data
  } catch (error: any) {
    console.error(`getGptVisual error: `, error)
  }
}

export async function searchDpCustomerReport(
  orgCategoryTierId: string,
  departmentId: string,
  roleId: string
) {
  try {
    let url = `/api/route/sp/gptCustomerReport/searchDpCustomerReport?orgCategoryTierId=${orgCategoryTierId}&departmentId=${departmentId}&roleId=${roleId}`;
    let response = await request({
      url: url,
      method: 'get'
    });
    return response.data._embedded
  } catch (error: any) {
    console.error(`searchDpCustomerReport error: `, error)
  }
}

export async function searchDpCustomerReportShot(
  customerReportId: string
) {
  try {
    let url = `/api/route/sp/gptCustomerReportShots/search/findOnlineByCustomerReportId?customerReportId=${customerReportId}`;
    let response = await request({
      url: url,
      method: 'get'
    });
    return response.data._embedded
  } catch (error: any) {
    console.error(`searchDpCustomerReportShot error: `, error)
  }
}

export async function findReportVisualsBy3Id(
  stencilId: string,
  shotId: string,
  vertexId: string
) {
  try {
    let url = `/api/route/sp/gpt/findReportVisualsBy3Id?stencilId=${stencilId}&shotId=${shotId}&vertexId=${vertexId}`;
    let response = await request({
      url: url,
      method: 'get'
    });
    return response.data
  } catch (error: any) {
    console.error(`findReportVisualsBy3Id error: `, error)
  }
}

export async function findGptReport(
  stencilId: string,
  shotId: string,
  vertexId: string
) {
  try {
    let url = `/api/route/sp/gptReports/search/findByStencilIdAndShotIdAndVertexId?stencilId=${stencilId}&shotId=${shotId}&vertexId=${vertexId}`;
    let response = await request({
      url: url,
      method: 'get'
    });
    return response.data._embedded
  } catch (error: any) {
    console.error(`findGptReport error: `, error)
  }
}

export async function downloadDpPPT(
  pptPath: string
) {
  try {
    let url = `/api/route/sp/gpt/downloadDpPPT?pptPath=${pptPath}`;
    let response = await request({
      url: url,
      method: 'get'
    });
    return response.data
  } catch (error: any) {
    console.error(`downloadDpPPT error: `, error)
  }
}
