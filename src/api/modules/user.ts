import api from '../index'

export default {
  // 登录
  login: (data: {
    account: string
    password: string
  }) => api.post('user/login', data, {
    baseURL: '/mock/',
  }),

  // 获取权限
  permission: () => api.get('user/permission', {
    baseURL: '/mock/',
  }),

  // 修改密码
  passwordEdit: (data: {
    password: string
    newPassword: string
    checkPassword: string
  }) => api.post('/api/sys/user/password/edit', data),

  // 获取偏好设置
  preferences: () => api.get('user/preferences', {
    baseURL: '/mock/',
  }),

  // 修改偏好设置
  preferencesEdit: (preferences: string) => api.post('user/preferences/edit', {
    preferences,
  }, {
    baseURL: '/mock/',
  }),

  // 获取标签栏固定标签页数据
  tabbar: () => api.get('user/tabbar', {
    baseURL: '/mock/',
  }),

  // 修改标签栏固定标签页数据
  tabbarEdit: (tabbar: string) => api.post('user/tabbar/edit', {
    tabbar,
  }, {
    baseURL: '/mock/',
  }),

  // 获取收藏夹
  favorites: () => api.get('user/favorites', {
    baseURL: '/mock/',
  }),

  // 修改收藏夹
  favoritesEdit: (favorites: string) => api.post('user/favorites/edit', {
    favorites,
  }, {
    baseURL: '/mock/',
  }),

  // 完成引导页面
  completeOnboarding: () => {
    //console.log('📡 [API] 发起完成引导API请求: POST /api/sys/user/complete-onboarding')
    return api.post('/api/sys/user/complete-onboarding').then((response) => {
      //console.log('📥 [API] 完成引导API响应:', response)
      return response
    }).catch((error) => {
      console.error('❌ [API] 完成引导API失败:', error)
      throw error
    })
  },

  // 接受用户协议
  acceptUserAgreement: () => {
    //console.log('📡 [API] 发起接受用户协议API请求: POST /api/sys/user/accept-user-agreement')
    return api.post('/api/sys/user/accept-user-agreement').then((response) => {
      //console.log('📥 [API] 接受用户协议API响应:', response)
      return response
    }).catch((error) => {
      console.error('❌ [API] 接受用户协议API失败:', error)
      throw error
    })
  },

  // 检查用户协议状态
  checkUserAgreement: () => {
    //console.log('📡 [API] 发起检查用户协议API请求: GET /api/sys/user/check-user-agreement')
    return api.get('/api/sys/user/check-user-agreement').then((response) => {
      //console.log('📥 [API] 检查用户协议API响应:', response)
      return response
    }).catch((error) => {
      console.error('❌ [API] 检查用户协议API失败:', error)
      throw error
    })
  },
}
