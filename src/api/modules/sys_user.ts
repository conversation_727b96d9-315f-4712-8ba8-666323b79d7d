import api from '../index'

// API 基础路径
const API_BASE_URL = '/api'

export default {
  // 分页查询用户列表
  list: (data: {
    username?: string
    email?: string
    phone?: string
    realName?: string
    status?: string | number
    userType?: string
    page: number
    size: number
  }) => api.get(`${API_BASE_URL}/sys/user/list`, {
    params: data,
  }),

  // 获取用户详情
  detail: (id: number | string) => api.get(`${API_BASE_URL}/sys/user/detail`, {
    params: {
      id,
    },
  }),

  // 创建用户
  create: (data: {
    username: string
    email: string
    phone?: string
    password: string
    realName: string
    nickname?: string
    gender?: number
    birthday?: string
    status: number
    userType: string
    tenantId: number
    roleIds: number[]
  }) => api.post(`${API_BASE_URL}/sys/user/create`, data),

  // 更新用户
  edit: (data: {
    id: number | string
    email?: string
    phone?: string
    password?: string
    realName?: string
    nickname?: string
    gender?: number
    birthday?: string
    status?: number
    userType?: string
    roleIds: number[]
  }) => api.post(`${API_BASE_URL}/sys/user/edit`, data),

  // 删除用户
  delete: (id: number | string) => api.post(`${API_BASE_URL}/sys/user/delete`, null, {
    params: {
      id,
    },
  }),

  // 批量删除用户
  batchDelete: (ids: (number | string)[]) => api.post(`${API_BASE_URL}/sys/user/batch-delete`, ids),

  // 更新用户状态
  updateStatus: (id: number | string, status: number) => api.post(`${API_BASE_URL}/sys/user/update-status`, null, {
    params: {
      id,
      status,
    },
  }),

  // 重置用户密码
  resetPassword: (id: number | string) => api.post(`${API_BASE_URL}/sys/user/reset-password`, null, {
    params: {
      id,
    },
  }),

  // 检查用户名是否存在
  checkUsername: (username: string) => api.get(`${API_BASE_URL}/sys/user/check-username`, {
    params: {
      username,
    },
  }),

  // 检查邮箱是否存在
  checkEmail: (email: string) => api.get(`${API_BASE_URL}/sys/user/check-email`, {
    params: {
      email,
    },
  }),

  // 检查手机号是否存在
  checkPhone: (phone: string) => api.get(`${API_BASE_URL}/sys/user/check-phone`, {
    params: {
      phone,
    },
  }),

  // 生成用户免登录链接
  generateNoLoginLink: (userId: number | string) => api.post(`${API_BASE_URL}/sys/user/generate-no-login-link`, null, {
    params: {
      userId,
    },
  }),

  // 获取当前用户信息
  getCurrentProfile: () => api.get(`${API_BASE_URL}/sys/user/profile`),

  // 更新当前用户信息
  updateCurrentProfile: (data: {
    email?: string
    phone?: string
    realName?: string
    nickname?: string
    avatar?: string
    gender?: number
    birthday?: string
  }) => api.post(`${API_BASE_URL}/sys/user/profile/edit`, data),

  // 上传头像
  uploadAvatar: (file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    return api.post(`${API_BASE_URL}/sys/user/avatar/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 获取头像URL
  getAvatarUrl: (key: string) => api.get(`${API_BASE_URL}/sys/user/avatar/url`, {
    params: { key }
  }),
}
