import { request } from '@/api'

// 基础计费接口
export const billingApi = {
  // 获取套餐列表
  getPackages: () => request.get('/api/billing/packages'),

  // 获取用户套餐信息
  getUserPackage: (userId: number) =>
    request.get(`/api/billing/users/${userId}/package`),

  // 订阅套餐
  subscribePackage: (data: any) =>
    request.post('/api/billing/subscribe', data),

  // 获取计费记录
  getBillingRecords: (params: any) =>
    request.get('/api/billing/records', { params }),
}

// 分析相关接口
export const billingAnalyticsApi = {
  // 获取概览数据
  getOverviewData: (timeRange: string) =>
    request.get<OverviewData>(`/api/billing/analytics/overview?timeRange=${timeRange}`),

  // 获取收入趋势数据
  getRevenueTrend: (type: 'daily' | 'weekly' | 'monthly', timeRange: string) =>
    request.get<RevenueTrendData>(`/api/billing/analytics/revenue-trend?type=${type}&timeRange=${timeRange}`),

  // 获取用户分层数据
  getUserLayerData: (timeRange: string) =>
    request.get<UserLayerData>(`/api/billing/analytics/user-layer?timeRange=${timeRange}`),

  // 获取套餐ROI分析
  getPackageROIAnalysis: (timeRange: string) =>
    request.get<PackageROIData>(`/api/billing/analytics/package-roi?timeRange=${timeRange}`),

  // 获取资格达标分析
  getQualificationAnalysis: (timeRange: string) =>
    request.get<QualificationAnalysisData>(`/api/billing/analytics/qualification?timeRange=${timeRange}`),

  // 获取实时监控数据
  getRealTimeMetrics: () =>
    request.get<RealTimeMetricsData>('/api/billing/analytics/real-time'),

  // 获取用户详细列表
  getUserList: (params: UserListParams) =>
    request.get<PaginatedUserData>('/api/billing/analytics/users', { params }),

  // 导出分析报告
  exportAnalyticsReport: (timeRange: string, format: 'excel' | 'pdf') =>
    request.get(`/api/billing/analytics/export?timeRange=${timeRange}&format=${format}`, {
      responseType: 'blob',
    }),

  // 获取异常告警列表
  getAlertList: () =>
    request.get<AlertData[]>('/api/billing/analytics/alerts'),
}

// 类型定义
export interface OverviewData {
  totalRevenue: number
  activeUsers: number
  packageSubscriptions: number
  qualificationRate: number
  revenueTrend: TrendData
  usersTrend: TrendData
  packagesTrend: TrendData
  qualificationTrend: TrendData
}

export interface TrendData {
  type: 'up' | 'down' | 'stable'
  percentage: number
}

export interface RevenueTrendData {
  dates: string[]
  values: number[]
}

export interface UserLayerData {
  layers: {
    name: string
    value: number
    percentage: number
  }[]
}

export interface PackageROIData {
  packages: {
    name: string
    revenue: number
    profitMargin: number
    userCount: number
  }[]
}

export interface QualificationAnalysisData {
  qualificationRate: {
    dates: string[]
    rates: number[]
  }
  consumptionDistribution: {
    ranges: string[]
    counts: number[]
  }
}

export interface RealTimeMetricsData {
  todayRevenue: number
  yesterdayRevenue: number
  onlineUsers: number
  peakUsers: number
  systemLoad: number
  alertCount: number
}

export interface UserListParams {
  page: number
  size: number
  keyword?: string
  packageType?: string
  qualificationStatus?: string
  timeRange?: string
}

export interface UserDetailData {
  userId: number
  userName: string
  packageName: string
  packageType: string
  monthlySpending: number
  qualificationStatus: 'QUALIFIED' | 'UNQUALIFIED'
  lifetimeValue: number
  lastActiveTime: string
}

export interface PaginatedUserData {
  content: UserDetailData[]
  totalElements: number
  totalPages: number
  size: number
  number: number
}

export interface AlertData {
  id: number
  type: 'WARNING' | 'ERROR' | 'INFO'
  title: string
  description: string
  createTime: string
  isRead: boolean
}
