import type {
  ApiResponse,
  Conversation,
  Message,
  ChainExecution,
  InputSuggestion,
  ExecutionResult,
  PaginatedResponse,
  PaginationParams
} from '@/types/agent'
import { request } from '@/api'

// API 基础路径常量
const API_BASE_PATH = '/api/agent-chat/v1'

// 请求参数类型
export interface CreateConversationRequest {
  title: string
  description?: string
}

export interface SendMessageRequest {
  content: string
  messageType?: 'USER' | 'SYSTEM' | 'AGENT'
  metadata?: Record<string, any>
  enableStreaming?: boolean
  files?: File[]
}

export interface SendMessageResponse {
  message: Message
  execution?: ChainExecution
}

export interface ConversationListParams extends PaginationParams {
  archived?: boolean
  search?: string
}

export interface MessageListParams extends PaginationParams {
  conversationId: string
  beforeMessageId?: string
  afterMessageId?: string
}

// Agent Chat API
export const agentChatApi = {
  /**
   * 获取对话列表
   */
  async getConversations(params?: ConversationListParams): Promise<any> {
    const response = await request.get(`${API_BASE_PATH}/conversations`, {
      params: {
        page: params?.page || 0,
        size: params?.size || 20,
        sort: params?.sort || 'updatedAt',
        order: params?.order || 'desc',
        archived: params?.archived || false,
        search: params?.search
      }
    })
    // 直接返回 axios 处理后的结果
    return response
  },

  /**
   * 创建新对话
   */
  async createConversation(data: CreateConversationRequest): Promise<ApiResponse<Conversation>> {
    const response = await request.post<ApiResponse<Conversation>>(`${API_BASE_PATH}/conversations`, data)
    return response.data
  },

  /**
   * 获取对话详情
   */
  async getConversation(conversationId: string): Promise<ApiResponse<Conversation>> {
    const response = await request.get<ApiResponse<Conversation>>(`${API_BASE_PATH}/conversations/${conversationId}`)
    return response.data
  },

  /**
   * 更新对话
   */
  async updateConversation(conversationId: string, updates: Partial<Conversation>): Promise<ApiResponse<Conversation>> {
    const response = await request.put<ApiResponse<Conversation>>(`${API_BASE_PATH}/conversations/${conversationId}`, updates)
    return response.data
  },

  /**
   * 删除对话
   */
  async deleteConversation(conversationId: string): Promise<ApiResponse<void>> {
    const response = await request.delete<ApiResponse<void>>(`${API_BASE_PATH}/conversations/${conversationId}`)
    return response.data
  },

  /**
   * 归档/取消归档对话
   */
  async archiveConversation(conversationId: string, archived: boolean = true): Promise<ApiResponse<Conversation>> {
    const response = await request.patch<ApiResponse<Conversation>>(`${API_BASE_PATH}/conversations/${conversationId}/archive`, {
      archived
    })
    return response.data
  },

  /**
   * 获取对话消息列表
   */
  async getMessages(conversationId: string, params?: MessageListParams): Promise<ApiResponse<Message[]>> {
    const response = await request.get<ApiResponse<Message[]>>(`${API_BASE_PATH}/conversations/${conversationId}/messages`, {
      params: {
        page: params?.page || 0,
        size: params?.size || 50,
        sort: params?.sort || 'timestamp',
        order: params?.order || 'asc',
        beforeMessageId: params?.beforeMessageId,
        afterMessageId: params?.afterMessageId
      }
    })
    return response.data
  },

  /**
   * 发送消息
   */
  async sendMessage(conversationId: string, data: SendMessageRequest): Promise<ApiResponse<SendMessageResponse>> {
    // 构建 JSON 请求体，与后端 SendMessageRequest 保持一致
    const requestBody = {
      content: data.content,
      messageType: data.messageType || 'USER',
      metadata: data.metadata || {},
      enableStreaming: data.enableStreaming !== undefined ? data.enableStreaming : true
    }

    const response = await request.post<ApiResponse<SendMessageResponse>>(
      `${API_BASE_PATH}/conversations/${conversationId}/messages`,
      requestBody,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
    return response.data
  },

  /**
   * 重新生成回复
   */
  async regenerateMessage(conversationId: string, messageId: string): Promise<ApiResponse<SendMessageResponse>> {
    const response = await request.post<ApiResponse<SendMessageResponse>>(
      `${API_BASE_PATH}/conversations/${conversationId}/messages/${messageId}/regenerate`
    )
    return response.data
  },

  /**
   * 获取消息详情
   */
  async getMessage(conversationId: string, messageId: string): Promise<ApiResponse<Message>> {
    const response = await request.get<ApiResponse<Message>>(`${API_BASE_PATH}/conversations/${conversationId}/messages/${messageId}`)
    return response.data
  },

  /**
   * 删除消息
   */
  async deleteMessage(conversationId: string, messageId: string): Promise<ApiResponse<void>> {
    const response = await request.delete<ApiResponse<void>>(`${API_BASE_PATH}/conversations/${conversationId}/messages/${messageId}`)
    return response.data
  },

  /**
   * 获取执行状态
   */
  async getExecutionStatus(executionId: string): Promise<ApiResponse<ChainExecution>> {
    const response = await request.get<ApiResponse<ChainExecution>>(`${API_BASE_PATH}/executions/${executionId}`)
    return response.data
  },

  /**
   * 暂停执行
   */
  async pauseExecution(executionId: string): Promise<ApiResponse<ChainExecution>> {
    const response = await request.post<ApiResponse<ChainExecution>>(`${API_BASE_PATH}/executions/${executionId}/pause`)
    return response.data
  },

  /**
   * 恢复执行
   */
  async resumeExecution(executionId: string): Promise<ApiResponse<ChainExecution>> {
    const response = await request.post<ApiResponse<ChainExecution>>(`${API_BASE_PATH}/executions/${executionId}/resume`)
    return response.data
  },

  /**
   * 停止执行
   */
  async stopExecution(executionId: string): Promise<ApiResponse<ChainExecution>> {
    const response = await request.post<ApiResponse<ChainExecution>>(`${API_BASE_PATH}/executions/${executionId}/stop`)
    return response.data
  },

  /**
   * 取消执行
   */
  async cancelExecution(executionId: string): Promise<ApiResponse<ChainExecution>> {
    const response = await request.post<ApiResponse<ChainExecution>>(`${API_BASE_PATH}/executions/${executionId}/cancel`)
    return response.data
  },

  /**
   * 重试执行
   */
  async retryExecution(executionId: string): Promise<ApiResponse<ChainExecution>> {
    const response = await request.post<ApiResponse<ChainExecution>>(`${API_BASE_PATH}/executions/${executionId}/retry`)
    return response.data
  },

  /**
   * 获取执行结果
   */
  async getExecutionResult(executionId: string): Promise<ApiResponse<ExecutionResult>> {
    const response = await request.get<ApiResponse<ExecutionResult>>(`${API_BASE_PATH}/executions/${executionId}/result`)
    return response.data
  },

  /**
   * 获取执行日志
   */
  async getExecutionLogs(executionId: string, params?: PaginationParams): Promise<ApiResponse<any[]>> {
    const response = await request.get<ApiResponse<any[]>>(`${API_BASE_PATH}/executions/${executionId}/logs`, {
      params: {
        page: params?.page || 0,
        size: params?.size || 100,
        sort: params?.sort || 'timestamp',
        order: params?.order || 'asc'
      }
    })
    return response.data
  },

  /**
   * 获取思维链
   */
  async getThinkingChain(executionId: string): Promise<ApiResponse<any[]>> {
    const response = await request.get<ApiResponse<any[]>>(`${API_BASE_PATH}/executions/${executionId}/thinking`)
    return response.data
  },

  /**
   * 导出执行结果
   */
  async exportExecutionResult(executionId: string, format: 'json' | 'pdf' | 'xlsx' = 'json'): Promise<Blob> {
    const response = await request.get(`${API_BASE_PATH}/executions/${executionId}/export`, {
      params: { format },
      responseType: 'blob'
    })
    return response.data
  },

  /**
   * 获取输入建议
   */
  async getInputSuggestions(conversationId?: string): Promise<ApiResponse<InputSuggestion[]>> {
    const response = await request.get<ApiResponse<InputSuggestion[]>>(`${API_BASE_PATH}/suggestions`, {
      params: { conversationId }
    })
    return response.data
  },

  /**
   * 获取对话统计
   */
  async getConversationStats(conversationId: string): Promise<ApiResponse<any>> {
    const response = await request.get<ApiResponse<any>>(`${API_BASE_PATH}/conversations/${conversationId}/stats`)
    return response.data
  },

  /**
   * 搜索对话
   */
  async searchConversations(query: string, params?: PaginationParams): Promise<ApiResponse<PaginatedResponse<Conversation>>> {
    const response = await request.get<ApiResponse<PaginatedResponse<Conversation>>>(`${API_BASE_PATH}/conversations/search`, {
      params: {
        q: query,
        page: params?.page || 0,
        size: params?.size || 20,
        sort: params?.sort || 'relevance',
        order: params?.order || 'desc'
      }
    })
    return response.data
  },

  /**
   * 搜索消息
   */
  async searchMessages(conversationId: string, query: string, params?: PaginationParams): Promise<ApiResponse<PaginatedResponse<Message>>> {
    const response = await request.get<ApiResponse<PaginatedResponse<Message>>>(`${API_BASE_PATH}/conversations/${conversationId}/messages/search`, {
      params: {
        q: query,
        page: params?.page || 0,
        size: params?.size || 20,
        sort: params?.sort || 'relevance',
        order: params?.order || 'desc'
      }
    })
    return response.data
  },

  /**
   * 获取对话模板
   */
  async getConversationTemplates(): Promise<ApiResponse<any[]>> {
    const response = await request.get<ApiResponse<any[]>>(`${API_BASE_PATH}/templates`)
    return response.data
  },

  /**
   * 从模板创建对话
   */
  async createConversationFromTemplate(templateId: string, variables?: Record<string, any>): Promise<ApiResponse<Conversation>> {
    const response = await request.post<ApiResponse<Conversation>>(`${API_BASE_PATH}/templates/${templateId}/create`, {
      variables
    })
    return response.data
  },

  /**
   * 分享对话
   */
  async shareConversation(conversationId: string, options: {
    includeThinking?: boolean
    includeMetadata?: boolean
    expiresAt?: string
  } = {}): Promise<ApiResponse<{ shareUrl: string; shareId: string }>> {
    const response = await request.post<ApiResponse<{ shareUrl: string; shareId: string }>>(
      `${API_BASE_PATH}/conversations/${conversationId}/share`,
      options
    )
    return response.data
  },

  /**
   * 获取分享的对话
   */
  async getSharedConversation(shareId: string): Promise<ApiResponse<{
    conversation: Conversation
    messages: Message[]
  }>> {
    const response = await request.get<ApiResponse<{
      conversation: Conversation
      messages: Message[]
    }>>(`${API_BASE_PATH}/shared/${shareId}`)
    return response.data
  },

  /**
   * 添加消息反馈
   */
  async addMessageFeedback(conversationId: string, messageId: string, feedback: {
    type: 'like' | 'dislike' | 'report'
    comment?: string
    categories?: string[]
  }): Promise<ApiResponse<void>> {
    const response = await request.post<ApiResponse<void>>(
      `${API_BASE_PATH}/conversations/${conversationId}/messages/${messageId}/feedback`,
      feedback
    )
    return response.data
  },

  /**
   * 语音转文字
   */
  async speechToText(audioFile: File): Promise<ApiResponse<{ text: string; confidence: number }>> {
    const formData = new FormData()
    formData.append('audio', audioFile)

    const response = await request.post<ApiResponse<{ text: string; confidence: number }>>(
      `${API_BASE_PATH}/speech-to-text`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }
    )
    return response.data
  },

  /**
   * 文字转语音
   */
  async textToSpeech(text: string, options: {
    voice?: string
    speed?: number
    format?: 'mp3' | 'wav'
  } = {}): Promise<Blob> {
    const response = await request.post(`${API_BASE_PATH}/text-to-speech`, {
      text,
      ...options
    }, {
      responseType: 'blob'
    })
    return response.data
  },

  /**
   * 上传文件
   */
  async uploadFile(file: File, purpose: 'message' | 'analysis' | 'document' = 'message'): Promise<ApiResponse<{
    fileId: string
    filename: string
    size: number
    mimeType: string
    uploadUrl?: string
  }>> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('purpose', purpose)

    const response = await request.post<ApiResponse<{
      fileId: string
      filename: string
      size: number
      mimeType: string
      uploadUrl?: string
    }>>(`${API_BASE_PATH}/files/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  },

  /**
   * 下载文件
   */
  async downloadFile(fileId: string): Promise<Blob> {
    const response = await request.get(`${API_BASE_PATH}/files/${fileId}/download`, {
      responseType: 'blob'
    })
    return response.data
  },

  /**
   * 获取系统状态
   */
  async getSystemStatus(): Promise<ApiResponse<{
    status: 'healthy' | 'degraded' | 'down'
    activeExecutions: number
    queueLength: number
    averageResponseTime: number
    lastUpdated: string
  }>> {
    const response = await request.get<ApiResponse<{
      status: 'healthy' | 'degraded' | 'down'
      activeExecutions: number
      queueLength: number
      averageResponseTime: number
      lastUpdated: string
    }>>(`${API_BASE_PATH}/system/status`)
    return response.data
  }
}

// 默认导出
export default agentChatApi
