import type { ApiResponse, PagedResponse } from '@/types/billing'
import { request } from '@/api'

// 发票相关类型定义
export interface InvoiceListItem {
  id: number
  invoiceNo: string
  invoiceTitle: string
  invoiceType: 'NORMAL' | 'SPECIAL'
  totalAmount: number
  status: 'PENDING' | 'COMPLETED' | 'CANCELLED'
  applicationTime: string
  processedTime?: string
  applicantName?: string
  applicantEmail?: string
  applicantPhone?: string
}

export interface InvoiceDetail extends InvoiceListItem {
  userId: number
  taxNumber?: string
  companyAddress?: string
  companyPhone?: string
  bankName?: string
  bankAccount?: string
  recipientName?: string
  recipientPhone?: string
  recipientEmail?: string
  recipientAddress?: string
  invoiceFileUrl?: string
  invoiceFileName?: string
  invoiceCode?: string
  invoiceNumber?: string
  invoiceDate?: string
  applicantRemarks?: string
  processorRemarks?: string
  transactions?: InvoiceTransaction[]
}

export interface InvoiceTransaction {
  id: number
  transactionId: number
  transactionNo: string
  transactionAmount: number
  description?: string
  createdAt: string
}

export interface BillingTransaction {
  id: number
  transactionNo: string
  amount: number
  description?: string
  createdAt: string
  status: string
}

export interface InvoiceApplication {
  transactionIds: number[]
  invoiceTitle: string
  invoiceType: 'NORMAL' | 'SPECIAL'
  taxNumber?: string
  companyAddress?: string
  companyPhone?: string
  bankName?: string
  bankAccount?: string
  recipientName: string
  recipientPhone: string
  recipientEmail?: string
  recipientAddress?: string
  applicantRemarks?: string
}

export interface InvoiceStats {
  totalCount: number
  pendingCount: number
  completedCount: number
  totalAmount: number
}

export interface UninvoicedAmount {
  totalAmount: number
  transactionCount: number
  currency: string
}

// 发票API接口
export const invoiceApi = {
  // 用户端接口

  /**
   * 获取用户发票列表
   */
  getUserInvoices(params: {
    page: number
    size: number
    sort?: string
    direction?: string
  }): Promise<ApiResponse<PagedResponse<InvoiceListItem>>> {
    return request.get('/api/billing/invoice/list', { params })
  },

  /**
   * 获取未开票的交易记录
   */
  getUninvoicedTransactions(): Promise<ApiResponse<BillingTransaction[]>> {
    return request.get('/api/billing/invoice/uninvoiced-transactions')
  },

  /**
   * 申请开票
   */
  applyForInvoice(data: InvoiceApplication): Promise<ApiResponse<InvoiceDetail>> {
    return request.post('/api/billing/invoice/apply', data)
  },

  /**
   * 获取发票详情
   */
  getInvoiceDetail(invoiceId: number): Promise<ApiResponse<InvoiceDetail>> {
    return request.get(`/api/billing/invoice/${invoiceId}`)
  },

  /**
   * 获取发票文件下载链接
   */
  downloadInvoiceFile(invoiceId: number): Promise<ApiResponse<string>> {
    return request.get(`/api/billing/invoice/${invoiceId}/download`)
  },

  /**
   * 获取用户发票统计信息
   */
  getUserInvoiceStats(): Promise<ApiResponse<InvoiceStats>> {
    return request.get('/api/billing/invoice/stats')
  },

  /**
   * 获取未开票交易总金额
   */
  getUninvoicedAmount(): Promise<ApiResponse<UninvoicedAmount>> {
    return request.get('/api/billing/invoice/uninvoiced-amount')
  },

  /**
   * 验证发票申请数据
   */
  validateInvoiceApplication(data: InvoiceApplication): Promise<ApiResponse<void>> {
    return request.post('/api/billing/invoice/validate', data)
  },

  // 财务管理端接口

  /**
   * 获取待处理发票列表
   */
  getPendingInvoices(params: {
    page: number
    size: number
    sort?: string
    direction?: string
  }): Promise<ApiResponse<PagedResponse<InvoiceListItem>>> {
    return request.get('/api/billing/admin/invoice/pending', { params })
  },

  /**
   * 获取所有发票列表
   */
  getAllInvoices(params: {
    page: number
    size: number
    sort?: string
    direction?: string
    status?: string
    invoiceNo?: string
    invoiceType?: string
    invoiceTitle?: string
  }): Promise<ApiResponse<PagedResponse<InvoiceListItem>>> {
    return request.get('/api/billing/admin/invoice/all', { params })
  },

  /**
   * 获取发票详情（管理员）
   */
  getInvoiceDetailForAdmin(invoiceId: number): Promise<ApiResponse<InvoiceDetail>> {
    return request.get(`/api/billing/admin/invoice/${invoiceId}`)
  },

  /**
   * 上传发票文件
   */
  uploadInvoiceFile(file: File): Promise<ApiResponse<string>> {
    const formData = new FormData()
    formData.append('file', file)
    return request.post('/api/billing/admin/invoice/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 上传发票文件并关联到发票
   */
  uploadInvoiceFileToInvoice(invoiceId: number, formData: FormData): Promise<ApiResponse<InvoiceDetail>> {
    return request.post(`/api/billing/admin/invoice/${invoiceId}/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 处理发票申请
   */
  processInvoice(params: {
    invoiceId: number
    fileUrl: string
    fileName: string
    fileSize: number
    invoiceCode?: string
    invoiceNumber?: string
    processorRemarks?: string
  }): Promise<ApiResponse<void>> {
    const { invoiceId, ...data } = params
    // console.log('API processInvoice - invoiceId:', invoiceId)
    // console.log('API processInvoice - data:', data)
    // console.log('API processInvoice - URL:', `/api/billing/admin/invoice/${invoiceId}/process`)
    return request.post(`/api/billing/admin/invoice/${invoiceId}/process`, null, {
      params: data
    })
  },

  /**
   * 取消发票申请
   */
  cancelInvoice(invoiceId: number, cancelReason: string): Promise<ApiResponse<void>> {
    return request.post(`/api/billing/admin/invoice/${invoiceId}/cancel`, null, {
      params: { cancelReason }
    })
  },

  /**
   * 批量处理发票申请
   */
  batchProcessInvoices(params: {
    invoiceIds: number[]
    action: 'process' | 'cancel'
    remarks?: string
  }): Promise<ApiResponse<void>> {
    return request.post('/api/billing/admin/invoice/batch-process', null, {
      params
    })
  },

  /**
   * 获取系统发票统计信息
   */
  getSystemInvoiceStats(): Promise<ApiResponse<any>> {
    return request.get('/api/billing/admin/invoice/stats')
  }
}
