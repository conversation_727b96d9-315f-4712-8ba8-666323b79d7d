// AI Agent 相关类型定义

export interface AgentDefinition {
  id?: number
  name: string
  description?: string
  systemPrompt?: string
  category: string
  agentRole: AgentRole
  status: AgentStatus
  priority?: number
  timeoutSeconds?: number
  maxRetries?: number
  capabilities?: string[]
  createdBy?: number
  createdAt?: string
  updatedAt?: string
}

export type AgentRole =
  | 'INTENT_RECOGNITION'    // 意图识别
  | 'DATA_QUERY'           // 数据查询
  | 'DATA_ANALYZER'        // 数据分析
  | 'REASONING'            // 推理
  | 'CHART_GENERATOR'      // 图表生成
  | 'CONTENT_CREATOR'      // 内容创作
  | 'RESULT_CHECKER'       // 结果检查
  | 'GENERIC'              // 通用

export type AgentStatus = 'ACTIVE' | 'INACTIVE' | 'DRAFT'

export type AgentCategory =
  | 'AI_CORE'              // AI核心
  | 'DATA_ANALYSIS'        // 数据分析
  | 'DATA_PROCESSING'      // 数据处理
  | 'INFERENCE'            // 推理
  | 'REPORTING'            // 报告
  | 'VISUALIZATION'        // 可视化
  | 'CONTENT_CREATION'     // 内容创作
  | 'QUALITY_CONTROL'      // 质量控制

// 对话相关
export interface Conversation {
  id: string
  title: string
  createdAt: string
  updatedAt: string
  messageCount: number
  isArchived?: boolean
  userId?: string
  status?: string
  conversationType?: string
  metadata?: Record<string, any>
}

export interface Message {
  id: string
  conversationId: string
  content: string
  role: 'user' | 'assistant'
  timestamp: string
  metadata?: MessageMetadata
  chainOfThought?: ChainOfThoughtCompatible
  executionId?: string
  type?: 'user' | 'assistant' | 'agent' | 'system'  // 兼容字段
  isTemporary?: boolean
}

export interface MessageMetadata {
  agentName?: string
  executionTime?: number
  tokens?: number
  confidence?: number
  sources?: string[]
  sender?: string
  status?: string
  duration?: number
  tokensUsed?: number
  attachments?: any[]
  error?: string
  isThinking?: boolean  // 是否为思考消息
}

// 链式执行相关
export interface ChainExecution {
  id: string
  conversationId: string
  flowId?: string
  flowName?: string
  status: ExecutionStatus
  currentStep?: number
  totalSteps: number
  steps: ChainStep[]
  startTime: string
  endTime?: string
  duration?: number
  eta?: number
  result?: any
  error?: string
  metadata?: ExecutionMetadata
}

export type ExecutionStatus =
  | 'pending'     // 等待中
  | 'running'     // 执行中
  | 'paused'      // 已暂停
  | 'completed'   // 已完成
  | 'failed'      // 执行失败
  | 'cancelled'   // 已取消

export interface ChainStep {
  id: string
  executionId: string
  name: string
  description?: string
  agentName: string
  agentRole: AgentRole
  order: number
  status: ExecutionStatus
  type?: 'sequential' | 'parallel'
  isParallel?: boolean
  dependencies?: string[]
  parameters?: Record<string, any>
  result?: any
  startTime?: string
  endTime?: string
  duration?: number
  progress?: number
  error?: string
  retryCount?: number
}

export interface ExecutionMetadata {
  totalTokens?: number
  totalCost?: number
  userId?: string
  sessionId?: string
  clientInfo?: Record<string, any>
}

// 思维链相关
export interface ChainOfThought {
  steps: ThinkingStep[]
  summary?: string
  confidence?: number
  reasoning?: string
  length?: number  // 添加 length 属性兼容
}

// 兼容类型 - 支持数组或对象格式
export type ChainOfThoughtCompatible = ChainOfThought | ThinkingStep[]

export interface ThinkingStep {
  id?: string  // 添加 id 字段
  agentName: string
  timestamp: string
  content: string
  type: 'analysis' | 'reasoning' | 'decision' | 'execution'
  confidence?: number
  data?: Record<string, any>
  isActive?: boolean  // 添加激活状态
  duration?: number  // 添加持续时间
}

export interface ThinkingProcess {
  id?: string
  agentName: string
  timestamp: string
  content: string  // 思考内容
  analysis?: string
  reasoning?: string
  confidence?: number
  status?: string
  type?: string
  data?: Record<string, any>
}

// 流程相关
export interface AgentFlow {
  id: string
  name: string
  description?: string
  steps: FlowStep[]
  status: FlowStatus
  createdAt: string
  updatedAt: string
}

export type FlowStatus = 'draft' | 'active' | 'archived'

export interface FlowStep {
  id: string
  flowId: string
  name: string
  description?: string
  agentName?: string
  agentRole: AgentRole
  order: number
  type: 'sequential' | 'parallel' | 'conditional'
  isParallel?: boolean
  dependencies?: string[]
  conditions?: FlowCondition[]
  parameters?: Record<string, any>
  timeoutSeconds?: number
  retryPolicy?: RetryPolicy
  status?: ExecutionStatus
  result?: any
  duration?: number
  progress?: number
}

export interface FlowCondition {
  field: string
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains'
  value: any
  nextStepId?: string
}

export interface RetryPolicy {
  maxRetries: number
  retryDelayMs: number
  exponentialBackoff?: boolean
}

// 执行历史
export interface AgentExecution {
  id: string
  agentName: string
  agentRole: AgentRole
  status: ExecutionStatus
  startTime: string
  endTime?: string
  duration?: number
  input?: any
  output?: any
  error?: string
  metadata?: ExecutionMetadata
}

// SSE 事件相关
export interface SSEEvent {
  type: SSEEventType
  data: any
  timestamp: string
  executionId?: string
  stepId?: string
}

export type SSEEventType =
  | 'execution_started'      // 执行开始
  | 'step_started'          // 步骤开始
  | 'step_progress'         // 步骤进度
  | 'step_completed'        // 步骤完成
  | 'step_failed'           // 步骤失败
  | 'thinking_process'      // 思维过程
  | 'execution_completed'   // 执行完成
  | 'execution_failed'      // 执行失败
  | 'execution_paused'      // 执行暂停
  | 'execution_resumed'     // 执行恢复
  | 'connection_status'     // 连接状态
  // Agent Chat 事件类型
  | 'CONNECTED'             // SSE连接建立
  | 'DISCONNECTED'          // SSE连接断开
  | 'HEARTBEAT'             // 心跳消息
  | 'MESSAGE_RECEIVED'      // 消息已接收
  | 'AGENT_THINKING'        // Agent思考
  | 'AGENT_PROGRESS'        // Agent进度
  | 'AGENT_COMPLETED'       // Agent完成
  | 'AGENT_FAILED'          // Agent失败
  | 'ORCHESTRATION_STARTED' // 编排开始
  | 'ORCHESTRATION_PROGRESS'// 编排进度
  | 'ORCHESTRATION_COMPLETED'// 编排完成
  | 'ORCHESTRATION_FAILED'  // 编排失败
  | 'ERROR'                 // 错误消息

// 执行日志
export interface ExecutionLog {
  timestamp: string
  level: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR' | 'SUCCESS'
  agentName: string
  message: string
  data?: any
  executionId?: string
  stepId?: string
}

// 执行结果
export interface ExecutionResult {
  executionId: string
  status: ExecutionStatus
  finalResult: any
  totalDuration: number
  totalSteps: number
  successSteps: number
  failedSteps: number
  totalTokens?: number
  totalCost?: number
  artifacts?: Artifact[]
  summary?: string
  recommendations?: string[]
  metadata?: Record<string, any>
}

export interface Artifact {
  id: string
  name: string
  type: 'chart' | 'report' | 'data' | 'image' | 'document'
  size: number
  url?: string
  downloadUrl?: string
  metadata?: Record<string, any>
}

// 连接状态
export type ConnectionStatus =
  | 'disconnected'  // 未连接
  | 'connecting'    // 连接中
  | 'connected'     // 已连接
  | 'reconnecting'  // 重连中
  | 'error'         // 连接错误

// 用户设置
export interface AgentChatSettings {
  autoScroll: boolean
  showChainOfThought: boolean
  enableSound: boolean
  enableVoiceInput: boolean
  theme: 'light' | 'dark' | 'auto'
  language?: string
}

// 输入建议
export interface InputSuggestion {
  id: string
  text: string
  description?: string
  icon?: string
  category?: string
  priority?: number
}

// 错误类型
export interface AgentError {
  code: string
  message: string
  details?: any
  timestamp: string
  recoverable?: boolean
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
  timestamp: string
}

// 分页相关
export interface PaginationParams {
  page: number
  size: number
  sort?: string
  order?: 'asc' | 'desc'
}

export interface PaginatedResponse<T> {
  content: T[]
  totalElements: number
  totalPages: number
  size: number
  number: number
  first: boolean
  last: boolean
}

// WebSocket 消息类型
export interface WebSocketMessage {
  type: string
  payload: any
  timestamp: string
  messageId?: string
}

// 监控相关
export interface SystemMetrics {
  cpuUsage: number
  memoryUsage: number
  diskUsage: number
  networkIn: number
  networkOut: number
  activeConnections: number
  timestamp: string
}

export interface AgentMetrics {
  agentName: string
  executionCount: number
  successRate: number
  averageExecutionTime: number
  totalTokens: number
  lastExecutionTime: string
  errorRate: number
}

// 配置相关
export interface SystemConfig {
  maxConcurrentExecutions: number
  defaultTimeoutSeconds: number
  enableDebugMode: boolean
  logLevel: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR'
  enableMetrics: boolean
  retentionDays: number
}

// 导出功能相关
export interface ExportOptions {
  format: 'json' | 'csv' | 'xlsx' | 'pdf'
  includeMetadata: boolean
  dateRange?: {
    start: string
    end: string
  }
  filters?: Record<string, any>
}

export interface ExportResult {
  filename: string
  downloadUrl: string
  size: number
  recordCount: number
  createdAt: string
}
