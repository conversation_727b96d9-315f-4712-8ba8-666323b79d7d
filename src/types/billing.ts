/**
 * 计费系统相关类型定义
 */

// 基础响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message?: string
  code?: number
}

// 不计费结果类型
export interface NoChargeResult {
  shouldCharge: boolean
  noChargeType?: string
  reason?: string
}

// 扩展计费类型
export type BillingType =
  | 'TOKEN_BASED'
  | 'FAILED'
  | 'MANUAL'
  | 'NO_CHARGE_INSUFFICIENT_BALANCE'
  | 'NO_CHARGE_NO_RESULT'
  | 'NO_CHARGE_TEMPLATE_NO_DATA'
  | 'NO_CHARGE_SYSTEM_MESSAGE'

// 扣除来源类型
export type DeductionSource =
  | 'FREE_TOKENS_ONLY'
  | 'GIFT_BALANCE_ONLY'
  | 'RECHARGED_BALANCE_ONLY'
  | 'MIXED_DEDUCTION'

// 分页响应类型
export interface PagedResponse<T> {
  content: T[]
  totalElements: number
  totalPages: number
  number: number
  size: number
  first: boolean
  last: boolean
}

// 支付方式信息
export interface PaymentMethod {
  code: 'ALIPAY' | 'WECHAT' | 'BANK_CARD' | 'OTHER'
  name: string
  enabled: boolean
  icon?: string
  description?: string
}

// 计费账户信息
export interface BillingAccount {
  id: string
  userId: string
  balance: number
  currency: string
  status: 'ACTIVE' | 'SUSPENDED' | 'FROZEN'
  createdAt: string
  updatedAt: string
}

// 计费配置 - 与后端 BillingConfigDto 对齐
export interface BillingConfig {
  userId: number
  packageId: number
  packageName: string
  packageDescription: string
  inputTokenPrice: number
  outputTokenPrice: number
  thoughtChainTokenPrice: number
  totalBalance: number
  rechargedBalance: number
  giftBalance: number
  freeTokens: number
  todayTokenUsage: number
  dailyFreeTokens: number
  isFrozen: boolean
  isPackageActive: boolean
  packageCreatedAt: string
  lastUpdated: string
  minRechargeAmount: number
}

// 分页响应类型（如果不存在）
export interface PagedResponse<T> {
  content: T[]
  totalElements: number
  totalPages: number
  number: number
  size: number
  first: boolean
  last: boolean
}

// Token计算请求 - 与后端 TokenCostCalculateDto 对齐（去掉 modelName）
export interface TokenCalculationRequest {
  inputTokens: number
  outputTokens: number
  thoughtChainTokens?: number
}

// Token计算响应
export interface TokenCalculationResponse {
  inputTokens: number
  outputTokens: number
  thoughtChainTokens?: number
  totalTokens: number
  inputCost: number
  outputCost: number
  totalCost: number
  currency: string
  formattedCost: string
}

// 使用记录 - 与后端 UsageRecordDto 对齐（去掉 modelName）
export interface UsageRecord {
  id: number
  messageId: string
  conversationId: string
  inputTokens: number
  outputTokens: number
  thoughtChainTokens: number
  totalTokens: number
  inputCost: number
  outputCost: number
  totalCost: number
  status: 'SUCCESS' | 'FAILED' | 'PENDING'
  billingStatus: 'BILLED' | 'UNBILLED' | 'REFUNDED' | 'NO_CHARGE'
  billingType: BillingType
  deductionSource?: DeductionSource // 扣除来源字段
  packageId: number
  inputTokenPrice: number
  outputTokenPrice: number
  visualDiscountFactor: number
  isAppealed: boolean
  appealStatus: string
  requestTime: string
  responseTime: string
  durationMs: number
  createdAt: string
  modelName?: string // 可选字段，用于前端显示
}

// 充值记录
export interface RechargeRecord {
  id: string
  userId: string
  paymentNo?: string
  amount: number
  currency: string
  paymentMethod: 'ALIPAY' | 'WECHAT' | 'BANK_CARD' | 'OTHER'
  paymentId?: string
  platformTransactionNo?: string
  status: 'PENDING' | 'SUCCESS' | 'FAILED' | 'CANCELLED' | 'REFUNDED'
  description?: string
  completedAt?: string
  remark?: string
  createdAt: string
  updatedAt: string
}

// 申诉记录
export interface AppealRecord {
  id: string
  userId: string
  usageRecordId: string
  type: 'BILLING_ERROR' | 'SERVICE_ISSUE' | 'REFUND_REQUEST' | 'OTHER'
  reason: string
  description: string
  attachments?: string[]
  status: 'PENDING' | 'IN_REVIEW' | 'APPROVED' | 'REJECTED' | 'RESOLVED'
  reviewNote?: string
  refundAmount?: number
  createdAt: string
  updatedAt: string
  resolvedAt?: string
  // 前端组件需要的额外字段（可选）
  title?: string
  contact?: string
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  processRecords?: AppealProcessRecord[]
}

// 申诉处理记录
export interface AppealProcessRecord {
  id: string
  appealId: string
  action: string
  comment: string
  operatorId: string
  operatorName: string
  createdAt: string
}

// 附件信息
export interface AttachmentInfo {
  id: string
  name: string
  url: string
  size: number
  type: string
}

// 充值请求
export interface RechargeRequest {
  amount: number
  paymentMethod: 'ALIPAY' | 'WECHAT' | 'BANK_CARD' | 'OTHER'
  description?: string
}

// 充值预设金额
export interface RechargePresetDto {
  id: number
  amount: number
  bonusAmount: number
  title: string
  description: string
  recommended: boolean
  sortOrder: number
  enabled: boolean
  tag: string
  discountRate: number
}

// 申诉请求
export interface AppealRequest {
  usageRecordId: string
  type: 'BILLING_ERROR' | 'SERVICE_ISSUE' | 'REFUND_REQUEST' | 'OTHER'
  reason: string
  description: string
  attachments?: File[]
}

// 查询参数类型
export interface UsageRecordQueryParams {
  page?: number
  size?: number
  startDate?: string
  endDate?: string
  status?: 'SUCCESS' | 'FAILED' | 'PENDING'
  minAmount?: number
  maxAmount?: number
}

export interface RechargeRecordQueryParams {
  page?: number
  size?: number
  startDate?: string
  endDate?: string
  status?: 'PENDING' | 'SUCCESS' | 'FAILED' | 'CANCELLED' | 'REFUNDED'
  paymentMethod?: 'ALIPAY' | 'WECHAT' | 'BANK_CARD' | 'OTHER'
  minAmount?: number
  maxAmount?: number
}

export interface AppealRecordQueryParams {
  page?: number
  size?: number
  startDate?: string
  endDate?: string
  type?: 'BILLING_ERROR' | 'SERVICE_ISSUE' | 'REFUND_REQUEST' | 'OTHER'
  status?: 'PENDING' | 'IN_REVIEW' | 'APPROVED' | 'REJECTED' | 'RESOLVED'
}

// 统计数据类型 - 与后端 UsageStatisticsDto 对齐（去掉 modelDistribution）
export interface BillingStatistics {
  period: string
  startTime: string
  endTime: string
  totalTokens: number
  totalInputTokens: number
  totalOutputTokens: number
  totalThoughtChainTokens: number
  totalCost: number
  totalInputCost: number
  totalOutputCost: number
  conversationCount: number
  successfulConversationCount: number
  failedConversationCount: number
  averageTokensPerConversation: number
  averageCostPerConversation: number
  maxTokensPerConversation: number
  maxCostPerConversation: number
  dailyTrend: Array<{
    date: string
    tokens: number
    cost: number
    conversations: number
  }>
}

// 余额警告配置
export interface BalanceWarningConfig {
  lowBalanceThreshold: number
  criticalBalanceThreshold: number
  enableNotifications: boolean
}

// 余额检查响应
export interface BalanceCheckResponse {
  sufficient: boolean
  current: number
  required: number
  shortage: number
}

// 分页配置
export interface PaginationConfig {
  current: number
  pageSize: number
  total: number
  showSizeChanger: boolean
  showQuickJumper: boolean
  showTotal: (total: number) => string
}

// 前端状态类型
export interface BillingState {
  account: BillingAccount | null
  configs: BillingConfig | null
  usageRecords: UsageRecord[]
  rechargeRecords: RechargeRecord[]
  appealRecords: AppealRecord[]
  statistics: BillingStatistics | null
  currentTokenCalculation: TokenCalculationResponse | null
  paymentMethods: PaymentMethod[]
  usageRecordsPagination: PaginationConfig
  rechargeRecordsPagination: PaginationConfig
  appealRecordsPagination: PaginationConfig
  isLoading: boolean
  error: string | null
}

// 微信支付二维码响应
export interface WechatPaymentResponse {
  paymentId: number
  paymentNo: string
  userId: number
  amount: number
  paymentMethod: 'WECHAT'
  status: 'PENDING'
  codeUrl: string
  expiredAt: string
  createdAt: string
  paymentMethodDisplayName: string
  formattedAmount: string
  paymentTips: string
  expired: boolean
  pending: boolean
  wechatPay: boolean
  alipay: boolean
  success: boolean
}

// 支付状态查询响应
export interface PaymentStatusResponse {
  paymentId: number
  status: 'PENDING' | 'SUCCESS' | 'FAILED' | 'CANCELLED' | 'EXPIRED'
  updatedAt: string
}

// 发票相关类型定义
export interface InvoiceRequest {
  outTradeNo: string
  amount: number
  invoiceType: 'NORMAL' | 'SPECIAL'
  invoiceTitle: string
  taxNumber?: string
  registeredAddress?: string
  registeredPhone?: string
  bankName?: string
  bankAccount?: string
  recipientName?: string
  recipientPhone?: string
  recipientEmail?: string
}

export interface InvoiceResponse {
  invoiceNo: string
  outTradeNo: string
  transactionId?: string
  amount: number
  invoiceType: 'NORMAL' | 'SPECIAL'
  invoiceTitle: string
  status: 'PENDING' | 'PROCESSING' | 'SUCCESS' | 'FAILED' | 'CANCELLED'
  statusDisplayName: string
  cardId?: string
  invoiceFileUrl?: string
  invoiceCode?: string
  invoiceNumber?: string
  invoiceDate?: string
  checkCode?: string
  failureReason?: string
  createdAt: string
  updatedAt: string
  formattedAmount: string
  invoiceTypeDisplayName: string
  canDownload: boolean
  canReissue: boolean
}

export interface InvoiceStatistics {
  totalInvoices: number
  pendingInvoices: number
  successInvoices: number
  failedInvoices: number
  totalAmount: number
  formattedTotalAmount: string
}

export interface InvoiceQueryParams {
  page?: number
  size?: number
  status?: 'PENDING' | 'PROCESSING' | 'SUCCESS' | 'FAILED' | 'CANCELLED'
  startDate?: string
  endDate?: string
}

// 套餐功能特性
export interface PackageFeature {
  name: string
  description: string
  included: boolean
}

// 行业租户定价
export interface IndustryTenantPricing {
  id: number
  industryType: string
  tenantType: string
  packageName: string
  packageDescription?: string
  price?: number
  originalPrice?: number
  discountText?: string
  discountEndDate?: string
  period?: string
  isAvailable: boolean
  isPopular: boolean
  sortOrder: number
  features?: PackageFeature[]
  createdAt: string
  updatedAt: string
}
