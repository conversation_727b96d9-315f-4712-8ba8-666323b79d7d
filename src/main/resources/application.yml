spring:
  application:
    name: dips-pro-server
  # =======================================
  # Profiles Configuration
  # =======================================
  profiles:
    # 默认激活 dev 环境配置
    # 运行时可以通过环境变量 SPRING_PROFILES_ACTIVE=prod 或 JVM 参数 -Dspring.profiles.active=prod 来覆盖
    active: dev
  # =======================================
  # Config Import
  # =======================================
  config:
    import:
      - classpath:application-route.yml
      - classpath:application-embedding.yml
      - classpath:application-springai.yml

  # =======================================
  # Web MVC Configuration
  # =======================================
  mvc:
    # 明确指定静态资源路径模式，避免与API路径冲突
    static-path-pattern: /static/**
    # 禁用默认的错误页面处理
    throw-exception-if-no-handler-found: true
  web:
    resources:
      # 明确指定静态资源位置
      static-locations: classpath:/static/,classpath:/public/
      # 启用资源映射
      add-mappings: true

  # =======================================
  # Database Configuration
  # =======================================
  datasource:
    # 主数据源 (PostgreSQL)
    primary:
      driver-class-name: org.postgresql.Driver
      url: ***********************************************************************************
      username: ${DB_USERNAME:postgres}
      password: ${DB_PASSWORD:password}

  # =======================================
  # Common JPA Configuration
  # =======================================
  jpa:
    # =======================================
    # Common JPA Configuration
    # =======================================
    properties:
      hibernate:
        # PostgreSQL 方言是通用的
        dialect: org.hibernate.dialect.PostgreSQLDialect
        # 格式化显示的 SQL 语句
        format_sql: true
    # 在控制台显示执行的 SQL 语句
    show-sql: false
    # DDL (Data Definition Language) 自动操作
    # - none: 不做任何操作
    # - validate: 启动时校验 schema 是否匹配
    # - update: 启动时根据实体类更新 schema (开发时常用)
    # - create: 每次启动时删除并重新创建 schema (测试时常用)
    # - create-drop: 启动时创建，关闭时删除
    hibernate:
      ddl-auto: validate

  # =======================================
  # Spring Security Configuration
  # =======================================
  security:
    user:
      # 禁用默认用户，因为我们使用JWT认证
      name: disabled
      password: disabled
      roles: disabled

# 通用的其他配置可以放在这里...

# =======================================
# 其他配置
# =======================================

# =======================================
# Redis Configuration (Placeholder)
# =======================================
# spring.data.redis.host=localhost
# spring.data.redis.port=6379
# spring.data.redis.password=

# =======================================
# Server Configuration
# =======================================
# 可以设置服务器端口等
server:
  port: ${SERVER_PORT:8080}
  # 设置异步请求超时时间为5分钟，与WebClient超时时间一致
  servlet:
    async:
      request-timeout: 300000 # 5分钟，单位毫秒
    context-path: /

# =======================================
# Logging Configuration (Example)
# =======================================
# logging.level.com.dipspro=DEBUG
# logging.level.org.springframework=INFO
# logging.level.org.hibernate.SQL=DEBUG
# logging.level.org.hibernate.type.descriptor.sql=TRACE
# logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# (调试完成后，恢复默认日志级别)
# logging:
#   level:
#     root: INFO # 或者 WARN
#     com.dipspro: INFO

# =======================================
# 认证配置 - 仅使用JWT认证
# =======================================

# JWT配置
jwt:
  secret: ${JWT_SECRET:dips-pro-secret-key-for-jwt-token-generation-and-validation-must-be-at-least-256-bits}
  expiration: ${JWT_EXPIRATION:3600000} # 1小时
  refresh-expiration: ${JWT_REFRESH_EXPIRATION:86400000} # 1天
  no-login-expiration: ${JWT_NO_LOGIN_EXPIRATION:604800000} # 7天

# 路由服务认证配置
route:
  basic_auth:
    username: yt-service
    password: p6QqyH2^LYZrHwGLDc2#(ZUfv

# embedding模型配置
# embedding-path: /mnt/yt-files/models/text2vec-base-chinese/model_qint8_avx512_vnni.onnx
embedding-path: /models/text2vec-base-chinese/model.onnx
embedding-vocab-path: /models/text2vec-base-chinese/vocab.txt

# 计费配置
billing:
  no-charge:
    enabled: true
    record-enabled: true
    insufficient-balance-keywords:
      - "账户余额不足"
      - "请先充值后再发起对话"
      - "余额不足"
      - "insufficient balance"
      - "请先充值"
    no-result-keywords:
      - "未找到"
      - "没有查询到预期结果"
      - "无匹配结果"
      - "暂无数据"
      - "没有查询到"
      - "未查询到"
      - "无相关数据"
      - "查询结果为空"
    template-no-data-keywords:
      - "模板数据为空"
      - "无模板数据"
      - "模板未找到数据"
      - "模板中没有数据"
      - "模板无匹配数据"
    system-message-keywords:
      - "系统消息"
      - "system message"
      - "服务暂时不可用"
      - "系统维护中"

# 日志配置
logging:
  level:
    com.dipspro: DEBUG

# =======================================
# 数据库监控配置
# =======================================
dipspro:
  database:
    monitoring:
      # 是否启用数据库监控
      enabled: true
      # 健康检查间隔 (毫秒) - 5分钟
      health-check-interval: 300000
      # 状态日志间隔 (毫秒) - 1小时
      status-log-interval: 3600000
      # 是否启用自动恢复
      auto-recovery: true

# 微信支付配置
wechat:
  pay:
    appid: wxacc68a2bd0846081
    mchid: ********** # 商户号
    merchantSerialNumber: 5494D244FB3A7781F0610160F75E46C51CE91FE0 # 商户证书序列号
    apiV3Key: ${WECHAT_PAY_API_V3_KEY:PpfECq7hycAXePPgi9PkDUKoXP3frfDZ}
    privateKeyPath: ${WECHAT_PAY_PRIVATE_KEY_PATH:classpath:wechat-pay/apiclient_key.pem}
    publicKeyId: PUB_KEY_ID_01**********2025062900381616000403 # 微信支付公钥
    publicKeyPath: ${WECHAT_PAY_PUBLIC_KEY_PATH:classpath:wechat-pay/pub_key.pem} # 微信支付公钥
    payCallbackUrl: ${WECHAT_PAY_CALLBACK_URL:https://dipsai.cn/server/api/pay/wechat/callback}
    invoiceTemplateId: ${WECHAT_PAY_INVOICE_TEMPLATE_ID:pfso7vp1IZbhHeVWYkF-pssoEe20}
    invoiceCallbackUrl: ${WECHAT_PAY_INVOICE_CALLBACK_URL:https://dipsai.cn/server/api/pay/wechat/invoice/callback/notify}

# 七牛云配置
qiniu:
  access-key: ${QINIU_ACCESS_KEY:R5Zpv1kvOL-uyUH2E5H_vnmFTbFhV8xH10Q6ytJA}
  secret-key: ${QINIU_SECRET_KEY:4eeMs7in9Sf31bjSKYVRFa5OD-DzJpk0j6Oqtocu}
  bucket: ${QINIU_BUCKET:dips-pro}
  domain: ${QINIU_DOMAIN:https://file.dipsai.cn}
  region: huanan
  max-file-size: 10485760  # 10MB
  allowed-file-types:
    - jpg
    - jpeg
    - png
    - gif
    - pdf
    - doc
    - docx
    - xls
    - xlsx
