# =======================================
# Development Profile Configuration
# =======================================

server:
  port: 8080 # 开发环境端口

spring:
  # 数据源配置
  datasource:
    # 主数据源 (PostgreSQL)
    primary:
      driver-class-name: org.postgresql.Driver
      # 增加自动重连参数和TCP keepalive
      jdbcUrl: ***************************************************************************************************************************************************************************************************************
      username: dips
      password: VcyAHPgY4nz38wRWJER6
      hikari:
        # 连接池大小配置
        maximum-pool-size: 15
        minimum-idle: 5
        # 连接超时配置 (20秒)
        connection-timeout: 20000
        # 空闲连接超时 (10分钟)
        idle-timeout: 600000
        # 连接最大生命周期 (25分钟)
        max-lifetime: 1500000
        # 连接验证查询 - 心跳检查
        connection-test-query: SELECT 1
        # 验证超时时间 (5秒)
        validation-timeout: 5000
        # 连接泄漏检测 (30秒)
        leak-detection-threshold: 30000
        # 连接池名称，便于监控
        pool-name: DipsPro-Primary-Pool
        # 是否自动提交
        auto-commit: true
        # 是否初始化失败时快速失败
        initialization-fail-timeout: 1
        # 是否隔离内部池查询
        isolate-internal-queries: false
        # 是否允许池挂起
        allow-pool-suspension: false
        # 是否只读
        read-only: false
        # 是否注册JMX管理
        register-mbeans: false
    # MySQL数据源 (用于data_namelist表)
    mysql:
      driver-class-name: com.mysql.cj.jdbc.Driver
      # 增加MySQL自动重连参数
      jdbcUrl: **************************************************************************************************************************************************************************************************************************************************************************************************
      username: root
      password: ABQqfF*Xm7EUyn)D7Vjq7MiubsU8r8
      hikari:
        # 连接池大小配置
        maximum-pool-size: 15
        minimum-idle: 5
        # 连接超时配置 (20秒)
        connection-timeout: 20000
        # 空闲连接超时 (10分钟)
        idle-timeout: 600000
        # 连接最大生命周期 (25分钟)
        max-lifetime: 1500000
        # 连接验证查询 - 心跳检查
        connection-test-query: SELECT 1
        # 验证超时时间 (5秒)
        validation-timeout: 5000
        # 连接泄漏检测 (30秒)
        leak-detection-threshold: 30000
        # 连接池名称，便于监控
        pool-name: DipsPro-MySQL-Pool
        # 是否自动提交
        auto-commit: true
        
  # 开发环境 JPA 配置
  jpa:
    hibernate:
      # 启动时更新 schema
      ddl-auto: validate
    # 显示并格式化 SQL
    show-sql: false
    properties:
      hibernate:
        format_sql: false
        
  # Redis配置
  data:
    redis:
      host: ***********
      port: 6379
      password: KxpV4cCAZY
      database: 1  # 使用的数据库索引，默认为0
      timeout: 10000  # 连接超时时间(毫秒)
      lettuce:
        pool:
          max-active: 16  # 连接池最大连接数，增加以支持并发
          max-idle: 16    # 连接池最大空闲连接数，与max-active匹配
          min-idle: 8     # 连接池最小空闲连接数，保持足够的空闲连接以避免频繁创建
          max-wait: 3000  # 连接池最大阻塞等待时间(毫秒)，设置有限等待时间
          time-between-eviction-runs: 60000  # 空闲连接检测间隔(毫秒)
      client-name: dips-pro  # 客户端名称，便于在Redis监控中识别

# 开发环境日志级别
logging:
  level:
    com.dipspro: INFO  # 将应用包的日志级别设为DEBUG，以便打印更多日志
    org.springframework.web: INFO # 显示 web 相关 INFO 日志
    org.hibernate.SQL: INFO
    org.hibernate.type.descriptor.sql.BasicBinder: WARN # 显示 SQL 参数绑定
    org.springframework.data.redis: INFO  # 降低Redis操作的日志级别，避免过多日志
    org.springframework.jdbc.core: INFO  # JDBC操作的详细日志
    com.zaxxer.hikari: INFO # 开启HikariCP的DEBUG日志，查看连接池详细信息

# 开发环境特定配置

n8n:
  auth:
    username: dipspro
    password: L4NshqmpVKVL6fpZ8GDK
  webhook:
    url: http://***********:5678/webhook/25afd8bc-1b85-48ab-a965-1043d2813989

# 其他开发环境配置...
# embedding模型配置
# embedding-path: /Users/<USER>/work/model/text2vec-base-chinese/model_qint8_avx512_vnni.onnx
embedding-path: /Users/<USER>/work/model/text2vec-base-chinese/model.onnx
embedding-vocab-path: /Users/<USER>/work/model/text2vec-base-chinese/vocab.txt