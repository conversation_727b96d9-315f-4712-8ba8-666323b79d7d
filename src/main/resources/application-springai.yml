# =======================================
# Spring AI Configuration
# =======================================
spring:
  ai:
    openai:
      # DeepSeek API配置
      api-key: ${DEEPSEEK_API_KEY:***********************************}
      base-url: ${DEEPSEEK_BASE_URL:https://api.deepseek.com}
      chat:
        options:
          # 默认模型
          model: ${DEEPSEEK_MODEL:deepseek-chat}
          # 温度参数，控制回答的随机性 (0.0-2.0)
          temperature: ${DEEPSEEK_TEMPERATURE:0.2}
          # 最大token数
          max-tokens: ${DEEPSEEK_MAX_TOKENS:4000}
          # 多样性惩罚参数
          presence-penalty: ${DEEPSEEK_PRESENCE_PENALTY:0.0}
          # 频率惩罚参数
          frequency-penalty: ${DEEPSEEK_FREQUENCY_PENALTY:0.0}
      embedding:
        options:
          # 嵌入模型
          model: ${DEEPSEEK_EMBEDDING_MODEL:text-embedding-ada-002}

# =======================================
# Spring AI Chat Configuration
# =======================================
app:
  ai:
    # 聊天配置
    chat:
      # 默认系统提示词
      system-prompt: "你是一个有用的DIPS AI助手，请用中文回答用户的问题。"
      # 最大对话历史长度
      max-history-length: 10
      # 请求超时时间（秒）
      timeout: 30
    # 嵌入配置
    embedding:
      # 向量维度
      dimension: 1536
      # 批处理大小
      batch-size: 100