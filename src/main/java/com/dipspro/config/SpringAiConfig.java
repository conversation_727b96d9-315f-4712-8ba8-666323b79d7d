package com.dipspro.config;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import lombok.extern.slf4j.Slf4j;

/**
 * AI助手配置类
 */
@Configuration
@Slf4j
public class SpringAiConfig {

    /**
     * 配置ChatClient Bean
     * Spring AI会自动配置OpenAiChatModel
     */
    @Bean
    public ChatClient chatClient(OpenAiChatModel openAiChatModel) {
        log.info("正在配置AI助手ChatClient...");
        return ChatClient.builder(openAiChatModel).build();
    }
}