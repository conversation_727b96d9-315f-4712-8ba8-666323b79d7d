package com.dipspro.config;

import java.util.Map;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import com.dipspro.util.DatabaseConnectionUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据库连接监控配置
 * 提供定时健康检查和自动恢复功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Configuration
@EnableScheduling
@ConditionalOnProperty(
    value = "dipspro.database.monitoring.enabled", 
    havingValue = "true", 
    matchIfMissing = true
)
public class DatabaseMonitoringConfig {
    
    @Autowired(required = false)
    @Qualifier("primaryDataSource")
    private DataSource primaryDataSource;
    
    @Autowired(required = false)
    @Qualifier("mysqlDataSource")
    private DataSource mysqlDataSource;
    
    /**
     * 定时健康检查 - 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void scheduledHealthCheck() {
        log.debug("开始执行定时数据库健康检查");
        
        try {
            if (primaryDataSource != null) {
                boolean primaryHealthy = DatabaseConnectionUtil.isConnectionHealthy(primaryDataSource);
                if (!primaryHealthy) {
                    log.warn("主数据库连接不健康，尝试自动恢复");
                    boolean recovered = DatabaseConnectionUtil.checkAndRecoverConnection(primaryDataSource);
                    if (recovered) {
                        log.info("主数据库连接自动恢复成功");
                    } else {
                        log.error("主数据库连接自动恢复失败，需要人工干预");
                    }
                }
            }
            
            if (mysqlDataSource != null) {
                boolean mysqlHealthy = DatabaseConnectionUtil.isConnectionHealthy(mysqlDataSource);
                if (!mysqlHealthy) {
                    log.warn("MySQL数据库连接不健康，尝试自动恢复");
                    boolean recovered = DatabaseConnectionUtil.checkAndRecoverConnection(mysqlDataSource);
                    if (recovered) {
                        log.info("MySQL数据库连接自动恢复成功");
                    } else {
                        log.error("MySQL数据库连接自动恢复失败，需要人工干预");
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("定时健康检查执行失败", e);
        }
    }
    
    /**
     * 定时连接池状态日志 - 每小时执行一次
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void scheduledConnectionPoolLogging() {
        log.debug("开始记录连接池状态信息");
        
        try {
            if (primaryDataSource != null) {
                log.info("=== 主数据库连接池状态 ===");
                DatabaseConnectionUtil.logConnectionPoolInfo(primaryDataSource);
            }
            
            if (mysqlDataSource != null) {
                log.info("=== MySQL数据库连接池状态 ===");
                DatabaseConnectionUtil.logConnectionPoolInfo(mysqlDataSource);
            }
            
            // 生成健康报告
            if (primaryDataSource != null && mysqlDataSource != null) {
                Map<String, Object> healthReport = DatabaseConnectionUtil.getHealthReport(primaryDataSource, mysqlDataSource);
                log.info("数据库整体健康状态: {}", healthReport.get("overall"));
            }
            
        } catch (Exception e) {
            log.error("记录连接池状态信息失败", e);
        }
    }
}