package com.dipspro.config;

import java.util.Arrays;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.dipspro.security.JwtAuthenticationEntryPoint;
import com.dipspro.security.JwtAuthenticationFilter;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Spring Security配置 - 统一管理安全和Web配置
 * 只使用JWT认证，不使用Basic认证
 */
@Slf4j
@Configuration
@EnableWebSecurity
@EnableMethodSecurity
@RequiredArgsConstructor
public class SecurityConfig implements WebMvcConfigurer {

    private final JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;
    private final JwtAuthenticationFilter jwtAuthenticationFilter;

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        log.info("配置Spring Security过滤器链 - 仅使用JWT认证");

        http
                // 禁用CSRF
                .csrf(AbstractHttpConfigurer::disable)

                // 禁用HTTP Basic认证
                .httpBasic(AbstractHttpConfigurer::disable)

                // 禁用表单登录
                .formLogin(AbstractHttpConfigurer::disable)

                // 禁用默认登出
                .logout(AbstractHttpConfigurer::disable)

                // 配置CORS
                .cors(cors -> cors.configurationSource(corsConfigurationSource()))

                // 配置会话管理 - 无状态
                .sessionManagement(session -> {
                    session.sessionCreationPolicy(SessionCreationPolicy.STATELESS);
                    log.info("配置无状态会话管理");
                })

                // 配置异步请求支持
                .requestCache(cache -> {
                    cache.requestCache(new org.springframework.security.web.savedrequest.NullRequestCache());
                    log.info("配置空请求缓存以支持异步请求");
                })

                // 配置异常处理
                .exceptionHandling(exception -> {
                    exception.authenticationEntryPoint(jwtAuthenticationEntryPoint);
                    log.info("配置JWT认证入口点");
                })

                // 配置请求授权
                .authorizeHttpRequests(auth -> {
                    log.info("配置请求授权规则");
                    auth
                            // 允许认证相关接口
                            .requestMatchers("/api/auth/**").permitAll()
                            // 允许健康检查
                            .requestMatchers("/actuator/**").permitAll()
                            // 允许Swagger文档
                            .requestMatchers("/swagger-ui/**", "/v3/api-docs/**").permitAll()
                            // 允许静态资源
                            .requestMatchers("/static/**", "/public/**", "/webjars/**").permitAll()
                            // 允许错误页面
                            .requestMatchers("/error").permitAll()
                            // 允许路由服务健康检查
                            .requestMatchers("/api/route/health").permitAll()
                            // 允许微信支付回调接口
                            .requestMatchers("/api/pay/wechat/callback/**").permitAll()
                            .requestMatchers("/api/pay/wechat/*/callback/**").permitAll()
                            .requestMatchers("/api/pay/wechat/invoice/callback/**").permitAll()
                            // 允许创建通知
                            .requestMatchers("/api/sys/notification/createNotification").permitAll()
                            // 允许 SSE 流式连接（通过URL参数中的token进行认证）
                            .requestMatchers("/api/agent-chat/v*/conversations/*/stream").permitAll()
                            // 其他请求需要认证
                            .anyRequest().authenticated();
                    log.info("请求授权规则配置完成");
                })

                // 添加JWT过滤器 - 在UsernamePasswordAuthenticationFilter之前
                .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        log.info("Spring Security配置完成 - 仅使用JWT认证");
        return http.build();
    }

    /**
     * 配置静态资源处理器
     * 统一在SecurityConfig中管理静态资源的路由和安全配置
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        log.info("配置静态资源处理器");

        // 配置静态资源映射，确保与上面的安全配置保持一致
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/")
                .setCachePeriod(3600); // 设置缓存时间1小时

        registry.addResourceHandler("/public/**")
                .addResourceLocations("classpath:/public/")
                .setCachePeriod(3600);

        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/")
                .setCachePeriod(3600);

        log.info("静态资源处理器配置完成");
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        log.info("配置CORS");
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(Arrays.asList("*"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);
        configuration.setMaxAge(3600L);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        log.info("CORS配置完成");
        return source;
    }

    /**
     * 配置密码编码器
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        log.info("配置BCrypt密码编码器");
        return new BCryptPasswordEncoder();
    }

    /**
     * 配置一个空的UserDetailsService来避免Spring Security自动配置默认用户
     * 由于我们使用JWT认证，不需要UserDetailsService，但提供一个空实现来避免警告
     */
    @Bean
    public UserDetailsService userDetailsService() {
        log.info("配置空的UserDetailsService - 使用JWT认证，不需要默认用户");
        // 创建一个空的用户详情服务，避免Spring Security生成默认密码
        return new InMemoryUserDetailsManager();
    }
}