package com.dipspro.util;

import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URL;

import javax.imageio.ImageIO;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.dipspro.modules.common.dto.QiniuUploadResult;
import com.dipspro.modules.common.service.QiniuService;
import com.dipspro.modules.tenant.service.TenantService;

/**
 * 海报图片合成工具类
 * <p>
 * 用于将二维码图片嵌入到七牛云上的海报模板指定位置，并上传合成图片到七牛云。
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
public class PosterImageUtil {

    private static final Logger log = LoggerFactory.getLogger(PosterImageUtil.class);

    private PosterImageUtil() {
        // 防止实例化
    }

    /**
     * 通过七牛key下载图片为字节数组
     * 
     * @param qiniuService 七牛服务
     * @param key 文件key
     * @return 文件字节数组，失败返回null
     */
    private static byte[] downloadImageFromQiniu(QiniuService qiniuService, String key) {
        return qiniuService.downloadFile(key);
    }

    /**
     * 合成海报图片并上传到七牛云
     *
     * @param tenantId  租户ID
     * @param posterKey 海报模板在七牛的key（如"ym/poster/poster1.png"）
     * @param x         二维码嵌入位置x坐标（默认100）
     * @param y         二维码嵌入位置y坐标（默认100）
     * @param qrWidth   二维码宽度（默认400）
     * @param qrHeight  二维码高度（默认400）
     * @return 合成后图片的七牛key，失败返回null
     */
    public static String composePosterAndUpload(Long tenantId,
            String posterKey,
            int x,
            int y,
            int qrWidth,
            int qrHeight) {
        try {
            QiniuService qiniuService = SpringContextHolder.getBean(QiniuService.class);
            TenantService tenantService = SpringContextHolder
                    .getBean(TenantService.class);

            log.info("[Poster] 开始下载海报模板: {}", posterKey);
            // 1. 下载海报模板图片
            byte[] posterBytes = downloadImageFromQiniu(qiniuService, posterKey);
            if (posterBytes == null) {
                log.error("下载海报模板失败: {}", posterKey);
                return null;
            }
            log.info("[Poster] 海报模板下载成功, 大小: {} bytes", posterBytes.length);
            
            // 如果下载的文件很小，可能是错误页面，记录前几个字节用于调试
            if (posterBytes.length < 1024) {
                String preview = new String(posterBytes, 0, Math.min(posterBytes.length, 200));
                log.warn("[Poster] 下载内容预览（前200字符）: {}", preview);
            }
            
            BufferedImage posterImg = ImageIO.read(new ByteArrayInputStream(posterBytes));
            if (posterImg == null) {
                log.error("[Poster] 海报模板图片解析失败，可能不是有效的图片格式: {}", posterKey);
                return null;
            }
            log.info("[Poster] 海报模板图片解析成功, 尺寸: {}x{}", posterImg.getWidth(), posterImg.getHeight());

            // 2. 获取二维码图片下载链接
            String qrCodeUrl = tenantService.getTenantQrCodeUrl(tenantId);
            if (qrCodeUrl == null) {
                log.error("未获取到租户二维码图片链接, tenantId={}", tenantId);
                return null;
            }
            log.info("[Poster] 获取到租户二维码图片下载链接: {}", qrCodeUrl);

            // 2.1 下载二维码图片
            log.info("[Poster] 开始下载二维码图片: {}", qrCodeUrl);
            byte[] qrBytes;
            try (InputStream in = new URL(qrCodeUrl).openStream();
                    ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                byte[] buffer = new byte[4096];
                int len;
                while ((len = in.read(buffer)) != -1) {
                    baos.write(buffer, 0, len);
                }
                qrBytes = baos.toByteArray();
            }
            if (qrBytes == null || qrBytes.length == 0) {
                log.error("下载二维码图片失败: {}", qrCodeUrl);
                return null;
            }
            log.info("[Poster] 二维码图片下载成功, 大小: {} bytes", qrBytes.length);
            
            // 如果下载的文件很小，可能是错误页面，记录前几个字节用于调试
            if (qrBytes.length < 1024) {
                String preview = new String(qrBytes, 0, Math.min(qrBytes.length, 200));
                log.warn("[Poster] 二维码下载内容预览（前200字符）: {}", preview);
            }
            
            BufferedImage qrImg = ImageIO.read(new ByteArrayInputStream(qrBytes));
            if (qrImg == null) {
                log.error("[Poster] 二维码图片解析失败，可能不是有效的图片格式: {}", qrCodeUrl);
                return null;
            }
            log.info("[Poster] 二维码图片解析成功, 尺寸: {}x{}", qrImg.getWidth(), qrImg.getHeight());

            log.info("[Poster] 开始合成图片, 二维码位置: ({}, {}), 大小: {}x{}", x, y, qrWidth, qrHeight);
            // 3. 合成图片
            BufferedImage combined = new BufferedImage(
                    posterImg.getWidth(), posterImg.getHeight(), BufferedImage.TYPE_INT_ARGB);
            Graphics2D g = combined.createGraphics();
            g.drawImage(posterImg, 0, 0, null);
            g.drawImage(qrImg, x, y, qrWidth, qrHeight, null);
            g.dispose();
            log.info("[Poster] 图片合成完成");

            // 4. 转为字节流
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(combined, "png", baos);
            byte[] resultBytes = baos.toByteArray();
            log.info("[Poster] 合成图片转字节流完成, 大小: {} bytes", resultBytes.length);

            // 4.1 保存本地一份，便于调试
            try {
                String localDir = "/Users/<USER>/Downloads/01-Image/ym";
                String resultKey = String.format("ym/poster/tenant_%d_poster1.png", tenantId);
                String fileName = resultKey.substring(resultKey.lastIndexOf('/') + 1);
                java.io.File dir = new java.io.File(localDir);
                if (!dir.exists())
                    dir.mkdirs();
                java.io.File localFile = new java.io.File(dir, fileName);
                ImageIO.write(combined, "png", localFile);
                log.info("[Poster] 合成图片已保存到本地: {}", localFile.getAbsolutePath());
            } catch (Exception e) {
                log.warn("[Poster] 合成图片本地保存失败", e);
            }

            // 5. 上传到七牛云
            // 根据海报模板key生成唯一的结果key
            String resultKey = generateComposedPosterKey(tenantId, posterKey);
            log.info("[Poster] 开始上传合成图片到七牛云: {}", resultKey);
            QiniuUploadResult uploadResult = qiniuService.uploadFile(resultBytes, resultKey, null);
            if (uploadResult.getSuccess() == null || !uploadResult.getSuccess()) {
                log.error("上传合成海报失败: {}", uploadResult.getErrorMessage());
                return null;
            }
            log.info("合成海报上传成功: {}", uploadResult.getKey());
            return uploadResult.getKey(); // 返回实际的key，而不是传入的key
        } catch (Exception e) {
            log.error("合成海报图片失败", e);
            return null;
        }
    }

    /**
     * 根据海报模板key生成合成后海报的key
     * 
     * @param tenantId  租户ID
     * @param posterKey 海报模板key，如"ym/poster/poster1.png"
     * @return 合成后海报的key，如"ym/poster/tenant_1518_poster1.png"
     */
    private static String generateComposedPosterKey(Long tenantId, String posterKey) {
        if (posterKey == null || posterKey.trim().isEmpty()) {
            return String.format("ym/poster/tenant_%d_poster.png", tenantId);
        }

        // 从海报模板key中提取文件名部分
        // 如："ym/poster/poster1.png" -> "poster1.png"
        String fileName = posterKey.substring(posterKey.lastIndexOf('/') + 1);

        // 生成组合后的key
        // 如："ym/poster/tenant_1518_poster1.png"
        return String.format("ym/poster/tenant_%d_%s", tenantId, fileName);
    }

    /**
     * 使用默认参数合成并上传（二维码位置x=100, y=100, width=400, height=400）
     */
    public static String composePosterAndUploadDefault(Long tenantId) {
        String posterKey = "ym/poster/poster1.png";
        return composePosterAndUpload(tenantId, posterKey, 100, 100, 400, 400);
    }
}