package com.dipspro.util;

import java.lang.management.ManagementFactory;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

import javax.management.JMX;
import javax.management.MBeanServer;
import javax.management.ObjectName;
import javax.sql.DataSource;

import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.HikariPoolMXBean;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据库连接工具类
 * 提供连接池监控、健康检查、连接恢复等功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class DatabaseConnectionUtil {
    

    
    /**
     * 检查数据库连接是否健康
     * 
     * @param dataSource 数据源
     * @return 连接是否健康
     */
    public static boolean isConnectionHealthy(DataSource dataSource) {
        if (dataSource == null) {
            log.warn("数据源为空，连接不健康");
            return false;
        }
        
        try (Connection connection = dataSource.getConnection()) {
            if (connection == null || connection.isClosed()) {
                log.warn("无法获取数据库连接或连接已关闭");
                return false;
            }
            
            // 执行简单查询验证连接
            if (dataSource instanceof HikariDataSource) {
                HikariDataSource hikariDS = (HikariDataSource) dataSource;
                String testQuery = "SELECT 1";
                // PostgreSQL 和 MySQL 都支持 SELECT 1
                try (var statement = connection.createStatement();
                     var resultSet = statement.executeQuery(testQuery)) {
                    boolean hasResult = resultSet.next();
                    log.debug("数据库健康检查成功，连接池: {}", hikariDS.getPoolName());
                    return hasResult;
                }
            }
            return true;
        } catch (SQLException e) {
            log.error("数据库连接健康检查失败", e);
            return false;
        }
    }
    
    /**
     * 使用 JdbcTemplate 检查数据库连接
     * 
     * @param jdbcTemplate JDBC模板
     * @return 连接是否健康
     */
    public static boolean isConnectionHealthy(JdbcTemplate jdbcTemplate) {
        if (jdbcTemplate == null) {
            log.warn("JdbcTemplate为空，连接不健康");
            return false;
        }
        
        try {
            Integer result = jdbcTemplate.queryForObject("SELECT 1", Integer.class);
            boolean isHealthy = result != null && result == 1;
            log.debug("JdbcTemplate健康检查结果: {}", isHealthy);
            return isHealthy;
        } catch (DataAccessException e) {
            log.error("JdbcTemplate健康检查失败", e);
            return false;
        }
    }
    
    /**
     * 获取连接池状态信息
     * 
     * @param dataSource 数据源
     * @return 连接池状态Map
     */
    public static Map<String, Object> getConnectionPoolStatus(DataSource dataSource) {
        Map<String, Object> status = new HashMap<>();
        
        if (!(dataSource instanceof HikariDataSource)) {
            status.put("error", "不是HikariDataSource类型");
            return status;
        }
        
        HikariDataSource hikariDS = (HikariDataSource) dataSource;
        
        try {
            status.put("poolName", hikariDS.getPoolName());
            status.put("maximumPoolSize", hikariDS.getMaximumPoolSize());
            status.put("minimumIdle", hikariDS.getMinimumIdle());
            status.put("connectionTimeout", hikariDS.getConnectionTimeout());
            status.put("idleTimeout", hikariDS.getIdleTimeout());
            status.put("maxLifetime", hikariDS.getMaxLifetime());
            status.put("isClosed", hikariDS.isClosed());
            status.put("isRunning", hikariDS.isRunning());
            
            // 获取连接池的实时统计信息
            HikariPoolMXBean poolMXBean = getHikariPoolMXBean(hikariDS.getPoolName());
            if (poolMXBean != null) {
                status.put("activeConnections", poolMXBean.getActiveConnections());
                status.put("idleConnections", poolMXBean.getIdleConnections());
                status.put("totalConnections", poolMXBean.getTotalConnections());
                status.put("threadsAwaitingConnection", poolMXBean.getThreadsAwaitingConnection());
            }
            
            log.debug("连接池状态获取成功: {}", status);
        } catch (Exception e) {
            log.error("获取连接池状态失败", e);
            status.put("error", e.getMessage());
        }
        
        return status;
    }
    
    /**
     * 通过JMX获取HikariPool的MXBean
     * 
     * @param poolName 连接池名称
     * @return HikariPoolMXBean
     */
    private static HikariPoolMXBean getHikariPoolMXBean(String poolName) {
        try {
            MBeanServer mBeanServer = ManagementFactory.getPlatformMBeanServer();
            ObjectName objectName = new ObjectName("com.zaxxer.hikari:type=Pool (" + poolName + ")");
            
            if (mBeanServer.isRegistered(objectName)) {
                return JMX.newMXBeanProxy(mBeanServer, objectName, HikariPoolMXBean.class);
            } else {
                log.debug("连接池 {} 的MXBean未注册", poolName);
                return null;
            }
        } catch (Exception e) {
            log.warn("获取连接池MXBean失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 检查并尝试恢复数据库连接
     * 
     * @param dataSource 数据源
     * @return 恢复是否成功
     */
    public static boolean checkAndRecoverConnection(DataSource dataSource) {
        log.info("开始检查并尝试恢复数据库连接");
        
        // 首先检查连接是否健康
        if (isConnectionHealthy(dataSource)) {
            log.info("数据库连接正常，无需恢复");
            return true;
        }
        
        if (!(dataSource instanceof HikariDataSource)) {
            log.error("数据源不是HikariDataSource类型，无法自动恢复");
            return false;
        }
        
        HikariDataSource hikariDS = (HikariDataSource) dataSource;
        String poolName = hikariDS.getPoolName();
        
        try {
            log.warn("连接池 {} 出现问题，尝试软重启", poolName);
            
            // 获取连接池状态
            Map<String, Object> statusBefore = getConnectionPoolStatus(dataSource);
            log.info("重启前连接池状态: {}", statusBefore);
            
            // 尝试清理连接池（HikariCP会自动重新创建连接）
            hikariDS.getHikariPoolMXBean().softEvictConnections();
            
            // 等待一段时间让连接池重新初始化
            Thread.sleep(2000);
            
            // 再次检查连接
            boolean recovered = isConnectionHealthy(dataSource);
            if (recovered) {
                Map<String, Object> statusAfter = getConnectionPoolStatus(dataSource);
                log.info("连接池 {} 恢复成功，恢复后状态: {}", poolName, statusAfter);
            } else {
                log.error("连接池 {} 恢复失败", poolName);
            }
            
            return recovered;
            
        } catch (Exception e) {
            log.error("尝试恢复连接池 {} 时发生异常", poolName, e);
            return false;
        }
    }
    
    /**
     * 获取连接池健康状态报告
     * 
     * @param primaryDataSource 主数据源
     * @param mysqlDataSource MySQL数据源
     * @return 健康状态报告
     */
    public static Map<String, Object> getHealthReport(DataSource primaryDataSource, DataSource mysqlDataSource) {
        Map<String, Object> report = new HashMap<>();
        
        try {
            // 主数据源健康检查
            boolean primaryHealthy = isConnectionHealthy(primaryDataSource);
            Map<String, Object> primaryStatus = getConnectionPoolStatus(primaryDataSource);
            
            report.put("primary", Map.of(
                "healthy", primaryHealthy,
                "status", primaryStatus
            ));
            
            // MySQL数据源健康检查
            boolean mysqlHealthy = isConnectionHealthy(mysqlDataSource);
            Map<String, Object> mysqlStatus = getConnectionPoolStatus(mysqlDataSource);
            
            report.put("mysql", Map.of(
                "healthy", mysqlHealthy,
                "status", mysqlStatus
            ));
            
            // 整体健康状态
            report.put("overall", Map.of(
                "healthy", primaryHealthy && mysqlHealthy,
                "timestamp", System.currentTimeMillis()
            ));
            
            log.debug("数据库健康状态报告生成完成");
            
        } catch (Exception e) {
            log.error("生成健康状态报告失败", e);
            report.put("error", e.getMessage());
        }
        
        return report;
    }
    
    /**
     * 记录连接池详细信息到日志
     * 
     * @param dataSource 数据源
     */
    public static void logConnectionPoolInfo(DataSource dataSource) {
        if (!(dataSource instanceof HikariDataSource)) {
            log.info("数据源不是HikariDataSource类型");
            return;
        }
        
        HikariDataSource hikariDS = (HikariDataSource) dataSource;
        
        log.info("=== 连接池信息 ===");
        log.info("连接池名称: {}", hikariDS.getPoolName());
        log.info("最大连接数: {}", hikariDS.getMaximumPoolSize());
        log.info("最小空闲连接数: {}", hikariDS.getMinimumIdle());
        log.info("连接超时时间: {} ms", hikariDS.getConnectionTimeout());
        log.info("空闲超时时间: {} ms", hikariDS.getIdleTimeout());
        log.info("连接最大生命周期: {} ms", hikariDS.getMaxLifetime());
        log.info("是否关闭: {}", hikariDS.isClosed());
        log.info("是否运行中: {}", hikariDS.isRunning());
        
        HikariPoolMXBean poolMXBean = getHikariPoolMXBean(hikariDS.getPoolName());
        if (poolMXBean != null) {
            log.info("活跃连接数: {}", poolMXBean.getActiveConnections());
            log.info("空闲连接数: {}", poolMXBean.getIdleConnections());
            log.info("总连接数: {}", poolMXBean.getTotalConnections());
            log.info("等待连接的线程数: {}", poolMXBean.getThreadsAwaitingConnection());
        }
        log.info("=================");
    }
}