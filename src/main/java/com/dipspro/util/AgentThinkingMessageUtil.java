package com.dipspro.util;

import java.util.Map;

import org.springframework.stereotype.Component;

import com.dipspro.modules.agent.dto.api.SseEvent;
import com.dipspro.modules.agent.service.SseEmitterManager;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Agent思考消息工具类 - 统一处理智能体思考状态消息发送
 * 
 * 核心功能：
 * 1. 统一的思考消息创建逻辑
 * 2. SSE连接状态检查
 * 3. 异常处理确保不影响主流程
 * 4. 标准化的消息格式
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class AgentThinkingMessageUtil {

    private final SseEmitterManager sseEmitterManager;

    /**
     * 发送智能体思考消息
     * 
     * @param conversationId 会话ID
     * @param agentName Agent名称
     * @param userMessage 用户消息
     */
    public static void sendThinkingMessage(SseEmitterManager sseEmitterManager,
                                         String conversationId, 
                                         String agentName, 
                                         String userMessage) {
        try {
            // 创建思考消息事件
            SseEvent thinkingEvent = createThinkingEvent(conversationId, agentName, userMessage);

            // 检查是否有SSE连接，如果有则发送消息
            if (sseEmitterManager.hasActiveConnections(conversationId)) {
                sseEmitterManager.broadcast(conversationId, thinkingEvent);
                log.info("已向前端发送思考消息: conversationId={}, agentName={}", conversationId, agentName);
            } else {
                log.warn("无SSE连接，跳过思考消息发送: conversationId={}", conversationId);
            }
            
        } catch (Exception e) {
            log.warn("发送思考消息失败: conversationId={}, error={}", conversationId, e.getMessage());
            // 不中断正常执行流程
        }
    }

    /**
     * 实例方法版本 - 使用注入的sseEmitterManager
     * 
     * @param conversationId 会话ID
     * @param agentName Agent名称
     * @param userMessage 用户消息
     */
    public void sendThinkingMessage(String conversationId, String agentName, String userMessage) {
        sendThinkingMessage(sseEmitterManager, conversationId, agentName, userMessage);
    }

    /**
     * 创建思考消息事件
     * 
     * @param conversationId 会话ID
     * @param agentName Agent名称
     * @param userMessage 用户消息
     * @return SseEvent
     */
    public static SseEvent createThinkingEvent(String conversationId, String agentName, String userMessage) {
        return SseEvent.agentThinking(conversationId, agentName, userMessage);
    }

    /**
     * 发送自定义思考消息
     * 
     * @param conversationId 会话ID
     * @param agentName Agent名称
     * @param customMessage 自定义消息内容
     * @param stepId 步骤ID
     */
    public void sendCustomThinkingMessage(String conversationId, 
                                        String agentName, 
                                        String customMessage,
                                        String stepId) {
        try {
            SseEvent thinkingEvent = SseEvent.agentThinking(conversationId, agentName, customMessage);

            if (sseEmitterManager.hasActiveConnections(conversationId)) {
                sseEmitterManager.broadcast(conversationId, thinkingEvent);
                log.info("已向前端发送自定义思考消息: conversationId={}, agentName={}, message={}", 
                        conversationId, agentName, customMessage);
            } else {
                log.debug("无SSE连接，跳过思考消息发送: conversationId={}", conversationId);
            }
            
        } catch (Exception e) {
            log.warn("发送自定义思考消息失败: conversationId={}, error={}", conversationId, e.getMessage());
        }
    }

    /**
     * 批量发送思考消息（用于多步骤场景）
     * 
     * @param conversationId 会话ID
     * @param messages 消息列表，包含agentName和message的映射
     */
    public void sendBatchThinkingMessages(String conversationId, Map<String, String> messages) {
        if (messages == null || messages.isEmpty()) {
            return;
        }

        try {
            if (!sseEmitterManager.hasActiveConnections(conversationId)) {
                log.debug("无SSE连接，跳过批量思考消息发送: conversationId={}", conversationId);
                return;
            }

            int stepCounter = 1;
            for (Map.Entry<String, String> entry : messages.entrySet()) {
                String agentName = entry.getKey();
                String message = entry.getValue();
                String stepId = "batch_thinking_" + stepCounter;

                SseEvent thinkingEvent = SseEvent.agentThinking(conversationId, agentName, message);

                sseEmitterManager.broadcast(conversationId, thinkingEvent);
                stepCounter++;
            }

            log.info("已向前端发送批量思考消息: conversationId={}, count={}", conversationId, messages.size());
            
        } catch (Exception e) {
            log.warn("发送批量思考消息失败: conversationId={}, error={}", conversationId, e.getMessage());
        }
    }

    /**
     * 检查是否有活跃的SSE连接
     * 
     * @param conversationId 会话ID
     * @return 是否有连接
     */
    public boolean hasActiveConnection(String conversationId) {
        return sseEmitterManager.hasActiveConnections(conversationId);
    }
}
