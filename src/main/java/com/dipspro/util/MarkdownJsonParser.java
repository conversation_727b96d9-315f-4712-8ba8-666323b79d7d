package com.dipspro.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * Markdown格式JSON解析工具类
 * 
 * 主要功能：
 * 1. 从Markdown格式文本中提取JSON内容
 * 2. 清理JSON格式标记符号
 * 3. 安全解析JSON数据
 * 4. 支持多种Markdown代码块格式
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class MarkdownJsonParser {
    
    // Markdown JSON代码块的正则表达式模式
    private static final Pattern JSON_CODE_BLOCK_PATTERN = Pattern.compile(
        "```(?:json)?\\s*\\n?([\\s\\S]*?)\\n?```", 
        Pattern.CASE_INSENSITIVE | Pattern.MULTILINE
    );
    
    // 单行代码的正则表达式模式
    private static final Pattern INLINE_CODE_PATTERN = Pattern.compile(
        "`([^`]+)`"
    );
    
    // 移除Markdown标记的正则表达式
    private static final Pattern MARKDOWN_MARKERS_PATTERN = Pattern.compile(
        "^\\s*```(?:json)?\\s*$|^\\s*```\\s*$", 
        Pattern.MULTILINE
    );
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    private MarkdownJsonParser() {
        // 防止实例化
    }
    
    /**
     * 从Markdown格式文本中提取并解析JSON
     * 
     * @param markdownText 包含JSON的Markdown文本
     * @return 解析后的JsonNode，如果解析失败返回null
     */
    public static JsonNode extractAndParseJson(String markdownText) {
        if (markdownText == null || markdownText.trim().isEmpty()) {
            log.warn("输入的Markdown文本为空");
            return null;
        }
        
        try {
            // 1. 首先尝试提取代码块中的JSON
            String jsonContent = extractJsonFromCodeBlock(markdownText);
            
            // 2. 如果没有找到代码块，尝试直接解析整个文本
            if (jsonContent == null) {
                jsonContent = cleanMarkdownMarkers(markdownText);
            }
            
            // 3. 解析JSON
            if (jsonContent != null && !jsonContent.trim().isEmpty()) {
                return parseJsonSafely(jsonContent);
            }
            
            log.warn("无法从Markdown文本中提取有效的JSON内容");
            return null;
            
        } catch (Exception e) {
            log.error("解析Markdown JSON时发生错误", e);
            return null;
        }
    }
    
    /**
     * 从代码块中提取JSON内容
     * 
     * @param markdownText Markdown文本
     * @return 提取的JSON字符串，如果没有找到返回null
     */
    public static String extractJsonFromCodeBlock(String markdownText) {
        if (markdownText == null) {
            return null;
        }
        
        Matcher matcher = JSON_CODE_BLOCK_PATTERN.matcher(markdownText);
        if (matcher.find()) {
            String jsonContent = matcher.group(1);
            log.debug("从代码块中提取JSON内容，长度: {}", jsonContent.length());
            return jsonContent.trim();
        }
        
        // 尝试单行代码格式
        matcher = INLINE_CODE_PATTERN.matcher(markdownText);
        if (matcher.find()) {
            String content = matcher.group(1);
            if (isLikelyJson(content)) {
                log.debug("从单行代码中提取JSON内容");
                return content.trim();
            }
        }
        
        return null;
    }
    
    /**
     * 清理Markdown标记符号
     * 
     * @param text 原始文本
     * @return 清理后的文本
     */
    public static String cleanMarkdownMarkers(String text) {
        if (text == null) {
            return null;
        }
        
        // 移除代码块标记
        String cleaned = MARKDOWN_MARKERS_PATTERN.matcher(text).replaceAll("");
        
        // 移除首尾空白
        cleaned = cleaned.trim();
        
        log.debug("清理Markdown标记，原长度: {}, 清理后长度: {}", text.length(), cleaned.length());
        return cleaned;
    }
    
    /**
     * 安全解析JSON字符串
     * 
     * @param jsonString JSON字符串
     * @return 解析后的JsonNode，解析失败返回null
     */
    public static JsonNode parseJsonSafely(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }
        
        try {
            JsonNode result = objectMapper.readTree(jsonString);
            log.debug("JSON解析成功，节点类型: {}", result.getNodeType());
            return result;
        } catch (Exception e) {
            log.warn("JSON解析失败: {}", e.getMessage());
            
            // 尝试修复常见的JSON格式问题
            String fixedJson = attemptJsonFix(jsonString);
            if (!fixedJson.equals(jsonString)) {
                try {
                    JsonNode result = objectMapper.readTree(fixedJson);
                    log.info("JSON修复成功并解析");
                    return result;
                } catch (Exception fixException) {
                    log.warn("JSON修复后仍然解析失败: {}", fixException.getMessage());
                }
            }
            
            return null;
        }
    }
    
    /**
     * 尝试修复常见的JSON格式问题
     * 
     * @param jsonString 原始JSON字符串
     * @return 修复后的JSON字符串
     */
    private static String attemptJsonFix(String jsonString) {
        String fixed = jsonString;
        
        // 1. 移除开头和结尾的非JSON字符
        fixed = fixed.replaceAll("^[^{\\[]*", "").replaceAll("[^}\\]]*$", "");
        
        // 2. 修复常见的引号问题（中文引号转英文引号）
        fixed = fixed.replace("\u201c", "\"").replace("\u201d", "\"");
        fixed = fixed.replace("\u2018", "'").replace("\u2019", "'");
        
        // 3. 移除可能的BOM标记
        if (fixed.startsWith("\uFEFF")) {
            fixed = fixed.substring(1);
        }
        
        // 4. 修复末尾缺少括号的问题
        if (fixed.startsWith("{") && !fixed.endsWith("}")) {
            // 简单的括号计数修复
            long openBraces = fixed.chars().filter(ch -> ch == '{').count();
            long closeBraces = fixed.chars().filter(ch -> ch == '}').count();
            while (closeBraces < openBraces) {
                fixed += "}";
                closeBraces++;
            }
        }
        
        if (fixed.startsWith("[") && !fixed.endsWith("]")) {
            long openBrackets = fixed.chars().filter(ch -> ch == '[').count();
            long closeBrackets = fixed.chars().filter(ch -> ch == ']').count();
            while (closeBrackets < openBrackets) {
                fixed += "]";
                closeBrackets++;
            }
        }
        
        return fixed;
    }
    
    /**
     * 判断字符串是否可能是JSON格式
     * 
     * @param text 待判断的字符串
     * @return 如果可能是JSON返回true
     */
    private static boolean isLikelyJson(String text) {
        if (text == null || text.trim().isEmpty()) {
            return false;
        }
        
        String trimmed = text.trim();
        return (trimmed.startsWith("{") && trimmed.endsWith("}")) ||
               (trimmed.startsWith("[") && trimmed.endsWith("]"));
    }
    
    /**
     * 获取JSON节点的字符串值，支持默认值
     * 
     * @param jsonNode JSON节点
     * @param fieldName 字段名
     * @param defaultValue 默认值
     * @return 字段值或默认值
     */
    public static String getJsonString(JsonNode jsonNode, String fieldName, String defaultValue) {
        if (jsonNode == null || !jsonNode.has(fieldName) || jsonNode.get(fieldName).isNull()) {
            return defaultValue;
        }
        return jsonNode.get(fieldName).asText();
    }
    
    /**
     * 验证提取的JSON是否包含期望的字段
     * 
     * @param jsonNode JSON节点
     * @param requiredFields 必需的字段列表
     * @return 如果包含所有必需字段返回true
     */
    public static boolean validateRequiredFields(JsonNode jsonNode, String... requiredFields) {
        if (jsonNode == null || requiredFields == null) {
            return false;
        }
        
        for (String field : requiredFields) {
            if (!jsonNode.has(field)) {
                log.warn("JSON缺少必需字段: {}", field);
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 从复杂的Markdown文本中提取多个JSON块
     * 
     * @param markdownText Markdown文本
     * @return JSON字符串数组
     */
    public static String[] extractAllJsonBlocks(String markdownText) {
        if (markdownText == null || markdownText.trim().isEmpty()) {
            return new String[0];
        }
        
        Matcher matcher = JSON_CODE_BLOCK_PATTERN.matcher(markdownText);
        return matcher.results()
                .map(matchResult -> matchResult.group(1).trim())
                .filter(json -> !json.isEmpty())
                .toArray(String[]::new);
    }
}
