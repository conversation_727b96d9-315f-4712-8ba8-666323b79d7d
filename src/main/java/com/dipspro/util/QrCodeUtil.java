package com.dipspro.util;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;

import com.dipspro.modules.common.dto.QiniuUploadResult;
import com.dipspro.modules.common.service.QiniuService;
import com.dipspro.util.dto.QrCodeResult;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;

import lombok.extern.slf4j.Slf4j;

/**
 * 二维码生成工具类
 * 支持生成二维码图片并上传到七牛云
 * 
 * <AUTHOR>
 * @since 2025-01-16
 */
@Slf4j
public class QrCodeUtil {

    /**
     * 私有构造函数，防止实例化
     */
    private QrCodeUtil() {
        // 防止实例化
    }

    /**
     * 默认二维码宽度
     */
    private static final int DEFAULT_WIDTH = 300;

    /**
     * 默认二维码高度
     */
    private static final int DEFAULT_HEIGHT = 300;

    /**
     * 默认边距
     */
    private static final int DEFAULT_MARGIN = 1;

    /**
     * 生成二维码并上传到七牛云
     * 
     * @param content 二维码内容（文本或链接）
     * @return 二维码生成结果
     */
    public static QrCodeResult generateQrCode(String content) {
        return generateQrCode(content, DEFAULT_WIDTH, DEFAULT_HEIGHT);
    }

    /**
     * 生成二维码并上传到七牛云
     * 
     * @param content 二维码内容（文本或链接）
     * @param width   二维码宽度
     * @param height  二维码高度
     * @return 二维码生成结果
     */
    public static QrCodeResult generateQrCode(String content, int width, int height) {
        return generateQrCode(content, width, height, null);
    }

    /**
     * 生成二维码并上传到七牛云
     * 
     * @param content  二维码内容（文本或链接）
     * @param width    二维码宽度
     * @param height   二维码高度
     * @param fileName 自定义文件名（不包含扩展名）
     * @return 二维码生成结果
     */
    public static QrCodeResult generateQrCode(String content, int width, int height, String fileName) {
        try {
            // 参数验证
            if (StringUtils.isBlank(content)) {
                return QrCodeResult.failure("二维码内容不能为空");
            }

            if (width <= 0 || height <= 0) {
                return QrCodeResult.failure("二维码尺寸必须大于0");
            }

            if (width > 2000 || height > 2000) {
                return QrCodeResult.failure("二维码尺寸不能超过2000x2000");
            }

            log.info("开始生成二维码: content=" + content + ", width=" + width + ", height=" + height);

            // 获取 QiniuService 实例
            QiniuService qiniuService = SpringContextHolder.getBean(QiniuService.class);

            // 生成二维码图片字节数组
            byte[] imageBytes = generateQrCodeImageBytes(content, width, height);

            // 生成文件名
            String finalFileName = generateFileName(fileName);

            // 上传到七牛云
            QiniuUploadResult uploadResult = qiniuService.uploadFile(imageBytes, finalFileName, null);

            if (!uploadResult.getSuccess()) {
                log.error("二维码图片上传失败: " + uploadResult.getErrorMessage());
                return QrCodeResult.failure("图片上传失败: " + uploadResult.getErrorMessage());
            }

            // 生成私有访问URL（7天有效期）
            String privateUrl = qiniuService.getPrivateFileUrl(uploadResult.getKey(), 7 * 24 * 60 * 60);

            log.info("二维码生成并上传成功: key=" + uploadResult.getKey() + ", privateUrl=" + privateUrl);

            return QrCodeResult.success(
                    privateUrl,
                    uploadResult.getKey(),
                    uploadResult.getFileName(),
                    uploadResult.getSize(),
                    content,
                    width,
                    height);

        } catch (Exception e) {
            log.error("二维码生成失败: " + e.getMessage());
            e.printStackTrace();
            return QrCodeResult.failure("二维码生成失败: " + e.getMessage());
        }
    }

    /**
     * 生成二维码图片字节数组
     * 
     * @param content 二维码内容
     * @param width   宽度
     * @param height  高度
     * @return 图片字节数组
     */
    private static byte[] generateQrCodeImageBytes(String content, int width, int height)
            throws WriterException, IOException {

        // 设置二维码参数
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.M);
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        hints.put(EncodeHintType.MARGIN, DEFAULT_MARGIN);

        // 生成二维码矩阵
        MultiFormatWriter multiFormatWriter = new MultiFormatWriter();
        BitMatrix bitMatrix = multiFormatWriter.encode(content, BarcodeFormat.QR_CODE, width, height, hints);

        // 使用 MatrixToImageWriter 将矩阵转换为图片字节数组
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        MatrixToImageWriter.writeToStream(bitMatrix, "PNG", baos);
        return baos.toByteArray();
    }

    /**
     * 批量生成二维码
     * 
     * @param contents 二维码内容列表
     * @param width    二维码宽度
     * @param height   二维码高度
     * @return 二维码生成结果列表
     */
    public static List<QrCodeResult> generateQrCodeBatch(List<String> contents, int width, int height) {
        if (contents == null || contents.isEmpty()) {
            return Collections.emptyList();
        }

        log.info("开始批量生成二维码: count=" + contents.size() + ", width=" + width + ", height=" + height);

        List<QrCodeResult> results = new ArrayList<>();

        for (String content : contents) {
            QrCodeResult result = generateQrCode(content, width, height);
            results.add(result);

            // 添加小延迟，避免过快的请求
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }

        long successCount = results.stream().mapToLong(r -> r.getSuccess() ? 1 : 0).sum();
        log.info("批量生成二维码完成: total=" + results.size() + ", success=" + successCount);

        return results;
    }

    /**
     * 生成文件名
     * 
     * @param customName 自定义文件名
     * @return 文件名
     */
    private static String generateFileName(String customName) {
        if (StringUtils.isNotBlank(customName)) {
            return customName + ".png";
        }

        String uuid = UUID.randomUUID().toString().replace("-", "");
        return "qrcode_" + uuid + ".png";
    }

    /**
     * 验证二维码内容是否为有效URL
     * 
     * @param content 内容
     * @return 是否为有效URL
     */
    public static boolean isValidUrl(String content) {
        if (StringUtils.isBlank(content)) {
            return false;
        }

        try {
            new java.net.URL(content);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取二维码推荐尺寸
     * 
     * @param contentLength 内容长度
     * @return 推荐尺寸数组 [width, height]
     */
    public static int[] getRecommendedSize(int contentLength) {
        if (contentLength <= 50) {
            return new int[] { 200, 200 };
        } else if (contentLength <= 100) {
            return new int[] { 300, 300 };
        } else if (contentLength <= 200) {
            return new int[] { 400, 400 };
        } else {
            return new int[] { 500, 500 };
        }
    }

    /**
     * 生成二维码内容的简单验证
     * 
     * @param content 内容
     * @return 验证结果
     */
    public static boolean validateContent(String content) {
        if (StringUtils.isBlank(content)) {
            return false;
        }

        // 检查内容长度（二维码有容量限制）
        if (content.length() > 2000) {
            return false;
        }

        return true;
    }

    /**
     * 获取二维码容量信息
     * 
     * @param errorCorrectionLevel 错误纠正级别
     * @return 容量信息描述
     */
    public static String getCapacityInfo(ErrorCorrectionLevel errorCorrectionLevel) {
        switch (errorCorrectionLevel) {
            case L:
                return "低级别纠错(~7%): 数字最多7089字符, 字母最多4296字符, 中文最多1817字符";
            case M:
                return "中级别纠错(~15%): 数字最多5596字符, 字母最多3391字符, 中文最多1433字符";
            case Q:
                return "较高级别纠错(~25%): 数字最多3993字符, 字母最多2420字符, 中文最多1022字符";
            case H:
                return "高级别纠错(~30%): 数字最多3057字符, 字母最多1852字符, 中文最多784字符";
            default:
                return "未知纠错级别";
        }
    }
}