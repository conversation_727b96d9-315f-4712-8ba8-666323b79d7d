package com.dipspro.util;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Map;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 模型调用日志工具类
 * 统一管理AI模型调用的输入输出日志记录
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@Slf4j
public class ModelCallLogger {

    /**
     * 带日志记录的模型调用
     *
     * @param chatClient   Spring AI ChatClient
     * @param systemPrompt 系统提示词
     * @param userPrompt   用户提示词
     * @param contextInfo  上下文信息（如Agent ID、名称等）
     * @return AI响应内容
     */
    public String callWithLogging(ChatClient chatClient,
                                  String systemPrompt,
                                  String userPrompt,
                                  Map<String, Object> contextInfo) {
        LocalDateTime startTime = LocalDateTime.now();

        try {
            // 记录调用开始信息
            logCallStart(systemPrompt, userPrompt, contextInfo, startTime);

            // 执行AI调用
            String response = chatClient.prompt()
                    .system(systemPrompt)
                    .user(userPrompt)
                    .call()
                    .content();

            LocalDateTime endTime = LocalDateTime.now();

            // 记录调用结束信息
            logCallEnd(response, startTime, endTime, contextInfo);

            return response;

        } catch (Exception e) {
            LocalDateTime endTime = LocalDateTime.now();
            logCallError(e, startTime, endTime, contextInfo);
            throw e;
        }
    }

    /**
     * 带日志记录的流式模型调用
     *
     * @param chatClient   Spring AI ChatClient
     * @param systemPrompt 系统提示词
     * @param userPrompt   用户提示词
     * @param contextInfo  上下文信息
     * @return AI响应流
     */
    public reactor.core.publisher.Flux<String> callStreamWithLogging(
            ChatClient chatClient,
            String systemPrompt,
            String userPrompt,
            Map<String, Object> contextInfo) {

        LocalDateTime startTime = LocalDateTime.now();

        // 记录调用开始信息
        logCallStart(systemPrompt, userPrompt, contextInfo, startTime);

        return chatClient.prompt()
                .system(systemPrompt)
                .user(userPrompt)
                .stream()
                .content()
                .doOnNext(chunk -> {
                    // 记录流式响应片段（可选，避免日志过多）
                    if (log.isDebugEnabled()) {
                        log.debug("Stream chunk received: {}", chunk);
                    }
                })
                .doOnComplete(() -> {
                    LocalDateTime endTime = LocalDateTime.now();
                    log.info("=== 流式AI模型调用完成 ===");
                    log.info("总耗时: {} ms", Duration.between(startTime, endTime).toMillis());
                    log.info("========================");
                })
                .doOnError(error -> {
                    LocalDateTime endTime = LocalDateTime.now();
                    logCallError(error, startTime, endTime, contextInfo);
                });
    }

    /**
     * 记录调用开始信息
     */
    private void logCallStart(String systemPrompt, String userPrompt,
                              Map<String, Object> contextInfo, LocalDateTime startTime) {
        log.info("=== AI模型调用开始 ===");

        // 记录上下文信息
        if (contextInfo != null) {
            contextInfo.forEach((key, value) ->
                    log.info("{}: {}", key, value));
        }

        // 记录模型配置
        log.info("Model: {}", getModelConfig());
        log.info("Base URL: {}", getBaseUrl());
        log.info("调用时间: {}", startTime);

        // 记录提示词（可能很长，考虑截断）
        log.info("System Prompt: \n{}", truncateIfNeeded(systemPrompt, 50));
//        log.info("System Prompt: {}", systemPrompt);
//        log.info("User Prompt: {}", truncateIfNeeded(userPrompt, 1000));
        log.info("User Prompt: {}", userPrompt);
    }

    /**
     * 记录调用结束信息
     */
    private void logCallEnd(String response, LocalDateTime startTime,
                            LocalDateTime endTime, Map<String, Object> contextInfo) {
        log.info("=== AI模型调用结束 ===");
        log.info("响应时间: {}", endTime);
        log.info("耗时: {} ms", Duration.between(startTime, endTime).toMillis());
        log.info("响应长度: {} 字符", response.length());
//        log.info("AI Response: {}", truncateIfNeeded(response, 2000));
        log.info("AI Response: \n{}", response);
        log.info("========================");
    }

    /**
     * 记录调用错误信息
     */
    private void logCallError(Throwable error, LocalDateTime startTime,
                              LocalDateTime endTime, Map<String, Object> contextInfo) {
        log.error("=== AI模型调用失败 ===");
        log.error("错误时间: {}", endTime);
        log.error("失败耗时: {} ms", Duration.between(startTime, endTime).toMillis());
        log.error("错误信息: {}", error.getMessage());
        log.error("错误详情: ", error);
        log.error("========================");
    }

    /**
     * 获取当前模型配置
     */
    private String getModelConfig() {
        return System.getenv().getOrDefault("DEEPSEEK_MODEL",
                System.getProperty("spring.ai.openai.chat.options.model", "deepseek-chat"));
    }

    /**
     * 获取当前Base URL配置
     */
    private String getBaseUrl() {
        return System.getenv().getOrDefault("DEEPSEEK_BASE_URL",
                System.getProperty("spring.ai.openai.base-url", "https://api.deepseek.com"));
    }

    /**
     * 截断过长的文本用于日志记录
     */
    private String truncateIfNeeded(String text, int maxLength) {
        if (text == null) {
            return "null";
        }
        if (text.length() <= maxLength) {
            return text;
        }
        return text.substring(0, maxLength) + "... (截断，总长度: " + text.length() + " 字符)";
    }

    /**
     * 记录模型参数信息
     */
    public void logModelParameters() {
        log.info("=== AI模型参数配置 ===");
        log.info("Model: {}", getModelConfig());
        log.info("Base URL: {}", getBaseUrl());
        log.info("Temperature: {}", System.getenv().getOrDefault("DEEPSEEK_TEMPERATURE", "0.2"));
        log.info("Max Tokens: {}", System.getenv().getOrDefault("DEEPSEEK_MAX_TOKENS", "4000"));
        log.info("Presence Penalty: {}", System.getenv().getOrDefault("DEEPSEEK_PRESENCE_PENALTY", "0.0"));
        log.info("Frequency Penalty: {}", System.getenv().getOrDefault("DEEPSEEK_FREQUENCY_PENALTY", "0.0"));
        log.info("========================");
    }
}
