package com.dipspro.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.dipspro.modules.tenant.dto.TenantConfigCsvImportDto;
import com.dipspro.modules.tenant.dto.TenantImportResultDto;
import com.dipspro.modules.tenant.entity.Tenant;
import com.dipspro.modules.tenant.entity.TenantConfig;
import com.dipspro.modules.tenant.repository.TenantConfigRepository;
import com.dipspro.modules.tenant.repository.TenantRepository;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import lombok.extern.slf4j.Slf4j;

/**
 * 租户配置CSV导入工具类
 */
@Slf4j
public class TenantConfigCsvImportUtil {

    private static final int BATCH_SIZE = 100;
    private static final String INDUSTRY_TYPE = "MEDICAL_BEAUTY";
    private static final String TENANT_TYPE = "ORGANIZATION";

    private TenantConfigCsvImportUtil() {
        // 私有构造函数，防止实例化
    }

    /**
     * 从CSV内容导入租户配置数据
     *
     * @param csvContent             CSV文件内容
     * @param tenantRepository       租户仓库
     * @param tenantConfigRepository 租户配置仓库
     * @param validator              数据验证器
     * @return 导入结果
     */
    @Transactional(rollbackFor = Exception.class)
    public static TenantImportResultDto importConfigFromCsv(
            String csvContent,
            TenantRepository tenantRepository,
            TenantConfigRepository tenantConfigRepository,
            Validator validator) {

        long startTime = System.currentTimeMillis();
        List<String> errorMessages = new ArrayList<>();
        List<TenantConfigCsvImportDto> importData = new ArrayList<>();

        log.info("开始执行CSV租户配置导入, 内容长度: {}", csvContent.length());

        try {
            // 解析CSV内容
            log.info("步骤1: 开始解析CSV内容");
            importData = parseCsvContent(csvContent, errorMessages);
            log.info("CSV内容解析完成, 解析到 {} 条记录", importData.size());

            if (!errorMessages.isEmpty()) {
                log.warn("CSV解析阶段发现错误: {} 个", errorMessages.size());
                return TenantImportResultDto.builder()
                        .totalCount(0)
                        .successCount(0)
                        .failureCount(0)
                        .allSuccess(false)
                        .errorMessages(errorMessages)
                        .processingTime(System.currentTimeMillis() - startTime)
                        .build();
            }

            // 验证数据
            log.info("步骤2: 开始验证导入数据");
            validateImportData(importData, validator, errorMessages);
            log.info("数据验证完成");

            if (!errorMessages.isEmpty()) {
                log.warn("数据验证阶段发现错误: {} 个", errorMessages.size());
                return TenantImportResultDto.builder()
                        .totalCount(importData.size())
                        .successCount(0)
                        .failureCount(importData.size())
                        .allSuccess(false)
                        .errorMessages(errorMessages)
                        .processingTime(System.currentTimeMillis() - startTime)
                        .build();
            }

            // 批量更新租户配置数据
            log.info("步骤3: 开始批量更新租户配置数据, 总计 {} 条记录", importData.size());
            int successCount = batchUpdateTenantConfigs(importData, tenantRepository, tenantConfigRepository,
                    errorMessages);
            log.info("批量更新完成, 成功: {}, 失败: {}", successCount, importData.size() - successCount);

            return TenantImportResultDto.builder()
                    .totalCount(importData.size())
                    .successCount(successCount)
                    .failureCount(importData.size() - successCount)
                    .allSuccess(successCount == importData.size())
                    .errorMessages(errorMessages)
                    .processingTime(System.currentTimeMillis() - startTime)
                    .build();

        } catch (Exception e) {
            log.error("CSV导入过程中发生异常", e);
            errorMessages.add("导入过程中发生系统异常: " + e.getMessage());

            return TenantImportResultDto.builder()
                    .totalCount(importData.size())
                    .successCount(0)
                    .failureCount(importData.size())
                    .allSuccess(false)
                    .errorMessages(errorMessages)
                    .processingTime(System.currentTimeMillis() - startTime)
                    .build();
        }
    }

    /**
     * 解析CSV内容
     */
    private static List<TenantConfigCsvImportDto> parseCsvContent(String csvContent, List<String> errorMessages)
            throws IOException {

        List<TenantConfigCsvImportDto> importData = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new StringReader(csvContent))) {
            String line;
            int lineNumber = 0;
            String[] headers = null;

            while ((line = reader.readLine()) != null) {
                lineNumber++;

                if (lineNumber == 1) {
                    // 解析表头
                    headers = parseCsvLine(line);
                    if (!validateHeaders(headers)) {
                        errorMessages.add("CSV文件表头格式不正确，必须包含：id,usci,name,telephone,region,address");
                        return importData;
                    }
                    log.info("CSV表头解析成功: {}", String.join(", ", headers));
                    continue;
                }

                // 解析数据行
                String[] values = parseCsvLine(line);

                // 检查列数
                if (values.length < 6) {
                    errorMessages.add("第" + lineNumber + "行数据列数不足，需要6列数据");
                    continue;
                }
                if (values.length > 6) {
                    errorMessages.add("第" + lineNumber + "行数据列数过多");
                    continue;
                }

                TenantConfigCsvImportDto dto = new TenantConfigCsvImportDto();

                // 解析各个字段
                dto.setId(values[0].trim()); // id列
                dto.setUsci(values[1].trim());
                dto.setName(values[2].trim());
                dto.setTelephone(values[3].trim());
                dto.setRegion(values[4].trim());
                dto.setAddress(values[5].trim());

                // 解析行政区信息
                parseRegionInfo(dto);

                importData.add(dto);
            }
        }

        log.info("CSV解析完成，共解析 {} 条数据记录", importData.size());
        return importData;
    }

    /**
     * 解析CSV行
     * 支持双引号包裹的字段，双引号内的逗号不作为分隔符
     */
    private static String[] parseCsvLine(String line) {
        if (line == null || line.trim().isEmpty()) {
            return new String[0];
        }
        
        List<String> fields = new ArrayList<>();
        StringBuilder currentField = new StringBuilder();
        boolean inQuotes = false;
        
        for (int i = 0; i < line.length(); i++) {
            char c = line.charAt(i);
            
            if (c == '"') {
                // 处理双引号
                if (inQuotes && i + 1 < line.length() && line.charAt(i + 1) == '"') {
                    // 转义的双引号（""表示一个双引号字符）
                    currentField.append('"');
                    i++; // 跳过下一个双引号
                } else {
                    // 切换引号状态
                    inQuotes = !inQuotes;
                }
            } else if (c == ',' && !inQuotes) {
                // 在引号外的逗号才是分隔符
                fields.add(currentField.toString().trim());
                currentField.setLength(0);
            } else {
                // 普通字符
                currentField.append(c);
            }
        }
        
        // 添加最后一个字段
        fields.add(currentField.toString().trim());
        
        return fields.toArray(new String[0]);
    }

    /**
     * 验证CSV表头
     */
    private static boolean validateHeaders(String[] headers) {
        if (headers.length < 6) {
            return false;
        }

        // 检查必需的列
        String[] requiredHeaders = { "_id", "usci", "name", "telephone", "region", "address" };
        for (int i = 0; i < requiredHeaders.length; i++) {
            if (!requiredHeaders[i].equals(headers[i].trim())) {
                return false;
            }
        }

        return true;
    }

    /**
     * 解析行政区信息
     */
    private static void parseRegionInfo(TenantConfigCsvImportDto dto) {
        String region = dto.getRegion();
        if (!StringUtils.hasText(region)) {
            return;
        }

        // 按空格分割行政区信息
        String[] parts = region.trim().split("\\s+");

        if (parts.length >= 3) {
            // 省份 城市 行政区
            dto.setProvince(parts[0]);
            dto.setCity(parts[1]);
            dto.setDistrict(parts[2]);
        } else if (parts.length == 2) {
            // 直辖市情况：省份和城市相同
            dto.setProvince(parts[0]);
            dto.setCity(parts[0]);
            dto.setDistrict(parts[1]);
        } else if (parts.length == 1) {
            // 只有一个部分，作为省份
            dto.setProvince(parts[0]);
        }
    }

    /**
     * 验证导入数据
     */
    private static void validateImportData(List<TenantConfigCsvImportDto> importData,
            Validator validator,
            List<String> errorMessages) {

        for (int i = 0; i < importData.size(); i++) {
            TenantConfigCsvImportDto dto = importData.get(i);
            Set<ConstraintViolation<TenantConfigCsvImportDto>> violations = validator.validate(dto);

            if (!violations.isEmpty()) {
                for (ConstraintViolation<TenantConfigCsvImportDto> violation : violations) {
                    String em = "第" + (i + 2) + "行数据验证失败: " + violation.getMessage();
                    log.warn("{}", em);
                    errorMessages.add(em);
                }
            }
        }
    }

    /**
     * 批量更新租户配置数据
     */
    @Transactional(rollbackFor = Exception.class)
    private static int batchUpdateTenantConfigs(List<TenantConfigCsvImportDto> importData,
            TenantRepository tenantRepository,
            TenantConfigRepository tenantConfigRepository,
            List<String> errorMessages) {

        int successCount = 0;

        for (int i = 0; i < importData.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, importData.size());
            List<TenantConfigCsvImportDto> batch = importData.subList(i, endIndex);

            log.info("处理批次 {}-{}/{}", i + 1, endIndex, importData.size());

            // 批量查询租户信息
            List<String> tenantNames = batch.stream()
                    .map(TenantConfigCsvImportDto::getName)
                    .collect(Collectors.toList());
            
            List<Tenant> tenants = tenantRepository.findByTenantNameInAndDeleted(tenantNames, 0);
            
            // 创建租户名称到租户对象的映射
             Map<String, Tenant> tenantMap = tenants.stream()
                     .collect(Collectors.toMap(Tenant::getTenantName, tenant -> tenant));

            // 需要更新的租户列表
            List<Tenant> tenantsToUpdate = new ArrayList<>();
            int batchProcessedCount = 0;
            int batchSkippedCount = 0;
            
            // 收集批次中所有需要更新的租户编码，用于重复检查
            Map<String, String> usciToTenantNameMap = new HashMap<>();
            for (TenantConfigCsvImportDto dto : batch) {
                if (StringUtils.hasText(dto.getUsci())) {
                    String existingTenantName = usciToTenantNameMap.put(dto.getUsci(), dto.getName());
                    if (existingTenantName != null) {
                        log.warn("批次中发现重复的USCI: {}, 租户: {} 和 {}", dto.getUsci(), existingTenantName, dto.getName());
                        errorMessages.add(String.format("批次中USCI重复: %s (租户: %s 和 %s)", dto.getUsci(), existingTenantName, dto.getName()));
                    }
                }
            }

            for (TenantConfigCsvImportDto dto : batch) {
                try {
                    Tenant tenant = tenantMap.get(dto.getName());

                    if (tenant == null) {
                        log.warn("批次中租户不存在，跳过处理: {}", dto.getName());
                        errorMessages.add("租户不存在: " + dto.getName());
                        batchSkippedCount++;
                        continue;
                    }

                    // 验证租户类型
                    if (!INDUSTRY_TYPE.equals(tenant.getIndustryType())
                            || !TENANT_TYPE.equals(tenant.getTenantType())) {
                        log.warn("批次中租户类型不匹配，跳过处理: {} (industryType: {}, tenantType: {})", 
                                dto.getName(), tenant.getIndustryType(), tenant.getTenantType());
                        errorMessages.add("租户类型不匹配，只能处理医美机构类型的租户: " + dto.getName());
                        batchSkippedCount++;
                        continue;
                    }

                    // 更新租户基本信息
                    boolean tenantUpdated = false;

                    // 更新租户代码（增加重复检查）
                    if (StringUtils.hasText(dto.getUsci()) &&
                            !dto.getUsci().equals(tenant.getTenantCode())) {
                        
                        // 检查新的租户编码是否已被其他租户使用
                        if (tenantRepository.existsByTenantCodeExcludingId(dto.getUsci(), tenant.getId())) {
                            log.warn("租户编码已存在，跳过更新: {} (租户: {})", dto.getUsci(), dto.getName());
                            errorMessages.add(String.format("租户编码已存在: %s (租户: %s)", dto.getUsci(), dto.getName()));
                            batchSkippedCount++;
                            continue;
                        }
                        
                        tenant.setTenantCode(dto.getUsci());
                        tenantUpdated = true;
                        log.debug("更新租户编码: {} -> {} (租户: {})", tenant.getTenantCode(), dto.getUsci(), dto.getName());
                    }

                    // 更新联系电话
                    if (StringUtils.hasText(dto.getTelephone()) &&
                            !dto.getTelephone().equals(tenant.getContactPhone())) {
                        tenant.setContactPhone(dto.getTelephone());
                        tenantUpdated = true;
                    }

                    if (tenantUpdated) {
                        tenantsToUpdate.add(tenant);
                    }

                    // 保存或更新租户配置
                    saveTenantConfigs(tenant.getId(), dto, tenantConfigRepository);

                    successCount++;
                    batchProcessedCount++;

                } catch (Exception e) {
                    log.error("批次中处理租户配置失败: {}", dto.getName(), e);
                    errorMessages.add("处理租户配置失败: " + dto.getName() + ", 错误: " + e.getMessage());
                    batchSkippedCount++;
                }
            }

            // 批量保存更新的租户信息（增加最终重复检查）
            if (!tenantsToUpdate.isEmpty()) {
                try {
                    // 最终检查：确保没有重复的租户编码
                    Set<String> tenantCodes = tenantsToUpdate.stream()
                            .map(Tenant::getTenantCode)
                            .collect(Collectors.toSet());
                    
                    if (tenantCodes.size() != tenantsToUpdate.size()) {
                        log.error("批次中存在重复的租户编码，取消批量更新");
                        errorMessages.add("批次中存在重复的租户编码，无法完成更新");
                    } else {
                        tenantRepository.saveAll(tenantsToUpdate);
                        log.info("批量更新租户信息，数量: {}", tenantsToUpdate.size());
                    }
                } catch (Exception e) {
                    log.error("批量更新租户信息失败", e);
                    errorMessages.add("批量更新租户信息失败: " + e.getMessage());
                }
            }
            
            // 批次处理汇总日志
            log.info("批次处理完成 - 总数: {}, 成功: {}, 跳过: {}", 
                    batch.size(), batchProcessedCount, batchSkippedCount);
        }

        return successCount;
    }

    /**
     * 保存租户配置信息（批量优化版本）
     */
    private static void saveTenantConfigs(Long tenantId, TenantConfigCsvImportDto dto,
            TenantConfigRepository tenantConfigRepository) {

        // 准备要保存的配置列表
        List<ConfigItem> configItems = new ArrayList<>();
        
        // 添加USCI配置
        if (StringUtils.hasText(dto.getUsci())) {
            configItems.add(new ConfigItem("business.usci", dto.getUsci(), "business", "租户（医美机构）唯一码"));
        }

        // 添加省份配置
        if (StringUtils.hasText(dto.getProvince())) {
            configItems.add(new ConfigItem("business.province", dto.getProvince(), "business", "省份"));
        }

        // 添加城市配置
        if (StringUtils.hasText(dto.getCity())) {
            configItems.add(new ConfigItem("business.city", dto.getCity(), "business", "城市"));
        }

        // 添加行政区配置
        if (StringUtils.hasText(dto.getDistrict())) {
            configItems.add(new ConfigItem("business.district", dto.getDistrict(), "business", "行政区"));
        }

        // 添加地址配置
        if (StringUtils.hasText(dto.getAddress())) {
            configItems.add(new ConfigItem("business.address", dto.getAddress(), "business", "详细地址"));
        }
        
        // 批量保存配置
        if (!configItems.isEmpty()) {
            batchSaveTenantConfigs(tenantId, configItems, tenantConfigRepository);
        }
    }
    
    /**
     * 配置项内部类
     */
    private static class ConfigItem {
        private final String configKey;
        private final String configValue;
        private final String configType;
        private final String description;
        
        public ConfigItem(String configKey, String configValue, String configType, String description) {
            this.configKey = configKey;
            this.configValue = configValue;
            this.configType = configType;
            this.description = description;
        }
        
        public String getConfigKey() { return configKey; }
        public String getConfigValue() { return configValue; }
        public String getConfigType() { return configType; }
        public String getDescription() { return description; }
    }
    
    /**
     * 批量保存租户配置
     */
    private static void batchSaveTenantConfigs(Long tenantId, List<ConfigItem> configItems,
            TenantConfigRepository tenantConfigRepository) {
        
        // 批量查询现有配置
        List<String> configKeys = configItems.stream()
                .map(ConfigItem::getConfigKey)
                .collect(Collectors.toList());
        
        List<TenantConfig> existingConfigs = tenantConfigRepository
                .findByTenantIdAndConfigKeyIn(tenantId, configKeys);
        
        // 创建现有配置的映射
         Map<String, TenantConfig> existingConfigMap = existingConfigs.stream()
                 .collect(Collectors.toMap(TenantConfig::getConfigKey, config -> config));
        
        // 准备要保存的配置列表
        List<TenantConfig> configsToSave = new ArrayList<>();
        
        for (ConfigItem item : configItems) {
            TenantConfig config = existingConfigMap.get(item.getConfigKey());
            
            if (config != null) {
                // 更新现有配置
                config.setConfigValue(item.getConfigValue());
            } else {
                // 创建新配置
                config = new TenantConfig();
                config.setTenantId(tenantId);
                config.setConfigKey(item.getConfigKey());
                config.setConfigValue(item.getConfigValue());
                config.setConfigType(item.getConfigType());
                config.setDescription(item.getDescription());
            }
            
            configsToSave.add(config);
        }
        
        // 批量保存
        if (!configsToSave.isEmpty()) {
            tenantConfigRepository.saveAll(configsToSave);
        }
    }


}