package com.dipspro.modules.agent.exception;

/**
 * 意图识别异常
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class IntentRecognitionException extends AgentException {

    private final String userMessage;

    public IntentRecognitionException(String userMessage, String message) {
        super(message);
        this.userMessage = userMessage;
    }

    public IntentRecognitionException(String userMessage, String message, Throwable cause) {
        super(message, cause);
        this.userMessage = userMessage;
    }

    public String getUserMessage() {
        return userMessage;
    }

    @Override
    public String getMessage() {
        return String.format("[UserMessage='%s'] %s", userMessage, super.getMessage());
    }
}