package com.dipspro.modules.agent.exception;

/**
 * 流程不存在异常
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class FlowNotFoundException extends AgentException {

    public FlowNotFoundException(Long flowId) {
        super("Agent flow not found: ID=" + flowId);
    }

    public FlowNotFoundException(String flowName) {
        super("Agent flow not found: " + flowName);
    }

    public FlowNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}