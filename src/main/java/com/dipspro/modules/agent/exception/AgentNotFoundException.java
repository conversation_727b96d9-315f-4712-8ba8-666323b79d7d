package com.dipspro.modules.agent.exception;

/**
 * Agent 不存在异常
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class AgentNotFoundException extends AgentException {

    public AgentNotFoundException(Long agentId) {
        super("Agent not found: ID=" + agentId);
    }

    public AgentNotFoundException(String agentName) {
        super("Agent not found: " + agentName);
    }

    public AgentNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}