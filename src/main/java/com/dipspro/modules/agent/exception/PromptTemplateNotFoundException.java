package com.dipspro.modules.agent.exception;

/**
 * 提示词模板未找到异常
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class PromptTemplateNotFoundException extends RuntimeException {

    public PromptTemplateNotFoundException(Long templateId) {
        super("提示词模板未找到: ID=" + templateId);
    }

    public PromptTemplateNotFoundException(String templateName) {
        super("提示词模板未找到: Name=" + templateName);
    }

    public PromptTemplateNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}