package com.dipspro.modules.agent.exception;

import java.util.UUID;

/**
 * Agent 执行异常
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class AgentExecutionException extends AgentException {

    private final UUID executionId;
    private final Long agentId;

    public AgentExecutionException(UUID executionId, Long agentId, String message) {
        super(message);
        this.executionId = executionId;
        this.agentId = agentId;
    }

    public AgentExecutionException(UUID executionId, Long agentId, String message, Throwable cause) {
        super(message, cause);
        this.executionId = executionId;
        this.agentId = agentId;
    }

    public UUID getExecutionId() {
        return executionId;
    }

    public Long getAgentId() {
        return agentId;
    }

    @Override
    public String getMessage() {
        return String.format("[ExecutionId=%s, AgentId=%s] %s", executionId, agentId, super.getMessage());
    }
}