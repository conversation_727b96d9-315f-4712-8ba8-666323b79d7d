package com.dipspro.modules.agent.dto;

import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Agent 编排请求
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrchestrationRequest {

    /**
     * 会话ID
     */
    private String conversationId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * Agent ID列表
     */
    private List<Long> agentIds;

    /**
     * 输入数据
     */
    private Map<String, Object> inputData;

    /**
     * 用户消息
     */
    private String userMessage;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 执行模式
     */
    private ExecutionMode executionMode;

    /**
     * 是否启用流式响应
     */
    @Builder.Default
    private Boolean streamEnabled = false;

    /**
     * 超时时间（秒）
     */
    @Builder.Default
    private Integer timeoutSeconds = 300;

    /**
     * 编排选项
     */
    private OrchestrationOptions options;

    /**
     * 上下文数据
     */
    private Map<String, Object> context;

    /**
     * 执行模式枚举
     */
    public enum ExecutionMode {
        SEQUENTIAL,  // 顺序执行
        PARALLEL,    // 并行执行
        CONDITIONAL, // 条件执行
        LOOP        // 循环执行
    }

    /**
     * 编排选项内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrchestrationOptions {
        
        /**
         * 是否启用流式响应
         */
        @Builder.Default
        private Boolean enableStreaming = false;

        /**
         * 超时时间（秒）
         */
        @Builder.Default
        private Integer timeoutSeconds = 300;

        /**
         * 最大重试次数
         */
        @Builder.Default
        private Integer maxRetries = 3;

        /**
         * 是否启用缓存
         */
        @Builder.Default
        private Boolean enableCache = true;

        /**
         * 优先级
         */
        @Builder.Default
        private Integer priority = 50;
    }
}