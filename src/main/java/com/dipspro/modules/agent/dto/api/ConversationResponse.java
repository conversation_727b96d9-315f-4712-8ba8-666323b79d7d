package com.dipspro.modules.agent.dto.api;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 对话响应对象
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ConversationResponse {
    
    /**
     * 对话 ID
     */
    private String conversationId;
    
    /**
     * 对话标题
     */
    private String title;
    
    /**
     * 对话类型
     */
    private String conversationType;
    
    /**
     * 用户 ID
     */
    private Long userId;
    
    /**
     * 对话状态
     */
    private String status;
    
    /**
     * 消息数量
     */
    private Integer messageCount;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 元数据
     */
    private Map<String, Object> metadata;
}