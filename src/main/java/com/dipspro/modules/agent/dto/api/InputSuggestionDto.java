package com.dipspro.modules.agent.dto.api;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 输入建议DTO - 对应前端InputSuggestion类型
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InputSuggestionDto {

    /**
     * 建议ID
     */
    private String id;

    /**
     * 建议文本
     */
    private String text;

    /**
     * 建议描述
     */
    private String description;

    /**
     * 图标（可选）
     */
    private String icon;

    /**
     * 分类
     */
    private String category;

    /**
     * 优先级（数字越大优先级越高）
     */
    private Integer priority;
}
