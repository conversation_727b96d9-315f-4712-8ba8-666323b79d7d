package com.dipspro.modules.agent.dto.api;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 消息响应对象
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MessageResponse {
    
    /**
     * 消息 ID
     */
    private String messageId;
    
    /**
     * 对话 ID
     */
    private String conversationId;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 消息类型
     */
    private String messageType;
    
    /**
     * 消息状态
     */
    private String status;
    
    /**
     * 发送者
     */
    private String sender;
    
    /**
     * 创建时间
     */
    private LocalDateTime timestamp;
    
    /**
     * 元数据
     */
    private Map<String, Object> metadata;
    
    /**
     * 消息状态枚举
     */
    public static class Status {
        public static final String PROCESSING = "PROCESSING";
        public static final String COMPLETED = "COMPLETED";
        public static final String FAILED = "FAILED";
        public static final String PENDING = "PENDING";
    }
}