package com.dipspro.modules.agent.dto;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import com.dipspro.modules.agent.entity.AgentDefinition;
import com.fasterxml.jackson.annotation.JsonInclude;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Agent 定义 DTO
 * 用于前后端数据传输，处理JSON序列化问题
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AgentDefinitionDto {

    private Long id;

    @NotBlank(message = "Agent名称不能为空")
    @Size(min = 2, max = 100, message = "Agent名称长度必须在2-100字符之间")
    private String name;

    @NotBlank(message = "Agent描述不能为空")
    @Size(max = 500, message = "Agent描述长度不能超过500字符")
    private String description;

    private String category;

    // 前端传来的是数组，这里用List接收
    private List<String> capabilities;

    @NotBlank(message = "系统提示不能为空")
    @Size(min = 10, message = "系统提示长度不能少于10字符")
    private String systemPrompt;

    // 前端传来的是对象，这里用Map接收
    private Map<String, Object> configuration;

    @NotNull(message = "Agent状态不能为空")
    private AgentDefinition.AgentStatus status;

    private AgentDefinition.AgentType agentType;

    private AgentDefinition.AgentRole agentRole;

    private Boolean isSystemAgent;

    private Integer priority;

    private Integer timeoutSeconds;

    private Integer maxRetries;

    private Long createdBy;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;
}