package com.dipspro.modules.agent.dto.api;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 执行状态响应对象
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExecutionStatusResponse {

    /**
     * 执行 ID
     */
    private String executionId;

    /**
     * 执行状态
     */
    private String status;

    /**
     * 进度百分比 (0.0-1.0)
     */
    private Double progress;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 执行结果
     */
    private Map<String, Object> result;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 执行指标
     */
    private ExecutionMetrics metrics;

    /**
     * 执行指标
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ExecutionMetrics {

        /**
         * 执行时长（毫秒）
         */
        private Long durationMs;

        /**
         * 使用的 Token 数
         */
        private Integer tokensUsed;

        /**
         * 执行成本
         */
        private Double cost;

        /**
         * 重试次数
         */
        private Integer retryCount;
    }
}