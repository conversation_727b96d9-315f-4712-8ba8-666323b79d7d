package com.dipspro.modules.agent.dto;

import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 链路指令 - AI生成的执行计划
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChainInstruction {

    /**
     * 意图分析结果
     */
    private String analysisResult;

    /**
     * Agent链路步骤
     */
    private List<ChainStep> agentChain;

    /**
     * 执行模式
     */
    private String executionMode; // sequential, parallel, conditional

    /**
     * 是否需要用户确认
     */
    private Boolean needUserConfirm;

    /**
     * 需要用户回答的问题
     */
    private List<String> userQuestions;

    /**
     * 预估执行时间（秒）
     */
    private Integer estimatedDurationSeconds;

    /**
     * 置信度
     */
    private Double confidence;

    /**
     * 链路描述
     */
    private String description;

    /**
     * 链路步骤
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChainStep {
        /**
         * 步骤ID
         */
        private String stepId;

        /**
         * Agent名称
         */
        private String agentName;

        /**
         * 执行顺序
         */
        private Integer order;

        /**
         * 步骤描述
         */
        private String description;

        /**
         * 步骤参数
         */
        private Map<String, Object> parameters;

        /**
         * 依赖的前置步骤
         */
        private List<String> dependencies;

        /**
         * 是否为关键步骤（失败时中断链路）
         */
        private Boolean isCritical;

        /**
         * 超时时间（秒）
         */
        private Integer timeoutSeconds;

        /**
         * 重试次数
         */
        private Integer retryCount;
    }
}
