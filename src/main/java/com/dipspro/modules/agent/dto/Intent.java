package com.dipspro.modules.agent.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 用户意图识别结果
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Intent {

    /**
     * 意图类别
     */
    private String category;

    /**
     * 置信度 (0.0 - 1.0)
     */
    private Double confidence;

    /**
     * 匹配的流程 ID
     */
    private Long flowId;

    /**
     * 流程名称
     */
    private String flowName;

    /**
     * 提取的实体
     */
    private Map<String, Object> entities;

    /**
     * 意图描述
     */
    private String description;

    /**
     * 是否需要人工确认
     */
    private Boolean requiresConfirmation;

    /**
     * 原始用户输入
     */
    private String originalInput;

    /**
     * 意图类别常量
     */
    public static class Category {
        public static final String DATA_ANALYSIS = "DATA_ANALYSIS";
        public static final String REPORT_GENERATION = "REPORT_GENERATION";
        public static final String CHART_CREATION = "CHART_CREATION";
        public static final String DATA_VISUALIZATION = "DATA_VISUALIZATION";
        public static final String QUESTION_ANSWERING = "QUESTION_ANSWERING";
        public static final String GENERAL_CHAT = "GENERAL_CHAT";
        public static final String UNKNOWN = "UNKNOWN";
    }

    /**
     * 置信度级别
     */
    public static class ConfidenceLevel {
        public static final double HIGH = 0.8;
        public static final double MEDIUM = 0.6;
        public static final double LOW = 0.4;
    }

    /**
     * 判断置信度是否足够高
     */
    public boolean isHighConfidence() {
        return confidence != null && confidence >= ConfidenceLevel.HIGH;
    }

    /**
     * 判断置信度是否中等
     */
    public boolean isMediumConfidence() {
        return confidence != null && confidence >= ConfidenceLevel.MEDIUM && confidence < ConfidenceLevel.HIGH;
    }

    /**
     * 判断置信度是否较低
     */
    public boolean isLowConfidence() {
        return confidence != null && confidence < ConfidenceLevel.MEDIUM;
    }

    /**
     * 创建高置信度意图
     */
    public static Intent highConfidence(String category, Long flowId, String flowName) {
        return Intent.builder()
                .category(category)
                .confidence(0.95)
                .flowId(flowId)
                .flowName(flowName)
                .requiresConfirmation(false)
                .build();
    }

    /**
     * 创建低置信度意图（需要确认）
     */
    public static Intent lowConfidence(String category, String description) {
        return Intent.builder()
                .category(category)
                .confidence(0.3)
                .description(description)
                .requiresConfirmation(true)
                .build();
    }

    /**
     * 创建未知意图
     */
    public static Intent unknown(String originalInput) {
        return Intent.builder()
                .category(Category.UNKNOWN)
                .confidence(0.1)
                .description("无法识别用户意图")
                .originalInput(originalInput)
                .requiresConfirmation(true)
                .build();
    }
}