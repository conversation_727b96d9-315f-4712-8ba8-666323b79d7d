package com.dipspro.modules.agent.dto.api;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

/**
 * SSE 事件对象
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SseEvent {
    
    /**
     * 事件类型
     */
    private String eventType;
    
    /**
     * 对话 ID
     */
    private String conversationId;
    
    /**
     * Agent 名称
     */
    private String agentName;
    
    /**
     * 步骤 ID
     */
    private String stepId;
    
    /**
     * 时间戳
     */
    private LocalDateTime timestamp;
    
    /**
     * 事件数据
     */
    private Map<String, Object> data;
    
    /**
     * 事件类型常量
     */
    public static class EventType {
        public static final String CONNECTED = "CONNECTED";
        public static final String DISCONNECTED = "DISCONNECTED";
        public static final String HEARTBEAT = "HEARTBEAT";
        public static final String MESSAGE_RECEIVED = "MESSAGE_RECEIVED";
        public static final String AGENT_THINKING = "AGENT_THINKING";
        public static final String AGENT_PROGRESS = "AGENT_PROGRESS";
        public static final String AGENT_COMPLETED = "AGENT_COMPLETED";
        public static final String AGENT_FAILED = "AGENT_FAILED";
        public static final String ORCHESTRATION_STARTED = "ORCHESTRATION_STARTED";
        public static final String ORCHESTRATION_PROGRESS = "ORCHESTRATION_PROGRESS";
        public static final String ORCHESTRATION_COMPLETED = "ORCHESTRATION_COMPLETED";
        public static final String ORCHESTRATION_FAILED = "ORCHESTRATION_FAILED";
        public static final String ERROR = "ERROR";
    }
    
    /**
     * 创建连接事件
     */
    public static SseEvent connected(String conversationId) {
        return SseEvent.builder()
            .eventType(EventType.CONNECTED)
            .conversationId(conversationId)
            .timestamp(LocalDateTime.now())
            .data(Map.of(
                "status", "connected",
                "message", "成功连接到对话流"
            ))
            .build();
    }
    
    /**
     * 创建心跳事件
     */
    public static SseEvent heartbeat() {
        return SseEvent.builder()
            .eventType(EventType.HEARTBEAT)
            .timestamp(LocalDateTime.now())
            .data(Map.of("timestamp", System.currentTimeMillis()))
            .build();
    }
    
    /**
     * 创建消息接收事件
     */
    public static SseEvent messageReceived(String conversationId, String messageId, String content) {
        return SseEvent.builder()
            .eventType(EventType.MESSAGE_RECEIVED)
            .conversationId(conversationId)
            .timestamp(LocalDateTime.now())
            .data(Map.of(
                "messageId", messageId,
                "content", content,
                "status", "received"
            ))
            .build();
    }
    
    /**
     * 创建 Agent 思考事件
     */
    public static SseEvent agentThinking(String conversationId, String agentName, String thought) {
        return SseEvent.builder()
            .eventType(EventType.AGENT_THINKING)
            .conversationId(conversationId)
            .agentName(agentName)
            .timestamp(LocalDateTime.now())
            .data(Map.of(
                "thought", thought,
                "status", "thinking"
            ))
            .build();
    }
    
    /**
     * 创建 Agent 进度事件
     */
    public static SseEvent agentProgress(String conversationId, String agentName, int progress, String message) {
        return SseEvent.builder()
            .eventType(EventType.AGENT_PROGRESS)
            .conversationId(conversationId)
            .agentName(agentName)
            .timestamp(LocalDateTime.now())
            .data(Map.of(
                "progress", progress,
                "message", message,
                "status", "progress"
            ))
            .build();
    }
    
    /**
     * 创建 Agent 完成事件
     */
    public static SseEvent agentCompleted(String conversationId, String agentName, String result) {
        return SseEvent.builder()
            .eventType(EventType.AGENT_COMPLETED)
            .conversationId(conversationId)
            .agentName(agentName)
            .timestamp(LocalDateTime.now())
            .data(Map.of(
                "result", result,
                "status", "completed"
            ))
            .build();
    }
    
    /**
     * 创建编排开始事件
     */
    public static SseEvent orchestrationStarted(String conversationId, String flowName) {
        return SseEvent.builder()
            .eventType(EventType.ORCHESTRATION_STARTED)
            .conversationId(conversationId)
            .timestamp(LocalDateTime.now())
            .data(Map.of(
                "flowName", flowName,
                "status", "started",
                "message", "开始执行智能处理流程"
            ))
            .build();
    }
    
    /**
     * 创建编排进度事件
     */
    public static SseEvent orchestrationProgress(String conversationId, int currentStep, int totalSteps, String stepName) {
        return SseEvent.builder()
            .eventType(EventType.ORCHESTRATION_PROGRESS)
            .conversationId(conversationId)
            .timestamp(LocalDateTime.now())
            .data(Map.of(
                "currentStep", currentStep,
                "totalSteps", totalSteps,
                "stepName", stepName,
                "progress", totalSteps > 0 ? (double) currentStep / totalSteps * 100 : 0,
                "status", "progress"
            ))
            .build();
    }
    
    /**
     * 创建编排完成事件
     */
    public static SseEvent orchestrationCompleted(String conversationId, String finalResult) {
        return SseEvent.builder()
            .eventType(EventType.ORCHESTRATION_COMPLETED)
            .conversationId(conversationId)
            .timestamp(LocalDateTime.now())
            .data(Map.of(
                "finalResult", finalResult,
                "status", "completed",
                "message", "智能处理流程执行完成"
            ))
            .build();
    }
    
    /**
     * 创建错误事件
     */
    public static SseEvent error(String conversationId, String errorMessage) {
        return SseEvent.builder()
            .eventType(EventType.ERROR)
            .conversationId(conversationId)
            .timestamp(LocalDateTime.now())
            .data(Map.of(
                "error", errorMessage,
                "status", "error"
            ))
            .build();
    }
}