package com.dipspro.modules.agent.dto.api;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Size;
import java.util.Map;

/**
 * 创建对话请求对象
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateConversationRequest {
    
    /**
     * 对话标题
     */
    @Size(max = 200, message = "对话标题不能超过200字符")
    private String title;
    
    /**
     * 对话类型
     */
    @Builder.Default
    private String conversationType = "AGENT_CHAT";
    
    /**
     * 元数据
     */
    private Map<String, Object> metadata;
}