package com.dipspro.modules.agent.dto;

import java.util.Map;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 链式执行请求 - 启动AI驱动链路的请求参数
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChainExecutionRequest {

    /**
     * 用户输入
     */
    @NotBlank(message = "用户输入不能为空")
    private String userInput;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 会话ID（可选，如果不提供会自动生成）
     */
    private String conversationId;

    /**
     * 执行选项
     */
    private ExecutionOptions options;

    /**
     * 上下文数据
     */
    private Map<String, Object> context;

    /**
     * 执行选项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExecutionOptions {
        
        /**
         * 是否启用思维链
         */
        private Boolean enableThinkingChain;

        /**
         * 是否启用流式响应
         */
        private Boolean enableStreaming;

        /**
         * 最大链路长度
         */
        private Integer maxChainLength;

        /**
         * 单步超时时间（秒）
         */
        private Integer stepTimeoutSeconds;

        /**
         * 是否需要用户确认
         */
        private Boolean requireUserConfirm;

        /**
         * 优先级
         */
        private Integer priority;

        /**
         * 默认选项
         */
        public static ExecutionOptions defaultOptions() {
            return ExecutionOptions.builder()
                    .enableThinkingChain(true)
                    .enableStreaming(true)
                    .maxChainLength(10)
                    .stepTimeoutSeconds(30)
                    .requireUserConfirm(false)
                    .priority(5)
                    .build();
        }

        /**
         * 快速响应选项
         */
        public static ExecutionOptions fastOptions() {
            return ExecutionOptions.builder()
                    .enableThinkingChain(false)
                    .enableStreaming(true)
                    .maxChainLength(5)
                    .stepTimeoutSeconds(15)
                    .requireUserConfirm(false)
                    .priority(10)
                    .build();
        }

        /**
         * 详细分析选项
         */
        public static ExecutionOptions detailedOptions() {
            return ExecutionOptions.builder()
                    .enableThinkingChain(true)
                    .enableStreaming(true)
                    .maxChainLength(15)
                    .stepTimeoutSeconds(60)
                    .requireUserConfirm(true)
                    .priority(1)
                    .build();
        }
    }
}