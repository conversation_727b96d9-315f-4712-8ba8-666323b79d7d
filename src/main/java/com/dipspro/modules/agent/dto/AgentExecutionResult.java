package com.dipspro.modules.agent.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

import com.dipspro.modules.agent.entity.AgentExecution.ExecutionStatus;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Agent 执行结果对象
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentExecutionResult {

    /**
     * 执行 ID
     */
    private UUID executionId;

    /**
     * Agent ID
     */
    private Long agentId;

    /**
     * Agent 名称
     */
    private String agentName;

    /**
     * Agent 角色（用于全AI Agent架构）
     */
    private com.dipspro.modules.agent.entity.AgentDefinition.AgentRole agentRole;

    /**
     * 执行状态
     */
    private ExecutionStatus status;

    /**
     * 输入数据
     */
    private Map<String, Object> inputData;

    /**
     * 输出数据
     */
    private Map<String, Object> outputData;

    /**
     * 响应内容
     */
    private String content;

    /**
     * 思维链
     */
    private String chainOfThought;

    /**
     * 执行指标
     */
    private ExecutionMetrics metrics;

    /**
     * 链路指令（用于意图分析Agent）
     */
    private Map<String, Object> chainInstruction;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 执行指标类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ExecutionMetrics {

        /**
         * 总执行时间（毫秒）
         */
        private Long durationMs;

        /**
         * 使用的 Token 数
         */
        private Integer tokensUsed;

        /**
         * 执行成本
         */
        private BigDecimal cost;

        /**
         * AI 调用次数
         */
        private Integer aiCallCount;

        /**
         * 重试次数
         */
        private Integer retryCount;

        /**
         * 缓存命中
         */
        private Boolean cacheHit;
    }

    /**
     * 判断执行是否成功
     */
    public boolean isSuccessful() {
        return ExecutionStatus.COMPLETED.equals(status);
    }

    /**
     * 判断执行是否失败
     */
    public boolean isFailed() {
        return ExecutionStatus.FAILED.equals(status);
    }

    /**
     * 获取执行时长（毫秒）
     */
    public Long getDurationMs() {
        if (metrics != null && metrics.getDurationMs() != null) {
            return metrics.getDurationMs();
        }

        if (startTime != null && endTime != null) {
            return java.time.Duration.between(startTime, endTime).toMillis();
        }

        return null;
    }
}