package com.dipspro.modules.agent.dto;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 链路执行结果
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChainExecutionResult {

    /**
     * 会话ID
     */
    private String conversationId;

    /**
     * 执行状态
     */
    private ExecutionStatus status;

    /**
     * 最终输出内容
     */
    private String finalContent;

    /**
     * 步骤执行结果列表
     */
    private List<StepResult> stepResults;

    /**
     * 执行总结
     */
    private String summary;

    /**
     * 执行指标
     */
    private ExecutionMetrics metrics;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 附加数据
     */
    private Map<String, Object> metadata;

    /**
     * 执行状态
     */
    public enum ExecutionStatus {
        SUCCESS,    // 成功
        PARTIAL,    // 部分成功
        FAILED,     // 失败
        TIMEOUT,    // 超时
        CANCELLED   // 取消
    }

    /**
     * 步骤执行结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StepResult {
        private String stepId;
        private String agentName;
        private String status;
        private String content;
        private Long durationMs;
        private String errorMessage;
        private Map<String, Object> outputData;
    }

    /**
     * 执行指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExecutionMetrics {
        private Long totalDurationMs;
        private Integer totalSteps;
        private Integer successfulSteps;
        private Integer failedSteps;
        private Integer totalTokensUsed;
        private Integer totalModelCalls;
        private Double averageStepDuration;
        private Double successRate;
    }

    /**
     * 检查是否成功
     */
    public boolean isSuccessful() {
        return ExecutionStatus.SUCCESS.equals(status);
    }

    /**
     * 检查是否部分成功
     */
    public boolean isPartiallySuccessful() {
        return ExecutionStatus.PARTIAL.equals(status);
    }

    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        if (metrics != null && metrics.getSuccessRate() != null) {
            return metrics.getSuccessRate();
        }
        if (stepResults == null || stepResults.isEmpty()) {
            return 0.0;
        }
        long successCount = stepResults.stream()
                .mapToLong(step -> "SUCCESS".equals(step.getStatus()) ? 1 : 0)
                .sum();
        return (double) successCount / stepResults.size();
    }
}
