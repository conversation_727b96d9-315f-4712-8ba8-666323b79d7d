package com.dipspro.modules.agent.dto;

import java.time.LocalDateTime;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Agent 编排结果
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrchestrationResult {

    /**
     * 会话ID
     */
    private String conversationId;

    /**
     * 流程ID
     */
    private String flowId;

    /**
     * 流程名称
     */
    private String flowName;

    /**
     * 执行状态
     */
    private String status;

    /**
     * 是否成功
     */
    private Boolean successful;

    /**
     * 最终结果
     */
    private AgentExecutionResult finalResult;

    /**
     * 所有步骤结果
     */
    private List<AgentExecutionResult> stepResults;

    /**
     * 执行时长（毫秒）
     */
    private Long executionTime;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 已执行步骤数
     */
    private Integer executedSteps;

    /**
     * 执行统计
     */
    private ExecutionStats stats;

    /**
     * 执行指标（别名）
     */
    public ExecutionStats getMetrics() {
        return stats;
    }

    /**
     * 执行统计内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExecutionStats {
        
        /**
         * 总Agent数
         */
        private Integer totalAgents;

        /**
         * 成功Agent数
         */
        private Integer successfulAgents;

        /**
         * 失败Agent数
         */
        private Integer failedAgents;

        /**
         * 总Token使用量
         */
        private Integer totalTokens;

        /**
         * 平均执行时间
         */
        private Long averageExecutionTime;

        /**
         * 总执行时长
         */
        private Long totalDurationMs;
    }

    /**
     * 判断执行是否成功
     */
    public boolean isSuccessful() {
        return Boolean.TRUE.equals(successful);
    }

    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        if (stats == null || stats.getTotalAgents() == null || stats.getTotalAgents() == 0) {
            return 0.0;
        }
        return (double) stats.getSuccessfulAgents() / stats.getTotalAgents();
    }
}