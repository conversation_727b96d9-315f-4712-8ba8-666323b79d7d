package com.dipspro.modules.agent.dto.api;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.util.Map;

/**
 * 执行 Agent 请求对象
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExecuteAgentRequest {

    /**
     * Agent ID
     */
    @NotNull(message = "Agent ID 不能为空")
    private Long agentId;

    /**
     * 输入数据
     */
    private Map<String, Object> inputData;

    /**
     * 执行选项
     */
    private ExecutionOptions options;

    /**
     * 执行选项配置
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ExecutionOptions {

        /**
         * 执行超时时间（秒）
         */
        @Builder.Default
        private Integer timeoutSeconds = 30;

        /**
         * 最大重试次数
         */
        @Builder.Default
        private Integer maxRetries = 3;

        /**
         * 是否启用缓存
         */
        @Builder.Default
        private Boolean enableCache = true;

        /**
         * 是否启用流式输出
         */
        @Builder.Default
        private Boolean enableStreaming = true;

        /**
         * 自定义参数
         */
        private Map<String, String> parameters;
    }
}