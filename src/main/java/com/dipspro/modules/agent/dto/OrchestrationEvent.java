package com.dipspro.modules.agent.dto;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Agent 编排事件对象 - 用于流式编排输出
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrchestrationEvent {
    
    /**
     * 事件类型
     */
    private String eventType;
    
    /**
     * 编排 ID
     */
    private UUID orchestrationId;
    
    /**
     * 流程 ID
     */
    private Long flowId;
    
    /**
     * 流程名称
     */
    private String flowName;
    
    /**
     * 当前步骤 ID
     */
    private String stepId;
    
    /**
     * 当前步骤名称
     */
    private String stepName;
    
    /**
     * Agent ID
     */
    private Long agentId;
    
    /**
     * Agent 名称
     */
    private String agentName;
    
    /**
     * 事件数据
     */
    private Map<String, Object> data;
    
    /**
     * 时间戳
     */
    private LocalDateTime timestamp;
    
    /**
     * 事件状态
     */
    private String status;
    
    /**
     * 进度百分比 (0-100)
     */
    private Integer progress;
    
    /**
     * 事件类型常量
     */
    public static class EventType {
        public static final String ORCHESTRATION_STARTED = "ORCHESTRATION_STARTED";
        public static final String INTENT_RECOGNIZED = "INTENT_RECOGNIZED";
        public static final String FLOW_MATCHED = "FLOW_MATCHED";
        public static final String STEP_STARTED = "STEP_STARTED";
        public static final String STEP_THINKING = "STEP_THINKING";
        public static final String STEP_COMPLETED = "STEP_COMPLETED";
        public static final String STEP_FAILED = "STEP_FAILED";
        public static final String ORCHESTRATION_PROGRESS = "ORCHESTRATION_PROGRESS";
        public static final String ORCHESTRATION_COMPLETED = "ORCHESTRATION_COMPLETED";
        public static final String ORCHESTRATION_FAILED = "ORCHESTRATION_FAILED";
    }
    
    /**
     * 状态常量
     */
    public static class Status {
        public static final String PENDING = "PENDING";
        public static final String RUNNING = "RUNNING";
        public static final String COMPLETED = "COMPLETED";
        public static final String FAILED = "FAILED";
        public static final String CANCELLED = "CANCELLED";
    }
    
    /**
     * 创建编排开始事件
     */
    public static OrchestrationEvent orchestrationStarted(UUID orchestrationId, String userMessage) {
        return OrchestrationEvent.builder()
            .eventType(EventType.ORCHESTRATION_STARTED)
            .orchestrationId(orchestrationId)
            .timestamp(LocalDateTime.now())
            .status(Status.RUNNING)
            .progress(0)
            .data(Map.of("userMessage", userMessage))
            .build();
    }
    
    /**
     * 创建意图识别事件
     */
    public static OrchestrationEvent intentRecognized(UUID orchestrationId, Intent intent) {
        return OrchestrationEvent.builder()
            .eventType(EventType.INTENT_RECOGNIZED)
            .orchestrationId(orchestrationId)
            .timestamp(LocalDateTime.now())
            .status(Status.RUNNING)
            .progress(20)
            .data(Map.of(
                "intentCategory", intent.getCategory(),
                "confidence", intent.getConfidence(),
                "requiresConfirmation", intent.getRequiresConfirmation()
            ))
            .build();
    }
    
    /**
     * 创建流程匹配事件
     */
    public static OrchestrationEvent flowMatched(UUID orchestrationId, Long flowId, String flowName) {
        return OrchestrationEvent.builder()
            .eventType(EventType.FLOW_MATCHED)
            .orchestrationId(orchestrationId)
            .flowId(flowId)
            .flowName(flowName)
            .timestamp(LocalDateTime.now())
            .status(Status.RUNNING)
            .progress(30)
            .data(Map.of("message", "已匹配到合适的处理流程"))
            .build();
    }
    
    /**
     * 创建步骤开始事件
     */
    public static OrchestrationEvent stepStarted(UUID orchestrationId, String stepId, String stepName, 
                                               Long agentId, String agentName) {
        return OrchestrationEvent.builder()
            .eventType(EventType.STEP_STARTED)
            .orchestrationId(orchestrationId)
            .stepId(stepId)
            .stepName(stepName)
            .agentId(agentId)
            .agentName(agentName)
            .timestamp(LocalDateTime.now())
            .status(Status.RUNNING)
            .data(Map.of("message", "开始执行步骤: " + stepName))
            .build();
    }
    
    /**
     * 创建步骤思考事件
     */
    public static OrchestrationEvent stepThinking(UUID orchestrationId, String stepId, String thought) {
        return OrchestrationEvent.builder()
            .eventType(EventType.STEP_THINKING)
            .orchestrationId(orchestrationId)
            .stepId(stepId)
            .timestamp(LocalDateTime.now())
            .status(Status.RUNNING)
            .data(Map.of("thought", thought))
            .build();
    }
    
    /**
     * 创建步骤完成事件
     */
    public static OrchestrationEvent stepCompleted(UUID orchestrationId, String stepId, String stepName, 
                                                 AgentExecutionResult result) {
        return OrchestrationEvent.builder()
            .eventType(EventType.STEP_COMPLETED)
            .orchestrationId(orchestrationId)
            .stepId(stepId)
            .stepName(stepName)
            .agentId(result.getAgentId())
            .agentName(result.getAgentName())
            .timestamp(LocalDateTime.now())
            .status(Status.COMPLETED)
            .data(Map.of(
                "executionResult", result.getContent(),
                "duration", result.getDurationMs(),
                "status", result.getStatus().toString()
            ))
            .build();
    }
    
    /**
     * 创建步骤失败事件
     */
    public static OrchestrationEvent stepFailed(UUID orchestrationId, String stepId, String stepName, 
                                              String errorMessage) {
        return OrchestrationEvent.builder()
            .eventType(EventType.STEP_FAILED)
            .orchestrationId(orchestrationId)
            .stepId(stepId)
            .stepName(stepName)
            .timestamp(LocalDateTime.now())
            .status(Status.FAILED)
            .data(Map.of("error", errorMessage))
            .build();
    }
    
    /**
     * 创建编排进度事件
     */
    public static OrchestrationEvent orchestrationProgress(UUID orchestrationId, int progress, String message) {
        return OrchestrationEvent.builder()
            .eventType(EventType.ORCHESTRATION_PROGRESS)
            .orchestrationId(orchestrationId)
            .timestamp(LocalDateTime.now())
            .status(Status.RUNNING)
            .progress(progress)
            .data(Map.of("message", message))
            .build();
    }
    
    /**
     * 创建编排完成事件
     */
    public static OrchestrationEvent orchestrationCompleted(UUID orchestrationId, OrchestrationResult result) {
        return OrchestrationEvent.builder()
            .eventType(EventType.ORCHESTRATION_COMPLETED)
            .orchestrationId(orchestrationId)
            .flowId(result.getFlowId() != null ? Long.parseLong(result.getFlowId()) : null)
            .flowName(result.getFlowName())
            .timestamp(LocalDateTime.now())
            .status(Status.COMPLETED)
            .progress(100)
            .data(Map.of(
                "finalResult", result.getFinalResult(),
                "executedSteps", result.getExecutedSteps() != null ? result.getExecutedSteps() : 0,
                "totalDuration", result.getMetrics() != null ? result.getMetrics().getTotalDurationMs() : 0
            ))
            .build();
    }
    
    /**
     * 创建编排失败事件
     */
    public static OrchestrationEvent orchestrationFailed(UUID orchestrationId, String errorMessage) {
        return OrchestrationEvent.builder()
            .eventType(EventType.ORCHESTRATION_FAILED)
            .orchestrationId(orchestrationId)
            .timestamp(LocalDateTime.now())
            .status(Status.FAILED)
            .progress(-1)
            .data(Map.of("error", errorMessage))
            .build();
    }
    
    /**
     * 从 Agent 执行事件转换为编排事件
     */
    public static OrchestrationEvent fromAgentEvent(UUID orchestrationId, String stepId, 
                                                   AgentExecutionEvent agentEvent) {
        String orchestrationEventType;
        switch (agentEvent.getEventType()) {
            case AgentExecutionEvent.EventType.EXECUTION_STARTED:
                orchestrationEventType = EventType.STEP_STARTED;
                break;
            case AgentExecutionEvent.EventType.EXECUTION_THINKING:
                orchestrationEventType = EventType.STEP_THINKING;
                break;
            case AgentExecutionEvent.EventType.EXECUTION_COMPLETED:
                orchestrationEventType = EventType.STEP_COMPLETED;
                break;
            case AgentExecutionEvent.EventType.EXECUTION_FAILED:
                orchestrationEventType = EventType.STEP_FAILED;
                break;
            default:
                orchestrationEventType = EventType.ORCHESTRATION_PROGRESS;
        }
        
        return OrchestrationEvent.builder()
            .eventType(orchestrationEventType)
            .orchestrationId(orchestrationId)
            .stepId(stepId)
            .agentName(agentEvent.getAgentName())
            .timestamp(agentEvent.getTimestamp())
            .status(agentEvent.getStatus())
            .data(agentEvent.getData())
            .build();
    }
}