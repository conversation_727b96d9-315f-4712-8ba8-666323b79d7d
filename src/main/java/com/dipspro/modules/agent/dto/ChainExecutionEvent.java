package com.dipspro.modules.agent.dto;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 链路执行事件 - SSE 流式响应数据模型
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChainExecutionEvent {

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 事件ID
     */
    private String eventId;

    /**
     * 会话ID
     */
    private String conversationId;

    /**
     * 时间戳
     */
    private LocalDateTime timestamp;

    /**
     * 步骤信息
     */
    private StepInfo stepInfo;

    /**
     * 思维过程
     */
    private ThinkingProcess thinkingProcess;

    /**
     * 执行结果
     */
    private ExecutionResult result;

    /**
     * 执行指标
     */
    private ExecutionMetrics executionMetrics;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 附加数据
     */
    private Map<String, Object> metadata;

    /**
     * 步骤信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StepInfo {
        private String stepId;
        private String agentName;
        private String stepDescription;
        private Integer order;
        private String status; // started, running, completed, failed
    }

    /**
     * 思维过程
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ThinkingProcess {
        private String analysis;
        private String approach;
        private List<String> keyFindings;
        private Double confidence;
        private String reasoning;
        private Map<String, Object> intermediateData;
    }

    /**
     * 执行结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExecutionResult {
        private String content;
        private Map<String, Object> dataSummary;
        private String resultType; // content, data_analysis, chart, report, etc.
        private Boolean isSuccessful;
    }

    /**
     * 执行指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExecutionMetrics {
        private Long durationMs;
        private Integer tokensUsed;
        private Integer modelCalls;
        private String modelName;
    }

    // 静态工厂方法

    /**
     * 创建开始事件
     */
    public static ChainExecutionEvent started(String conversationId, String userInput) {
        return ChainExecutionEvent.builder()
                .eventType("execution_started")
                .eventId(generateEventId("started"))
                .conversationId(conversationId)
                .timestamp(LocalDateTime.now())
                .metadata(Map.of("userInput", userInput))
                .build();
    }

    /**
     * 创建步骤开始事件
     */
    public static ChainExecutionEvent stepStarted(String stepId, String description) {
        return ChainExecutionEvent.builder()
                .eventType("step_started")
                .eventId(generateEventId("step_started"))
                .timestamp(LocalDateTime.now())
                .stepInfo(StepInfo.builder()
                        .stepId(stepId)
                        .stepDescription(description)
                        .status("started")
                        .build())
                .build();
    }

    /**
     * 创建步骤完成事件
     */
    public static ChainExecutionEvent stepCompleted(String stepId, String description, Object result) {
        return ChainExecutionEvent.builder()
                .eventType("step_completed")
                .eventId(generateEventId("step_completed"))
                .timestamp(LocalDateTime.now())
                .stepInfo(StepInfo.builder()
                        .stepId(stepId)
                        .stepDescription(description)
                        .status("completed")
                        .build())
                .result(ExecutionResult.builder()
                        .content(result.toString())
                        .isSuccessful(true)
                        .build())
                .build();
    }

    /**
     * 创建思维链事件
     */
    public static ChainExecutionEvent thinking(String conversationId, String stepId, 
                                             ThinkingProcess thinking) {
        return ChainExecutionEvent.builder()
                .eventType("thinking_chain")
                .eventId(generateEventId("thinking"))
                .conversationId(conversationId)
                .timestamp(LocalDateTime.now())
                .stepInfo(StepInfo.builder()
                        .stepId(stepId)
                        .status("thinking")
                        .build())
                .thinkingProcess(thinking)
                .build();
    }

    /**
     * 创建完成事件
     */
    public static ChainExecutionEvent completed(String conversationId) {
        return ChainExecutionEvent.builder()
                .eventType("execution_completed")
                .eventId(generateEventId("completed"))
                .conversationId(conversationId)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 创建错误事件
     */
    public static ChainExecutionEvent error(String conversationId, String errorMessage) {
        return ChainExecutionEvent.builder()
                .eventType("execution_error")
                .eventId(generateEventId("error"))
                .conversationId(conversationId)
                .timestamp(LocalDateTime.now())
                .errorMessage(errorMessage)
                .build();
    }

    /**
     * 生成事件ID
     */
    private static String generateEventId(String type) {
        return type + "_" + System.currentTimeMillis() + "_" + 
               (int)(Math.random() * 1000);
    }
}
