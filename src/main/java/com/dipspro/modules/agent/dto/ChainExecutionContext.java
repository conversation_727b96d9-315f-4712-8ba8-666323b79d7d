package com.dipspro.modules.agent.dto;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 链路执行上下文 - 运行时状态管理
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChainExecutionContext {

    /**
     * 执行指令
     */
    private ChainInstruction instruction;

    /**
     * 原始用户输入
     */
    private String originalInput;

    /**
     * 会话ID
     */
    private String conversationId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 步骤执行结果
     */
    @Builder.Default
    private Map<String, AgentExecutionResult> stepResults = new HashMap<>();

    /**
     * 当前传递的数据
     */
    private String currentData;

    /**
     * 全局上下文数据
     */
    @Builder.Default
    private Map<String, Object> globalContext = new HashMap<>();

    /**
     * 执行开始时间
     */
    private LocalDateTime startTime;

    /**
     * 执行结束时间
     */
    private LocalDateTime endTime;

    /**
     * 当前步骤索引
     */
    private Integer currentStepIndex;

    /**
     * 总步骤数
     */
    private Integer totalSteps;

    /**
     * 执行状态
     */
    private ExecutionStatus status;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 执行状态枚举
     */
    public enum ExecutionStatus {
        PENDING,    // 待执行
        RUNNING,    // 执行中
        COMPLETED,  // 已完成
        FAILED,     // 执行失败
        CANCELLED   // 已取消
    }

    /**
     * 添加步骤结果
     */
    public void addStepResult(String stepId, AgentExecutionResult result) {
        stepResults.put(stepId, result);
        if (result.isSuccessful()) {
            currentData = result.getContent();
        }
    }

    /**
     * 获取步骤结果
     */
    public AgentExecutionResult getStepResult(String stepId) {
        return stepResults.get(stepId);
    }

    /**
     * 检查是否已完成
     */
    public boolean isCompleted() {
        return ExecutionStatus.COMPLETED.equals(status) || 
               ExecutionStatus.FAILED.equals(status) || 
               ExecutionStatus.CANCELLED.equals(status);
    }

    /**
     * 检查是否成功
     */
    public boolean isSuccessful() {
        return ExecutionStatus.COMPLETED.equals(status);
    }

    /**
     * 获取执行进度百分比
     */
    public int getProgressPercentage() {
        if (totalSteps == null || totalSteps == 0) {
            return 0;
        }
        if (currentStepIndex == null) {
            return 0;
        }
        return (int) ((currentStepIndex * 100.0) / totalSteps);
    }

    /**
     * 设置全局上下文数据
     */
    public void setGlobalContextData(String key, Object value) {
        globalContext.put(key, value);
    }

    /**
     * 获取全局上下文数据
     */
    public Object getGlobalContextData(String key) {
        return globalContext.get(key);
    }
}
