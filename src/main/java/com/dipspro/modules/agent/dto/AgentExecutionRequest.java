package com.dipspro.modules.agent.dto;

import java.time.Duration;
import java.util.Map;
import java.util.UUID;

import com.dipspro.modules.agent.entity.AgentDefinition;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Agent 执行请求对象
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentExecutionRequest {

    /**
     * Agent ID
     */
    private Long agentId;
    private String agentName;

    /**
     * Agent 角色（用于全AI Agent架构）
     */
    private AgentDefinition.AgentRole agentRole;

    /**
     * 会话 ID
     */
    private String conversationId;

    /**
     * 消息 ID
     */
    private UUID messageId;

    /**
     * 步骤 ID
     */
    private String stepId;

    /**
     * 执行 ID
     */
    private UUID executionId;

    /**
     * 用户 ID
     */
    private Long userId;

    /**
     * 输入数据
     */
    private Map<String, Object> inputData;

    /**
     * 执行选项
     */
    private ExecutionOptions options;

    /**
     * 执行选项配置
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ExecutionOptions {

        /**
         * 执行超时时间
         */
        private Duration timeout;

        /**
         * 最大重试次数
         */
        private Integer maxRetries;

        /**
         * 是否启用缓存
         */
        private Boolean enableCache;

        /**
         * 自定义参数
         */
        private Map<String, String> parameters;

        /**
         * 是否启用流式输出
         */
        private Boolean enableStreaming;

        /**
         * 获取默认执行选项
         */
        public static ExecutionOptions defaultOptions() {
            return ExecutionOptions.builder()
                    .timeout(Duration.ofSeconds(30))
                    .maxRetries(3)
                    .enableCache(true)
                    .enableStreaming(false)
                    .build();
        }
    }
}