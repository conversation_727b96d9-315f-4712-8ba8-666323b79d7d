package com.dipspro.modules.agent.entity;

import java.time.LocalDateTime;
import java.util.UUID;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Agent 流程步骤实体
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "agent_flow_step")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentFlowStep {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 执行ID（关联 AgentFlow）
     */
    @Column(name = "execution_id", nullable = false)
    private UUID executionId;

    /**
     * 步骤ID（在执行中唯一）
     */
    @Column(name = "step_id", nullable = false, length = 100)
    private String stepId;

    /**
     * 步骤名称
     */
    @Column(name = "step_name", nullable = false, length = 255)
    private String stepName;

    /**
     * 步骤描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    /**
     * Agent ID
     */
    @Column(name = "agent_id", nullable = false)
    private Long agentId;

    /**
     * Agent 名称
     */
    @Column(name = "agent_name", length = 255)
    private String agentName;

    /**
     * 步骤顺序
     */
    @Column(name = "step_order", nullable = false)
    private Integer stepOrder;

    /**
     * 步骤状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    @Builder.Default
    private StepStatus status = StepStatus.PENDING;

    /**
     * 输入数据
     */
    @Column(name = "input_data", columnDefinition = "TEXT")
    private String inputData;

    /**
     * 输出结果
     */
    @Column(name = "output_result", columnDefinition = "TEXT")
    private String outputResult;

    /**
     * 思维过程（AI思考链）
     */
    @Column(name = "thinking_process", columnDefinition = "TEXT")
    private String thinkingProcess;

    /**
     * 错误信息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    /**
     * 执行开始时间
     */
    @Column(name = "started_at")
    private LocalDateTime startedAt;

    /**
     * 执行结束时间
     */
    @Column(name = "completed_at")
    private LocalDateTime completedAt;

    /**
     * 执行时长（毫秒）
     */
    @Column(name = "duration_ms")
    private Long durationMs;

    /**
     * 重试次数
     */
    @Column(name = "retry_count")
    @Builder.Default
    private Integer retryCount = 0;

    /**
     * 最大重试次数
     */
    @Column(name = "max_retries")
    @Builder.Default
    private Integer maxRetries = 3;

    /**
     * 依赖的步骤ID列表（JSON格式）
     */
    @Column(name = "dependencies", columnDefinition = "TEXT")
    private String dependencies;

    /**
     * 执行参数（JSON格式）
     */
    @Column(name = "execution_params", columnDefinition = "TEXT")
    private String executionParams;

    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    @Builder.Default
    private LocalDateTime createdAt = LocalDateTime.now();

    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    @Builder.Default
    private LocalDateTime updatedAt = LocalDateTime.now();

    /**
     * 是否删除
     */
    @Column(name = "deleted", nullable = false)
    @Builder.Default
    private Boolean deleted = false;

    /**
     * 步骤状态枚举
     */
    public enum StepStatus {
        PENDING,    // 待执行
        RUNNING,    // 执行中
        COMPLETED,  // 已完成
        FAILED,     // 执行失败
        SKIPPED,    // 已跳过
        CANCELLED   // 已取消
    }

    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 开始执行步骤
     */
    public void start() {
        this.status = StepStatus.RUNNING;
        this.startedAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 完成步骤
     */
    public void complete(String result) {
        this.status = StepStatus.COMPLETED;
        this.outputResult = result;
        this.completedAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        
        if (startedAt != null) {
            this.durationMs = java.time.Duration.between(startedAt, completedAt).toMillis();
        }
    }

    /**
     * 失败步骤
     */
    public void fail(String errorMessage) {
        this.status = StepStatus.FAILED;
        this.errorMessage = errorMessage;
        this.completedAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        
        if (startedAt != null) {
            this.durationMs = java.time.Duration.between(startedAt, completedAt).toMillis();
        }
    }

    /**
     * 检查是否可以重试
     */
    public boolean canRetry() {
        return retryCount < maxRetries;
    }

    /**
     * 增加重试次数
     */
    public void incrementRetry() {
        this.retryCount++;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 检查是否已完成
     */
    public boolean isCompleted() {
        return StepStatus.COMPLETED.equals(status);
    }

    /**
     * 检查是否失败
     */
    public boolean isFailed() {
        return StepStatus.FAILED.equals(status);
    }

    /**
     * 检查是否正在运行
     */
    public boolean isRunning() {
        return StepStatus.RUNNING.equals(status);
    }

    /**
     * 检查是否待执行
     */
    public boolean isPending() {
        return StepStatus.PENDING.equals(status);
    }
}
