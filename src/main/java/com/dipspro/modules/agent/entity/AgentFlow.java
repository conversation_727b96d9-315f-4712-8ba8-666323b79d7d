package com.dipspro.modules.agent.entity;

import java.time.LocalDateTime;
import java.util.UUID;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Agent 执行流程实体
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "agent_flow")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentFlow {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 执行ID（UUID）
     */
    @Column(name = "execution_id", nullable = false, unique = true)
    private UUID executionId;

    /**
     * 流程名称
     */
    @Column(name = "flow_name", nullable = false, length = 255)
    private String flowName;

    /**
     * 流程描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 会话ID
     */
    @Column(name = "conversation_id", length = 255)
    private String conversationId;

    /**
     * 原始用户输入
     */
    @Column(name = "original_input", columnDefinition = "TEXT")
    private String originalInput;

    /**
     * 流程状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    @Builder.Default
    private FlowStatus status = FlowStatus.PENDING;

    /**
     * 执行结果
     */
    @Column(name = "result", columnDefinition = "TEXT")
    private String result;

    /**
     * 错误信息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    /**
     * 总步骤数
     */
    @Column(name = "total_steps")
    private Integer totalSteps;

    /**
     * 已完成步骤数
     */
    @Column(name = "completed_steps")
    @Builder.Default
    private Integer completedSteps = 0;

    /**
     * 执行开始时间
     */
    @Column(name = "started_at")
    private LocalDateTime startedAt;

    /**
     * 执行结束时间
     */
    @Column(name = "completed_at")
    private LocalDateTime completedAt;

    /**
     * 执行时长（毫秒）
     */
    @Column(name = "duration_ms")
    private Long durationMs;

    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    @Builder.Default
    private LocalDateTime createdAt = LocalDateTime.now();

    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    @Builder.Default
    private LocalDateTime updatedAt = LocalDateTime.now();

    /**
     * 是否删除
     */
    @Column(name = "deleted", nullable = false)
    @Builder.Default
    private Boolean deleted = false;

    /**
     * 流程状态枚举
     */
    public enum FlowStatus {
        PENDING,    // 待执行
        RUNNING,    // 执行中
        COMPLETED,  // 已完成
        FAILED,     // 执行失败
        CANCELLED   // 已取消
    }

    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 计算执行进度
     */
    public Double getProgress() {
        if (totalSteps == null || totalSteps == 0) {
            return 0.0;
        }
        return (double) completedSteps / totalSteps;
    }

    /**
     * 检查是否完成
     */
    public boolean isCompleted() {
        return FlowStatus.COMPLETED.equals(status);
    }

    /**
     * 检查是否失败
     */
    public boolean isFailed() {
        return FlowStatus.FAILED.equals(status);
    }

    /**
     * 检查是否正在运行
     */
    public boolean isRunning() {
        return FlowStatus.RUNNING.equals(status);
    }
}
