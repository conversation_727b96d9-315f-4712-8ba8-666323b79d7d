package com.dipspro.modules.agent.entity;

import java.time.LocalDateTime;

import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Agent 定义实体
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "agent_definitions")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AgentDefinition {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", length = 100, nullable = false)
    private String name;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "capabilities", columnDefinition = "jsonb")
    private String capabilities;

    @Column(name = "system_prompt", columnDefinition = "TEXT")
    private String systemPrompt;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "configuration", columnDefinition = "jsonb")
    private String configuration;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", length = 20, nullable = false)
    private AgentStatus status = AgentStatus.ACTIVE;

    @Column(name = "category", length = 50)
    private String category;

    @Enumerated(EnumType.STRING)
    @Column(name = "agent_type", length = 20, nullable = false)
    private AgentType agentType = AgentType.BUSINESS;

    @Enumerated(EnumType.STRING)
    @Column(name = "agent_role", length = 30)
    private AgentRole agentRole;

    @Column(name = "is_system_agent", nullable = false)
    private Boolean isSystemAgent = false;

    @Column(name = "priority")
    private Integer priority = 50;

    @Column(name = "timeout_seconds")
    private Integer timeoutSeconds = 30;

    @Column(name = "max_retries")
    private Integer maxRetries = 3;

    @Column(name = "created_by", nullable = false)
    private Long createdBy;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        createdAt = now;
        updatedAt = now;
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    /**
     * Agent 状态枚举
     */
    public enum AgentStatus {
        ACTIVE, // 启用
        INACTIVE, // 禁用
        DRAFT // 草稿
    }

    /**
     * Agent 类型枚举
     */
    public enum AgentType {
        SYSTEM,    // 系统内置AI Agent
        BUSINESS,  // 业务AI Agent
        TOOL,      // 工具调用AI Agent
        WORKFLOW   // 工作流AI Agent
    }

    /**
     * Agent 角色枚举 - MVP版本，只包含核心系统Agent
     */
    public enum AgentRole {
        // 系统核心Agent
        INTENT_RECOGNITION,     // 意图识别Agent
        ORCHESTRATION,          // 流程编排Agent
        RESULT_AGGREGATION,     // 结果聚合Agent
        RESPONSE_GENERATION,    // 响应生成Agent
        TOOL_INVOCATION,        // 工具调用Agent
        
        // 通用Agent
        GENERAL_ASSISTANT,      // 通用助手Agent
        CUSTOM                  // 自定义Agent
    }
}