package com.dipspro.modules.agent.util;

import java.util.List;
import java.util.Map;

import com.dipspro.modules.agent.dto.AgentDefinitionDto;
import com.dipspro.modules.agent.entity.AgentDefinition;
import com.dipspro.util.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;

/**
 * Agent 定义转换器
 * 用于DTO和Entity之间的数据转换
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class AgentDefinitionConverter {

    /**
     * DTO转Entity
     */
    public static AgentDefinition toEntity(AgentDefinitionDto dto) {
        if (dto == null) {
            return null;
        }

        AgentDefinition entity = new AgentDefinition();
        entity.setId(dto.getId());
        entity.setName(dto.getName());
        entity.setDescription(dto.getDescription());
        entity.setCategory(dto.getCategory());
        entity.setSystemPrompt(dto.getSystemPrompt());
        entity.setStatus(dto.getStatus());
        entity.setAgentType(dto.getAgentType());
        entity.setAgentRole(dto.getAgentRole());
        entity.setIsSystemAgent(dto.getIsSystemAgent());
        entity.setPriority(dto.getPriority());
        entity.setTimeoutSeconds(dto.getTimeoutSeconds());
        entity.setMaxRetries(dto.getMaxRetries());
        entity.setCreatedBy(dto.getCreatedBy());
        entity.setCreatedAt(dto.getCreatedAt());
        entity.setUpdatedAt(dto.getUpdatedAt());

        // 将List<String>转换为JSON字符串
        if (dto.getCapabilities() != null) {
            entity.setCapabilities(JsonUtils.toJson(dto.getCapabilities()));
        }

        // 将Map<String, Object>转换为JSON字符串
        if (dto.getConfiguration() != null) {
            entity.setConfiguration(JsonUtils.toJson(dto.getConfiguration()));
        }

        return entity;
    }

    /**
     * Entity转DTO
     */
    public static AgentDefinitionDto toDto(AgentDefinition entity) {
        if (entity == null) {
            return null;
        }

        AgentDefinitionDto dto = new AgentDefinitionDto();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setDescription(entity.getDescription());
        dto.setCategory(entity.getCategory());
        dto.setSystemPrompt(entity.getSystemPrompt());
        dto.setStatus(entity.getStatus());
        dto.setAgentType(entity.getAgentType());
        dto.setAgentRole(entity.getAgentRole());
        dto.setIsSystemAgent(entity.getIsSystemAgent());
        dto.setPriority(entity.getPriority());
        dto.setTimeoutSeconds(entity.getTimeoutSeconds());
        dto.setMaxRetries(entity.getMaxRetries());
        dto.setCreatedBy(entity.getCreatedBy());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());

        // 将JSON字符串转换为List<String>
        if (entity.getCapabilities() != null) {
            dto.setCapabilities(JsonUtils.fromJson(entity.getCapabilities(), new TypeReference<List<String>>() {}));
        }

        // 将JSON字符串转换为Map<String, Object>
        if (entity.getConfiguration() != null) {
            dto.setConfiguration(JsonUtils.fromJson(entity.getConfiguration(), new TypeReference<Map<String, Object>>() {}));
        }

        return dto;
    }
}