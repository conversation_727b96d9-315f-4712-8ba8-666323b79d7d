package com.dipspro.modules.agent.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.dipspro.modules.agent.entity.AgentDefinition;
import com.dipspro.modules.agent.entity.AgentDefinition.AgentStatus;

/**
 * Agent 定义 Repository 接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface AgentDefinitionRepository extends JpaRepository<AgentDefinition, Long> {

    /**
     * 根据状态查询 Agent
     */
    List<AgentDefinition> findByStatus(AgentStatus status);

    /**
     * 根据状态和创建人查询 Agent
     */
    List<AgentDefinition> findByStatusAndCreatedBy(AgentStatus status, Long createdBy);

    /**
     * 根据名称和创建人查询 Agent（用于检查重名）
     */
    Optional<AgentDefinition> findByNameAndCreatedBy(String name, Long createdBy);

    /**
     * 检查名称是否存在
     */
    boolean existsByNameAndCreatedBy(String name, Long createdBy);

    /**
     * 根据类别查询 Agent
     */
    List<AgentDefinition> findByCategory(String category);

    /**
     * 根据类别和状态查询 Agent
     */
    List<AgentDefinition> findByCategoryAndStatus(String category, AgentStatus status);

    /**
     * 查询启用的 Agent，按优先级降序排列
     */
    @Query("SELECT a FROM AgentDefinition a WHERE a.status = 'ACTIVE' ORDER BY a.priority DESC, a.createdAt ASC")
    List<AgentDefinition> findActiveAgentsOrderByPriority();

    /**
     * 根据能力查询 Agent（JSON 查询）
     */
    @Query(value = "SELECT * FROM agent_definitions WHERE capabilities::jsonb @> :capability AND status = 'ACTIVE'", nativeQuery = true)
    List<AgentDefinition> findByCapability(@Param("capability") String capability);

    /**
     * 查询用户创建的 Agent，按创建时间降序排列
     */
    @Query("SELECT a FROM AgentDefinition a WHERE a.createdBy = :createdBy ORDER BY a.createdAt DESC")
    List<AgentDefinition> findByCreatedByOrderByCreatedAtDesc(@Param("createdBy") Long createdBy);

    /**
     * 根据名称模糊查询（用于搜索）
     */
    @Query("SELECT a FROM AgentDefinition a WHERE a.name LIKE %:name% ORDER BY a.priority DESC")
    List<AgentDefinition> findByNameContainingIgnoreCaseOrderByPriority(@Param("name") String name);

    /**
     * 统计各状态的 Agent 数量
     */
    @Query("SELECT a.status, COUNT(a) FROM AgentDefinition a GROUP BY a.status")
    List<Object[]> countByStatus();

    /**
     * 统计各类别的 Agent 数量
     */
    @Query("SELECT a.category, COUNT(a) FROM AgentDefinition a WHERE a.status = 'ACTIVE' GROUP BY a.category")
    List<Object[]> countActiveByCategoryGroupBy();

    /**
     * 根据创建人分页查询 Agent
     */
    Page<AgentDefinition> findByCreatedBy(Long createdBy, Pageable pageable);

    /**
     * 根据角色和状态查询 Agent
     */
    List<AgentDefinition> findByAgentRoleAndStatus(AgentDefinition.AgentRole agentRole, AgentStatus status);

    /**
     * 根据名称和状态查询 Agent
     */
    Optional<AgentDefinition> findByNameAndStatus(String name, AgentStatus status);
}