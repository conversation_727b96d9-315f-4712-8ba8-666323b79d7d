package com.dipspro.modules.agent.repository;

import com.dipspro.modules.agent.entity.AgentExecution;
import com.dipspro.modules.agent.entity.AgentExecution.ExecutionStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Agent 执行记录 Repository 接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface AgentExecutionRepository extends JpaRepository<AgentExecution, UUID> {

    /**
     * 根据会话 ID 查询执行记录
     */
    List<AgentExecution> findByConversationId(UUID conversationId);

    /**
     * 根据会话 ID 查询执行记录，按创建时间降序排列
     */
    @Query("SELECT e FROM AgentExecution e WHERE e.conversationId = :conversationId ORDER BY e.createdAt DESC")
    List<AgentExecution> findByConversationIdOrderByCreatedAtDesc(@Param("conversationId") UUID conversationId);

    /**
     * 根据用户 ID 查询执行记录
     */
    Page<AgentExecution> findByUserId(Long userId, Pageable pageable);

    /**
     * 根据用户 ID 和状态查询执行记录
     */
    Page<AgentExecution> findByUserIdAndStatus(Long userId, ExecutionStatus status, Pageable pageable);

    /**
     * 根据 Agent ID 查询执行记录
     */
    Page<AgentExecution> findByAgentId(Long agentId, Pageable pageable);

    /**
     * 根据流程 ID 查询执行记录
     */
    Page<AgentExecution> findByFlowId(Long flowId, Pageable pageable);

    /**
     * 根据状态查询执行记录
     */
    List<AgentExecution> findByStatus(ExecutionStatus status);

    /**
     * 查询正在运行的执行记录
     */
    @Query("SELECT e FROM AgentExecution e WHERE e.status = 'RUNNING' ORDER BY e.startedAt ASC")
    List<AgentExecution> findRunningExecutions();

    /**
     * 查询超时的执行记录
     */
    @Query("SELECT e FROM AgentExecution e WHERE e.status = 'RUNNING' AND e.startedAt < :timeoutBefore")
    List<AgentExecution> findTimeoutExecutions(@Param("timeoutBefore") LocalDateTime timeoutBefore);

    /**
     * 查询用户的最近执行记录
     */
    @Query("SELECT e FROM AgentExecution e WHERE e.userId = :userId ORDER BY e.createdAt DESC")
    Page<AgentExecution> findRecentExecutionsByUser(@Param("userId") Long userId, Pageable pageable);

    /**
     * 统计各状态的执行数量
     */
    @Query("SELECT e.status, COUNT(e) FROM AgentExecution e GROUP BY e.status")
    List<Object[]> countByStatus();

    /**
     * 统计用户的执行数量
     */
    @Query("SELECT COUNT(e) FROM AgentExecution e WHERE e.userId = :userId")
    Long countByUserId(@Param("userId") Long userId);

    /**
     * 统计 Agent 的执行数量
     */
    @Query("SELECT COUNT(e) FROM AgentExecution e WHERE e.agentId = :agentId AND e.status = 'COMPLETED'")
    Long countSuccessfulExecutionsByAgent(@Param("agentId") Long agentId);

    /**
     * 查询指定时间范围内的执行记录
     */
    @Query("SELECT e FROM AgentExecution e WHERE e.createdAt BETWEEN :startTime AND :endTime ORDER BY e.createdAt DESC")
    List<AgentExecution> findExecutionsBetween(@Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);

    /**
     * 统计每日执行数量
     */
    @Query(value = "SELECT DATE(created_at) as execution_date, COUNT(*) as execution_count " +
            "FROM agent_executions WHERE created_at >= :startDate " +
            "GROUP BY DATE(created_at) ORDER BY execution_date DESC", nativeQuery = true)
    List<Object[]> countExecutionsByDate(@Param("startDate") LocalDateTime startDate);

    /**
     * 查询最近失败的执行记录
     */
    @Query("SELECT e FROM AgentExecution e WHERE e.status = 'FAILED' ORDER BY e.completedAt DESC")
    Page<AgentExecution> findRecentFailedExecutions(Pageable pageable);

    /**
     * 查询执行时间最长的记录
     */
    @Query(value = "SELECT *, EXTRACT(EPOCH FROM (completed_at - started_at)) as duration_seconds " +
            "FROM agent_executions WHERE completed_at IS NOT NULL AND started_at IS NOT NULL " +
            "ORDER BY duration_seconds DESC", nativeQuery = true)
    Page<AgentExecution> findLongestExecutions(Pageable pageable);

    /**
     * 根据会话 ID 查询最近的执行记录
     */
    @Query("SELECT e FROM AgentExecution e WHERE e.conversationId = :conversationId ORDER BY e.createdAt DESC LIMIT 1")
    Optional<AgentExecution> findLatestByConversationId(@Param("conversationId") UUID conversationId);
}