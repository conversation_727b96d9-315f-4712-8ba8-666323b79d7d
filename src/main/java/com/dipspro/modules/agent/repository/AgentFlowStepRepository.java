package com.dipspro.modules.agent.repository;

import com.dipspro.modules.agent.entity.AgentFlowStep;
import com.dipspro.modules.agent.entity.AgentFlowStep.StepStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Agent 流程步骤 Repository 接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface AgentFlowStepRepository extends JpaRepository<AgentFlowStep, Long> {

    /**
     * 根据执行 ID 查询所有步骤
     */
    @Query("SELECT s FROM AgentFlowStep s WHERE s.executionId = :executionId ORDER BY s.stepOrder ASC")
    List<AgentFlowStep> findByExecutionIdOrderByStepOrder(@Param("executionId") UUID executionId);

    /**
     * 根据执行 ID 和步骤 ID 查询步骤
     */
    Optional<AgentFlowStep> findByExecutionIdAndStepId(UUID executionId, String stepId);

    /**
     * 根据执行 ID 和状态查询步骤
     */
    List<AgentFlowStep> findByExecutionIdAndStatus(UUID executionId, StepStatus status);

    /**
     * 查询执行中的步骤
     */
    @Query("SELECT s FROM AgentFlowStep s WHERE s.executionId = :executionId AND s.status = 'RUNNING'")
    List<AgentFlowStep> findRunningStepsByExecution(@Param("executionId") UUID executionId);

    /**
     * 查询下一个待执行的步骤
     */
    @Query("SELECT s FROM AgentFlowStep s WHERE s.executionId = :executionId AND s.status = 'PENDING' " +
            "ORDER BY s.stepOrder ASC LIMIT 1")
    Optional<AgentFlowStep> findNextPendingStep(@Param("executionId") UUID executionId);

    /**
     * 统计执行中各状态步骤数量
     */
    @Query("SELECT s.status, COUNT(s) FROM AgentFlowStep s WHERE s.executionId = :executionId GROUP BY s.status")
    List<Object[]> countStepStatusByExecution(@Param("executionId") UUID executionId);

    /**
     * 根据 Agent ID 查询步骤（用于统计 Agent 使用情况）
     */
    List<AgentFlowStep> findByAgentId(Long agentId);

    /**
     * 查询完成的步骤，按完成时间排序
     */
    @Query("SELECT s FROM AgentFlowStep s WHERE s.executionId = :executionId AND s.status = 'COMPLETED' " +
            "ORDER BY s.completedAt ASC")
    List<AgentFlowStep> findCompletedStepsByExecution(@Param("executionId") UUID executionId);

    /**
     * 查询失败的步骤
     */
    @Query("SELECT s FROM AgentFlowStep s WHERE s.executionId = :executionId AND s.status = 'FAILED'")
    List<AgentFlowStep> findFailedStepsByExecution(@Param("executionId") UUID executionId);

    /**
     * 计算执行进度
     */
    @Query("SELECT " +
            "COUNT(CASE WHEN s.status = 'COMPLETED' THEN 1 END), " +
            "COUNT(s) " +
            "FROM AgentFlowStep s WHERE s.executionId = :executionId")
    List<Object[]> calculateExecutionProgress(@Param("executionId") UUID executionId);

    /**
     * 统计 Agent 执行步骤的平均耗时
     */
    @Query("SELECT AVG(s.durationMs) FROM AgentFlowStep s WHERE s.agentId = :agentId AND s.status = 'COMPLETED'")
    Double findAverageDurationByAgent(@Param("agentId") Long agentId);

    /**
     * 查询最耗时的步骤
     */
    @Query("SELECT s FROM AgentFlowStep s WHERE s.durationMs IS NOT NULL ORDER BY s.durationMs DESC")
    List<AgentFlowStep> findSlowestSteps();

    /**
     * 根据步骤名称统计执行次数
     */
    @Query("SELECT s.stepName, COUNT(s) FROM AgentFlowStep s WHERE s.status = 'COMPLETED' GROUP BY s.stepName ORDER BY COUNT(s) DESC")
    List<Object[]> countExecutionsByStepName();

    /**
     * 查询指定执行的最后一个步骤
     */
    @Query("SELECT s FROM AgentFlowStep s WHERE s.executionId = :executionId ORDER BY s.stepOrder DESC LIMIT 1")
    Optional<AgentFlowStep> findLastStepByExecution(@Param("executionId") UUID executionId);
}