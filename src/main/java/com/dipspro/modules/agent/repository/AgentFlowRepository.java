package com.dipspro.modules.agent.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.dipspro.modules.agent.entity.AgentFlow;
import com.dipspro.modules.agent.entity.AgentFlow.FlowStatus;

/**
 * Agent 流程 Repository 接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface AgentFlowRepository extends JpaRepository<AgentFlow, Long> {

    /**
     * 根据状态查询流程
     */
    List<AgentFlow> findByStatus(FlowStatus status);

    /**
     * 根据状态和用户查询流程
     */
    List<AgentFlow> findByStatusAndUserId(FlowStatus status, Long userId);

    /**
     * 根据流程名称和用户查询流程（用于检查重名）
     */
    Optional<AgentFlow> findByFlowNameAndUserId(String flowName, Long userId);

    /**
     * 检查流程名称是否存在
     */
    boolean existsByFlowNameAndUserId(String flowName, Long userId);

    /**
     * 查询用户的流程，按创建时间降序排列
     */
    @Query("SELECT f FROM AgentFlow f WHERE f.userId = :userId ORDER BY f.createdAt DESC")
    List<AgentFlow> findByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId);

    /**
     * 根据流程名称模糊查询
     */
    @Query("SELECT f FROM AgentFlow f WHERE f.flowName LIKE %:name% ORDER BY f.createdAt DESC")
    List<AgentFlow> findByFlowNameContainingIgnoreCaseOrderByCreatedAt(@Param("name") String name);

    /**
     * 根据会话ID查询流程
     */
    List<AgentFlow> findByConversationIdOrderByCreatedAtDesc(String conversationId);

    /**
     * 统计各状态的流程数量
     */
    @Query("SELECT f.status, COUNT(f) FROM AgentFlow f GROUP BY f.status")
    List<Object[]> countByStatus();

    /**
     * 根据用户分页查询流程
     */
    Page<AgentFlow> findByUserId(Long userId, Pageable pageable);

    /**
     * 根据状态和用户分页查询流程
     */
    Page<AgentFlow> findByStatusAndUserId(FlowStatus status, Long userId, Pageable pageable);

    /**
     * 查询正在运行的流程
     */
    List<AgentFlow> findByStatusIn(List<FlowStatus> statuses);

    /**
     * 根据执行ID查询流程
     */
    Optional<AgentFlow> findByExecutionId(java.util.UUID executionId);
}