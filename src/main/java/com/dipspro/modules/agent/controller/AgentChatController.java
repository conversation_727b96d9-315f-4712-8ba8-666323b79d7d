package com.dipspro.modules.agent.controller;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.modules.agent.dto.AgentExecutionRequest;
import com.dipspro.modules.agent.dto.AgentExecutionResult;
import com.dipspro.modules.agent.dto.OrchestrationRequest;
import com.dipspro.modules.agent.dto.OrchestrationResult;
import com.dipspro.modules.agent.dto.api.ConversationResponse;
import com.dipspro.modules.agent.dto.api.CreateConversationRequest;
import com.dipspro.modules.agent.dto.api.ExecuteAgentRequest;
import com.dipspro.modules.agent.dto.api.ExecutionResponse;
import com.dipspro.modules.agent.dto.api.ExecutionStatusResponse;
import com.dipspro.modules.agent.dto.api.InputSuggestionDto;
import com.dipspro.modules.agent.dto.api.MessageResponse;
import com.dipspro.modules.agent.dto.api.SendMessageRequest;
import com.dipspro.modules.agent.dto.api.SseEvent;
import com.dipspro.modules.agent.service.AgentExecutionEngine;
import com.dipspro.modules.agent.service.AiAgentOrchestrationService;
import com.dipspro.modules.agent.service.InputSuggestionService;
import com.dipspro.modules.agent.service.SseEmitterManager;
import com.dipspro.modules.chat.entity.ChatMessage;
import com.dipspro.modules.chat.entity.Conversation;
import com.dipspro.modules.chat.repository.ChatMessageRepository;
import com.dipspro.modules.chat.repository.ConversationRepository;
import com.dipspro.modules.chat.service.ChatService;
import com.dipspro.security.util.SecurityUtils;
import com.dipspro.util.JwtUtil;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * Agent Chat API 控制器
 * 提供专用的 Agent 对话 API 端点
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/agent-chat/v1")
@Slf4j
@Validated
public class AgentChatController {

    private final AiAgentOrchestrationService orchestrationService;
    private final ChatService chatService;
    private final AgentExecutionEngine executionEngine;
    private final SseEmitterManager sseEmitterManager;
    private final ConversationRepository conversationRepository;
    private final ChatMessageRepository chatMessageRepository;
    private final JwtUtil jwtUtil;
    private final ApplicationContext applicationContext;
    private final InputSuggestionService inputSuggestionService;

    @Autowired
    public AgentChatController(
            AiAgentOrchestrationService orchestrationService,
            ChatService chatService,
            AgentExecutionEngine executionEngine,
            SseEmitterManager sseEmitterManager,
            ConversationRepository conversationRepository,
            ChatMessageRepository chatMessageRepository,
            JwtUtil jwtUtil,
            ApplicationContext applicationContext,
            InputSuggestionService inputSuggestionService) {
        this.orchestrationService = orchestrationService;
        this.chatService = chatService;
        this.executionEngine = executionEngine;
        this.sseEmitterManager = sseEmitterManager;
        this.conversationRepository = conversationRepository;
        this.chatMessageRepository = chatMessageRepository;
        this.jwtUtil = jwtUtil;
        this.applicationContext = applicationContext;
        this.inputSuggestionService = inputSuggestionService;
    }

    /**
     * 创建 Agent 对话
     */
    @PostMapping("/conversations")
    @Transactional
    // @PreAuthorize("@ss.hasPerm('agent-chat:conversation:list')")
    public ResponseEntity<ApiResponse<ConversationResponse>> createConversation(
            @Valid @RequestBody CreateConversationRequest request,
            Authentication authentication) {

        log.info("创建 Agent 对话: 用户={}, 标题={}",
                authentication.getName(), request.getTitle());

        try {
            Long userId = getUserId(authentication);

            // 生成会话ID
            UUID conversationUuid = UUID.randomUUID();
            String conversationId = conversationUuid.toString();
            String title = request.getTitle() != null ? request.getTitle() : "Agent 对话";

            // 创建会话实体并保存到数据库
            Conversation conversation = new Conversation();
            conversation.setId(conversationUuid);
            conversation.setLabel(title);
            conversation.setUserId(userId);
            
            // 保存到数据库
            conversation = conversationRepository.save(conversation);
            
            log.info("会话实体已保存到数据库: 对话ID={}, 用户ID={}", conversationId, userId);

            // 构建响应
            ConversationResponse response = ConversationResponse.builder()
                    .conversationId(conversationId)
                    .title(title)
                    .conversationType(request.getConversationType())
                    .userId(userId)
                    .status("ACTIVE")
                    .messageCount(0)
                    .createdAt(conversation.getCreatedAt() != null ? 
                        LocalDateTime.ofInstant(conversation.getCreatedAt(), java.time.ZoneId.systemDefault()) : 
                        LocalDateTime.now())
                    .updatedAt(conversation.getUpdatedAt() != null ? 
                        LocalDateTime.ofInstant(conversation.getUpdatedAt(), java.time.ZoneId.systemDefault()) : 
                        LocalDateTime.now())
                    .metadata(request.getMetadata())
                    .build();

            log.info("Agent 对话创建成功: 对话ID={}", conversationId);
            return ResponseEntity.ok(ApiResponse.success(response));

        } catch (Exception e) {
            log.error("创建 Agent 对话失败: 用户={}, 错误={}",
                    authentication.getName(), e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("创建对话失败: " + e.getMessage()));
        }
    }

    /**
     * 获取 Agent 对话列表
     */
    @GetMapping("/conversations")
    // @PreAuthorize("@ss.hasPerm('agent-chat:conversation:list')")
    public ResponseEntity<ApiResponse<java.util.List<ConversationResponse>>> getConversations(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            Authentication authentication) {
            
        log.info("获取 Agent 对话列表: 用户={}, 页码={}, 每页大小={}", 
                authentication.getName(), page, size);
        
        try {
            Long userId = getUserId(authentication);
            
            if (userId == null) {
                log.warn("无法获取当前用户ID，拒绝访问对话列表");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error("用户未认证"));
            }
            
            // 直接使用 Repository 按用户ID查询对话，确保数据隔离
            org.springframework.data.domain.Pageable pageable = 
                    org.springframework.data.domain.PageRequest.of(page, size);
            
            org.springframework.data.domain.Page<com.dipspro.modules.chat.dto.ConversationSummaryDTO> basicSummariesPage = 
                    chatMessageRepository.findConversationSummariesByUserId(userId, pageable);
            
            // 转换为 ConversationResponse 格式
            java.util.List<ConversationResponse> conversations = basicSummariesPage.getContent().stream()
                    .map(summary -> ConversationResponse.builder()
                            .conversationId(summary.getId().toString())
                            .title(summary.getLabel() != null ? summary.getLabel() : "未命名对话")
                            .conversationType("AGENT_CHAT")
                            .userId(userId)
                            .status("ACTIVE")
                            .messageCount(0) // TODO: 后续可以通过 Repository 查询消息数量
                            .createdAt(java.time.LocalDateTime.ofInstant(summary.getTimestamp(), java.time.ZoneOffset.UTC))
                            .updatedAt(java.time.LocalDateTime.ofInstant(summary.getTimestamp(), java.time.ZoneOffset.UTC))
                            .metadata(new java.util.HashMap<>())
                            .build())
                    .collect(java.util.stream.Collectors.toList());
            
            log.info("获取 Agent 对话列表成功: 用户ID={}, 用户名={}, 对话数量={}", 
                    userId, authentication.getName(), conversations.size());
            return ResponseEntity.ok(ApiResponse.success(conversations));
            
        } catch (Exception e) {
            log.error("获取 Agent 对话列表失败: 用户={}, 错误={}", 
                    authentication.getName(), e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取对话列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取单个 Agent 对话详情
     */
    @GetMapping("/conversations/{conversationId}")
    // @PreAuthorize("@ss.hasPerm('agent-chat:conversation:list')")
    public ResponseEntity<ApiResponse<ConversationResponse>> getConversation(
            @PathVariable String conversationId,
            Authentication authentication) {
            
        log.info("获取 Agent 对话详情: 用户={}, 对话ID={}", 
                authentication.getName(), conversationId);
        
        try {
            Long userId = getUserId(authentication);
            UUID conversationUuid;
            
            try {
                conversationUuid = UUID.fromString(conversationId);
            } catch (IllegalArgumentException e) {
                log.warn("无效的对话ID格式: {}", conversationId);
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("无效的对话ID格式"));
            }
            
            // 查询对话是否存在且属于当前用户
            Optional<Conversation> conversationOpt = conversationRepository.findByIdAndUserId(conversationUuid, userId);
            
            if (conversationOpt.isEmpty()) {
                log.warn("对话不存在或用户无权访问: 对话ID={}, 用户ID={}", conversationId, userId);
                return ResponseEntity.notFound()
                        .build();
            }
            
            Conversation conversation = conversationOpt.get();
            
            // 构建响应对象
            ConversationResponse response = ConversationResponse.builder()
                    .conversationId(conversationId)
                    .title(conversation.getLabel() != null ? conversation.getLabel() : "未命名对话")
                    .conversationType("AGENT_CHAT")
                    .userId(userId)
                    .status("ACTIVE")
                    .messageCount(conversation.getMessages().size())
                    .createdAt(java.time.LocalDateTime.ofInstant(conversation.getCreatedAt(), java.time.ZoneOffset.UTC))
                    .updatedAt(java.time.LocalDateTime.ofInstant(conversation.getUpdatedAt(), java.time.ZoneOffset.UTC))
                    .metadata(new java.util.HashMap<>())
                    .build();
            
            log.info("获取 Agent 对话详情成功: 对话ID={}", conversationId);
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (Exception e) {
            log.error("获取 Agent 对话详情失败: 用户={}, 对话ID={}, 错误={}", 
                    authentication.getName(), conversationId, e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取对话详情失败: " + e.getMessage()));
        }
    }

    /**
     * 删除 Agent 对话
     */
    @DeleteMapping("/conversations/{conversationId}")
    // @PreAuthorize("@ss.hasPerm('agent-chat:conversation:list')")
    public ResponseEntity<ApiResponse<Void>> deleteConversation(
            @PathVariable String conversationId,
            Authentication authentication) {
            
        log.info("删除 Agent 对话: 用户={}, 对话ID={}", 
                authentication.getName(), conversationId);
        
        try {
            // TODO: 实现删除对话的逻辑
            // 这里需要调用相应的服务来删除对话及其相关数据
            
            log.info("删除 Agent 对话成功: 对话ID={}", conversationId);
            return ResponseEntity.ok(ApiResponse.success(null));
            
        } catch (Exception e) {
            log.error("删除 Agent 对话失败: 用户={}, 对话ID={}, 错误={}", 
                    authentication.getName(), conversationId, e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("删除对话失败: " + e.getMessage()));
        }
    }

    /**
     * 获取 Agent 对话消息列表
     */
    @GetMapping("/conversations/{conversationId}/messages")
    // @PreAuthorize("@ss.hasPerm('agent-chat:conversation:list')")
    public ResponseEntity<ApiResponse<java.util.List<MessageResponse>>> getMessages(
            @PathVariable String conversationId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int limit,
            @RequestParam(required = false) String messageType,
            Authentication authentication) {
            
        log.info("获取 Agent 对话消息列表: 对话ID={}, 用户={}, 页码={}, 限制={}",
                conversationId, authentication.getName(), page, limit);
        
        try {
            Long userId = getUserId(authentication);
            UUID conversationUuid;
            
            try {
                conversationUuid = UUID.fromString(conversationId);
            } catch (IllegalArgumentException e) {
                log.warn("无效的对话ID格式: {}", conversationId);
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("无效的对话ID格式"));
            }
            
            // 从数据库查询消息列表
            java.util.List<ChatMessage> chatMessages = chatMessageRepository
                    .findByConversationIdOrderByCreatedAtAsc(conversationUuid);
            
            // 应用消息类型过滤
            if (messageType != null && !messageType.trim().isEmpty()) {
                try {
                    ChatMessage.MessageRole roleFilter = ChatMessage.MessageRole.valueOf(messageType.toUpperCase());
                    chatMessages = chatMessages.stream()
                            .filter(msg -> msg.getRole() == roleFilter)
                            .collect(java.util.stream.Collectors.toList());
                } catch (IllegalArgumentException e) {
                    log.warn("无效的消息类型过滤条件: {}", messageType);
                }
            }
            
            // 应用分页（简单的内存分页，生产环境应使用数据库分页）
            int start = page * limit;
            int end = Math.min(start + limit, chatMessages.size());
            if (start >= chatMessages.size()) {
                chatMessages = java.util.Collections.emptyList();
            } else {
                chatMessages = chatMessages.subList(start, end);
            }
            
            // 转换为 MessageResponse 格式
            java.util.List<MessageResponse> messages = chatMessages.stream()
                    .map(chatMessage -> MessageResponse.builder()
                            .messageId(chatMessage.getId().toString())
                            .conversationId(conversationId)
                            .content(chatMessage.getContent())
                            .messageType(chatMessage.getRole().name())
                            .sender(chatMessage.getRole() == ChatMessage.MessageRole.USER ? 
                                    "user-" + chatMessage.getUserId() : "assistant")
                            .status("DELIVERED")
                            .timestamp(java.time.LocalDateTime.ofInstant(chatMessage.getCreatedAt(), java.time.ZoneOffset.UTC))
                            .metadata(new java.util.HashMap<>())
                            .build())
                    .collect(java.util.stream.Collectors.toList());
            
            log.info("获取 Agent 对话消息列表成功: 对话ID={}, 消息数量={}", 
                    conversationId, messages.size());
            return ResponseEntity.ok(ApiResponse.success(messages));
            
        } catch (Exception e) {
            log.error("获取 Agent 对话消息列表失败: 对话ID={}, 用户={}, 错误={}", 
                    conversationId, authentication.getName(), e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取消息列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取输入建议
     */
    @GetMapping("/suggestions")
    public ResponseEntity<ApiResponse<List<InputSuggestionDto>>> getInputSuggestions(
            @RequestParam(required = false) String conversationId,
            Authentication authentication) {

        log.debug("获取输入建议: conversationId={}, user={}", conversationId, authentication.getName());

        try {
            Long userId = getUserId(authentication);
            
            List<InputSuggestionDto> suggestions = inputSuggestionService.getInputSuggestions(conversationId, userId);
            
            log.info("获取输入建议成功: conversationId={}, 建议数量={}", conversationId, suggestions.size());
            return ResponseEntity.ok(ApiResponse.success(suggestions));
            
        } catch (Exception e) {
            log.error("获取输入建议失败: conversationId={}, user={}, error={}", 
                    conversationId, authentication.getName(), e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取输入建议失败: " + e.getMessage()));
        }
    }

    /**
     * 发送消息到 Agent 对话
     */
    @PostMapping("/conversations/{conversationId}/messages")
    // @PreAuthorize("@ss.hasPerm('agent-chat:conversation:list')")
    public ResponseEntity<ApiResponse<MessageResponse>> sendMessage(
            @PathVariable String conversationId,
            @Valid @RequestBody SendMessageRequest request,
            Authentication authentication) {

        log.info("发送消息到 Agent 对话: 对话ID={}, 用户={}, 内容长度={}",
                conversationId, authentication.getName(), request.getContent().length());

        try {
            Long userId = getUserId(authentication);
            String messageId = UUID.randomUUID().toString();

            // 保存用户消息到数据库
            UUID conversationUuid = UUID.fromString(conversationId);
            ChatMessage userMessage = saveUserMessage(conversationUuid, userId, request.getContent(), messageId);
            log.info("用户消息已保存: 消息ID={}, 对话ID={}, 轮次={}", 
                    userMessage.getId(), conversationId, userMessage.getRoundSequence());

            // 创建编排请求
            OrchestrationRequest orchestrationRequest = OrchestrationRequest.builder()
                    .userMessage(request.getContent())
                    .conversationId(conversationId)
                    .messageId(messageId)
                    .userId(userId)
                    .options(buildOrchestrationOptions(request))
                    .context(request.getMetadata())
                    .build();

            // 立即返回消息已接收的响应
            MessageResponse response = MessageResponse.builder()
                    .messageId(messageId)
                    .conversationId(conversationId)
                    .content(request.getContent())
                    .messageType(request.getMessageType().name())
                    .status(MessageResponse.Status.PROCESSING)
                    .sender("USER")
                    .timestamp(LocalDateTime.now())
                    .metadata(request.getMetadata())
                    .build();

            // 向 SSE 客户端发送消息接收确认
            SseEvent messageReceivedEvent = SseEvent.messageReceived(
                    conversationId, messageId, request.getContent());
            sseEmitterManager.broadcast(conversationId, messageReceivedEvent);

            // 异步执行全AI Agent编排
//            if (request.getEnableStreaming()) {
//                // TODO: 暂时不支持流式编排，使用非流式模式
//                log.warn("全AI Agent编排暂不支持流式模式，使用非流式模式");
//            }
            
            // 执行全AI Agent编排
            Mono<OrchestrationResult> resultMono = orchestrationService.orchestrate(orchestrationRequest);

            resultMono.subscribe(
                    result -> handleOrchestrationResult(conversationId, messageId, result, userId, userMessage.getRoundSequence()),
                    error -> handleOrchestrationError(conversationId, messageId, error));

            log.info("消息处理已启动: 对话ID={}, 消息ID={}", conversationId, messageId);
            return ResponseEntity.ok(ApiResponse.success(response));

        } catch (Exception e) {
            log.error("发送消息失败: 对话ID={}, 用户={}, 错误={}",
                    conversationId, authentication.getName(), e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("发送消息失败: " + e.getMessage()));
        }
    }

    /**
     * SSE 流式推送端点
     */
    @GetMapping(value = "/conversations/{conversationId}/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    // @PreAuthorize("@ss.hasPerm('agent-chat:conversation:list')")
    public SseEmitter streamConversation(
            @PathVariable String conversationId,
            @RequestParam(defaultValue = "300000") Long timeout,
            @RequestParam(required = false) String token,
            @RequestParam(required = false) String lastEventId,
            Authentication authentication,
            HttpServletRequest request) {

        log.info("SSE端点被调用: conversationId={}, timeout={}, hasToken={}, hasAuth={}, lastEventId={}", 
                conversationId, timeout, token != null, authentication != null, lastEventId);

        try {
            Long userId = null;
            String username = null;

            // 处理SSE认证：优先使用token参数，其次使用Authentication
            if (token != null && !token.trim().isEmpty()) {
                log.info("启动 SSE 流式连接(Token认证): 对话ID={}, Token={}..., 超时={}ms",
                        conversationId, token.length() > 10 ? token.substring(0, 10) + "..." : token, timeout);
                
                try {
                    // 获取JwtUtil实例（处理依赖注入可能失败的情况）
                    JwtUtil jwtUtilInstance = getJwtUtil();
                    if (jwtUtilInstance == null) {
                        log.error("JwtUtil 实例获取失败");
                        throw new SecurityException("JWT工具未初始化");
                    }
                    
                    // 验证token并获取用户信息
                    if (jwtUtilInstance.validateAccessToken(token)) {
                        username = jwtUtilInstance.getUsernameFromToken(token);
                        userId = jwtUtilInstance.getUserIdFromToken(token);
                        
                        // 将用户信息设置到请求属性中，以便getUserId方法能正常工作
                        request.setAttribute("userId", userId);
                        request.setAttribute("username", username);
                        request.setAttribute("tenantId", jwtUtilInstance.getTenantIdFromToken(token));
                        
                        log.info("Token认证成功: 用户={}, ID={}", username, userId);
                    } else {
                        log.error("Token验证失败");
                        throw new SecurityException("无效的访问令牌");
                    }
                } catch (Exception e) {
                    log.error("Token解析失败: {}", e.getMessage());
                    throw new SecurityException("认证失败: " + e.getMessage());
                }
            } else if (authentication != null) {
                log.info("启动 SSE 流式连接(Session认证): 对话ID={}, 用户={}, 超时={}ms",
                        conversationId, authentication.getName(), timeout);
                
                userId = getUserId(authentication);
                username = authentication.getName();
            } else {
                log.error("SSE连接缺少认证信息");
                throw new SecurityException("缺少认证信息");
            }

            // 创建 SSE 连接
            SseEmitter emitter = new SseEmitter(timeout);

            // 注册连接
            sseEmitterManager.registerEmitter(conversationId, userId, emitter);

            // 发送连接成功事件
            SseEvent connectEvent = SseEvent.connected(conversationId);
            emitter.send(SseEmitter.event()
                    .name("agent-event")
                    .data(connectEvent)
                    .id("connected_" + System.currentTimeMillis()));

            // 如果有lastEventId，检查是否需要发送遗漏的消息
            if (lastEventId != null && !lastEventId.trim().isEmpty()) {
                log.info("检测到重连请求，lastEventId={}", lastEventId);
                // TODO: 实现消息恢复逻辑
                // 这里可以查询数据库中在lastEventId之后的消息，重新发送给客户端
            }

            log.info("SSE 连接建立成功: 对话ID={}, 用户ID={}", conversationId, userId);
            return emitter;

        } catch (Exception e) {
            log.error("创建 SSE 流式连接失败: 对话ID={}, 用户={}, 错误={}",
                    conversationId, authentication.getName(), e.getMessage(), e);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST,
                    "创建流式连接失败: " + e.getMessage());
        }
    }

    /**
     * 手动执行 Agent
     */
    @PostMapping("/conversations/{conversationId}/execute")
    // @PreAuthorize("@ss.hasPerm('agent-chat:conversation:list')")
    public ResponseEntity<ApiResponse<ExecutionResponse>> executeAgent(
            @PathVariable String conversationId,
            @Valid @RequestBody ExecuteAgentRequest request,
            Authentication authentication) {

        log.info("手动执行 Agent: 对话ID={}, 用户={}, AgentID={}",
                conversationId, authentication.getName(), request.getAgentId());

        try {
            Long userId = getUserId(authentication);
            UUID executionId = UUID.randomUUID();

            // 构建执行请求
            AgentExecutionRequest executionRequest = AgentExecutionRequest.builder()
                    .agentId(request.getAgentId())
                    .conversationId(conversationId)
                    .messageId(UUID.randomUUID())
                    .executionId(executionId)
                    .userId(userId)
                    .inputData(request.getInputData())
                    .options(buildExecutionOptions(request.getOptions()))
                    .build();

            // 异步执行 Agent
            Mono<AgentExecutionResult> result = Mono.fromCallable(() -> executionEngine.executeAgent(executionRequest));

            // 构建响应
            ExecutionResponse response = ExecutionResponse.builder()
                    .executionId(executionId.toString())
                    .agentId(request.getAgentId())
                    .status("STARTED")
                    .progress(0)
                    .timestamp(LocalDateTime.now())
                    .metadata(Map.of("conversationId", conversationId))
                    .build();

            // 异步处理执行结果
            result.subscribe(
                    execResult -> handleAgentExecutionResult(conversationId, execResult),
                    error -> handleAgentExecutionError(conversationId, executionId, error));

            log.info("Agent 执行已启动: 执行ID={}", executionId);
            return ResponseEntity.ok(ApiResponse.success(response));

        } catch (Exception e) {
            log.error("执行 Agent 失败: 对话ID={}, 用户={}, 错误={}",
                    conversationId, authentication.getName(), e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("执行 Agent 失败: " + e.getMessage()));
        }
    }

    /**
     * 查询执行状态
     */
    @GetMapping("/executions/{executionId}/status")
    // @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<ExecutionStatusResponse>> getExecutionStatus(
            @PathVariable String executionId,
            Authentication authentication) {

        log.info("查询 Agent 执行状态: 执行ID={}, 用户={}", 
                executionId, authentication.getName());

        try {
            Long userId = getUserId(authentication);
            
            // 验证 executionId 格式
            try {
                UUID.fromString(executionId);
            } catch (IllegalArgumentException e) {
                log.warn("无效的执行ID格式: {}", executionId);
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("无效的执行ID格式"));
            }

            // TODO: 需要实现真正的执行状态存储和查询机制
            // 当前没有执行状态的持久化存储，需要添加：
            // 1. AgentExecution 实体
            // 2. AgentExecutionRepository 
            // 3. 在执行过程中实时更新状态
            
            log.warn("执行状态查询功能尚未完全实现，返回未找到状态: 执行ID={}", executionId);
            return ResponseEntity.notFound()
                    .build();

        } catch (Exception e) {
            log.error("查询执行状态失败: 执行ID={}, 用户={}, 错误={}",
                    executionId, authentication.getName(), e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("查询执行状态失败: " + e.getMessage()));
        }
    }

    /**
     * 取消 Agent 执行
     */
    @PostMapping("/executions/{executionId}/cancel")
    // @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Void>> cancelExecution(
            @PathVariable String executionId,
            Authentication authentication) {

        log.info("取消 Agent 执行: 执行ID={}, 用户={}", 
                executionId, authentication.getName());

        try {
            Long userId = getUserId(authentication);

            // TODO: 实现取消执行的逻辑
            // 这里需要调用 AgentExecutionService 来取消执行
            log.info("Agent 执行取消成功: 执行ID={}", executionId);
            
            return ResponseEntity.ok(ApiResponse.success(null));

        } catch (Exception e) {
            log.error("取消 Agent 执行失败: 执行ID={}, 用户={}, 错误={}",
                    executionId, authentication.getName(), e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("取消执行失败: " + e.getMessage()));
        }
    }

    /**
     * 获取 SSE 连接统计
     */
    @GetMapping("/admin/connections")
    // @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getConnectionStats() {
        try {
            Map<String, Object> stats = sseEmitterManager.getConnectionStats();
            return ResponseEntity.ok(ApiResponse.success(stats));
        } catch (Exception e) {
            log.error("获取连接统计失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取连接统计失败: " + e.getMessage()));
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 从认证信息中获取用户ID
     */
    private Long getUserId(Authentication authentication) {
        try {
            // 首先尝试使用 SecurityUtils 获取当前登录用户ID
            Long userId = SecurityUtils.getCurrentUserId();
            
            if (userId != null) {
                log.debug("成功获取当前用户ID: {} (用户名: {})", userId, authentication.getName());
                return userId;
            }
            
            // 如果通过 SecurityUtils 获取不到，尝试从请求属性中获取
            try {
                ServletRequestAttributes requestAttributes = (ServletRequestAttributes) 
                    org.springframework.web.context.request.RequestContextHolder.getRequestAttributes();
                if (requestAttributes != null) {
                    HttpServletRequest request = requestAttributes.getRequest();
                    Object userIdAttr = request.getAttribute("userId");
                    if (userIdAttr instanceof Long) {
                        log.debug("从请求属性获取用户ID: {}", userIdAttr);
                        return (Long) userIdAttr;
                    }
                }
            } catch (Exception e) {
                log.debug("从请求属性获取用户ID失败: {}", e.getMessage());
            }
            
            log.error("无法获取当前用户ID，authentication: {}", authentication);
            return null;
            
        } catch (Exception e) {
            log.error("获取当前用户ID时发生异常", e);
            return null;
        }
    }

    /**
     * 构建编排选项
     */
    private OrchestrationRequest.OrchestrationOptions buildOrchestrationOptions(SendMessageRequest request) {
        return OrchestrationRequest.OrchestrationOptions.builder()
                .enableStreaming(request.getEnableStreaming())
                .timeoutSeconds(30)
                .build();
    }

    /**
     * 构建执行选项
     */
    private AgentExecutionRequest.ExecutionOptions buildExecutionOptions(ExecuteAgentRequest.ExecutionOptions options) {
        if (options == null) {
            return AgentExecutionRequest.ExecutionOptions.defaultOptions();
        }

        return AgentExecutionRequest.ExecutionOptions.builder()
                .timeout(Duration.ofSeconds(options.getTimeoutSeconds()))
                .enableStreaming(options.getEnableStreaming())
                .enableCache(options.getEnableCache())
                .build();
    }

    /**
     * 将编排事件转换为 SSE 事件
     */
    private SseEvent convertOrchestrationEventToSse(com.dipspro.modules.agent.dto.OrchestrationEvent event) {
        String eventType = mapOrchestrationEventType(event.getEventType());

        return SseEvent.builder()
                .eventType(eventType)
                .conversationId(extractConversationId(event))
                .agentName(event.getAgentName())
                .stepId(event.getStepId())
                .timestamp(event.getTimestamp())
                .data(event.getData() != null ? event.getData() : Map.of())
                .build();
    }

    /**
     * 映射编排事件类型
     */
    private String mapOrchestrationEventType(String orchestrationEventType) {
        switch (orchestrationEventType) {
            case "ORCHESTRATION_STARTED":
                return SseEvent.EventType.ORCHESTRATION_STARTED;
            case "ORCHESTRATION_PROGRESS":
                return SseEvent.EventType.ORCHESTRATION_PROGRESS;
            case "ORCHESTRATION_COMPLETED":
                return SseEvent.EventType.ORCHESTRATION_COMPLETED;
            case "ORCHESTRATION_FAILED":
                return SseEvent.EventType.ORCHESTRATION_FAILED;
            default:
                return SseEvent.EventType.ORCHESTRATION_PROGRESS;
        }
    }

    /**
     * 从编排事件中提取对话ID
     */
    private String extractConversationId(com.dipspro.modules.agent.dto.OrchestrationEvent event) {
        if (event.getData() != null && event.getData().containsKey("conversationId")) {
            return (String) event.getData().get("conversationId");
        }
        return null;
    }

    /**
     * 处理编排结果
     */
    private void handleOrchestrationResult(String conversationId, String messageId, OrchestrationResult result, 
                                          Long userId, Long roundSequence) {
        log.info("编排执行完成: 对话ID={}, 消息ID={}, 状态={}",
                conversationId, messageId, result.getStatus());

        try {
            // 发送完成事件 - 安全处理空值情况
            String resultContent = "处理完成";
            if (result.getFinalResult() != null && result.getFinalResult().getContent() != null) {
                resultContent = result.getFinalResult().getContent();
            } else if (result.getStatus() != null) {
                resultContent = "处理状态: " + result.getStatus();
            }
            
            SseEvent completedEvent = SseEvent.orchestrationCompleted(conversationId, resultContent);
            sseEmitterManager.broadcast(conversationId, completedEvent);

            // 保存AI回复消息到数据库 - 确保有内容可保存
            UUID conversationUuid = UUID.fromString(conversationId);
            String finalContent = null;
            
            if (result.isSuccessful() && result.getFinalResult() != null && result.getFinalResult().getContent() != null) {
                finalContent = result.getFinalResult().getContent();
            } else if (result.getErrorMessage() != null) {
                finalContent = "抱歉，处理您的请求时遇到了问题：" + result.getErrorMessage();
            } else {
                finalContent = "处理完成，但没有生成具体内容。";
            }
            
            ChatMessage assistantMessage = saveAssistantMessage(conversationUuid, userId, 
                    finalContent, roundSequence);
            log.info("AI回复消息已保存: 消息ID={}, 对话ID={}, 轮次={}, 内容长度={}", 
                    assistantMessage.getId(), conversationId, roundSequence, finalContent.length());

        } catch (Exception e) {
            log.error("处理编排结果失败: 对话ID={}, 消息ID={}, 错误={}",
                    conversationId, messageId, e.getMessage(), e);
        }
    }

    /**
     * 处理编排错误
     */
    private void handleOrchestrationError(String conversationId, String messageId, Throwable error) {
        log.error("编排执行失败: 对话ID={}, 消息ID={}, 错误={}",
                conversationId, messageId, error.getMessage(), error);

        try {
            // 发送错误事件
            SseEvent errorEvent = SseEvent.error(conversationId, "处理失败: " + error.getMessage());
            sseEmitterManager.broadcast(conversationId, errorEvent);

            // 尝试保存错误消息到数据库
            // 注意：这里需要获取userId和roundSequence，但是在当前上下文中没有这些信息
            // 所以我们只记录错误，不保存到数据库
            // TODO: 如果需要保存错误消息，需要修改方法签名传递这些参数
            
        } catch (Exception e) {
            log.error("发送错误事件失败: 对话ID={}, 消息ID={}", conversationId, messageId, e);
        }
    }

    /**
     * 处理 Agent 执行结果
     */
    private void handleAgentExecutionResult(String conversationId, AgentExecutionResult result) {
        log.info("Agent 执行完成: 对话ID={}, AgentID={}, 状态={}",
                conversationId, result.getAgentId(), result.getStatus());

        try {
            // 发送 Agent 完成事件
            SseEvent completedEvent = SseEvent.agentCompleted(conversationId,
                    result.getAgentName(), result.getContent());
            sseEmitterManager.broadcast(conversationId, completedEvent);

        } catch (Exception e) {
            log.error("处理 Agent 执行结果失败: 对话ID={}", conversationId, e);
        }
    }

    /**
     * 处理 Agent 执行错误
     */
    private void handleAgentExecutionError(String conversationId, UUID executionId, Throwable error) {
        log.error("Agent 执行失败: 对话ID={}, 执行ID={}, 错误={}",
                conversationId, executionId, error.getMessage(), error);

        try {
            // 发送错误事件
            SseEvent errorEvent = SseEvent.error(conversationId, "Agent 执行失败: " + error.getMessage());
            sseEmitterManager.broadcast(conversationId, errorEvent);

        } catch (Exception e) {
            log.error("发送 Agent 错误事件失败: 对话ID={}, 执行ID={}", conversationId, executionId, e);
        }
    }

    /**
     * 保存用户消息到数据库
     */
    private ChatMessage saveUserMessage(UUID conversationId, Long userId, String content, String messageId) {
        try {
            // 查找对话
            Conversation conversation = conversationRepository.findById(conversationId)
                    .orElseThrow(() -> new RuntimeException("对话不存在: " + conversationId));

            // 获取下一个轮次序号
            Long nextRoundSequence = getNextRoundSequence(conversationId);

            // 创建用户消息
            ChatMessage userMessage = new ChatMessage();
            userMessage.setId(UUID.fromString(messageId));
            userMessage.setConversation(conversation);
            userMessage.setRole(ChatMessage.MessageRole.USER);
            userMessage.setContent(content);
            userMessage.setUserId(userId.toString());
            userMessage.setRoundSequence(nextRoundSequence);
            userMessage.setMessageOrder(1); // 用户消息总是轮次中的第一条

            // 保存到数据库
            ChatMessage savedMessage = chatMessageRepository.save(userMessage);
            
            log.debug("用户消息保存成功: ID={}, 对话ID={}, 轮次={}", 
                    savedMessage.getId(), conversationId, nextRoundSequence);
                    
            return savedMessage;
            
        } catch (Exception e) {
            log.error("保存用户消息失败: 对话ID={}, 用户ID={}, 错误={}",
                    conversationId, userId, e.getMessage(), e);
            throw new RuntimeException("保存用户消息失败", e);
        }
    }

    /**
     * 保存AI回复消息到数据库
     */
    private ChatMessage saveAssistantMessage(UUID conversationId, Long userId, String content, Long roundSequence) {
        try {
            // 查找对话
            Conversation conversation = conversationRepository.findById(conversationId)
                    .orElseThrow(() -> new RuntimeException("对话不存在: " + conversationId));

            // 获取该轮次下一个消息序号
            Integer nextMessageOrder = getNextMessageOrderInRound(conversationId, roundSequence);

            // 创建AI回复消息
            ChatMessage assistantMessage = new ChatMessage();
            assistantMessage.setConversation(conversation);
            assistantMessage.setRole(ChatMessage.MessageRole.ASSISTANT);
            assistantMessage.setContent(content);
            assistantMessage.setUserId(userId.toString());
            assistantMessage.setRoundSequence(roundSequence);
            assistantMessage.setMessageOrder(nextMessageOrder);

            // 保存到数据库
            ChatMessage savedMessage = chatMessageRepository.save(assistantMessage);
            
            log.debug("AI回复消息保存成功: ID={}, 对话ID={}, 轮次={}, 顺序={}", 
                    savedMessage.getId(), conversationId, roundSequence, nextMessageOrder);
                    
            return savedMessage;
            
        } catch (Exception e) {
            log.error("保存AI回复消息失败: 对话ID={}, 用户ID={}, 错误={}",
                    conversationId, userId, e.getMessage(), e);
            throw new RuntimeException("保存AI回复消息失败", e);
        }
    }

    /**
     * 获取下一个轮次序号
     */
    private Long getNextRoundSequence(UUID conversationId) {
        try {
            // 查询该对话最大的轮次序号
            Long maxRoundSequence = chatMessageRepository.findMaxRoundSequenceByConversationId(conversationId);
            return (maxRoundSequence != null) ? maxRoundSequence + 1 : 1L;
        } catch (Exception e) {
            log.warn("获取轮次序号失败，使用默认值1: 对话ID={}, 错误={}", conversationId, e.getMessage());
            return 1L;
        }
    }

    /**
     * 获取轮次内下一个消息序号
     */
    private Integer getNextMessageOrderInRound(UUID conversationId, Long roundSequence) {
        try {
            // 查询该轮次内最大的消息序号
            Long maxMessageOrder = chatMessageRepository.findMaxMessageOrderInRound(conversationId, roundSequence);
            return (maxMessageOrder != null) ? (int)(maxMessageOrder + 1) : 2; // AI回复从2开始
        } catch (Exception e) {
            log.warn("获取轮次内消息序号失败，使用默认值2: 对话ID={}, 轮次={}, 错误={}", 
                    conversationId, roundSequence, e.getMessage());
            return 2;
        }
    }

    /**
     * 测试SSE端点是否可达 - 简化版本
     */
    @GetMapping("/test-sse/{conversationId}")
    public ResponseEntity<ApiResponse<String>> testSse(@PathVariable String conversationId) {
        return ResponseEntity.ok(ApiResponse.success("SSE端点可达，对话ID: " + conversationId));
    }

    /**
     * 健康检查端点
     */
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<String>> healthCheck() {
        return ResponseEntity.ok(ApiResponse.success("Agent Chat 服务运行正常"));
    }

    /**
     * 获取JwtUtil实例，处理依赖注入失败的情况
     */
    private JwtUtil getJwtUtil() {
        // 首先尝试使用注入的实例
        if (jwtUtil != null) {
            return jwtUtil;
        }
        
        // 如果注入失败，尝试从ApplicationContext获取
        try {
            JwtUtil jwtUtilFromContext = applicationContext.getBean(JwtUtil.class);
            log.debug("从ApplicationContext获取JwtUtil实例成功");
            return jwtUtilFromContext;
        } catch (Exception e) {
            log.error("从ApplicationContext获取JwtUtil实例失败: {}", e.getMessage());
            return null;
        }
    }
}