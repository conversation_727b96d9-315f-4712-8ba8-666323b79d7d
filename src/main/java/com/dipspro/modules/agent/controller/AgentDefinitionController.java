package com.dipspro.modules.agent.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.modules.agent.dto.AgentDefinitionDto;
import com.dipspro.modules.agent.entity.AgentDefinition;
import com.dipspro.modules.agent.service.AgentDefinitionService;
import com.dipspro.modules.agent.util.AgentDefinitionConverter;

import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;

/**
 * Agent 管理控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/admin/agents")
@Slf4j
@Validated
public class AgentDefinitionController {

    @Autowired
    private AgentDefinitionService agentDefinitionService;
    
    @Autowired
    private ChatClient chatClient;

    /**
     * 获取 Agent 列表
     */
    @GetMapping
    public ResponseEntity<ApiResponse<List<AgentDefinitionDto>>> getAgents(
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "limit", defaultValue = "20") int limit,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "category", required = false) String category,
            @RequestParam(value = "search", required = false) String search
    ) {
        log.info("获取 Agent 列表: page={}, limit={}, status={}, category={}, search={}", 
                page, limit, status, category, search);
        
        try {
            List<AgentDefinition> agents;
            
            // 如果有搜索关键词，使用搜索功能
            if (StringUtils.hasText(search)) {
                log.debug("使用搜索功能: {}", search);
                agents = agentDefinitionService.searchAgentsByName(search);
                
                // 对搜索结果进行额外的过滤（状态、类别）
                agents = applyFilters(agents, status, category);
                
                log.info("搜索并过滤 Agent 完成: 关键词='{}', 返回 {} 条记录", search, agents.size());
                
                // 转换为DTO
                List<AgentDefinitionDto> agentDtos = agents.stream()
                    .map(AgentDefinitionConverter::toDto)
                    .collect(Collectors.toList());
                
                return ResponseEntity.ok(ApiResponse.success(agentDtos));
            }
            
            // 创建分页参数
            Pageable pageable = PageRequest.of(
                Math.max(0, page - 1), // Spring Data 的页码从0开始，前端从1开始
                Math.max(1, limit),
                Sort.by(Sort.Direction.DESC, "createdAt")
            );
            
            // 执行分页查询
            Page<AgentDefinition> agentPage = agentDefinitionService.findAgents(pageable);
            
            // 获取当前页的数据
            agents = agentPage.getContent();
            
            // 应用过滤条件
            agents = applyFilters(agents, status, category);
            
            log.info("获取 Agent 列表成功: 返回 {} 条记录，总共 {} 条", 
                    agents.size(), agentPage.getTotalElements());
            
            // 转换为DTO
            List<AgentDefinitionDto> agentDtos = agents.stream()
                .map(AgentDefinitionConverter::toDto)
                .collect(Collectors.toList());
            
            // 构建响应数据
            // 注意：这里暂时只返回数据列表，分页信息可以在后续版本中通过响应头或包装对象提供
            return ResponseEntity.ok(ApiResponse.success(agentDtos));
            
        } catch (Exception e) {
            log.error("获取 Agent 列表失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取列表失败: " + e.getMessage()));
        }
    }

    /**
     * 根据 ID 获取 Agent
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<AgentDefinitionDto>> getAgentById(@PathVariable Long id) {
        log.info("获取 Agent，ID: {}", id);
        
        try {
            AgentDefinition agent = agentDefinitionService.getById(id);
            AgentDefinitionDto responseDto = AgentDefinitionConverter.toDto(agent);
            return ResponseEntity.ok(ApiResponse.success(responseDto));
        } catch (Exception e) {
            log.error("获取 Agent 失败，ID: {}", id, e);
            return ResponseEntity.ok(ApiResponse.error("获取失败: " + e.getMessage()));
        }
    }

    /**
     * 创建 Agent
     */
    @PostMapping
    public ResponseEntity<ApiResponse<AgentDefinitionDto>> createAgent(@Valid @RequestBody AgentDefinitionDto agentDefinitionDto) {
        log.info("创建 Agent: {}", agentDefinitionDto.getName());

        try {
            // DTO转Entity
            AgentDefinition agentDefinition = AgentDefinitionConverter.toEntity(agentDefinitionDto);
            AgentDefinition createdAgent = agentDefinitionService.createAgent(agentDefinition);
            
            // Entity转DTO返回
            AgentDefinitionDto responseDto = AgentDefinitionConverter.toDto(createdAgent);
            return ResponseEntity.ok(ApiResponse.success(responseDto));
        } catch (Exception e) {
            log.error("创建 Agent 失败", e);
            return ResponseEntity.ok(ApiResponse.error("创建失败: " + e.getMessage()));
        }
    }

    /**
     * 更新 Agent
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<AgentDefinitionDto>> updateAgent(
            @PathVariable Long id,
            @Valid @RequestBody AgentDefinitionDto agentDefinitionDto) {

        log.info("更新 Agent: {}", id);

        try {
            // 设置ID
            agentDefinitionDto.setId(id);
            
            // DTO转Entity
            AgentDefinition agentDefinition = AgentDefinitionConverter.toEntity(agentDefinitionDto);
            AgentDefinition updatedAgent = agentDefinitionService.updateAgent(agentDefinition);
            
            // Entity转DTO返回
            AgentDefinitionDto responseDto = AgentDefinitionConverter.toDto(updatedAgent);
            return ResponseEntity.ok(ApiResponse.success(responseDto));
        } catch (Exception e) {
            log.error("更新 Agent 失败", e);
            return ResponseEntity.ok(ApiResponse.error("更新失败: " + e.getMessage()));
        }
    }

    /**
     * 删除 Agent
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<String>> deleteAgent(@PathVariable Long id) {
        log.info("删除 Agent: {}", id);

        try {
            agentDefinitionService.deleteById(id);
            return ResponseEntity.ok(ApiResponse.success("删除成功"));
        } catch (Exception e) {
            log.error("删除 Agent 失败", e);
            return ResponseEntity.ok(ApiResponse.error("删除失败: " + e.getMessage()));
        }
    }

    /**
     * 更新 Agent 状态
     */
    @PatchMapping("/{id}/status")
    public ResponseEntity<ApiResponse<Void>> updateAgentStatus(
            @PathVariable Long id,
            @RequestBody Map<String, String> statusRequest) {

        log.info("更新 Agent 状态: {}, 状态: {}", id, statusRequest.get("status"));

        try {
            String statusStr = statusRequest.get("status");
            AgentDefinition.AgentStatus status = AgentDefinition.AgentStatus.valueOf(statusStr);
            agentDefinitionService.updateAgentStatus(id, status);
            return ResponseEntity.ok(ApiResponse.success(null));
        } catch (Exception e) {
            log.error("更新 Agent 状态失败", e);
            return ResponseEntity.ok(ApiResponse.error("状态更新失败: " + e.getMessage()));
        }
    }

    /**
     * 测试 Agent
     */
    @PostMapping("/{id}/test")
    public ResponseEntity<ApiResponse<Map<String, Object>>> testAgent(
            @PathVariable Long id,
            @RequestBody Map<String, Object> testData) {

        log.info("测试 Agent: {}", id);

        try {
            // 获取 Agent 信息
            AgentDefinition agent = agentDefinitionService.getById(id);
            
            // 获取测试输入
            String input = (String) testData.get("input");
            Map<String, Object> parameters = (Map<String, Object>) testData.get("parameters");
            
            // 模拟测试逻辑（后续可以集成真实的Agent执行引擎）
            long startTime = System.currentTimeMillis();
            
            // 这里可以调用实际的Agent执行逻辑
            String output = processAgentTest(agent, input, parameters);
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("output", output);
            
            Map<String, Object> metrics = new HashMap<>();
            metrics.put("duration", duration);
            metrics.put("status", "SUCCESS");
            metrics.put("agentName", agent.getName());
            response.put("metrics", metrics);
            
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("测试 Agent 失败", e);
            return ResponseEntity.ok(ApiResponse.error("测试失败: " + e.getMessage()));
        }
    }

    /**
     * 处理 Agent 测试逻辑 - 调用真实大模型
     */
    private String processAgentTest(AgentDefinition agent, String input, Map<String, Object> parameters) {
        try {
            // 构建系统提示词
            String systemPrompt = buildSystemPrompt(agent, parameters);
            
            // 构建用户输入
            String userPrompt = buildUserPrompt(input, parameters);

            log.info("======================================================");
            log.info("调用大模型测试 Agent: AgentId={}, AgentName={}, SystemPrompt Length={}, UserPrompt Length={}",
                    agent.getId(), agent.getName(), 
                    systemPrompt != null ? systemPrompt.length() : 0, 
                    userPrompt.length());
            
            // 调用 Spring AI ChatClient
            String aiResponse = chatClient.prompt()
                    .system(systemPrompt != null ? systemPrompt : getDefaultSystemPrompt())
                    .user(userPrompt)
                    .call()
                    .content();
            
//            log.info("大模型调用成功: AgentId={}, Response Length={}", agent.getId(), aiResponse.length());
            log.info("大模型调用成功: AgentId={}, Response={}", agent.getId(), aiResponse);
            log.info("======================================================");

            return aiResponse;
            
        } catch (Exception e) {
            log.error("大模型调用失败: AgentId={}, AgentName={}, Error={}", 
                    agent.getId(), agent.getName(), e.getMessage(), e);
            
            // 返回错误信息，但保持测试流程继续
            return String.format("大模型调用失败: %s\n\n--- 调试信息 ---\nAgent: %s\n输入: %s\n错误: %s", 
                    e.getMessage(), agent.getName(), input, e.toString());
        }
    }
    
    /**
     * 构建系统提示词
     */
    private String buildSystemPrompt(AgentDefinition agent, Map<String, Object> parameters) {
        StringBuilder systemPrompt = new StringBuilder();
        
        // 使用 Agent 的系统提示词
        if (!StringUtils.hasText(agent.getSystemPrompt())) {
            throw new IllegalArgumentException("Agent没有提示词");
        }
        
        // 添加 Agent 描述信息
        if (StringUtils.hasText(agent.getDescription())) {
            systemPrompt.append("\n\nAgent描述: ").append(agent.getDescription());
        }
        
        // 添加 Agent 能力信息
        if (StringUtils.hasText(agent.getCapabilities())) {
            systemPrompt.append("\n\nAgent能力: ").append(agent.getCapabilities());
        }
        
        // 添加参数信息
        if (parameters != null && !parameters.isEmpty()) {
            systemPrompt.append("\n\n当前测试参数: ");
            parameters.forEach((key, value) -> 
                systemPrompt.append("\n- ").append(key).append(": ").append(value));
        }
        
        return systemPrompt.toString();
    }
    
    /**
     * 构建用户提示词
     */
    private String buildUserPrompt(String input, Map<String, Object> parameters) {
        if (!StringUtils.hasText(input)) {
            return "请介绍一下你的功能和能力。";
        }
        
        // 如果有参数，可以在用户输入中引用
        if (parameters != null && !parameters.isEmpty()) {
            StringBuilder userPrompt = new StringBuilder(input);
            userPrompt.append("\n\n[测试参数]");
            parameters.forEach((key, value) -> 
                userPrompt.append("\n").append(key).append(": ").append(value));
            return userPrompt.toString();
        }
        
        return input;
    }
    
    /**
     * 获取默认系统提示词
     */
    private String getDefaultSystemPrompt() {
        return "你是一个有用的AI助手，请用中文回答用户的问题。这是一个Agent测试环境。";
    }

    /**
     * 获取 Agent 统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<ApiResponse<AgentDefinitionService.AgentStats>> getAgentStats() {
        log.info("获取 Agent 统计信息");
        
        try {
            AgentDefinitionService.AgentStats stats = agentDefinitionService.getAgentStatistics();
            return ResponseEntity.ok(ApiResponse.success(stats));
        } catch (Exception e) {
            log.error("获取 Agent 统计信息失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取统计信息失败: " + e.getMessage()));
        }
    }

    /**
     * 应用过滤条件到 Agent 列表
     */
    private List<AgentDefinition> applyFilters(List<AgentDefinition> agents, String status, String category) {
        return agents.stream()
                .filter(agent -> {
                    // 状态过滤
                    if (StringUtils.hasText(status)) {
                        try {
                            AgentDefinition.AgentStatus agentStatus = AgentDefinition.AgentStatus.valueOf(status.toUpperCase());
                            if (!agent.getStatus().equals(agentStatus)) {
                                return false;
                            }
                        } catch (IllegalArgumentException e) {
                            log.warn("无效的状态参数: {}", status);
                            return false;
                        }
                    }
                    
                    // 类别过滤
                    if (StringUtils.hasText(category)) {
                        if (!category.equalsIgnoreCase(agent.getCategory())) {
                            return false;
                        }
                    }
                    
                    return true;
                })
                .collect(Collectors.toList());
    }
}