package com.dipspro.modules.agent.service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.dipspro.modules.agent.dto.AgentExecutionRequest;
import com.dipspro.modules.agent.dto.AgentExecutionResult;
import com.dipspro.modules.agent.dto.OrchestrationRequest;
import com.dipspro.modules.agent.dto.OrchestrationResult;
import com.dipspro.modules.agent.entity.AgentDefinition;
import com.dipspro.util.AgentThinkingMessageUtil;
import com.dipspro.util.MarkdownJsonParser;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * AI Agent 编排服务 - 传统多Agent协调（与新的链式工作流并行存在）
 * <p>
 * 核心功能：
 * 1. 多Agent协调执行
 * 2. Agent间数据传递
 * 3. 执行流程管理
 * 4. 与新的链式工作流架构并行存在
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AiAgentOrchestrationService {

    private final AgentExecutionEngine executionEngine;
    private final AgentDefinitionService agentDefinitionService;
    private final AgentThinkingMessageUtil thinkingMessageUtil;
    private final ObjectMapper objectMapper;

    /**
     * 顺序执行多个Agent
     *
     * @param agentIds       Agent ID列表
     * @param initialData    初始数据
     * @param conversationId 会话ID
     * @param userId         用户ID
     * @return 最终执行结果
     */
    public AgentExecutionResult executeSequential(List<Long> agentIds, Map<String, Object> initialData,
                                                  String conversationId, Long userId) {
        try {
            log.info("开始顺序执行Agent链: AgentIds={}, ConversationId={}", agentIds, conversationId);

            // 安全处理空值情况
            if (agentIds == null || agentIds.isEmpty()) {
                log.warn("Agent链为空，返回空结果: ConversationId={}", conversationId);
                return AgentExecutionResult.builder()
                        .status(com.dipspro.modules.agent.entity.AgentExecution.ExecutionStatus.COMPLETED)
                        .content("没有可执行的Agent")
                        .startTime(LocalDateTime.now())
                        .endTime(LocalDateTime.now())
                        .build();
            }

            // 安全处理 initialData 为 null 的情况
            Map<String, Object> currentData = new HashMap<>(initialData != null ? initialData : new HashMap<>());
            AgentExecutionResult lastResult = null;

            for (Long agentId : agentIds) {
                // 构建执行请求
                AgentExecutionRequest request = AgentExecutionRequest.builder()
                        .agentId(agentId)
                        .conversationId(conversationId)
                        .userId(userId)
                        .inputData(currentData)
                        .build();

                // 执行Agent
                AgentExecutionResult result = executionEngine.execute(request);

                if (result.isFailed()) {
                    log.error("Agent执行失败，中断链路: AgentId={}, Error={}",
                            agentId, result.getErrorMessage());
                    return result;
                }

                // 更新数据用于下一个Agent
                if (result.getOutputData() != null) {
                    currentData.putAll(result.getOutputData());
                }

                lastResult = result;

                log.info("Agent执行完成: AgentId={}, Status={}", agentId, result.getStatus());
            }

            log.info("Agent链顺序执行完成: ConversationId={}", conversationId);
            return lastResult;

        } catch (Exception e) {
            log.error("Agent链执行失败: Error={}", e.getMessage(), e);

            return AgentExecutionResult.builder()
                    .status(com.dipspro.modules.agent.entity.AgentExecution.ExecutionStatus.FAILED)
                    .errorMessage("Agent链执行失败: " + e.getMessage())
                    .startTime(LocalDateTime.now())
                    .endTime(LocalDateTime.now())
                    .build();
        }
    }

    /**
     * AI驱动的编排执行 - 按照chain_agent.md设计文档实现
     *
     * @param request 编排请求
     * @return 编排结果
     */
    public Mono<OrchestrationResult> orchestrate(OrchestrationRequest request) {
        try {
            log.info("开始执行AI驱动编排: ConversationId={}, UserMessage={}",
                    request.getConversationId(), request.getUserMessage());

            LocalDateTime startTime = LocalDateTime.now();

            // 将编排请求转换为顺序执行 - 提供默认值
            List<Long> agentIds = request.getAgentIds();

            // 如果没有指定 agentIds，使用AI驱动的意图识别流程
            if (agentIds == null || agentIds.isEmpty()) {
                log.info("未指定Agent链，启动AI驱动意图识别: ConversationId={}", request.getConversationId());
                return executeAiDrivenOrchestration(request, startTime);
            }

            // 如果指定了agentIds，使用传统的顺序执行
            return executeTraditionalOrchestration(request, agentIds, startTime);

        } catch (Exception e) {
            log.error("编排执行失败: Error={}", e.getMessage(), e);

            OrchestrationResult errorResult = OrchestrationResult.builder()
                    .conversationId(request.getConversationId())
                    .status("FAILED")
                    .successful(false)
                    .errorMessage("编排执行失败: " + e.getMessage())
                    .startTime(LocalDateTime.now())
                    .endTime(LocalDateTime.now())
                    .build();

            return Mono.just(errorResult);
        }
    }

    /**
     * AI驱动的编排执行 - 核心实现
     */
    private Mono<OrchestrationResult> executeAiDrivenOrchestration(OrchestrationRequest request, LocalDateTime startTime) {
        try {
            // 1. 查找意图识别Agent
            List<AgentDefinition> intentAgents = agentDefinitionService.findByRole(
                    AgentDefinition.AgentRole.INTENT_RECOGNITION);

            if (intentAgents.isEmpty()) {
                log.warn("未找到意图识别Agent，返回默认回复");
                return createDefaultResponse(request, startTime);
            }

            AgentDefinition intentAgent = intentAgents.get(0); // 取第一个启用的意图识别Agent
            log.info("找到意图识别Agent: {}", intentAgent.getName());
            
            // 立即向前端发送"智能体正在思考"消息
            thinkingMessageUtil.sendThinkingMessage(request.getConversationId(), intentAgent.getName(), "智能体正在思考您的问题：" + request.getUserMessage());

            // 2. 构建意图识别请求
            Map<String, Object> intentInput = new HashMap<>();
            intentInput.put("user_message", request.getUserMessage());
            intentInput.put("conversation_id", request.getConversationId());
            intentInput.put("user_id", request.getUserId());
            intentInput.put("context", request.getContext());

            AgentExecutionRequest intentRequest = AgentExecutionRequest.builder()
                    .agentId(intentAgent.getId())
                    .conversationId(request.getConversationId())
                    .userId(request.getUserId())
                    .inputData(intentInput)
                    .executionId(java.util.UUID.randomUUID())
                    .build();

            // 3. 执行意图识别
            AgentExecutionResult intentResult = executionEngine.executeAgent(intentRequest);

            if (!intentResult.isSuccessful()) {
                log.error("意图识别失败: {}", intentResult.getErrorMessage());
                return createErrorResponse(request, "意图识别失败: " + intentResult.getErrorMessage(), startTime);
            }

            // 4. 输出思维过程
            String chainOfThought = intentResult.getChainOfThought();
            if (chainOfThought != null) {
                JsonNode jsonNode = objectMapper.readTree(chainOfThought);
                if (jsonNode.isArray()) {
                    for (JsonNode node : jsonNode) {
                        thinkingMessageUtil.sendThinkingMessage(request.getConversationId(), intentAgent.getName(), node.asText());
                    }
                }
            }

            // 根据意图识别指定的下一个Agent
            // 4. 解析链路指令
//            Map<String, Object> chainInstruction = parseChainInstruction(intentResult);
//            if (chainInstruction == null) {
//                log.warn("意图识别Agent未返回有效的链路指令，使用默认回复");
//                return createIntentBasedResponse(request, intentResult, startTime);
//            }

            // 5. 执行AI构建的Agent链
//            return executeAiGeneratedChain(request, chainInstruction, intentResult, startTime);
            return null;
        } catch (Exception e) {
            log.error("AI驱动编排执行失败", e);
            return createErrorResponse(request, "AI驱动编排执行失败: " + e.getMessage(), startTime);
        }
    }

    /**
     * 执行传统编排（指定了agentIds）
     */
    private Mono<OrchestrationResult> executeTraditionalOrchestration(
            OrchestrationRequest request, List<Long> agentIds, LocalDateTime startTime) {

        Map<String, Object> initialData = request.getInputData();
        AgentExecutionResult result = executeSequential(
                agentIds,
                initialData,
                request.getConversationId(),
                request.getUserId()
        );

        // 构建编排结果
        OrchestrationResult orchestrationResult = OrchestrationResult.builder()
                .conversationId(request.getConversationId())
                .status("COMPLETED")
                .successful(result.isSuccessful())
                .finalResult(result)
                .executionTime(result.getDurationMs())
                .startTime(startTime)
                .endTime(LocalDateTime.now())
                .build();

        return Mono.just(orchestrationResult);
    }

    // ==================== AI驱动编排的辅助方法 ====================

    /**
     * 解析AI返回的链路指令 - 按照设计文档中的JSON格式
     */
    private Map<String, Object> parseChainInstruction(AgentExecutionResult intentResult) {
        try {
            String content = intentResult.getContent();
            if (content == null || content.trim().isEmpty()) {
                return null;
            }

            // 使用MarkdownJsonParser处理可能包含Markdown格式的响应
            JsonNode rootNode = MarkdownJsonParser.extractAndParseJson(content);

            // 如果Markdown解析失败，尝试直接解析
            if (rootNode == null) {
                log.warn("Markdown JSON解析失败，尝试直接解析: {}", content.substring(0, Math.min(100, content.length())));
                try {
                    rootNode = objectMapper.readTree(content);
                } catch (Exception jsonException) {
                    log.warn("直接JSON解析也失败，content不是有效的JSON格式: {}", jsonException.getMessage());
                    // 如果content不是JSON格式，尝试从chainInstruction字段中解析
                    return parseFromChainInstructionField(intentResult);
                }
            }

            // 检查是否包含chain_instruction
            if (rootNode.has("chain_instruction")) {
                JsonNode chainNode = rootNode.get("chain_instruction");
                return objectMapper.convertValue(chainNode, Map.class);
            }

            // 检查response_type是否为chain_execution
            if (rootNode.has("response_type") &&
                    "chain_execution".equals(rootNode.get("response_type").asText())) {

                if (rootNode.has("chain_instruction")) {
                    JsonNode chainNode = rootNode.get("chain_instruction");
                    return objectMapper.convertValue(chainNode, Map.class);
                }
            }

            return null;

        } catch (Exception e) {
            log.warn("解析链路指令失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从 AgentExecutionResult 的 chainInstruction 字段中解析链路指令
     */
    private Map<String, Object> parseFromChainInstructionField(AgentExecutionResult intentResult) {
        try {
            Map<String, Object> chainInstruction = intentResult.getChainInstruction();
            if (chainInstruction != null && !chainInstruction.isEmpty()) {
                log.info("从 chainInstruction 字段解析到链路指令: {}", chainInstruction);
                return chainInstruction;
            }
            
            log.warn("chainInstruction 字段为空，无法解析链路指令");
            return null;
        } catch (Exception e) {
            log.warn("从 chainInstruction 字段解析链路指令失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 执行AI生成的Agent链
     */
    private Mono<OrchestrationResult> executeAiGeneratedChain(
            OrchestrationRequest request,
//            Map<String, Object> chainInstruction,
            AgentExecutionResult intentResult,
            LocalDateTime startTime) {

        try {
            log.info("开始执行AI生成的Agent链: ConversationId={}", request.getConversationId());

            // 解析agent_chain
            @SuppressWarnings("unchecked")
//            List<Map<String, Object>> agentChain = (List<Map<String, Object>>) chainInstruction.get("agent_chain");
            List<Map<String, Object>> agentChain = null;

            if (agentChain == null || agentChain.isEmpty()) {
                log.warn("AI返回的agent_chain为空，使用意图识别结果");
                return createIntentBasedResponse(request, intentResult, startTime);
            }

            // 按顺序执行Agent链
            AgentExecutionResult finalResult = executeAgentChain(agentChain, request);

            // 构建编排结果
            OrchestrationResult result = OrchestrationResult.builder()
                    .conversationId(request.getConversationId())
                    .status("COMPLETED")
                    .successful(finalResult.isSuccessful())
                    .finalResult(finalResult)
                    .executionTime(finalResult.getDurationMs())
                    .startTime(startTime)
                    .endTime(LocalDateTime.now())
                    .build();

            return Mono.just(result);

        } catch (Exception e) {
            log.error("执行AI生成的Agent链失败", e);
            return createErrorResponse(request, "执行AI生成的Agent链失败: " + e.getMessage(), startTime);
        }
    }

    /**
     * 执行Agent链
     */
    private AgentExecutionResult executeAgentChain(
            List<Map<String, Object>> agentChain,
            OrchestrationRequest request) {

        Map<String, Object> currentData = new HashMap<>();
        currentData.put("user_message", request.getUserMessage());
        currentData.put("conversation_id", request.getConversationId());

        AgentExecutionResult lastResult = null;

        for (Map<String, Object> stepConfig : agentChain) {
            try {
                String agentName = (String) stepConfig.get("agent_name");
                String stepDescription = (String) stepConfig.get("description");

                log.info("执行Agent步骤: {} - {}", agentName, stepDescription);

                // 查找Agent
                AgentDefinition agent = agentDefinitionService.findByName(agentName);

                // 构建执行请求
                AgentExecutionRequest agentRequest = AgentExecutionRequest.builder()
                        .agentId(agent.getId())
                        .conversationId(request.getConversationId())
                        .userId(request.getUserId())
                        .inputData(currentData)
                        .executionId(java.util.UUID.randomUUID())
                        .build();

                // 执行Agent
                AgentExecutionResult result = executionEngine.executeAgent(agentRequest);

                if (!result.isSuccessful()) {
                    log.error("Agent步骤执行失败: {} - {}", agentName, result.getErrorMessage());
                    return result; // 返回失败结果
                }

                // 更新数据用于下一个Agent
                if (result.getOutputData() != null) {
                    currentData.putAll(result.getOutputData());
                }
                currentData.put("previous_result", result.getContent());

                lastResult = result;
                log.info("Agent步骤执行成功: {}", agentName);

            } catch (Exception e) {
                log.error("Agent链执行中断", e);
                return AgentExecutionResult.builder()
                        .status(com.dipspro.modules.agent.entity.AgentExecution.ExecutionStatus.FAILED)
                        .errorMessage("Agent链执行中断: " + e.getMessage())
                        .startTime(LocalDateTime.now())
                        .endTime(LocalDateTime.now())
                        .build();
            }
        }

        return lastResult != null ? lastResult : AgentExecutionResult.builder()
                .status(com.dipspro.modules.agent.entity.AgentExecution.ExecutionStatus.COMPLETED)
                .content("Agent链执行完成，但无结果返回")
                .startTime(LocalDateTime.now())
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建默认响应（未找到意图识别Agent时）
     */
    private Mono<OrchestrationResult> createDefaultResponse(OrchestrationRequest request, LocalDateTime startTime) {
        AgentExecutionResult defaultResult = AgentExecutionResult.builder()
                .status(com.dipspro.modules.agent.entity.AgentExecution.ExecutionStatus.COMPLETED)
                .content("您好！我已经收到您的消息：\"" + request.getUserMessage() + "\"\n\n" +
                        "目前系统中还没有配置意图识别Agent，暂时返回这个默认回复。\n" +
                        "请联系管理员配置INTENT_RECOGNITION角色的Agent。")
                .startTime(startTime)
                .endTime(LocalDateTime.now())
                .build();

        OrchestrationResult result = OrchestrationResult.builder()
                .conversationId(request.getConversationId())
                .status("COMPLETED")
                .successful(true)
                .finalResult(defaultResult)
                .startTime(startTime)
                .endTime(LocalDateTime.now())
                .build();

        return Mono.just(result);
    }

    /**
     * 创建基于意图识别结果的响应（未返回有效链路指令时）
     */
    private Mono<OrchestrationResult> createIntentBasedResponse(
            OrchestrationRequest request,
            AgentExecutionResult intentResult,
            LocalDateTime startTime) {

        // 使用意图识别的结果作为最终回复
        AgentExecutionResult finalResult = AgentExecutionResult.builder()
                .status(com.dipspro.modules.agent.entity.AgentExecution.ExecutionStatus.COMPLETED)
                .content(intentResult.getContent())
                .chainOfThought(intentResult.getChainOfThought())
                .startTime(startTime)
                .endTime(LocalDateTime.now())
                .build();

        OrchestrationResult result = OrchestrationResult.builder()
                .conversationId(request.getConversationId())
                .status("COMPLETED")
                .successful(true)
                .finalResult(finalResult)
                .startTime(startTime)
                .endTime(LocalDateTime.now())
                .build();

        return Mono.just(result);
    }

    /**
     * 创建错误响应
     */
    private Mono<OrchestrationResult> createErrorResponse(
            OrchestrationRequest request,
            String errorMessage,
            LocalDateTime startTime) {

        AgentExecutionResult errorResult = AgentExecutionResult.builder()
                .status(com.dipspro.modules.agent.entity.AgentExecution.ExecutionStatus.FAILED)
                .errorMessage(errorMessage)
                .content("抱歉，处理您的请求时遇到了问题：" + errorMessage)
                .startTime(startTime)
                .endTime(LocalDateTime.now())
                .build();

        OrchestrationResult result = OrchestrationResult.builder()
                .conversationId(request.getConversationId())
                .status("FAILED")
                .successful(false)
                .finalResult(errorResult)
                .errorMessage(errorMessage)
                .startTime(startTime)
                .endTime(LocalDateTime.now())
                .build();

        return Mono.just(result);
    }


}
