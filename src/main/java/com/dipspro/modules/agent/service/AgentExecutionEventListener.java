package com.dipspro.modules.agent.service;

import com.dipspro.modules.agent.dto.AgentExecutionEvent;
import com.dipspro.modules.agent.dto.OrchestrationEvent;
import com.dipspro.modules.agent.dto.api.SseEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * Agent 执行事件监听器
 * 负责监听 Agent 执行事件并转发到 SSE 连接
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class AgentExecutionEventListener {
    
    private final SseEmitterManager sseEmitterManager;
    
    /**
     * 监听 Agent 执行事件
     */
    @EventListener
    @Async("agentExecutorPool")
    public void handleAgentExecutionEvent(AgentExecutionEvent event) {
        log.debug("处理 Agent 执行事件: 类型={}, 对话ID={}", 
                event.getEventType(), event.getConversationId());
        
        try {
            // 转换为 SSE 事件
            SseEvent sseEvent = convertToSseEvent(event);
            
            // 广播到相关对话
            if (StringUtils.hasText(event.getConversationId())) {
                sseEmitterManager.broadcast(event.getConversationId(), sseEvent);
            }
            
        } catch (Exception e) {
            log.error("处理 Agent 执行事件失败: 事件类型={}, 错误={}", 
                    event.getEventType(), e.getMessage(), e);
        }
    }
    
    /**
     * 监听编排事件
     */
    @EventListener
    @Async("agentExecutorPool")
    public void handleOrchestrationEvent(OrchestrationEvent event) {
        log.debug("处理编排事件: 类型={}, 编排ID={}", 
                event.getEventType(), event.getOrchestrationId());
        
        try {
            SseEvent sseEvent = convertOrchestrationToSseEvent(event);
            
            // 这里需要从事件中获取对话ID
            String conversationId = extractConversationId(event);
            if (StringUtils.hasText(conversationId)) {
                sseEmitterManager.broadcast(conversationId, sseEvent);
            }
            
        } catch (Exception e) {
            log.error("处理编排事件失败: 事件类型={}, 错误={}", 
                    event.getEventType(), e.getMessage(), e);
        }
    }
    
    /**
     * 将 Agent 执行事件转换为 SSE 事件
     */
    private SseEvent convertToSseEvent(AgentExecutionEvent event) {
        String sseEventType = mapAgentEventType(event.getEventType());
        
        return SseEvent.builder()
            .eventType(sseEventType)
            .conversationId(event.getConversationId())
            .agentName(event.getAgentName())
            .stepId(event.getStepId())
            .timestamp(event.getTimestamp())
            .data(buildAgentEventData(event))
            .build();
    }
    
    /**
     * 将编排事件转换为 SSE 事件
     */
    private SseEvent convertOrchestrationToSseEvent(OrchestrationEvent event) {
        String sseEventType = mapOrchestrationEventType(event.getEventType());
        String conversationId = extractConversationId(event);
        
        return SseEvent.builder()
            .eventType(sseEventType)
            .conversationId(conversationId)
            .agentName(event.getAgentName())
            .stepId(event.getStepId())
            .timestamp(event.getTimestamp())
            .data(buildOrchestrationEventData(event))
            .build();
    }
    
    /**
     * 映射 Agent 事件类型到 SSE 事件类型
     */
    private String mapAgentEventType(String agentEventType) {
        switch (agentEventType) {
            case AgentExecutionEvent.EventType.EXECUTION_STARTED:
                return SseEvent.EventType.AGENT_PROGRESS;
            case AgentExecutionEvent.EventType.EXECUTION_THINKING:
                return SseEvent.EventType.AGENT_THINKING;
            case AgentExecutionEvent.EventType.EXECUTION_PROGRESS:
                return SseEvent.EventType.AGENT_PROGRESS;
            case AgentExecutionEvent.EventType.EXECUTION_COMPLETED:
                return SseEvent.EventType.AGENT_COMPLETED;
            case AgentExecutionEvent.EventType.EXECUTION_FAILED:
                return SseEvent.EventType.AGENT_FAILED;
            default:
                return SseEvent.EventType.AGENT_PROGRESS;
        }
    }
    
    /**
     * 映射编排事件类型到 SSE 事件类型
     */
    private String mapOrchestrationEventType(String orchestrationEventType) {
        switch (orchestrationEventType) {
            case OrchestrationEvent.EventType.ORCHESTRATION_STARTED:
                return SseEvent.EventType.ORCHESTRATION_STARTED;
            case OrchestrationEvent.EventType.ORCHESTRATION_PROGRESS:
                return SseEvent.EventType.ORCHESTRATION_PROGRESS;
            case OrchestrationEvent.EventType.ORCHESTRATION_COMPLETED:
                return SseEvent.EventType.ORCHESTRATION_COMPLETED;
            case OrchestrationEvent.EventType.ORCHESTRATION_FAILED:
                return SseEvent.EventType.ORCHESTRATION_FAILED;
            case OrchestrationEvent.EventType.STEP_STARTED:
                return SseEvent.EventType.AGENT_PROGRESS;
            case OrchestrationEvent.EventType.STEP_THINKING:
                return SseEvent.EventType.AGENT_THINKING;
            case OrchestrationEvent.EventType.STEP_COMPLETED:
                return SseEvent.EventType.AGENT_COMPLETED;
            case OrchestrationEvent.EventType.STEP_FAILED:
                return SseEvent.EventType.AGENT_FAILED;
            default:
                return SseEvent.EventType.ORCHESTRATION_PROGRESS;
        }
    }
    
    /**
     * 构建 Agent 事件数据
     */
    private Map<String, Object> buildAgentEventData(AgentExecutionEvent event) {
        Map<String, Object> data = new java.util.HashMap<>();
        
        // 基础信息
        data.put("executionId", event.getExecutionId());
        data.put("agentName", event.getAgentName());
        data.put("status", event.getStatus());
        data.put("eventType", event.getEventType());
        
        // 原始事件数据
        if (event.getData() != null) {
            data.putAll(event.getData());
        }
        
        // 根据事件类型添加特定数据
        switch (event.getEventType()) {
            case AgentExecutionEvent.EventType.EXECUTION_THINKING:
                if (event.getData() != null && event.getData().containsKey("thought")) {
                    data.put("thought", event.getData().get("thought"));
                    data.put("message", "Agent 正在思考...");
                }
                break;
                
            case AgentExecutionEvent.EventType.EXECUTION_PROGRESS:
                if (event.getData() != null && event.getData().containsKey("progress")) {
                    data.put("progress", event.getData().get("progress"));
                    data.put("message", "执行进度更新");
                }
                break;
                
            case AgentExecutionEvent.EventType.EXECUTION_COMPLETED:
                data.put("message", "Agent 执行完成");
                break;
                
            case AgentExecutionEvent.EventType.EXECUTION_FAILED:
                data.put("message", "Agent 执行失败");
                break;
                
            case AgentExecutionEvent.EventType.EXECUTION_STARTED:
                data.put("message", "开始执行 Agent");
                break;
        }
        
        return data;
    }
    
    /**
     * 构建编排事件数据
     */
    private Map<String, Object> buildOrchestrationEventData(OrchestrationEvent event) {
        Map<String, Object> data = new java.util.HashMap<>();
        
        // 基础信息
        data.put("orchestrationId", event.getOrchestrationId());
        data.put("flowId", event.getFlowId());
        data.put("flowName", event.getFlowName());
        data.put("status", event.getStatus());
        data.put("progress", event.getProgress());
        
        // 步骤信息
        if (StringUtils.hasText(event.getStepId())) {
            data.put("stepId", event.getStepId());
            data.put("stepName", event.getStepName());
            data.put("agentName", event.getAgentName());
        }
        
        // 原始事件数据
        if (event.getData() != null) {
            data.putAll(event.getData());
        }
        
        // 根据事件类型添加消息
        String message = getOrchestrationEventMessage(event);
        if (StringUtils.hasText(message)) {
            data.put("message", message);
        }
        
        return data;
    }
    
    /**
     * 获取编排事件消息
     */
    private String getOrchestrationEventMessage(OrchestrationEvent event) {
        switch (event.getEventType()) {
            case OrchestrationEvent.EventType.ORCHESTRATION_STARTED:
                return "开始智能处理流程";
                
            case OrchestrationEvent.EventType.INTENT_RECOGNIZED:
                return "已识别用户意图";
                
            case OrchestrationEvent.EventType.FLOW_MATCHED:
                return "已匹配处理流程: " + event.getFlowName();
                
            case OrchestrationEvent.EventType.STEP_STARTED:
                return "开始执行步骤: " + event.getStepName();
                
            case OrchestrationEvent.EventType.STEP_THINKING:
                return event.getAgentName() + " 正在思考...";
                
            case OrchestrationEvent.EventType.STEP_COMPLETED:
                return "步骤执行完成: " + event.getStepName();
                
            case OrchestrationEvent.EventType.STEP_FAILED:
                return "步骤执行失败: " + event.getStepName();
                
            case OrchestrationEvent.EventType.ORCHESTRATION_PROGRESS:
                return "处理进度: " + event.getProgress() + "%";
                
            case OrchestrationEvent.EventType.ORCHESTRATION_COMPLETED:
                return "智能处理流程执行完成";
                
            case OrchestrationEvent.EventType.ORCHESTRATION_FAILED:
                return "智能处理流程执行失败";
                
            default:
                return null;
        }
    }
    
    /**
     * 从编排事件中提取对话ID
     * TODO: 需要在编排事件中包含对话ID，或者通过其他方式获取
     */
    private String extractConversationId(OrchestrationEvent event) {
        // 如果事件数据中包含对话ID
        if (event.getData() != null && event.getData().containsKey("conversationId")) {
            return (String) event.getData().get("conversationId");
        }
        
        // 或者通过编排ID查找对应的对话ID
        // 这需要额外的数据映射逻辑
        
        return null; // 暂时返回 null，后续需要完善
    }
}