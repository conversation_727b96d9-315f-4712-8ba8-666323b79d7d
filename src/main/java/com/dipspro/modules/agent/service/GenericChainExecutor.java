package com.dipspro.modules.agent.service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.dipspro.modules.agent.dto.AgentExecutionRequest;
import com.dipspro.modules.agent.dto.AgentExecutionResult;
import com.dipspro.modules.agent.dto.ChainExecutionContext;
import com.dipspro.modules.agent.dto.ChainExecutionEvent;
import com.dipspro.modules.agent.dto.ChainExecutionRequest;
import com.dipspro.modules.agent.dto.ChainExecutionResult;
import com.dipspro.modules.agent.dto.ChainInstruction;
import com.dipspro.modules.agent.entity.AgentDefinition;
import com.dipspro.modules.agent.entity.AgentDefinition.AgentRole;
import com.dipspro.util.AgentThinkingMessageUtil;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

/**
 * 通用链路执行器 - AI驱动的Agent链式工作流核心
 * 
 * 核心特点：
 * 1. 完全由AI（意图识别Agent）决定执行链路
 * 2. 支持实时流式响应和思维链展示
 * 3. Java代码只做通用执行框架，不包含业务逻辑
 * 4. 支持链路执行状态管理和错误处理
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class GenericChainExecutor {

    private final GenericAiAgentEngine agentEngine;
    private final AgentDefinitionService agentDefinitionService;
    private final ObjectMapper objectMapper;
    private final AgentThinkingMessageUtil thinkingMessageUtil;
/*

    */
/**
     * 执行AI驱动的链路 - 支持实时流式响应
     * 
     * @param userInput 用户输入
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @return 流式执行事件
     *//*

    public Flux<ChainExecutionEvent> executeAiDrivenChainStream(
            String userInput, String conversationId, Long userId) {
        
        return Flux.create(emitter -> {
            try {
                log.info("开始AI驱动链路执行: ConversationId={}, UserInput={}", 
                        conversationId, userInput);
                
                // 发送开始事件
                emitter.next(ChainExecutionEvent.started(conversationId, userInput));
                
                // 1. 意图识别阶段
                emitter.next(ChainExecutionEvent.stepStarted("intent_analysis", "AI正在分析您的需求..."));
                ChainInstruction instruction = getChainInstructionFromAI(userInput, conversationId, userId);
                emitter.next(ChainExecutionEvent.stepCompleted("intent_analysis", 
                        "意图分析完成", instruction.getAnalysisResult()));
                
                // 2. 链路执行阶段
                executeChainWithStreaming(instruction, userInput, conversationId, userId, emitter);
                
                // 发送完成事件
                emitter.next(ChainExecutionEvent.completed(conversationId));
                emitter.complete();
                
            } catch (Exception e) {
                log.error("链路执行失败: ConversationId={}, Error={}", conversationId, e.getMessage(), e);
                emitter.next(ChainExecutionEvent.error(conversationId, e.getMessage()));
                emitter.error(e);
            }
        });
    }

    */
/**
     * 同步执行AI驱动的链路
     * 
     * @param request 链路执行请求
     * @return 执行结果
     *//*

    public ChainExecutionResult executeAiDrivenChain(ChainExecutionRequest request) {
        try {
            String conversationId = StringUtils.hasText(request.getConversationId()) 
                    ? request.getConversationId() 
                    : UUID.randomUUID().toString();
            
            log.info("开始同步链路执行: ConversationId={}, UserInput={}", 
                    conversationId, request.getUserInput());
            
            // 1. 意图识别
            ChainInstruction instruction = getChainInstructionFromAI(
                    request.getUserInput(), conversationId, request.getUserId());
            
            // 2. 执行链路
            return executeChainByInstruction(instruction, request.getUserInput(), 
                    conversationId, request.getUserId());
            
        } catch (Exception e) {
            log.error("同步链路执行失败: Error={}", e.getMessage(), e);
            return ChainExecutionResult.builder()
                    .conversationId(request.getConversationId())
                    .status(ChainExecutionResult.ExecutionStatus.FAILED)
                    .errorMessage(e.getMessage())
                    .startTime(LocalDateTime.now())
                    .endTime(LocalDateTime.now())
                    .build();
        }
    }
*/
/*

    */
/**
     * 从AI获取链路指令
     *//*

    private ChainInstruction getChainInstructionFromAI(String userInput, String conversationId, Long userId) {
        try {
            // 查找意图识别Agent
            List<AgentDefinition> intentAgents = agentDefinitionService.findByRole(AgentRole.INTENT_RECOGNITION);
            AgentDefinition intentAgent = intentAgents.stream()
                    .findFirst()
                    .orElseThrow(() -> new RuntimeException("找不到意图识别 Agent"));

            log.info("找到意图识别Agent: {}", intentAgent.getName());

            // 向前端发送"智能体正在思考"消息
            thinkingMessageUtil.sendThinkingMessage(conversationId, intentAgent.getName(), userInput);

            // 构建请求数据
            Map<String, Object> inputData = new HashMap<>();
            inputData.put("userMessage", userInput);
            inputData.put("needAgentList", true);
            inputData.put("requestType", "chain_instruction");

            AgentExecutionRequest request = AgentExecutionRequest.builder()
                    .agentId(intentAgent.getId())
                    .agentName(intentAgent.getName())
                    .conversationId(conversationId)
                    .userId(userId)
                    .inputData(inputData)
                    .executionId(UUID.randomUUID())
                    .build();

            // 执行意图识别Agent
            AgentExecutionResult result = agentEngine.executeAgent(request);

            // 解析链路指令
            return parseChainInstruction(result);

        } catch (Exception e) {
            log.error("获取链路指令失败: {}", e.getMessage(), e);
            // 返回降级的单Agent处理指令
            return createFallbackInstruction(userInput);
        }
    }
*/
/*

    */
/**
     * 解析链路指令
     *//*

    private ChainInstruction parseChainInstruction(AgentExecutionResult result) {
        try {
            if (result.getChainInstruction() != null) {
                // 如果结果中包含链路指令，直接使用
                Map<String, Object> chainData = result.getChainInstruction();
                return objectMapper.convertValue(chainData, ChainInstruction.class);
            } else {
                // 否则从content中解析JSON
                return objectMapper.readValue(result.getContent(), ChainInstruction.class);
            }
        } catch (Exception e) {
            log.warn("解析链路指令失败，使用降级策略: {}", e.getMessage());
            return createFallbackInstruction(result.getContent());
        }
    }
*/
/*

    */
/**
     * 创建降级指令（使用通用Agent）
     *//*

    private ChainInstruction createFallbackInstruction(String userInput) {
        return ChainInstruction.builder()
                .analysisResult("使用通用Agent处理用户请求")
                .agentChain(List.of(
                    ChainInstruction.ChainStep.builder()
                        .stepId("fallback_step")
                        .agentName("通用Agent")
                        .order(1)
                        .description("通用处理")
                        .isCritical(true)
                        .build()
                ))
                .executionMode("sequential")
                .needUserConfirm(false)
                .confidence(0.5)
                .build();
    }

    */
/**
     * 基于指令执行链路
     *//*

    private ChainExecutionResult executeChainByInstruction(ChainInstruction instruction,
                                                         String userInput,
                                                         String conversationId,
                                                         Long userId) {
        if (instruction == null || instruction.getAgentChain().isEmpty()) {
            return executeSingleAgent("通用Agent", userInput, conversationId, userId);
        }
        
        // 创建执行上下文
        ChainExecutionContext context = ChainExecutionContext.builder()
                .instruction(instruction)
                .originalInput(userInput)
                .conversationId(conversationId)
                .userId(userId)
                .stepResults(new HashMap<>())
                .currentData(userInput)
                .startTime(LocalDateTime.now())
                .status(ChainExecutionContext.ExecutionStatus.RUNNING)
                .totalSteps(instruction.getAgentChain().size())
                .currentStepIndex(0)
                .build();
        
        // 执行链路步骤
        for (ChainInstruction.ChainStep step : instruction.getAgentChain()) {
            executeChainStep(step, context);
            context.setCurrentStepIndex(context.getCurrentStepIndex() + 1);
        }
        
        context.setStatus(ChainExecutionContext.ExecutionStatus.COMPLETED);
        context.setEndTime(LocalDateTime.now());
        
        return buildChainResult(context);
    }
*/
/*

    */
/**
     * 执行单个链路步骤
     *//*

    private void executeChainStep(ChainInstruction.ChainStep step, ChainExecutionContext context) {
        try {
            log.info("执行链路步骤: {}", step.getAgentName());
            
            // 查找Agent
            AgentDefinition agent = null;
            try {
                agent = agentDefinitionService.findByName(step.getAgentName());
            } catch (IllegalArgumentException e) {
                log.warn("找不到 Agent: {}，跳过步骤", step.getAgentName());
                return;
            }
            
            // 构建步骤输入
            Map<String, Object> stepInput = buildStepInput(step, context);
            
            // 执行Agent
            AgentExecutionRequest request = AgentExecutionRequest.builder()
                    .agentId(agent.getId())
                    .conversationId(context.getConversationId())
                    .userId(context.getUserId())
                    .inputData(stepInput)
                    .executionId(UUID.randomUUID())
                    .build();
            
            AgentExecutionResult result = agentEngine.executeAgent(request);
            
            // 保存结果
            context.addStepResult(step.getStepId(), result);
            
            if (result.isSuccessful()) {
                context.setCurrentData(result.getContent());
                log.info("步骤执行成功: {}", step.getAgentName());
            } else {
                log.warn("步骤执行失败: {}, Error: {}", step.getAgentName(), result.getErrorMessage());
                if (step.getIsCritical() != null && step.getIsCritical()) {
                    throw new RuntimeException("关键步骤执行失败: " + step.getAgentName());
                }
            }
            
        } catch (Exception e) {
            log.error("链路步骤执行失败: {}", step.getAgentName(), e);
            if (step.getIsCritical() != null && step.getIsCritical()) {
                throw new RuntimeException("关键步骤执行失败: " + step.getAgentName(), e);
            }
        }
    }
*/

    /**
     * 构建步骤输入数据
     */
    private Map<String, Object> buildStepInput(ChainInstruction.ChainStep step, ChainExecutionContext context) {
        Map<String, Object> input = new HashMap<>();
        
        // 基础数据
        input.put("userMessage", context.getCurrentData());
        input.put("originalInput", context.getOriginalInput());
        input.put("currentStep", step.getOrder());
        input.put("stepName", step.getAgentName());
        
        // 前序步骤结果
        Map<String, String> previousResults = new HashMap<>();
        context.getStepResults().forEach((stepId, result) -> {
            if (result.isSuccessful()) {
                previousResults.put(stepId, result.getContent());
            }
        });
        input.put("previousResults", previousResults);
        
        // 步骤特定参数
        if (step.getParameters() != null) {
            input.putAll(step.getParameters());
        }
        
        return input;
    }
/*

    */
/**
     * 带流式响应的链路执行
     *//*

    private void executeChainWithStreaming(ChainInstruction instruction,
                                         String userInput,
                                         String conversationId,
                                         Long userId,
                                         reactor.core.publisher.FluxSink<ChainExecutionEvent> emitter) {
        if (instruction == null || instruction.getAgentChain().isEmpty()) {
            // 单Agent处理
            executeSingleAgentWithStreaming("通用Agent", userInput, conversationId, userId, emitter);
            return;
        }
        
        // 创建执行上下文
        ChainExecutionContext context = ChainExecutionContext.builder()
                .instruction(instruction)
                .originalInput(userInput)
                .conversationId(conversationId)
                .userId(userId)
                .stepResults(new HashMap<>())
                .currentData(userInput)
                .startTime(LocalDateTime.now())
                .totalSteps(instruction.getAgentChain().size())
                .currentStepIndex(0)
                .build();
        
        // 执行每个步骤并发送事件
        for (ChainInstruction.ChainStep step : instruction.getAgentChain()) {
            // 发送步骤开始事件
            emitter.next(ChainExecutionEvent.stepStarted(step.getStepId(), 
                    step.getAgentName() + " 正在处理..."));
            
            executeChainStepWithStreaming(step, context, emitter);
            context.setCurrentStepIndex(context.getCurrentStepIndex() + 1);
        }
    }
*/
/*

    */
/**
     * 带流式响应的步骤执行
     *//*

    private void executeChainStepWithStreaming(ChainInstruction.ChainStep step,
                                             ChainExecutionContext context,
                                             reactor.core.publisher.FluxSink<ChainExecutionEvent> emitter) {
        try {
            // 查找Agent
            AgentDefinition agent = null;
            try {
                agent = agentDefinitionService.findByName(step.getAgentName());
            } catch (IllegalArgumentException e) {
                emitter.next(ChainExecutionEvent.stepCompleted(step.getStepId(), 
                        "找不到Agent: " + step.getAgentName(), "跳过"));
                return;
            }
            Map<String, Object> stepInput = buildStepInput(step, context);
            
            AgentExecutionRequest request = AgentExecutionRequest.builder()
                    .agentId(agent.getId())
                    .conversationId(context.getConversationId())
                    .userId(context.getUserId())
                    .inputData(stepInput)
                    .executionId(UUID.randomUUID())
                    .build();
            
            // 执行并发送思维链事件
            AgentExecutionResult result = agentEngine.executeAgent(request);
            
            // 发送思维链事件
            if (result.getChainOfThought() != null) {
                try {
                    // 解析思维链JSON
                    Map<String, Object> thinkingMap = objectMapper.readValue(result.getChainOfThought(), Map.class);
                    ChainExecutionEvent.ThinkingProcess thinkingProcess = 
                            ChainExecutionEvent.ThinkingProcess.builder()
                                    .analysis(thinkingMap.getOrDefault("analysis", "").toString())
                                    .approach(thinkingMap.getOrDefault("approach", "").toString())
                                    .confidence(((Number) thinkingMap.getOrDefault("confidence", 0.9)).doubleValue())
                                    .build();
                    
                    emitter.next(ChainExecutionEvent.thinking(context.getConversationId(), 
                            step.getStepId(), thinkingProcess));
                } catch (Exception e) {
                    log.warn("解析思维链失败: {}", e.getMessage());
                }
            }
            
            // 保存结果并发送完成事件
            context.addStepResult(step.getStepId(), result);
            emitter.next(ChainExecutionEvent.stepCompleted(step.getStepId(), 
                    step.getAgentName() + " 完成", result.getContent()));
            
        } catch (Exception e) {
            log.error("步骤执行失败: {}", step.getAgentName(), e);
            emitter.next(ChainExecutionEvent.stepCompleted(step.getStepId(), 
                    step.getAgentName() + " 失败", "执行失败: " + e.getMessage()));
        }
    }

    */
/**
     * 执行单个Agent（同步）
     *//*

    private ChainExecutionResult executeSingleAgent(String agentName, String userInput, 
                                                  String conversationId, Long userId) {
        // 实现单Agent处理逻辑
        // 这里可以简化为调用通用Agent
        return ChainExecutionResult.builder()
                .conversationId(conversationId)
                .status(ChainExecutionResult.ExecutionStatus.SUCCESS)
                .finalContent("已使用" + agentName + "处理您的请求")
                .startTime(LocalDateTime.now())
                .endTime(LocalDateTime.now())
                .build();
    }

    */
/**
     * 执行单个Agent（流式）
     *//*

    private void executeSingleAgentWithStreaming(String agentName, String userInput,
                                               String conversationId, Long userId,
                                               reactor.core.publisher.FluxSink<ChainExecutionEvent> emitter) {
        emitter.next(ChainExecutionEvent.stepStarted("single_agent", agentName + " 处理中..."));
        emitter.next(ChainExecutionEvent.stepCompleted("single_agent", agentName + " 完成", 
                "已使用" + agentName + "处理您的请求"));
    }

    */
/**
     * 构建链路执行结果
     *//*

    private ChainExecutionResult buildChainResult(ChainExecutionContext context) {
        int successfulSteps = (int) context.getStepResults().values().stream()
                .mapToLong(result -> result.isSuccessful() ? 1 : 0).sum();
        
        ChainExecutionResult.ExecutionMetrics metrics = ChainExecutionResult.ExecutionMetrics.builder()
                .totalSteps(context.getTotalSteps())
                .successfulSteps(successfulSteps)
                .failedSteps(context.getTotalSteps() - successfulSteps)
                .successRate((double) successfulSteps / context.getTotalSteps())
                .build();
        
        return ChainExecutionResult.builder()
                .conversationId(context.getConversationId())
                .status(context.isSuccessful() ? ChainExecutionResult.ExecutionStatus.SUCCESS 
                        : ChainExecutionResult.ExecutionStatus.PARTIAL)
                .finalContent(context.getCurrentData())
                .summary("链路执行完成，成功率: " + String.format("%.1f%%", metrics.getSuccessRate() * 100))
                .metrics(metrics)
                .startTime(context.getStartTime())
                .endTime(context.getEndTime())
                .build();
    }
*/


}
