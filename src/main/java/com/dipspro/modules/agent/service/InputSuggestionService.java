package com.dipspro.modules.agent.service;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.dipspro.modules.agent.dto.api.InputSuggestionDto;
import com.dipspro.modules.chat.entity.ChatMessage;
import com.dipspro.modules.chat.repository.ChatMessageRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 输入建议服务 - 生成智能输入建议
 * 
 * 核心功能：
 * 1. 基于对话历史生成智能建议
 * 2. 提供预设的通用建议
 * 3. 支持上下文相关的建议
 * 4. 建议优先级排序
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class InputSuggestionService {

    private final ChatMessageRepository chatMessageRepository;
    
    /**
     * 获取输入建议
     * 
     * @param conversationId 对话ID（可选）
     * @param userId 用户ID
     * @return 建议列表
     */
    public List<InputSuggestionDto> getInputSuggestions(String conversationId, Long userId) {
        try {
            log.debug("生成输入建议: conversationId={}, userId={}", conversationId, userId);
            
            List<InputSuggestionDto> suggestions = new ArrayList<>();
            
            // 1. 如果有对话上下文，生成上下文相关建议
            if (StringUtils.hasText(conversationId)) {
                suggestions.addAll(generateContextualSuggestions(conversationId, userId));
            }
            
            // 2. 添加通用建议
            suggestions.addAll(generateCommonSuggestions());
            
            // 3. 根据用户历史生成个性化建议
            suggestions.addAll(generatePersonalizedSuggestions(userId));
            
            // 4. 按优先级排序并限制数量
            suggestions.sort((a, b) -> Integer.compare(
                b.getPriority() != null ? b.getPriority() : 0,
                a.getPriority() != null ? a.getPriority() : 0
            ));
            
            // 限制返回数量，避免太多建议
            int maxSuggestions = 8;
            if (suggestions.size() > maxSuggestions) {
                suggestions = suggestions.subList(0, maxSuggestions);
            }
            
            log.info("生成输入建议完成: conversationId={}, 建议数量={}", conversationId, suggestions.size());
            return suggestions;
            
        } catch (Exception e) {
            log.error("生成输入建议失败: conversationId={}, error={}", conversationId, e.getMessage(), e);
            // 返回基础建议，确保不影响用户使用
            return generateBasicSuggestions();
        }
    }
    
    /**
     * 生成上下文相关建议
     */
    private List<InputSuggestionDto> generateContextualSuggestions(String conversationId, Long userId) {
        List<InputSuggestionDto> suggestions = new ArrayList<>();
        
        try {
            // 获取最近的消息来分析上下文
            List<ChatMessage> recentMessages = chatMessageRepository.findByConversationIdOrderByCreatedAtAsc(
                    java.util.UUID.fromString(conversationId)
                );
            
            // 限制为最近5条消息
            if (recentMessages.size() > 5) {
                recentMessages = recentMessages.subList(Math.max(0, recentMessages.size() - 5), recentMessages.size());
            }
            
            if (!recentMessages.isEmpty()) {
                String lastMessage = recentMessages.get(recentMessages.size() - 1).getContent();
                
                // 基于最后一条消息内容生成相关建议
                if (lastMessage.contains("数据") || lastMessage.contains("分析")) {
                    suggestions.add(InputSuggestionDto.builder()
                        .id("ctx_data_detail")
                        .text("请详细分析数据的趋势和异常")
                        .description("深入分析数据中的模式和异常情况")
                        .icon("i-carbon-chart-line")
                        .category("数据分析")
                        .priority(90)
                        .build());
                        
                    suggestions.add(InputSuggestionDto.builder()
                        .id("ctx_data_export")
                        .text("导出分析报告")
                        .description("将分析结果导出为报告")
                        .icon("i-carbon-download")
                        .category("导出")
                        .priority(85)
                        .build());
                }
                
                if (lastMessage.contains("报告") || lastMessage.contains("总结")) {
                    suggestions.add(InputSuggestionDto.builder()
                        .id("ctx_report_format")
                        .text("调整报告格式")
                        .description("修改报告的格式和布局")
                        .icon("i-carbon-document")
                        .category("报告")
                        .priority(88)
                        .build());
                }
                
                if (lastMessage.contains("图表") || lastMessage.contains("可视化")) {
                    suggestions.add(InputSuggestionDto.builder()
                        .id("ctx_chart_type")
                        .text("换个图表类型试试")
                        .description("尝试不同的数据可视化方式")
                        .icon("i-carbon-chart-pie")
                        .category("可视化")
                        .priority(87)
                        .build());
                }
            }
            
        } catch (Exception e) {
            log.warn("生成上下文建议失败: {}", e.getMessage());
        }
        
        return suggestions;
    }
    
    /**
     * 生成通用建议
     */
    private List<InputSuggestionDto> generateCommonSuggestions() {
        List<InputSuggestionDto> suggestions = new ArrayList<>();
        
        // 数据分析类
        suggestions.add(InputSuggestionDto.builder()
            .id("common_analyze_data")
            .text("分析我的销售数据")
            .description("对销售数据进行深度分析")
            .icon("i-carbon-analytics")
            .category("数据分析")
            .priority(80)
            .build());
            
        suggestions.add(InputSuggestionDto.builder()
            .id("common_data_trend")
            .text("查看数据趋势")
            .description("分析数据的变化趋势")
            .icon("i-carbon-chart-line")
            .category("数据分析")
            .priority(75)
            .build());
        
        // 报告生成类
        suggestions.add(InputSuggestionDto.builder()
            .id("common_monthly_report")
            .text("生成月度总结报告")
            .description("自动生成月度业务报告")
            .icon("i-carbon-document")
            .category("报告生成")
            .priority(78)
            .build());
            
        suggestions.add(InputSuggestionDto.builder()
            .id("common_weekly_summary")
            .text("创建周报")
            .description("生成本周工作总结")
            .icon("i-carbon-calendar")
            .category("报告生成")
            .priority(70)
            .build());
        
        // 流程优化类
        suggestions.add(InputSuggestionDto.builder()
            .id("common_optimize_workflow")
            .text("优化我的工作流程")
            .description("分析并优化当前工作流程")
            .icon("i-carbon-rocket")
            .category("流程优化")
            .priority(76)
            .build());
        
        // 可视化类
        suggestions.add(InputSuggestionDto.builder()
            .id("common_create_chart")
            .text("创建数据可视化图表")
            .description("将数据转换为直观的图表")
            .icon("i-carbon-chart-bar")
            .category("可视化")
            .priority(73)
            .build());
        
        return suggestions;
    }
    
    /**
     * 生成个性化建议（基于用户历史）
     */
    private List<InputSuggestionDto> generatePersonalizedSuggestions(Long userId) {
        List<InputSuggestionDto> suggestions = new ArrayList<>();
        
        try {
            // 分析用户的历史消息，生成个性化建议
            // 注意：这里简化处理，实际应该添加专门的查询方法
            List<ChatMessage> userHistory = new ArrayList<>();
            // TODO: 需要在ChatMessageRepository中添加按用户ID和角色查询的方法
            
            if (!userHistory.isEmpty()) {
                // 简单的关键词频率分析
                int dataCount = 0, reportCount = 0, chartCount = 0;
                
                for (ChatMessage message : userHistory) {
                    String content = message.getContent().toLowerCase();
                    if (content.contains("数据") || content.contains("分析")) dataCount++;
                    if (content.contains("报告") || content.contains("总结")) reportCount++;
                    if (content.contains("图表") || content.contains("可视化")) chartCount++;
                }
                
                // 基于用户偏好生成建议
                if (dataCount > reportCount && dataCount > chartCount) {
                    suggestions.add(InputSuggestionDto.builder()
                        .id("personal_advanced_analysis")
                        .text("进行高级数据分析")
                        .description("基于您的使用习惯推荐的深度分析")
                        .icon("i-carbon-machine-learning")
                        .category("个性化推荐")
                        .priority(82)
                        .build());
                } else if (reportCount > dataCount && reportCount > chartCount) {
                    suggestions.add(InputSuggestionDto.builder()
                        .id("personal_auto_report")
                        .text("设置自动化报告")
                        .description("基于您的使用习惯推荐的报告自动化")
                        .icon("i-carbon-automation")
                        .category("个性化推荐")
                        .priority(82)
                        .build());
                } else if (chartCount > 0) {
                    suggestions.add(InputSuggestionDto.builder()
                        .id("personal_interactive_viz")
                        .text("创建交互式可视化")
                        .description("基于您的使用习惯推荐的高级图表")
                        .icon("i-carbon-chart-multitype")
                        .category("个性化推荐")
                        .priority(82)
                        .build());
                }
            }
            
        } catch (Exception e) {
            log.warn("生成个性化建议失败: {}", e.getMessage());
        }
        
        return suggestions;
    }
    
    /**
     * 生成基础建议（兜底方案）
     */
    private List<InputSuggestionDto> generateBasicSuggestions() {
        List<InputSuggestionDto> suggestions = new ArrayList<>();
        
        suggestions.add(InputSuggestionDto.builder()
            .id("basic_help")
            .text("我需要帮助")
            .description("获取使用帮助和指导")
            .icon("i-carbon-help")
            .category("帮助")
            .priority(50)
            .build());
            
        suggestions.add(InputSuggestionDto.builder()
            .id("basic_analyze")
            .text("分析数据")
            .description("进行数据分析")
            .icon("i-carbon-analytics")
            .category("基础功能")
            .priority(60)
            .build());
            
        suggestions.add(InputSuggestionDto.builder()
            .id("basic_report")
            .text("生成报告")
            .description("创建报告文档")
            .icon("i-carbon-document")
            .category("基础功能")
            .priority(55)
            .build());
        
        return suggestions;
    }
}
