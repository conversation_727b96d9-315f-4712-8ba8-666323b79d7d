package com.dipspro.modules.agent.service.impl;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.dipspro.modules.agent.entity.AgentDefinition;
import com.dipspro.modules.agent.entity.AgentDefinition.AgentStatus;
import com.dipspro.modules.agent.repository.AgentDefinitionRepository;
import com.dipspro.modules.agent.service.AgentDefinitionService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Agent 定义服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class AgentDefinitionServiceImpl implements AgentDefinitionService {

    private final AgentDefinitionRepository agentDefinitionRepository;

    @Override
    public AgentDefinition createAgent(AgentDefinition agentDefinition) {
        log.info("创建 Agent: {}", agentDefinition.getName());

        // 验证配置
        validateAgentConfiguration(agentDefinition);

        // 检查名称是否重复
        if (existsByName(agentDefinition.getName(), agentDefinition.getCreatedBy())) {
            throw new IllegalArgumentException("Agent 名称已存在: " + agentDefinition.getName());
        }

        // 设置默认值
        if (agentDefinition.getStatus() == null) {
            agentDefinition.setStatus(AgentStatus.DRAFT);
        }

        if (agentDefinition.getPriority() == null) {
            agentDefinition.setPriority(50);
        }

        if (agentDefinition.getTimeoutSeconds() == null) {
            agentDefinition.setTimeoutSeconds(30);
        }

        if (agentDefinition.getMaxRetries() == null) {
            agentDefinition.setMaxRetries(3);
        }

        // 根据category自动设置agentRole
        if (agentDefinition.getAgentRole() == null && agentDefinition.getCategory() != null) {
            agentDefinition.setAgentRole(mapCategoryToAgentRole(agentDefinition.getCategory()));
        }

        AgentDefinition savedAgent = agentDefinitionRepository.save(agentDefinition);
        log.info("Agent 创建成功: ID={}, Name={}", savedAgent.getId(), savedAgent.getName());

        return savedAgent;
    }

    @Override
    public AgentDefinition updateAgent(AgentDefinition agentDefinition) {
        log.info("更新 Agent: ID={}, Name={}", agentDefinition.getId(), agentDefinition.getName());

        // 检查 Agent 是否存在
        AgentDefinition existingAgent = getById(agentDefinition.getId());

        // 验证配置
        validateAgentConfiguration(agentDefinition);

        // 检查名称是否与其他 Agent 重复
        Optional<AgentDefinition> duplicateAgent = agentDefinitionRepository
                .findByNameAndCreatedBy(agentDefinition.getName(), agentDefinition.getCreatedBy());

        if (duplicateAgent.isPresent() && !duplicateAgent.get().getId().equals(agentDefinition.getId())) {
            throw new IllegalArgumentException("Agent 名称已存在: " + agentDefinition.getName());
        }

        // 根据category自动设置agentRole（如果category发生变化或agentRole为空）
        if (agentDefinition.getCategory() != null && 
            (agentDefinition.getAgentRole() == null || 
             !agentDefinition.getCategory().equals(existingAgent.getCategory()))) {
            agentDefinition.setAgentRole(mapCategoryToAgentRole(agentDefinition.getCategory()));
        }

        AgentDefinition updatedAgent = agentDefinitionRepository.save(agentDefinition);
        log.info("Agent 更新成功: ID={}, Name={}", updatedAgent.getId(), updatedAgent.getName());

        return updatedAgent;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<AgentDefinition> findById(Long id) {
        return agentDefinitionRepository.findById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public AgentDefinition getById(Long id) {
        return findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Agent 不存在: ID=" + id));
    }

    @Override
    public void deleteById(Long id) {
        log.info("删除 Agent: ID={}", id);

        AgentDefinition agent = getById(id);
        agentDefinitionRepository.delete(agent);

        log.info("Agent 删除成功: ID={}, Name={}", id, agent.getName());
    }

    @Override
    @Transactional(readOnly = true)
    public List<AgentDefinition> findActiveAgents() {
        return agentDefinitionRepository.findActiveAgentsOrderByPriority();
    }

    @Override
    @Transactional(readOnly = true)
    public List<AgentDefinition> findActiveAgentsByCategory(String category) {
        return agentDefinitionRepository.findByCategoryAndStatus(category, AgentStatus.ACTIVE);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AgentDefinition> findAgentsByCapability(String capability) {
        return agentDefinitionRepository.findByCapability(capability);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<AgentDefinition> findAgents(Pageable pageable) {
        return agentDefinitionRepository.findAll(pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<AgentDefinition> findAgentsByCreatedBy(Long createdBy, Pageable pageable) {
        return agentDefinitionRepository.findByCreatedBy(createdBy, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByName(String name, Long createdBy) {
        return agentDefinitionRepository.existsByNameAndCreatedBy(name, createdBy);
    }

    @Override
    public AgentDefinition updateAgentStatus(Long id, AgentStatus status) {
        log.info("更新 Agent 状态: ID={}, Status={}", id, status);

        AgentDefinition agent = getById(id);
        agent.setStatus(status);

        AgentDefinition updatedAgent = agentDefinitionRepository.save(agent);
        log.info("Agent 状态更新成功: ID={}, Name={}, Status={}", id, agent.getName(), status);

        return updatedAgent;
    }

    @Override
    @Transactional(readOnly = true)
    public List<AgentDefinition> searchAgentsByName(String name) {
        if (!StringUtils.hasText(name)) {
            return List.of();
        }
        return agentDefinitionRepository.findByNameContainingIgnoreCaseOrderByPriority(name);
    }

    @Override
    public void validateAgentConfiguration(AgentDefinition agentDefinition) {
        // 验证必填字段
        if (!StringUtils.hasText(agentDefinition.getName())) {
            throw new IllegalArgumentException("Agent 名称不能为空");
        }

        if (!StringUtils.hasText(agentDefinition.getSystemPrompt())) {
            throw new IllegalArgumentException("系统提示词不能为空");
        }

        if (agentDefinition.getCreatedBy() == null) {
            throw new IllegalArgumentException("创建人不能为空");
        }

        // 验证数值范围
        if (agentDefinition.getPriority() != null &&
                (agentDefinition.getPriority() < 0 || agentDefinition.getPriority() > 100)) {
            throw new IllegalArgumentException("优先级必须在 0-100 之间");
        }

        if (agentDefinition.getTimeoutSeconds() != null &&
                (agentDefinition.getTimeoutSeconds() < 1 || agentDefinition.getTimeoutSeconds() > 300)) {
            throw new IllegalArgumentException("超时时间必须在 1-300 秒之间");
        }

        if (agentDefinition.getMaxRetries() != null &&
                (agentDefinition.getMaxRetries() < 0 || agentDefinition.getMaxRetries() > 5)) {
            throw new IllegalArgumentException("最大重试次数必须在 0-5 之间");
        }

        // 验证 JSON 格式
        validateJsonFields(agentDefinition);

        log.debug("Agent 配置验证通过: {}", agentDefinition.getName());
    }

    private void validateJsonFields(AgentDefinition agentDefinition) {
        // 这里可以添加 JSON 格式验证逻辑
        // 例如验证 capabilities 和 configuration 字段的 JSON 格式
        // 暂时跳过，可以后续增强
    }

    /**
     * 根据业务类别映射到系统Agent角色
     * 
     * @param category 业务类别
     * @return Agent角色
     */
    private AgentDefinition.AgentRole mapCategoryToAgentRole(String category) {
        if (category == null) {
            return AgentDefinition.AgentRole.CUSTOM;
        }

        switch (category.toUpperCase()) {
            case "INTENT_RECOGNITION":
                return AgentDefinition.AgentRole.INTENT_RECOGNITION;
            case "ANALYSIS":
            case "PROCESSING":
            case "INFERENCE":
                return AgentDefinition.AgentRole.GENERAL_ASSISTANT;
            case "REPORTING":
            case "VISUALIZATION":
                return AgentDefinition.AgentRole.RESPONSE_GENERATION;
            default:
                return AgentDefinition.AgentRole.CUSTOM;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public AgentStats getAgentStatistics() {
        List<Object[]> statusCounts = agentDefinitionRepository.countByStatus();

        long totalCount = 0;
        long activeCount = 0;
        long inactiveCount = 0;
        long draftCount = 0;

        for (Object[] row : statusCounts) {
            AgentStatus status = (AgentStatus) row[0];
            Long count = (Long) row[1];

            totalCount += count;

            switch (status) {
                case ACTIVE:
                    activeCount = count;
                    break;
                case INACTIVE:
                    inactiveCount = count;
                    break;
                case DRAFT:
                    draftCount = count;
                    break;
            }
        }

        return new AgentStats(totalCount, activeCount, inactiveCount, draftCount);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AgentDefinition> findByRole(AgentDefinition.AgentRole role) {
        return agentDefinitionRepository.findByAgentRoleAndStatus(role, AgentStatus.ACTIVE);
    }

    @Override
    @Transactional(readOnly = true)
    public AgentDefinition findByName(String name) {
        return agentDefinitionRepository.findByNameAndStatus(name, AgentStatus.ACTIVE)
                .orElseThrow(() -> new IllegalArgumentException("未找到名为 '" + name + "' 的活跃Agent"));
    }

    @Override
    @Transactional(readOnly = true)
    public List<AgentDefinition> getActiveAgents() {
        return findActiveAgents();
    }
}