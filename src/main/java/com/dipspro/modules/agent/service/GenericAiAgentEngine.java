package com.dipspro.modules.agent.service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.dipspro.util.AgentThinkingMessageUtil;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Service;

import com.dipspro.modules.agent.dto.AgentExecutionRequest;
import com.dipspro.modules.agent.dto.AgentExecutionResult;
import com.dipspro.modules.agent.entity.AgentDefinition;
import com.dipspro.modules.agent.entity.AgentDefinition.AgentRole;
import com.dipspro.modules.agent.entity.AgentExecution;
import com.dipspro.modules.agent.exception.AgentExecutionException;
import com.dipspro.modules.agent.exception.AgentNotFoundException;
import com.dipspro.util.MarkdownJsonParser;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * 通用AI Agent执行引擎 - 完全解耦的AI Agent架构核心
 * <p>
 * 核心特点：
 * 1. 完全由 system_prompt 驱动，Java代码不包含业务逻辑
 * 2. 支持思维链输出和解析
 * 3. 标准化JSON输入输出格式
 * 4. 错误处理和降级策略
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class GenericAiAgentEngine {

    private final ChatClient chatClient;
    private final AgentDefinitionService agentDefinitionService;
    private final ObjectMapper objectMapper;
    private final AgentThinkingMessageUtil thinkingMessageUtil;

    /**
     * 执行单个Agent - 支持思维链输出
     *
     * @param request 执行请求
     * @return 执行结果
     */
    public AgentExecutionResult executeAgent(AgentExecutionRequest request) {
        try {
            LocalDateTime startTime = LocalDateTime.now();
            log.info("开始执行Agent: AgentId={}, ConversationId={}",
                    request.getAgentId(), request.getConversationId());

            // 1. 加载Agent定义
            AgentDefinition agent = agentDefinitionService.getById(request.getAgentId());

            // 2. 检查Agent状态
            if (AgentDefinition.AgentStatus.INACTIVE.equals(agent.getStatus())) {
                throw new AgentExecutionException(request.getExecutionId(),
                        request.getAgentId(), "Agent 已被禁用");
            }

            // 3. 构建用户提示词（包含思维链要求）
//            String userPrompt = buildUserPromptWithThinking(request);
            Map<String, Object> inputData = request.getInputData();
            if (null == inputData) {
                thinkingMessageUtil.sendThinkingMessage(request.getConversationId(), request.getAgentName(), "没有输入数据");
                // 构建基础结果
                return AgentExecutionResult.builder()
                        .executionId(request.getExecutionId())
                        .agentId(request.getAgentId())
                        .agentName(agent.getName())
                        .content("没有用户输入数据")
                        .status(AgentExecution.ExecutionStatus.FAILED)
                        .startTime(startTime)
                        .endTime(LocalDateTime.now())
                        .build();
            }
            String userPrompt = inputData.get("user_message") + "";

            // 4. 调用AI（完全由system_prompt驱动）
            String aiResponse = chatClient.prompt()
                    .system(agent.getSystemPrompt())
                    .user(userPrompt)
                    .call()
                    .content();
            LocalDateTime endTime = LocalDateTime.now();

            // 5. 解析响应（包含思维链）
            return parseAgentResponseWithThinking(aiResponse, request, agent, startTime, endTime);

        } catch (Exception e) {
            log.error("Agent执行失败: AgentId={}, Error={}", request.getAgentId(), e.getMessage(), e);
            return createErrorResult(request, e);
        }
    }

    /**
     * 异步执行Agent - 支持Reactive流
     */
    public Mono<AgentExecutionResult> executeAgentAsync(AgentExecutionRequest request) {
        return Mono.fromCallable(() -> executeAgent(request));
    }

    /**
     * 构建包含思维链要求的用户提示词
     */
//    private String buildUserPromptWithThinking(AgentExecutionRequest request) {
//        Map<String, Object> promptData = new HashMap<>();
//
//        // 标准字段
//        promptData.put("user_input", request.getInputData().get("user_message"));
//        promptData.put("conversation_id", request.getConversationId());
//        promptData.put("execution_context", request.getInputData());
//        promptData.put("current_step", request.getInputData().get("currentStep"));
//        promptData.put("previous_results", request.getInputData().get("previousResults"));
//
//        // 思维链要求
//        promptData.put("thinking_required", true);
//        promptData.put("output_format", "请在响应中包含 thinking_process 字段，详细说明你的思考过程");
//
//        // 如果需要Agent列表（意图识别Agent）
//        if (request.getInputData().containsKey("needAgentList")) {
//            promptData.put("available_agents", getAvailableAgentList());
//        }
//
//        try {
//            return objectMapper.writeValueAsString(promptData);
//        } catch (Exception e) {
//            log.warn("构建用户提示词失败，使用简化格式", e);
//            return request.getInputData().get("userMessage").toString();
//        }
//    }

    /**
     * 解析包含思维链的Agent响应
     */
    private AgentExecutionResult parseAgentResponseWithThinking(String aiResponse,
                                                                AgentExecutionRequest request,
                                                                AgentDefinition agent,
                                                                LocalDateTime startTime,
                                                                LocalDateTime endTime) {
        try {
            // 使用MarkdownJsonParser处理可能包含Markdown格式的响应
            JsonNode responseNode = MarkdownJsonParser.extractAndParseJson(aiResponse);

            // 如果Markdown解析失败，尝试直接解析
            if (responseNode == null) {
                log.warn("Markdown JSON解析失败，尝试直接解析: {}", aiResponse.substring(0, Math.min(100, aiResponse.length())));
                responseNode = objectMapper.readTree(aiResponse);
            }

            // 智能提取内容 - 适应新的数据结构
            String content = extractContentFromResponse(responseNode, aiResponse);

            // 推断响应类型
            String responseType = inferResponseType(responseNode);

            // 构建基础结果
            AgentExecutionResult result = AgentExecutionResult.builder()
                    .executionId(request.getExecutionId())
                    .agentId(request.getAgentId())
                    .agentName(agent.getName())
                    .agentRole(agent.getAgentRole())
                    .inputData(request.getInputData())
                    .content(content)
                    .status(AgentExecution.ExecutionStatus.COMPLETED)
                    .startTime(startTime)
                    .endTime(endTime)
                    .build();

            // 提取完整的思维过程 - 处理复杂结构
            if (responseNode.has("thinking_process")) {
                JsonNode thinkingNode = responseNode.get("thinking_process");
                String thinkingJson = objectMapper.writeValueAsString(thinkingNode);
                result.setChainOfThought(thinkingJson);
                log.debug("提取思维链: {}", thinkingJson.substring(0, Math.min(200, thinkingJson.length())));
            }

            // 根据响应类型处理特殊逻辑
            switch (responseType) {
                case "intent_analysis":
                    handleIntentAnalysisResponse(responseNode, result);
                    break;
                case "chain_execution":
                    handleChainExecutionResponse(responseNode, result);
                    break;
                case "data_output":
                    handleDataOutputResponse(responseNode, result);
                    break;
                case "execution_plan":
                    handleExecutionPlanResponse(responseNode, result);
                    break;
                case "content":
                default:
                    // 标准内容响应，无需特殊处理
                    break;
            }

            return result;

        } catch (Exception e) {
            log.warn("解析Agent响应失败，使用原始内容: {}", e.getMessage());
            return AgentExecutionResult.builder()
                    .executionId(request.getExecutionId())
                    .agentId(request.getAgentId())
                    .agentName(agent.getName())
                    .content(aiResponse)
                    .status(AgentExecution.ExecutionStatus.COMPLETED)
                    .startTime(startTime)
                    .endTime(endTime)
                    .build();
        }
    }

    /**
     * 智能提取响应内容 - 适应多种数据结构
     */
    private String extractContentFromResponse(JsonNode responseNode, String originalResponse) {
        // 1. 尝试直接的content字段
        if (responseNode.has("content")) {
            return MarkdownJsonParser.getJsonString(responseNode, "content", originalResponse);
        }

        // 2. 尝试从response子对象中提取
        if (responseNode.has("response") && responseNode.get("response").has("content")) {
            JsonNode responseSubNode = responseNode.get("response");
            return MarkdownJsonParser.getJsonString(responseSubNode, "content", originalResponse);
        }

        // 3. 如果有intent_analysis，提取关键信息作为内容
        if (responseNode.has("intent_analysis")) {
            JsonNode intentNode = responseNode.get("intent_analysis");
            StringBuilder contentBuilder = new StringBuilder();

            if (intentNode.has("primary_agent")) {
                contentBuilder.append("推荐Agent: ").append(intentNode.get("primary_agent").asText()).append("\n");
            }

            if (intentNode.has("intent_category")) {
                contentBuilder.append("意图类别: ").append(intentNode.get("intent_category").asText()).append("\n");
            }

            if (intentNode.has("confidence_score")) {
                contentBuilder.append("置信度: ").append(intentNode.get("confidence_score").asText()).append("\n");
            }

            if (intentNode.has("reasoning") && intentNode.get("reasoning").has("business_context")) {
                contentBuilder.append("业务上下文: ").append(intentNode.get("reasoning").get("business_context").asText());
            }

            String extractedContent = contentBuilder.toString().trim();
            if (!extractedContent.isEmpty()) {
                return extractedContent;
            }
        }

        // 4. 如果有execution_plan，提取执行计划信息
        if (responseNode.has("execution_plan")) {
            JsonNode planNode = responseNode.get("execution_plan");
            StringBuilder contentBuilder = new StringBuilder();

            if (planNode.has("workflow_type")) {
                contentBuilder.append("工作流类型: ").append(planNode.get("workflow_type").asText()).append("\n");
            }

            if (planNode.has("primary_workflow") && planNode.get("primary_workflow").isArray()) {
                contentBuilder.append("执行步骤:\n");
                for (JsonNode stepNode : planNode.get("primary_workflow")) {
                    contentBuilder.append("- ").append(stepNode.asText()).append("\n");
                }
            }

            String extractedContent = contentBuilder.toString().trim();
            if (!extractedContent.isEmpty()) {
                return extractedContent;
            }
        }

        // 5. 如果都没有，返回原始响应
        return originalResponse;
    }

    /**
     * 推断响应类型
     */
    private String inferResponseType(JsonNode responseNode) {
        // 优先检查显式的response_type字段
        if (responseNode.has("response_type")) {
            return responseNode.get("response_type").asText();
        }

        // 根据存在的字段推断类型
        if (responseNode.has("intent_analysis")) {
            return "intent_analysis";
        }

        if (responseNode.has("execution_plan")) {
            return "execution_plan";
        }

        if (responseNode.has("chain_instruction")) {
            return "chain_execution";
        }

        if (responseNode.has("data_summary")) {
            return "data_output";
        }

        // 默认为content类型
        return "content";
    }

    /**
     * 处理意图分析响应
     */
    private void handleIntentAnalysisResponse(JsonNode responseNode, AgentExecutionResult result) {
        // 处理传统的chain_instruction字段
        if (responseNode.has("chain_instruction")) {
            JsonNode chainNode = responseNode.get("chain_instruction");
            Map<String, Object> chainData = objectMapper.convertValue(chainNode, Map.class);
            result.setChainInstruction(chainData);
        }

        // 处理新的intent_analysis结构
        if (responseNode.has("intent_analysis")) {
            JsonNode intentNode = responseNode.get("intent_analysis");
            Map<String, Object> intentData = objectMapper.convertValue(intentNode, Map.class);

            // 将意图分析数据添加到输出数据中
            Map<String, Object> outputData = result.getOutputData();
            if (outputData == null) {
                outputData = new HashMap<>();
                result.setOutputData(outputData);
            }
            outputData.put("intent_analysis", intentData);

            // 如果有primary_agent信息，构建链路指令
            if (intentNode.has("primary_agent")) {
                Map<String, Object> chainData = new HashMap<>();
                chainData.put("agent_name", intentNode.get("primary_agent").asText());

                if (intentNode.has("confidence_score")) {
                    chainData.put("confidence", intentNode.get("confidence_score").asDouble());
                }

                if (responseNode.has("execution_plan")) {
                    JsonNode planNode = responseNode.get("execution_plan");
                    Map<String, Object> planData = objectMapper.convertValue(planNode, Map.class);
                    chainData.put("execution_plan", planData);
                }

                result.setChainInstruction(chainData);
            }
        }
    }

    /**
     * 处理链路执行响应
     */
    private void handleChainExecutionResponse(JsonNode responseNode, AgentExecutionResult result) {
        // 提取执行结果数据
        if (responseNode.has("execution_result")) {
            JsonNode resultNode = responseNode.get("execution_result");
            Map<String, Object> resultData = objectMapper.convertValue(resultNode, Map.class);
            result.setOutputData(resultData);
        }
    }

    /**
     * 处理执行计划响应
     */
    private void handleExecutionPlanResponse(JsonNode responseNode, AgentExecutionResult result) {
        if (responseNode.has("execution_plan")) {
            JsonNode planNode = responseNode.get("execution_plan");
            Map<String, Object> planData = objectMapper.convertValue(planNode, Map.class);

            // 将执行计划添加到输出数据中
            Map<String, Object> outputData = result.getOutputData();
            if (outputData == null) {
                outputData = new HashMap<>();
                result.setOutputData(outputData);
            }
            outputData.put("execution_plan", planData);
        }

        // 处理质量控制信息
        if (responseNode.has("quality_control")) {
            JsonNode qualityNode = responseNode.get("quality_control");
            Map<String, Object> qualityData = objectMapper.convertValue(qualityNode, Map.class);

            Map<String, Object> outputData = result.getOutputData();
            if (outputData == null) {
                outputData = new HashMap<>();
                result.setOutputData(outputData);
            }
            outputData.put("quality_control", qualityData);
        }

        // 处理用户交互信息
        if (responseNode.has("user_interaction")) {
            JsonNode interactionNode = responseNode.get("user_interaction");
            Map<String, Object> interactionData = objectMapper.convertValue(interactionNode, Map.class);

            Map<String, Object> outputData = result.getOutputData();
            if (outputData == null) {
                outputData = new HashMap<>();
                result.setOutputData(outputData);
            }
            outputData.put("user_interaction", interactionData);
        }
    }

    /**
     * 处理数据输出响应
     */
    private void handleDataOutputResponse(JsonNode responseNode, AgentExecutionResult result) {
        if (responseNode.has("data_summary")) {
            JsonNode dataNode = responseNode.get("data_summary");
            Map<String, Object> dataMap = objectMapper.convertValue(dataNode, Map.class);
            result.setOutputData(dataMap);
        }
    }

    /**
     * 创建错误结果
     */
    private AgentExecutionResult createErrorResult(AgentExecutionRequest request, Exception e) {
        return AgentExecutionResult.builder()
                .executionId(request.getExecutionId())
                .agentId(request.getAgentId())
                .status(AgentExecution.ExecutionStatus.FAILED)
                .errorMessage(e.getMessage())
                .startTime(LocalDateTime.now())
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 获取可用Agent列表
     */
    private Map<String, String> getAvailableAgentList() {
        List<AgentDefinition> agents = agentDefinitionService.getActiveAgents();
        Map<String, String> agentMap = new HashMap<>();

        for (AgentDefinition agent : agents) {
            agentMap.put(agent.getName(), agent.getDescription());
        }

        return agentMap;
    }

    /**
     * 安全获取JSON字符串值 (已废弃，使用MarkdownJsonParser.getJsonString)
     */
    @Deprecated
    private String getJsonString(JsonNode node, String fieldName, String defaultValue) {
        return MarkdownJsonParser.getJsonString(node, fieldName, defaultValue);
    }

    // ========== 兼容性方法 ==========

    /**
     * 旧的执行AI Agent方法 - 保持兼容性
     */
    @Deprecated
    public Mono<AgentExecutionResult> executeAiAgent(AgentExecutionRequest request) {
        log.warn("使用已废弃的executeAiAgent方法，建议使用executeAgent或executeAgentAsync");
        return executeAgentAsync(request);
    }

    /**
     * 根据角色查找Agent (兼容性方法)
     */
    @Deprecated
    private AgentDefinition findAgentByRole(AgentRole agentRole) {
        List<AgentDefinition> agents = agentDefinitionService.findByRole(agentRole);
        if (agents.isEmpty()) {
            throw new AgentNotFoundException("找不到指定角色的Agent: " + agentRole);
        }
        return agents.get(0);
    }
}