package com.dipspro.modules.agent.service;

import com.dipspro.modules.agent.entity.AgentExecution;
import com.dipspro.modules.agent.entity.AgentExecution.ExecutionStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Agent 执行服务接口 - 基础版本
 * 完整的执行引擎将在 Story #002 中实现
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface AgentExecutionService {

    /**
     * 创建执行记录
     */
    AgentExecution createExecution(AgentExecution agentExecution);

    /**
     * 更新执行记录
     */
    AgentExecution updateExecution(AgentExecution agentExecution);

    /**
     * 根据 ID 获取执行记录
     */
    Optional<AgentExecution> findById(UUID id);

    /**
     * 根据 ID 获取执行记录（如果不存在则抛出异常）
     */
    AgentExecution getById(UUID id);

    /**
     * 根据会话 ID 查询执行记录
     */
    List<AgentExecution> findByConversationId(UUID conversationId);

    /**
     * 根据用户 ID 分页查询执行记录
     */
    Page<AgentExecution> findByUserId(Long userId, Pageable pageable);

    /**
     * 根据状态查询执行记录
     */
    List<AgentExecution> findByStatus(ExecutionStatus status);

    /**
     * 更新执行状态
     */
    AgentExecution updateExecutionStatus(UUID id, ExecutionStatus status);

    /**
     * 获取执行统计信息
     */
    ExecutionStats getExecutionStatistics();

    /**
     * 执行统计信息内部类
     */
    class ExecutionStats {
        private long totalCount;
        private long pendingCount;
        private long runningCount;
        private long completedCount;
        private long failedCount;
        private long cancelledCount;

        public ExecutionStats(long totalCount, long pendingCount, long runningCount,
                long completedCount, long failedCount, long cancelledCount) {
            this.totalCount = totalCount;
            this.pendingCount = pendingCount;
            this.runningCount = runningCount;
            this.completedCount = completedCount;
            this.failedCount = failedCount;
            this.cancelledCount = cancelledCount;
        }

        // Getters
        public long getTotalCount() {
            return totalCount;
        }

        public long getPendingCount() {
            return pendingCount;
        }

        public long getRunningCount() {
            return runningCount;
        }

        public long getCompletedCount() {
            return completedCount;
        }

        public long getFailedCount() {
            return failedCount;
        }

        public long getCancelledCount() {
            return cancelledCount;
        }
    }
}