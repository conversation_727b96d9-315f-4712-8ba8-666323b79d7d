package com.dipspro.modules.agent.service;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.dipspro.modules.agent.entity.AgentDefinition;
import com.dipspro.modules.agent.entity.AgentDefinition.AgentStatus;

/**
 * Agent 定义服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface AgentDefinitionService {

    /**
     * 创建 Agent
     */
    AgentDefinition createAgent(AgentDefinition agentDefinition);

    /**
     * 更新 Agent
     */
    AgentDefinition updateAgent(AgentDefinition agentDefinition);

    /**
     * 根据 ID 获取 Agent
     */
    Optional<AgentDefinition> findById(Long id);

    /**
     * 根据 ID 获取 Agent（如果不存在则抛出异常）
     */
    AgentDefinition getById(Long id);

    /**
     * 删除 Agent
     */
    void deleteById(Long id);

    /**
     * 获取所有启用的 Agent
     */
    List<AgentDefinition> findActiveAgents();

    /**
     * 根据类别获取启用的 Agent
     */
    List<AgentDefinition> findActiveAgentsByCategory(String category);

    /**
     * 根据能力查询 Agent
     */
    List<AgentDefinition> findAgentsByCapability(String capability);

    /**
     * 分页查询 Agent
     */
    Page<AgentDefinition> findAgents(Pageable pageable);

    /**
     * 根据创建人分页查询 Agent
     */
    Page<AgentDefinition> findAgentsByCreatedBy(Long createdBy, Pageable pageable);

    /**
     * 检查 Agent 名称是否存在
     */
    boolean existsByName(String name, Long createdBy);

    /**
     * 更新 Agent 状态
     */
    AgentDefinition updateAgentStatus(Long id, AgentStatus status);

    /**
     * 根据名称搜索 Agent
     */
    List<AgentDefinition> searchAgentsByName(String name);

    /**
     * 验证 Agent 配置
     */
    void validateAgentConfiguration(AgentDefinition agentDefinition);

    /**
     * 获取 Agent 统计信息
     */
    AgentStats getAgentStatistics();

    /**
     * 根据角色查找Agent
     */
    List<AgentDefinition> findByRole(AgentDefinition.AgentRole role);

    /**
     * 根据名称查找Agent
     */
    AgentDefinition findByName(String name);

    /**
     * 获取所有活跃的Agent（别名方法）
     */
    List<AgentDefinition> getActiveAgents();

    /**
     * Agent 统计信息内部类
     */
    class AgentStats {
        private long totalCount;
        private long activeCount;
        private long inactiveCount;
        private long draftCount;

        // 构造函数
        public AgentStats(long totalCount, long activeCount, long inactiveCount, long draftCount) {
            this.totalCount = totalCount;
            this.activeCount = activeCount;
            this.inactiveCount = inactiveCount;
            this.draftCount = draftCount;
        }

        // Getters
        public long getTotalCount() {
            return totalCount;
        }

        public long getActiveCount() {
            return activeCount;
        }

        public long getInactiveCount() {
            return inactiveCount;
        }

        public long getDraftCount() {
            return draftCount;
        }
    }
}