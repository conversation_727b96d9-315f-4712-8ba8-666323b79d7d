package com.dipspro.modules.agent.service;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.dipspro.modules.agent.dto.api.SseEvent;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;

/**
 * SSE 连接管理器
 * 负责管理所有的 SSE 连接，提供事件广播功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@Slf4j
public class SseEmitterManager {

    /**
     * 存储对话ID -> 用户ID -> SSE连接的映射
     */
    private final Map<String, Map<Long, SseEmitter>> emitters = new ConcurrentHashMap<>();

    /**
     * 心跳检测执行器
     */
    private final ScheduledExecutorService heartbeatExecutor = Executors.newScheduledThreadPool(2);

    /**
     * 连接计数器
     */
    private final AtomicInteger connectionCounter = new AtomicInteger(0);

    /**
     * 默认超时时间（5分钟）
     */
    private static final long DEFAULT_TIMEOUT = 5 * 60 * 1000L;

    /**
     * 心跳间隔（30秒）
     */
    private static final long HEARTBEAT_INTERVAL = 30;

    @PostConstruct
    public void init() {
        log.info("SSE 连接管理器初始化");

        // 启动心跳检测
        heartbeatExecutor.scheduleAtFixedRate(
                this::sendHeartbeat,
                HEARTBEAT_INTERVAL,
                HEARTBEAT_INTERVAL,
                TimeUnit.SECONDS);

        // 启动连接清理任务
        heartbeatExecutor.scheduleAtFixedRate(
                this::cleanupDeadConnections,
                60,
                60,
                TimeUnit.SECONDS);
    }

    @PreDestroy
    public void destroy() {
        log.info("SSE 连接管理器关闭");

        // 关闭所有连接
        emitters.values().forEach(userEmitters -> userEmitters.values().forEach(emitter -> {
            try {
                emitter.complete();
            } catch (Exception e) {
                log.debug("关闭 SSE 连接时出错: {}", e.getMessage());
            }
        }));

        emitters.clear();
        heartbeatExecutor.shutdown();

        try {
            if (!heartbeatExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                heartbeatExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            heartbeatExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 注册 SSE 连接
     */
    public void registerEmitter(String conversationId, Long userId, SseEmitter emitter) {
        emitters.computeIfAbsent(conversationId, k -> new ConcurrentHashMap<>())
                .put(userId, emitter);

        int currentConnections = connectionCounter.incrementAndGet();

        log.info("注册 SSE 连接: 对话={}, 用户={}, 当前连接数={}",
                conversationId, userId, currentConnections);

        // 设置连接生命周期事件处理
        setupEmitterEventHandlers(conversationId, userId, emitter);
    }

    /**
     * 移除 SSE 连接
     */
    public void removeEmitter(String conversationId, Long userId) {
        Map<Long, SseEmitter> conversationEmitters = emitters.get(conversationId);
        if (conversationEmitters != null) {
            SseEmitter removed = conversationEmitters.remove(userId);
            if (removed != null) {
                connectionCounter.decrementAndGet();
            }

            // 如果对话没有更多连接，移除对话映射
            if (conversationEmitters.isEmpty()) {
                emitters.remove(conversationId);
            }
        }

        log.info("移除 SSE 连接: 对话={}, 用户={}, 当前连接数={}",
                conversationId, userId, connectionCounter.get());
    }

    /**
     * 广播事件到对话的所有连接
     */
    public void broadcast(String conversationId, SseEvent event) {
        Map<Long, SseEmitter> conversationEmitters = emitters.get(conversationId);
        if (conversationEmitters == null || conversationEmitters.isEmpty()) {
            log.debug("对话 {} 没有活跃连接，跳过广播", conversationId);
            return;
        }

        log.debug("广播事件到对话 {}: 类型={}, 连接数={}",
                conversationId, event.getEventType(), conversationEmitters.size());

        List<Long> deadConnections = new java.util.ArrayList<>();

        conversationEmitters.forEach((userId, emitter) -> {
            try {
                // 检查连接是否有效
                if (!isEmitterValid(emitter)) {
                    log.debug("SSE 连接已失效，跳过发送: 用户={}, 对话={}", userId, conversationId);
                    deadConnections.add(userId);
                    return;
                }

                emitter.send(SseEmitter.event()
                        .name("agent-event")
                        .data(event));

                log.trace("成功发送 SSE 事件: 用户={}, 对话={}, 事件类型={}",
                        userId, conversationId, event.getEventType());

            } catch (IOException e) {
                // 检查是否为Broken pipe错误
                if (isBrokenPipeError(e)) {
                    log.debug("客户端断开连接 (Broken pipe): 用户={}, 对话={}", userId, conversationId);
                } else {
                    log.warn("发送 SSE 事件失败: 用户={}, 对话={}, 错误={}",
                            userId, conversationId, e.getMessage());
                }
                deadConnections.add(userId);

            } catch (IllegalStateException e) {
                // 连接已关闭或超时
                log.debug("SSE 连接状态异常: 用户={}, 对话={}, 错误={}",
                        userId, conversationId, e.getMessage());
                deadConnections.add(userId);

            } catch (Exception e) {
                log.error("发送 SSE 事件时发生未知错误: 用户={}, 对话={}",
                        userId, conversationId, e);
                deadConnections.add(userId);
            }
        });

        // 清理失效连接
        deadConnections.forEach(userId -> removeEmitter(conversationId, userId));
    }

    /**
     * 广播事件到特定用户
     */
    public void broadcastToUser(String conversationId, Long userId, SseEvent event) {
        Map<Long, SseEmitter> conversationEmitters = emitters.get(conversationId);
        if (conversationEmitters == null) {
            return;
        }

        SseEmitter emitter = conversationEmitters.get(userId);
        if (emitter == null) {
            return;
        }

        try {
            emitter.send(SseEmitter.event()
                    .name("agent-event")
                    .data(event));

            log.debug("成功发送个人 SSE 事件: 用户={}, 对话={}, 事件类型={}",
                    userId, conversationId, event.getEventType());

        } catch (IOException e) {
            log.warn("发送个人 SSE 事件失败: 用户={}, 对话={}, 错误={}",
                    userId, conversationId, e.getMessage());
            removeEmitter(conversationId, userId);

        } catch (Exception e) {
            log.error("发送个人 SSE 事件时发生未知错误: 用户={}, 对话={}",
                    userId, conversationId, e);
            removeEmitter(conversationId, userId);
        }
    }

    /**
     * 发送心跳检测
     */
    private void sendHeartbeat() {
        if (emitters.isEmpty()) {
            return;
        }

        SseEvent heartbeat = SseEvent.heartbeat();

        log.trace("发送心跳检测: 对话数={}, 总连接数={}",
                emitters.size(), connectionCounter.get());

        // 使用安全的心跳发送，避免在Broken pipe时触发全局异常处理
        emitters.forEach((conversationId, userEmitters) -> {
            safelyBroadcastHeartbeat(conversationId, heartbeat);
        });
    }

    /**
     * 安全地广播心跳，避免触发全局异常处理
     */
    private void safelyBroadcastHeartbeat(String conversationId, SseEvent heartbeat) {
        Map<Long, SseEmitter> conversationEmitters = emitters.get(conversationId);
        if (conversationEmitters == null || conversationEmitters.isEmpty()) {
            return;
        }

        List<Long> deadConnections = new java.util.ArrayList<>();

        conversationEmitters.forEach((userId, emitter) -> {
            try {
                if (!isEmitterValid(emitter)) {
                    deadConnections.add(userId);
                    return;
                }

                emitter.send(SseEmitter.event()
                        .name("agent-event")
                        .data(heartbeat));

            } catch (IOException e) {
                if (isBrokenPipeError(e)) {
                    log.trace("心跳发送时发现客户端断开: 用户={}, 对话={}", userId, conversationId);
                } else {
                    log.debug("心跳发送失败: 用户={}, 对话={}, 错误={}",
                            userId, conversationId, e.getMessage());
                }
                deadConnections.add(userId);

            } catch (IllegalStateException e) {
                log.trace("心跳发送时连接状态异常: 用户={}, 对话={}", userId, conversationId);
                deadConnections.add(userId);

            } catch (Exception e) {
                // 静默处理，避免在心跳中产生过多噪音
                log.trace("心跳发送时发生异常: 用户={}, 对话={}", userId, conversationId);
                deadConnections.add(userId);
            }
        });

        // 清理失效连接
        deadConnections.forEach(userId -> removeEmitter(conversationId, userId));
    }

    /**
     * 检查SSE连接是否有效
     */
    private boolean isEmitterValid(SseEmitter emitter) {
        try {
            // 通过反射获取SseEmitter的内部状态
            // 这是一个简单的有效性检查
            return emitter != null;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查是否为Broken pipe错误
     */
    private boolean isBrokenPipeError(IOException e) {
        String message = e.getMessage();
        if (message == null) {
            return false;
        }
        
        String lowerMessage = message.toLowerCase();
        return lowerMessage.contains("broken pipe") || 
               lowerMessage.contains("connection reset") ||
               lowerMessage.contains("connection aborted") ||
               lowerMessage.contains("socket closed");
    }

    /**
     * 清理失效连接
     */
    private void cleanupDeadConnections() {
        if (emitters.isEmpty()) {
            return;
        }

        log.trace("开始清理失效连接检查");

        List<String> emptyConversations = new java.util.ArrayList<>();

        emitters.forEach((conversationId, userEmitters) -> {
            List<Long> deadUsers = new java.util.ArrayList<>();

            userEmitters.forEach((userId, emitter) -> {
                // 检查连接是否仍然有效
                if (!isEmitterValid(emitter)) {
                    deadUsers.add(userId);
                    return;
                }

                // 尝试发送一个轻量级的ping事件来测试连接
                try {
                    emitter.send(SseEmitter.event()
                            .name("ping")
                            .data("connection-check"));
                } catch (IOException e) {
                    if (isBrokenPipeError(e)) {
                        log.debug("清理过程中发现断开连接: 用户={}, 对话={}", userId, conversationId);
                    } else {
                        log.debug("清理过程中连接测试失败: 用户={}, 对话={}, 错误={}",
                                userId, conversationId, e.getMessage());
                    }
                    deadUsers.add(userId);
                } catch (Exception e) {
                    log.error("清理过程中连接测试异常: 用户={}, 对话={}, 错误={}",
                            userId, conversationId, e.getMessage());
                    deadUsers.add(userId);
                }
            });

            deadUsers.forEach(userId -> removeEmitter(conversationId, userId));

            if (userEmitters.isEmpty()) {
                emptyConversations.add(conversationId);
            }
        });

        emptyConversations.forEach(emitters::remove);

        if (!emptyConversations.isEmpty()) {
            log.debug("清理了 {} 个空对话", emptyConversations.size());
        }
    }

    /**
     * 设置连接事件处理器
     */
    private void setupEmitterEventHandlers(String conversationId, Long userId, SseEmitter emitter) {
        emitter.onCompletion(() -> {
            log.debug("SSE 连接正常完成: 对话={}, 用户={}", conversationId, userId);
            removeEmitter(conversationId, userId);
        });

        emitter.onTimeout(() -> {
            log.debug("SSE 连接超时: 对话={}, 用户={}", conversationId, userId);
            removeEmitter(conversationId, userId);
        });

        emitter.onError(ex -> {
            log.warn("SSE 连接发生错误: 对话={}, 用户={}, 错误={}",
                    conversationId, userId, ex.getMessage());
            removeEmitter(conversationId, userId);
        });
    }

    /**
     * 获取活跃连接数
     */
    public int getActiveConnectionCount() {
        return connectionCounter.get();
    }

    /**
     * 获取活跃对话数
     */
    public int getActiveConversationCount() {
        return emitters.size();
    }

    /**
     * 检查对话是否有活跃连接
     */
    public boolean hasActiveConnections(String conversationId) {
        Map<Long, SseEmitter> conversationEmitters = emitters.get(conversationId);
        return conversationEmitters != null && !conversationEmitters.isEmpty();
    }

    /**
     * 获取对话的连接数
     */
    public int getConnectionCount(String conversationId) {
        Map<Long, SseEmitter> conversationEmitters = emitters.get(conversationId);
        return conversationEmitters != null ? conversationEmitters.size() : 0;
    }

    /**
     * 获取连接统计信息
     */
    public Map<String, Object> getConnectionStats() {
        return Map.of(
                "totalConnections", getActiveConnectionCount(),
                "totalConversations", getActiveConversationCount(),
                "avgConnectionsPerConversation",
                getActiveConversationCount() > 0 ? (double) getActiveConnectionCount() / getActiveConversationCount()
                        : 0);
    }
}