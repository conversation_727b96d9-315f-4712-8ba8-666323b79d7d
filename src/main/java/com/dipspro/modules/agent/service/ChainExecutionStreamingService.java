package com.dipspro.modules.agent.service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.dipspro.modules.agent.dto.ChainExecutionEvent;

import lombok.extern.slf4j.Slf4j;

/**
 * 链路执行流式响应服务 - 管理SSE连接和事件推送
 * 
 * 核心功能：
 * 1. 管理SSE连接生命周期
 * 2. 实时推送链路执行事件
 * 3. 处理连接超时和错误
 * 4. 支持心跳保持连接
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Slf4j
public class ChainExecutionStreamingService {

    /**
     * 活跃连接映射：conversationId -> SseEmitter
     */
    private final ConcurrentHashMap<String, SseEmitter> activeConnections = new ConcurrentHashMap<>();

    /**
     * 用于心跳的定时执行器
     */
    private final ScheduledExecutorService heartbeatExecutor = Executors.newScheduledThreadPool(2);

    /**
     * SSE连接超时时间（毫秒）
     */
    private static final long SSE_TIMEOUT = 300000L; // 5分钟

    /**
     * 心跳间隔（秒）
     */
    private static final long HEARTBEAT_INTERVAL = 30;

    /**
     * 创建SSE连接
     * 
     * @param conversationId 会话ID
     * @return SseEmitter
     */
    public SseEmitter createConnection(String conversationId) {
        log.info("创建SSE连接: conversationId={}", conversationId);
        
        SseEmitter emitter = new SseEmitter(SSE_TIMEOUT);
        
        // 设置连接事件处理器
        emitter.onCompletion(() -> {
            log.info("SSE连接完成: conversationId={}", conversationId);
            activeConnections.remove(conversationId);
        });
        
        emitter.onTimeout(() -> {
            log.warn("SSE连接超时: conversationId={}", conversationId);
            activeConnections.remove(conversationId);
        });
        
        emitter.onError(throwable -> {
            log.error("SSE连接错误: conversationId={}, error={}", conversationId, throwable.getMessage());
            activeConnections.remove(conversationId);
        });
        
        // 保存连接
        activeConnections.put(conversationId, emitter);
        
        // 发送连接建立事件
        try {
            emitter.send(SseEmitter.event()
                    .name("connection_established")
                    .data("{\"status\": \"connected\", \"conversationId\": \"" + conversationId + "\"}")
                    .id("conn_" + System.currentTimeMillis()));
        } catch (Exception e) {
            log.error("发送连接建立事件失败: conversationId={}", conversationId, e);
        }
        
        // 启动心跳
        startHeartbeat(conversationId);
        
        return emitter;
    }

    /**
     * 发送链路执行事件
     * 
     * @param conversationId 会话ID
     * @param event 执行事件
     */
    public void sendEvent(String conversationId, ChainExecutionEvent event) {
        SseEmitter emitter = activeConnections.get(conversationId);
        if (emitter == null) {
            log.warn("找不到SSE连接: conversationId={}", conversationId);
            return;
        }
        
        try {
            emitter.send(SseEmitter.event()
                    .name(event.getEventType())
                    .data(event)
                    .id(event.getEventId()));
            
            log.debug("发送SSE事件成功: conversationId={}, eventType={}", 
                    conversationId, event.getEventType());
            
        } catch (Exception e) {
            log.error("发送SSE事件失败: conversationId={}, eventType={}", 
                    conversationId, event.getEventType(), e);
            
            // 连接异常，移除连接
            activeConnections.remove(conversationId);
            try {
                emitter.completeWithError(e);
            } catch (Exception ignored) {
                // 忽略完成时的异常
            }
        }
    }

    /**
     * 发送思维链事件
     * 
     * @param conversationId 会话ID
     * @param stepId 步骤ID
     * @param thinking 思维过程
     */
    public void sendThinkingChain(String conversationId, String stepId, 
                                 ChainExecutionEvent.ThinkingProcess thinking) {
        ChainExecutionEvent event = ChainExecutionEvent.thinking(conversationId, stepId, thinking);
        sendEvent(conversationId, event);
    }

    /**
     * 发送步骤完成事件
     * 
     * @param conversationId 会话ID
     * @param stepId 步骤ID
     * @param description 步骤描述
     * @param result 执行结果
     */
    public void sendStepCompleted(String conversationId, String stepId,
                                 String description, Object result) {
        ChainExecutionEvent event = ChainExecutionEvent.stepCompleted(stepId, description, result);
        event.setConversationId(conversationId);
        sendEvent(conversationId, event);
    }

    /**
     * 发送错误事件
     * 
     * @param conversationId 会话ID
     * @param errorMessage 错误信息
     */
    public void sendError(String conversationId, String errorMessage) {
        ChainExecutionEvent event = ChainExecutionEvent.error(conversationId, errorMessage);
        sendEvent(conversationId, event);
    }

    /**
     * 发送完成事件并关闭连接
     * 
     * @param conversationId 会话ID
     */
    public void sendCompletionAndClose(String conversationId) {
        ChainExecutionEvent event = ChainExecutionEvent.completed(conversationId);
        sendEvent(conversationId, event);
        
        // 延迟关闭连接，确保最后的事件发送完成
        heartbeatExecutor.schedule(() -> {
            closeConnection(conversationId);
        }, 1, TimeUnit.SECONDS);
    }

    /**
     * 关闭连接
     * 
     * @param conversationId 会话ID
     */
    public void closeConnection(String conversationId) {
        SseEmitter emitter = activeConnections.remove(conversationId);
        if (emitter != null) {
            try {
                emitter.complete();
                log.info("SSE连接已关闭: conversationId={}", conversationId);
            } catch (Exception e) {
                log.error("关闭SSE连接失败: conversationId={}", conversationId, e);
            }
        }
    }

    /**
     * 获取活跃连接数
     * 
     * @return 活跃连接数
     */
    public int getActiveConnectionCount() {
        return activeConnections.size();
    }

    /**
     * 检查连接是否存在
     * 
     * @param conversationId 会话ID
     * @return 是否存在连接
     */
    public boolean hasConnection(String conversationId) {
        return activeConnections.containsKey(conversationId);
    }

    /**
     * 启动心跳保持连接
     * 
     * @param conversationId 会话ID
     */
    private void startHeartbeat(String conversationId) {
        heartbeatExecutor.scheduleAtFixedRate(() -> {
            SseEmitter emitter = activeConnections.get(conversationId);
            if (emitter != null) {
                try {
                    emitter.send(SseEmitter.event()
                            .name("heartbeat")
                            .data("{\"type\": \"heartbeat\", \"timestamp\": " + System.currentTimeMillis() + "}")
                            .id("heartbeat_" + System.currentTimeMillis()));
                    
                    log.debug("发送心跳: conversationId={}", conversationId);
                } catch (Exception e) {
                    log.warn("心跳发送失败，移除连接: conversationId={}", conversationId);
                    activeConnections.remove(conversationId);
                }
            }
        }, HEARTBEAT_INTERVAL, HEARTBEAT_INTERVAL, TimeUnit.SECONDS);
    }

    /**
     * 获取所有活跃连接ID
     * 
     * @return 连接ID集合
     */
    public java.util.Set<String> getActiveConnectionIds() {
        return activeConnections.keySet();
    }

    /**
     * 广播消息到所有连接
     * 
     * @param event 要广播的事件
     */
    public void broadcast(ChainExecutionEvent event) {
        activeConnections.forEach((conversationId, emitter) -> {
            try {
                emitter.send(SseEmitter.event()
                        .name(event.getEventType())
                        .data(event)
                        .id(event.getEventId()));
                
                log.debug("广播事件成功: conversationId={}, eventType={}", 
                        conversationId, event.getEventType());
            } catch (Exception e) {
                log.error("广播事件失败: conversationId={}, eventType={}", 
                        conversationId, event.getEventType(), e);
                activeConnections.remove(conversationId);
            }
        });
    }

    /**
     * 清理所有连接
     */
    public void shutdown() {
        log.info("关闭所有SSE连接: count={}", activeConnections.size());
        activeConnections.forEach((conversationId, emitter) -> {
            try {
                emitter.complete();
            } catch (Exception e) {
                log.error("关闭连接失败: conversationId={}", conversationId, e);
            }
        });
        activeConnections.clear();
        heartbeatExecutor.shutdown();
    }
}