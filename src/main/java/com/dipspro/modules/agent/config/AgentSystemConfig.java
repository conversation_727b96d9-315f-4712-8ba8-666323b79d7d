package com.dipspro.modules.agent.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * Agent 系统配置类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@EnableAsync
@Slf4j
public class AgentSystemConfig {
    
    /**
     * Agent 执行异步任务线程池
     */
    @Bean("agentExecutorPool")
    @ConditionalOnProperty(name = "app.agent.enable", havingValue = "true", matchIfMissing = true)
    public Executor agentExecutorPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(8);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("agent-executor-");
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        
        log.info("Agent 执行线程池初始化完成: CorePoolSize=2, MaxPoolSize=8");
        return executor;
    }
}