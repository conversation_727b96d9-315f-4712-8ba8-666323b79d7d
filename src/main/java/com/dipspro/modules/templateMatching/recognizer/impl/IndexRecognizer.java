package com.dipspro.modules.templateMatching.recognizer.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import org.springframework.stereotype.Component;

import com.dipspro.modules.templateMatching.recognizer.Recognizer;
import com.dipspro.modules.templateMatching.recognizer.RecognizerContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 指标识别器
 * 识别文本中的业务指标关键词
 * 如：质量发生率、客户满意度等
 * 
 */
@Slf4j
@Component
public class IndexRecognizer implements Recognizer {

    private static final String ENTITY_TYPE = "INDEX";
    private static final int PRIORITY = 20;

    // 预定义的指标关键词
    private static final Set<String> METRIC_KEYWORDS = Set.of(
            // todo：标准指标描述/标准指标关键词映射表
            "质量发生率", "总体满意度");

    @Override
    public String getName() {
        return "指标识别器";
    }

    @Override
    public int getPriority() {
        return PRIORITY;
    }

    @Override
    public void recognize(RecognizerContext context) {
        String userInput = context.getUserInput();
        List<String> recognizedMetrics = new ArrayList<>();

        log.debug("开始指标识别，输入文本: {}", userInput);

        // 1. 精确匹配预定义关键词
        for (String keyword : METRIC_KEYWORDS) {
            if (userInput.contains(keyword)) {
                recognizedMetrics.add(keyword);
                log.debug("精确匹配到指标: {}", keyword);
            }
        }
        // todo：更多的指标识别逻辑

        // 添加识别结果到上下文
        if (!recognizedMetrics.isEmpty()) {
            context.addRecognizedEntities(ENTITY_TYPE, recognizedMetrics);

            // 设置上下文属性
            context.setAttribute("hasIndexInfo", true);
            context.setAttribute("idnexCount", recognizedMetrics.size());
            context.setAttribute("primaryIndex", recognizedMetrics.get(0));

            log.info("指标识别完成，识别到{}个指标", recognizedMetrics.size());
        } else {
            log.debug("未识别到指标信息");
            context.setAttribute("hasIndexInfo", false);
        }
    }

    @Override
    public boolean supports(RecognizerContext context) {
        String userInput = context.getUserInput();
        if (userInput == null || userInput.trim().isEmpty()) {
            return false;
        }

        // 检查是否包含指标相关的关键词或修饰词
        return METRIC_KEYWORDS.stream().anyMatch(userInput::contains);
    }
}