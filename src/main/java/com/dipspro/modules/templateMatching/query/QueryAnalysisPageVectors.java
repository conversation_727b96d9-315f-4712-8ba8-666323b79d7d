package com.dipspro.modules.templateMatching.query;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.jdbc.core.JdbcTemplate;

import com.dipspro.modules.templateMatching.dto.AnalysisPageDto;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class QueryAnalysisPageVectors {
    /**
     * 查询分析页面向量数据
     */
    public static List<AnalysisPageDto> query(JdbcTemplate jdbcTemplate, Map<String, Object> standardParams) {
        try {
            StringBuilder sqlBuilder = new StringBuilder("SELECT * FROM vectors_analysis_page");
            List<String> conditions = new ArrayList<>();
            List<Object> params = new ArrayList<>();

            // 构建WHERE条件
            Map<String, String> fieldMapping = Map.of(
                    "resource_type", "resource_type",
                    "data_period", "data_period",
                    "customer_id", "customer_id",
                    "visual_id", "visual_id",
                    "node_id", "node_id");

            fieldMapping.forEach((paramKey, columnName) -> {
                if (standardParams.containsKey(paramKey) && standardParams.get(paramKey) != null) {
                    conditions.add(columnName + " = ?");
                    params.add(standardParams.get(paramKey));
                }
            });

            if (!conditions.isEmpty()) {
                sqlBuilder.append(" WHERE ").append(String.join(" AND ", conditions));
            }

            String sql = sqlBuilder.toString();
            log.debug("执行SQL: {}, 参数: {}", sql, params);

            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql, params.toArray());

            return rows.stream()
                    .map(AnalysisPageDto::fromDatabaseRow)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询分析页面向量数据时发生错误: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
}
