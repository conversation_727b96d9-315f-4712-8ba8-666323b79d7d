package com.dipspro.modules.auth.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * 登录响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LoginResponse {

    private String accessToken;
    private String refreshToken;
    private Long userId;
    private String username;
    private String realName;
    private String avatar;
    private Long tenantId;
    private String tenantName;
    private Boolean firstLoginCompleted;
    private Boolean userAgreementAccepted;
    private String industryType;
    private String tenantType;
    private Set<String> roles;
    private Set<String> perms;
}