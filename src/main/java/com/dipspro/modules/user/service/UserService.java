package com.dipspro.modules.user.service;

import java.util.List;

import org.springframework.data.domain.Page;

import com.dipspro.modules.user.dto.UserCreateDto;
import com.dipspro.modules.user.dto.UserQueryDto;
import com.dipspro.modules.user.dto.UserResponseDto;
import com.dipspro.modules.user.dto.UserUpdateDto;

/**
 * 用户服务接口
 */
public interface UserService {

    /**
     * 分页查询用户列表
     */
    Page<UserResponseDto> getUserList(UserQueryDto queryDto);

    /**
     * 根据ID获取用户详情
     */
    UserResponseDto getUserById(Long id);

    /**
     * 创建用户
     */
    UserResponseDto createUser(UserCreateDto createDto);

    /**
     * 更新用户
     */
    UserResponseDto updateUser(UserUpdateDto updateDto);

    /**
     * 删除用户
     */
    void deleteUser(Long id);

    /**
     * 批量删除用户
     */
    void batchDeleteUsers(List<Long> ids);

    /**
     * 更新用户状态
     */
    void updateUserStatus(Long id, Integer status);

    /**
     * 重置用户密码
     */
    void resetUserPassword(Long id);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 检查手机号是否存在
     */
    boolean existsByPhone(String phone);
} 