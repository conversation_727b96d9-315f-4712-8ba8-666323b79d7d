package com.dipspro.modules.chat.service;

import java.util.List;
import java.util.UUID;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dipspro.modules.chat.entity.ChatMessage;
import com.dipspro.modules.chat.repository.ChatMessageRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 轮次迁移服务
 * 负责为历史消息分配轮次序号和消息顺序
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RoundMigrationService {

    private final ChatMessageRepository chatMessageRepository;

    /**
     * 迁移所有历史消息的轮次信息
     * 
     * @return 迁移的消息数量
     */
    @Transactional
    public long migrateAllMessages() {
        log.info("开始迁移历史消息的轮次信息");

        long totalMigrated = 0;
        int batchSize = 1000;
        int pageNumber = 0;

        while (true) {
            Pageable pageable = PageRequest.of(pageNumber, batchSize);
            Page<UUID> conversationIds = chatMessageRepository.findDistinctConversationIds(pageable);

            if (conversationIds.isEmpty()) {
                break;
            }

            for (UUID conversationId : conversationIds.getContent()) {
                long migrated = migrateConversationMessages(conversationId);
                totalMigrated += migrated;
                log.debug("会话 {} 迁移了 {} 条消息", conversationId, migrated);
            }

            pageNumber++;

            if (!conversationIds.hasNext()) {
                break;
            }
        }

        log.info("历史消息轮次信息迁移完成，总计迁移 {} 条消息", totalMigrated);
        return totalMigrated;
    }

    /**
     * 迁移指定会话的消息轮次信息
     * 
     * @param conversationId 会话ID
     * @return 迁移的消息数量
     */
    @Transactional
    public long migrateConversationMessages(UUID conversationId) {
        log.debug("开始迁移会话 {} 的消息轮次信息", conversationId);

        // 获取会话中所有消息，按创建时间排序
        List<ChatMessage> messages = chatMessageRepository
                .findByConversationIdOrderByCreatedAtAsc(conversationId);

        if (messages.isEmpty()) {
            log.warn("会话 {} 中没有找到消息", conversationId);
            return 0;
        }

        long roundSequence = 1;
        int messageOrder = 1;
        String currentRole = null;

        for (ChatMessage message : messages) {
            // 如果轮次信息已存在，跳过
            if (message.getRoundSequence() != null && message.getMessageOrder() != null) {
                continue;
            }

            String messageRole = message.getRole() != null ? message.getRole().name().toLowerCase() : "assistant";

            // 如果是用户消息且不是第一条消息，开始新轮次
            if ("user".equals(messageRole) && currentRole != null) {
                roundSequence++;
                messageOrder = 1;
            }

            // 设置轮次信息
            message.setRoundSequence(roundSequence);
            message.setMessageOrder(messageOrder);

            // 更新当前角色和消息顺序
            currentRole = messageRole;
            messageOrder++;
        }

        // 批量保存更新
        chatMessageRepository.saveAll(messages);

        log.debug("会话 {} 的消息轮次信息迁移完成，共 {} 条消息，{} 个轮次",
                conversationId, messages.size(), roundSequence);

        return messages.size();
    }

    /**
     * 验证轮次信息的完整性
     * 
     * @return 验证结果统计
     */
    @Transactional(readOnly = true)
    public ValidationResult validateRoundIntegrity() {
        log.info("开始验证轮次信息完整性");

        ValidationResult result = new ValidationResult();

        // 统计缺失轮次信息的消息数量
        long messagesWithoutRound = chatMessageRepository.countByRoundSequenceIsNull();
        long messagesWithoutOrder = chatMessageRepository.countByMessageOrderIsNull();

        result.setMessagesWithoutRoundSequence(messagesWithoutRound);
        result.setMessagesWithoutMessageOrder(messagesWithoutOrder);

        // 检查轮次连续性
        List<Object[]> roundGaps = chatMessageRepository.findRoundSequenceGaps();
        result.setRoundGaps(roundGaps.size());

        // 检查消息顺序连续性
        List<Object[]> orderGaps = chatMessageRepository.findMessageOrderGaps();
        result.setOrderGaps(orderGaps.size());

        log.info("轮次信息验证完成: 缺失轮次序号: {}, 缺失消息顺序: {}, 轮次间隙: {}, 顺序间隙: {}",
                messagesWithoutRound, messagesWithoutOrder, roundGaps.size(), orderGaps.size());

        return result;
    }

    /**
     * 修复轮次信息的不一致问题
     * 
     * @return 修复的消息数量
     */
    @Transactional
    public long repairRoundInconsistencies() {
        log.info("开始修复轮次信息不一致问题");

        long totalRepaired = 0;

        // 获取所有有问题的会话
        List<UUID> problematicConversations = chatMessageRepository
                .findConversationsWithRoundProblems();

        for (UUID conversationId : problematicConversations) {
            // 清空该会话的轮次信息
            chatMessageRepository.clearRoundInfoByConversationId(conversationId);

            // 重新迁移
            long repaired = migrateConversationMessages(conversationId);
            totalRepaired += repaired;

            log.debug("修复会话 {} 的轮次信息，涉及 {} 条消息", conversationId, repaired);
        }

        log.info("轮次信息修复完成，总计修复 {} 条消息", totalRepaired);
        return totalRepaired;
    }

    /**
     * 验证结果统计类
     */
    public static class ValidationResult {
        private long messagesWithoutRoundSequence;
        private long messagesWithoutMessageOrder;
        private long roundGaps;
        private long orderGaps;

        // Getters and Setters
        public long getMessagesWithoutRoundSequence() {
            return messagesWithoutRoundSequence;
        }

        public void setMessagesWithoutRoundSequence(long messagesWithoutRoundSequence) {
            this.messagesWithoutRoundSequence = messagesWithoutRoundSequence;
        }

        public long getMessagesWithoutMessageOrder() {
            return messagesWithoutMessageOrder;
        }

        public void setMessagesWithoutMessageOrder(long messagesWithoutMessageOrder) {
            this.messagesWithoutMessageOrder = messagesWithoutMessageOrder;
        }

        public long getRoundGaps() {
            return roundGaps;
        }

        public void setRoundGaps(long roundGaps) {
            this.roundGaps = roundGaps;
        }

        public long getOrderGaps() {
            return orderGaps;
        }

        public void setOrderGaps(long orderGaps) {
            this.orderGaps = orderGaps;
        }

        public boolean isValid() {
            return messagesWithoutRoundSequence == 0 &&
                    messagesWithoutMessageOrder == 0 &&
                    roundGaps == 0 &&
                    orderGaps == 0;
        }

        @Override
        public String toString() {
            return String.format(
                    "ValidationResult{缺失轮次序号=%d, 缺失消息顺序=%d, 轮次间隙=%d, 顺序间隙=%d, 有效=%s}",
                    messagesWithoutRoundSequence, messagesWithoutMessageOrder,
                    roundGaps, orderGaps, isValid());
        }
    }
}