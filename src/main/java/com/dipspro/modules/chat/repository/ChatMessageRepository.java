package com.dipspro.modules.chat.repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.dipspro.modules.chat.dto.ConversationSummaryDTO;
import com.dipspro.modules.chat.entity.ChatMessage;
import com.dipspro.modules.chat.entity.ChatMessage.MessageRole;

@Repository
public interface ChatMessageRepository extends JpaRepository<ChatMessage, UUID> {

       /**
        * 根据会话 ID 查询所有聊天消息，按创建时间升序排列。
        * 
        * @param conversationId 会话的 UUID。
        * @return 聊天消息列表。
        */
       @Query("SELECT cm FROM ChatMessage cm WHERE cm.conversation.id = :conversationId " +
                     "ORDER BY cm.createdAt ASC")
       List<ChatMessage> findByConversationIdOrderByCreatedAtAsc(@Param("conversationId") UUID conversationId);

       /**
        * 查询会话摘要列表。
        * 该方法按会话 ID 分组消息，并获取会话 ID (作为 id)，
        * 用户发送的第一条消息内容（按字母顺序，存在潜在问题），
        * 以及会话的第一条消息的时间戳 (作为 timestamp)。
        * 结果按第一条消息的时间戳降序排列（最新的会话在前）。
        *
        * @return 包含可能不准确标签的 ConversationSummaryDTO 列表。
        */
       @Query("SELECT new com.dipspro.modules.chat.dto.ConversationSummaryDTO( " +
                     "    m.conversation.id, " +
                     "    MIN(CASE WHEN m.role = com.dipspro.modules.chat.entity.ChatMessage$MessageRole.USER THEN m.content ELSE NULL END), "
                     +
                     "    MIN(m.createdAt) " +
                     ") " +
                     "FROM ChatMessage m " +
                     "GROUP BY m.conversation.id " +
                     "ORDER BY MIN(m.createdAt) DESC")
       List<ConversationSummaryDTO> findConversationSummaries();

       /**
        * 根据用户ID查询会话摘要列表，支持分页。
        * 该方法按会话 ID 分组消息，并获取会话 ID (作为 id)，
        * 用户发送的第一条消息内容，以及会话的第一条消息的时间戳。
        * 结果按第一条消息的时间戳降序排列（最新的会话在前）。
        * 只返回属于指定用户的会话。
        *
        * @param userId   用户ID
        * @param pageable 分页参数
        * @return 包含用户会话摘要的分页 ConversationSummaryDTO
        */
       @Query("SELECT new com.dipspro.modules.chat.dto.ConversationSummaryDTO( " +
                     "    m.conversation.id, " +
                     "    MIN(CASE WHEN m.role = com.dipspro.modules.chat.entity.ChatMessage$MessageRole.USER THEN m.content ELSE NULL END), "
                     +
                     "    MIN(m.createdAt) " +
                     ") " +
                     "FROM ChatMessage m " +
                     "WHERE m.conversation.userId = :userId " +
                     "GROUP BY m.conversation.id " +
                     "ORDER BY MIN(m.createdAt) DESC")
       Page<ConversationSummaryDTO> findConversationSummariesByUserId(@Param("userId") Long userId, Pageable pageable);

       /**
        * 查询指定会话中按时间顺序用户发送的第一条消息的内容。
        *
        * @param conversationId 会话的 UUID。
        * @param role           用户的角色 (通常是 MessageRole.USER)。
        * @return 包含第一条用户消息内容的 Optional 对象，如果不存在用户消息则为空。
        */
       @Query("SELECT m.content " +
                     "FROM ChatMessage m " +
                     "WHERE m.conversation.id = :conversationId AND m.role = :role " +
                     "ORDER BY m.createdAt ASC " +
                     "LIMIT 1") // HQL specific, may need adjustment based on dialect or use findTop/findFirst
                                // convention
       Optional<String> findFirstMessageContentByConversationIdAndRole(UUID conversationId, MessageRole role);

       /**
        * 根据对话ID和轮次序号查询消息，按消息顺序排序
        */
       @Query("SELECT cm FROM ChatMessage cm WHERE cm.conversation.id = :conversationId " +
                     "AND cm.roundSequence = :roundSequence ORDER BY cm.messageOrder ASC")
       List<ChatMessage> findByConversationIdAndRoundSequenceOrderByMessageOrder(
                     @Param("conversationId") UUID conversationId,
                     @Param("roundSequence") Long roundSequence);

       /**
        * 根据对话ID查询消息，按轮次序号和消息顺序排序
        */
       @Query("SELECT cm FROM ChatMessage cm WHERE cm.conversation.id = :conversationId " +
                     "ORDER BY cm.roundSequence ASC, cm.messageOrder ASC")
       List<ChatMessage> findByConversationIdOrderByRoundSequenceAscMessageOrderAsc(
                     @Param("conversationId") UUID conversationId);

       /**
        * 查询轮次序号为空的不同对话ID
        */
       @Query("SELECT DISTINCT cm.conversation.id FROM ChatMessage cm WHERE cm.roundSequence IS NULL")
       List<UUID> findDistinctConversationIdsByRoundSequenceIsNull();

       /**
        * 根据对话ID查询轮次序号为空的消息，按创建时间排序
        */
       @Query("SELECT cm FROM ChatMessage cm WHERE cm.conversation.id = :conversationId " +
                     "AND cm.roundSequence IS NULL ORDER BY cm.createdAt ASC")
       List<ChatMessage> findByConversationIdAndRoundSequenceIsNullOrderByCreatedAtAsc(
                     @Param("conversationId") UUID conversationId);

       /**
        * 统计轮次序号为空的不同对话数量
        */
       @Query("SELECT COUNT(DISTINCT cm.conversation.id) FROM ChatMessage cm WHERE cm.roundSequence IS NULL")
       Long countDistinctConversationIdsByRoundSequenceIsNull();

       /**
        * 统计轮次序号不为空的不同对话数量
        */
       @Query("SELECT COUNT(DISTINCT cm.conversation.id) FROM ChatMessage cm WHERE cm.roundSequence IS NOT NULL")
       Long countDistinctConversationIdsByRoundSequenceIsNotNull();

       /**
        * 统计轮次序号不为空的消息数量
        */
       Long countByRoundSequenceIsNotNull();

       /**
        * 根据会话ID和轮次序号查询消息列表
        * 
        * @param conversationId 会话ID
        * @param roundSequence  轮次序号
        * @return 该轮次的所有消息，按消息顺序排序
        */
       @Query("SELECT cm FROM ChatMessage cm WHERE cm.conversation.id = :conversationId " +
                     "AND cm.roundSequence = :roundSequence ORDER BY cm.messageOrder ASC")
       List<ChatMessage> findByConversationIdAndRoundSequence(
                     @Param("conversationId") UUID conversationId,
                     @Param("roundSequence") Long roundSequence);

       /**
        * 获取会话中所有轮次的序号列表
        * 
        * @param conversationId 会话ID
        * @return 轮次序号列表，按序号升序排列
        */
       @Query("SELECT DISTINCT cm.roundSequence FROM ChatMessage cm " +
                     "WHERE cm.conversation.id = :conversationId AND cm.roundSequence IS NOT NULL " +
                     "ORDER BY cm.roundSequence ASC")
       List<Long> findRoundSequencesByConversationId(@Param("conversationId") UUID conversationId);

       /**
        * 获取指定轮次的消息ID列表
        * 
        * @param conversationId 会话ID
        * @param roundSequence  轮次序号
        * @return 消息ID列表，按消息顺序排序
        */
       @Query("SELECT cm.id FROM ChatMessage cm WHERE cm.conversation.id = :conversationId " +
                     "AND cm.roundSequence = :roundSequence ORDER BY cm.messageOrder ASC")
       List<UUID> findMessageIdsByRound(
                     @Param("conversationId") UUID conversationId,
                     @Param("roundSequence") Long roundSequence);

       /**
        * 获取会话中的最大轮次序号
        * 
        * @param conversationId 会话ID
        * @return 最大轮次序号，如果没有消息返回0
        */
       @Query("SELECT COALESCE(MAX(cm.roundSequence), 0) FROM ChatMessage cm " +
                     "WHERE cm.conversation.id = :conversationId")
       Long findMaxRoundSequenceByConversationId(@Param("conversationId") UUID conversationId);

       /**
        * 获取指定轮次中的最大消息序号
        * 
        * @param conversationId 会话ID
        * @param roundSequence  轮次序号
        * @return 最大消息序号，如果没有消息返回0
        */
       @Query("SELECT COALESCE(MAX(cm.messageOrder), 0) FROM ChatMessage cm " +
                     "WHERE cm.conversation.id = :conversationId AND cm.roundSequence = :roundSequence")
       Long findMaxMessageOrderInRound(@Param("conversationId") UUID conversationId,
                     @Param("roundSequence") Long roundSequence);

       /**
        * 统计指定轮次的消息数量
        * 
        * @param conversationId 会话ID
        * @param roundSequence  轮次序号
        * @return 消息数量
        */
       @Query("SELECT COUNT(cm) FROM ChatMessage cm WHERE cm.conversation.id = :conversationId " +
                     "AND cm.roundSequence = :roundSequence")
       Long countMessagesByRound(
                     @Param("conversationId") UUID conversationId,
                     @Param("roundSequence") Long roundSequence);

       /**
        * 获取所有不同的会话ID（用于数据迁移）
        * 
        * @param pageable 分页参数
        * @return 会话ID分页列表
        */
       @Query("SELECT DISTINCT cm.conversation.id FROM ChatMessage cm " +
                     "WHERE cm.conversation.id IS NOT NULL ORDER BY cm.conversation.id")
       Page<UUID> findDistinctConversationIds(Pageable pageable);

       /**
        * 统计缺失轮次序号的消息数量
        * 
        * @return 缺失轮次序号的消息数量
        */
       @Query("SELECT COUNT(cm) FROM ChatMessage cm WHERE cm.roundSequence IS NULL")
       Long countByRoundSequenceIsNull();

       /**
        * 统计缺失消息顺序的消息数量
        * 
        * @return 缺失消息顺序的消息数量
        */
       @Query("SELECT COUNT(cm) FROM ChatMessage cm WHERE cm.messageOrder IS NULL")
       Long countByMessageOrderIsNull();

       /**
        * 查找轮次序号存在间隙的会话
        * 
        * @return 存在轮次间隙的会话信息列表 [conversationId, missingRound]
        */
       @Query(value = "SELECT conversation_id, round_sequence + 1 as missing_round " +
                     "FROM chat_messages cm1 " +
                     "WHERE round_sequence IS NOT NULL " +
                     "AND NOT EXISTS (SELECT 1 FROM chat_messages cm2 " +
                     "WHERE cm2.conversation_id = cm1.conversation_id " +
                     "AND cm2.round_sequence = cm1.round_sequence + 1) " +
                     "AND round_sequence < (SELECT MAX(round_sequence) FROM chat_messages cm3 " +
                     "WHERE cm3.conversation_id = cm1.conversation_id)", nativeQuery = true)
       List<Object[]> findRoundSequenceGaps();

       /**
        * 查找消息顺序存在间隙的轮次
        * 
        * @return 存在消息顺序间隙的轮次信息列表 [conversationId, roundSequence, missingOrder]
        */
       @Query(value = "SELECT conversation_id, round_sequence, message_order + 1 as missing_order " +
                     "FROM chat_messages cm1 " +
                     "WHERE round_sequence IS NOT NULL AND message_order IS NOT NULL " +
                     "AND NOT EXISTS (SELECT 1 FROM chat_messages cm2 " +
                     "WHERE cm2.conversation_id = cm1.conversation_id " +
                     "AND cm2.round_sequence = cm1.round_sequence " +
                     "AND cm2.message_order = cm1.message_order + 1) " +
                     "AND message_order < (SELECT MAX(message_order) FROM chat_messages cm3 " +
                     "WHERE cm3.conversation_id = cm1.conversation_id " +
                     "AND cm3.round_sequence = cm1.round_sequence)", nativeQuery = true)
       List<Object[]> findMessageOrderGaps();

       /**
        * 查找存在轮次问题的会话ID列表
        * 
        * @return 存在问题的会话ID列表
        */
       @Query("SELECT DISTINCT cm.conversation.id FROM ChatMessage cm " +
                     "WHERE cm.conversation.id IS NOT NULL " +
                     "AND (cm.roundSequence IS NULL OR cm.messageOrder IS NULL)")
       List<UUID> findConversationsWithRoundProblems();

       /**
        * 清空指定会话的轮次信息（用于修复）
        * 
        * @param conversationId 会话ID
        * @return 更新的记录数
        */
       @Modifying
       @Query("UPDATE ChatMessage cm SET cm.roundSequence = NULL, cm.messageOrder = NULL " +
                     "WHERE cm.conversation.id = :conversationId")
       int clearRoundInfoByConversationId(@Param("conversationId") UUID conversationId);

       /**
        * 批量更新消息的轮次信息
        * 
        * @param messageId     消息ID
        * @param roundSequence 轮次序号
        * @param messageOrder  消息顺序
        * @return 更新的记录数
        */
       @Modifying
       @Query("UPDATE ChatMessage cm SET cm.roundSequence = :roundSequence, cm.messageOrder = :messageOrder " +
                     "WHERE cm.id = :messageId")
       int updateRoundInfo(@Param("messageId") UUID messageId,
                     @Param("roundSequence") Long roundSequence,
                     @Param("messageOrder") Integer messageOrder);

       /**
        * 根据轮次信息查询最后一条助手消息
        * 用于确定在哪条消息下显示 Token 用量
        * 
        * @param conversationId 会话ID
        * @param roundSequence  轮次序号
        * @return 该轮次的最后一条助手消息
        */
       @Query("SELECT cm FROM ChatMessage cm WHERE cm.conversation.id = :conversationId " +
                     "AND cm.roundSequence = :roundSequence AND cm.role = 'ASSISTANT' " +
                     "ORDER BY cm.messageOrder DESC LIMIT 1")
       Optional<ChatMessage> findLastAssistantMessageInRound(
                     @Param("conversationId") UUID conversationId,
                     @Param("roundSequence") Long roundSequence);

       /**
        * 查询轮次中的用户消息（通常每个轮次只有一条）
        * 
        * @param conversationId 会话ID
        * @param roundSequence  轮次序号
        * @return 该轮次的用户消息
        */
       @Query("SELECT cm FROM ChatMessage cm WHERE cm.conversation.id = :conversationId " +
                     "AND cm.roundSequence = :roundSequence AND cm.role = 'USER' " +
                     "ORDER BY cm.messageOrder ASC")
       List<ChatMessage> findUserMessagesInRound(
                     @Param("conversationId") UUID conversationId,
                     @Param("roundSequence") Long roundSequence);

       /**
        * 查询轮次中的助手消息
        * 
        * @param conversationId 会话ID
        * @param roundSequence  轮次序号
        * @return 该轮次的助手消息列表，按消息顺序排序
        */
       @Query("SELECT cm FROM ChatMessage cm WHERE cm.conversation.id = :conversationId " +
                     "AND cm.roundSequence = :roundSequence AND cm.role = 'ASSISTANT' " +
                     "ORDER BY cm.messageOrder ASC")
       List<ChatMessage> findAssistantMessagesInRound(
                     @Param("conversationId") UUID conversationId,
                     @Param("roundSequence") Long roundSequence);
}