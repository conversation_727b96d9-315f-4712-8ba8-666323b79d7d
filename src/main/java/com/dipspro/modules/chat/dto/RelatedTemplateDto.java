package com.dipspro.modules.chat.dto;

import java.util.UUID;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 相关模板 DTO
 * 用于在模板详情中显示关联的模板信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RelatedTemplateDto {

    private UUID id;
    private String name;
    private String description;
    private String agentType;
    private Integer priority;
    private UUID relationId; // 关联关系的ID，用于删除操作

    public RelatedTemplateDto(UUID id, String name, String description, String agentType, Integer priority) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.agentType = agentType;
        this.priority = priority;
    }
}