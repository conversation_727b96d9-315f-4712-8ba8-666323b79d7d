package com.dipspro.modules.profile.dao;

import java.util.List;
import java.util.Map;

/**
 * 楼盘分析数据访问接口
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
public interface ProjectAnalyticsDao {

    /**
     * 查询指标得分
     * 
     * @param projectName 项目名称
     * @param indexCodes  指标代码数组
     * @return 指标得分Map，key为指标代码，value为得分（已乘以100）
     */
    Map<String, Integer> queryIndexScore(String projectName, String[] indexCodes);

    /**
     * 查询指标得分（带业主类型）
     * 
     * @param projectName 项目名称
     * @param ownerType   业主类型
     * @param indexCodes  指标代码数组
     * @return 指标得分Map，key为指标代码，value为得分（已乘以100）
     */
    Map<String, Integer> queryIndexScoreWithOwnerType(String projectName, String ownerType, String[] indexCodes);

    /**
     * 批量查询指标得分
     * 
     * @param projectNames 项目名称列表
     * @param indexCodes   指标代码数组
     * @return 项目名称到指标得分的映射
     */
    Map<String, Map<String, Integer>> batchQueryIndexScore(List<String> projectNames, String[] indexCodes);

    /**
     * 查询样框数据
     * 
     * @param projectName 项目名称
     * @return 样框数据
     */
    Map<String, Object> queryTotalSample(String projectName);

    /**
     * 批量查询样框数据
     * 
     * @param projectNames 项目名称列表
     * @return 项目名称到样框数据的映射
     */
    Map<String, Map<String, Object>> batchQueryTotalSample(List<String> projectNames);

    /**
     * 查询年龄段数据
     * 
     * @param projectName 项目名称
     * @param ownerType   业主类型
     * @return 年龄段数据
     */
    Map<String, Integer> queryAgeGroup(String projectName, String ownerType);

    /**
     * 查询分析数据
     * 
     * @param projectName 项目名称
     * @param dataPeriod  数据周期
     * @param attribute   属性
     * @param indexName   指标名称
     * @return 分析数据
     */
    Map<String, Integer> queryAna(String projectName, String dataPeriod, String attribute, String indexName);
}