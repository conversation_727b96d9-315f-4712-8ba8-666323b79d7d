package com.dipspro.modules.system.controller;

import java.util.HashMap;
import java.util.Map;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.util.DatabaseConnectionUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据库监控控制器
 * 提供数据库连接池状态查询和管理功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/system/database")
@PreAuthorize("hasRole('ADMIN')")
public class DatabaseMonitorController {
    
    @Autowired(required = false)
    @Qualifier("primaryDataSource")
    private DataSource primaryDataSource;
    
    @Autowired(required = false)
    @Qualifier("mysqlDataSource")
    private DataSource mysqlDataSource;
    
    /**
     * 获取数据库健康状态
     */
    @GetMapping("/health")
    public ApiResponse<Map<String, Object>> getDatabaseHealth() {
        try {
            log.info("获取数据库健康状态");
            
            Map<String, Object> healthReport = new HashMap<>();
            
            if (primaryDataSource != null && mysqlDataSource != null) {
                healthReport = DatabaseConnectionUtil.getHealthReport(primaryDataSource, mysqlDataSource);
            } else {
                // 单独检查可用的数据源
                if (primaryDataSource != null) {
                    boolean primaryHealthy = DatabaseConnectionUtil.isConnectionHealthy(primaryDataSource);
                    Map<String, Object> primaryStatus = DatabaseConnectionUtil.getConnectionPoolStatus(primaryDataSource);
                    healthReport.put("primary", Map.of(
                        "healthy", primaryHealthy,
                        "status", primaryStatus
                    ));
                }
                
                if (mysqlDataSource != null) {
                    boolean mysqlHealthy = DatabaseConnectionUtil.isConnectionHealthy(mysqlDataSource);
                    Map<String, Object> mysqlStatus = DatabaseConnectionUtil.getConnectionPoolStatus(mysqlDataSource);
                    healthReport.put("mysql", Map.of(
                        "healthy", mysqlHealthy,
                        "status", mysqlStatus
                    ));
                }
                
                healthReport.put("overall", Map.of(
                    "healthy", true,
                    "timestamp", System.currentTimeMillis()
                ));
            }
            
            return ApiResponse.success(healthReport);
            
        } catch (Exception e) {
            log.error("获取数据库健康状态失败", e);
            return ApiResponse.error("获取数据库健康状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取主数据库连接池状态
     */
    @GetMapping("/primary/status")
    public ApiResponse<Map<String, Object>> getPrimaryDataSourceStatus() {
        try {
            if (primaryDataSource == null) {
                return ApiResponse.error("主数据源未配置");
            }
            
            Map<String, Object> status = DatabaseConnectionUtil.getConnectionPoolStatus(primaryDataSource);
            return ApiResponse.success(status);
            
        } catch (Exception e) {
            log.error("获取主数据库连接池状态失败", e);
            return ApiResponse.error("获取主数据库连接池状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取MySQL数据库连接池状态
     */
    @GetMapping("/mysql/status")
    public ApiResponse<Map<String, Object>> getMySQLDataSourceStatus() {
        try {
            if (mysqlDataSource == null) {
                return ApiResponse.error("MySQL数据源未配置");
            }
            
            Map<String, Object> status = DatabaseConnectionUtil.getConnectionPoolStatus(mysqlDataSource);
            return ApiResponse.success(status);
            
        } catch (Exception e) {
            log.error("获取MySQL数据库连接池状态失败", e);
            return ApiResponse.error("获取MySQL数据库连接池状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查并恢复主数据库连接
     */
    @PostMapping("/primary/recover")
    public ApiResponse<Map<String, Object>> recoverPrimaryConnection() {
        try {
            if (primaryDataSource == null) {
                return ApiResponse.error("主数据源未配置");
            }
            
            log.info("手动触发主数据库连接恢复");
            boolean recovered = DatabaseConnectionUtil.checkAndRecoverConnection(primaryDataSource);
            
            Map<String, Object> result = new HashMap<>();
            result.put("recovered", recovered);
            result.put("timestamp", System.currentTimeMillis());
            
            if (recovered) {
                result.put("status", DatabaseConnectionUtil.getConnectionPoolStatus(primaryDataSource));
                return ApiResponse.success(result, "主数据库连接恢复成功");
            } else {
                return ApiResponse.error(result, "主数据库连接恢复失败");
            }
            
        } catch (Exception e) {
            log.error("恢复主数据库连接失败", e);
            return ApiResponse.error("恢复主数据库连接失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查并恢复MySQL数据库连接
     */
    @PostMapping("/mysql/recover")
    public ApiResponse<Map<String, Object>> recoverMySQLConnection() {
        try {
            if (mysqlDataSource == null) {
                return ApiResponse.error("MySQL数据源未配置");
            }
            
            log.info("手动触发MySQL数据库连接恢复");
            boolean recovered = DatabaseConnectionUtil.checkAndRecoverConnection(mysqlDataSource);
            
            Map<String, Object> result = new HashMap<>();
            result.put("recovered", recovered);
            result.put("timestamp", System.currentTimeMillis());
            
            if (recovered) {
                result.put("status", DatabaseConnectionUtil.getConnectionPoolStatus(mysqlDataSource));
                return ApiResponse.success(result, "MySQL数据库连接恢复成功");
            } else {
                return ApiResponse.error(result, "MySQL数据库连接恢复失败");
            }
            
        } catch (Exception e) {
            log.error("恢复MySQL数据库连接失败", e);
            return ApiResponse.error("恢复MySQL数据库连接失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试数据库连接
     */
    @PostMapping("/test")
    public ApiResponse<Map<String, Object>> testDatabaseConnections() {
        try {
            log.info("执行数据库连接测试");
            
            Map<String, Object> testResults = new HashMap<>();
            
            if (primaryDataSource != null) {
                boolean primaryHealthy = DatabaseConnectionUtil.isConnectionHealthy(primaryDataSource);
                testResults.put("primary", Map.of(
                    "healthy", primaryHealthy,
                    "tested_at", System.currentTimeMillis()
                ));
            }
            
            if (mysqlDataSource != null) {
                boolean mysqlHealthy = DatabaseConnectionUtil.isConnectionHealthy(mysqlDataSource);
                testResults.put("mysql", Map.of(
                    "healthy", mysqlHealthy,
                    "tested_at", System.currentTimeMillis()
                ));
            }
            
            return ApiResponse.success(testResults, "数据库连接测试完成");
            
        } catch (Exception e) {
            log.error("数据库连接测试失败", e);
            return ApiResponse.error("数据库连接测试失败: " + e.getMessage());
        }
    }
}