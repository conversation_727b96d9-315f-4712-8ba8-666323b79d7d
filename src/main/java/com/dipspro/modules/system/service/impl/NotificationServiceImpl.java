package com.dipspro.modules.system.service.impl;

import com.dipspro.constant.StatusConstants;
import com.dipspro.modules.system.dto.NotificationDto;
import com.dipspro.modules.system.entity.Notification;
import com.dipspro.modules.system.repository.NotificationRepository;
import com.dipspro.modules.system.service.NotificationService;
import com.dipspro.util.SecurityUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-07-31
 **/
@Service
@Transactional
public class NotificationServiceImpl implements NotificationService {

    @Autowired
    NotificationRepository notificationRepository;

    @Override
    public List<NotificationDto> findByUserIdAndStatus(Long userId, String status) {
        List<Notification> notifications = notificationRepository.findByUserIdAndStatus(userId, status);
        return notifications.stream().map(this::convertToDto).collect(Collectors.toList());
    }

    private NotificationDto convertToDto(Notification notification) {
        NotificationDto dto = new NotificationDto();
        BeanUtils.copyProperties(notification, dto);
        return dto;
    }

    @Override
    public void readNotification(Long id) {
        Notification notification = notificationRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Notification 不存在，id=" + id));

        notification.setStatus(StatusConstants.READ);
        notification.setUpdateBy(SecurityUtil.getCurrentUsername());
        notification.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        notificationRepository.save(notification);
    }
}
