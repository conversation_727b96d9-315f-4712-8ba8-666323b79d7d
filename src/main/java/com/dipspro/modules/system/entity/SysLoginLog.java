package com.dipspro.modules.system.entity;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统登录日志实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "sys_login_log")
public class SysLoginLog {

    /**
     * 日志ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 租户ID
     */
    @Column(name = "tenant_id")
    private Long tenantId;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 用户名
     */
    @Column(name = "username")
    private String username;

    /**
     * 登录类型：password,sms,oauth
     */
    @Column(name = "login_type")
    private String loginType;

    /**
     * IP地址
     */
    @Column(name = "ip_address")
    private String ipAddress;

    /**
     * 登录地点
     */
    @Column(name = "location")
    private String location;

    /**
     * 用户代理
     */
    @Column(name = "user_agent")
    private String userAgent;

    /**
     * 设备类型：web,mobile,app
     */
    @Column(name = "device_type")
    private String deviceType;

    /**
     * 登录状态：0-失败，1-成功
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 失败原因
     */
    @Column(name = "fail_reason")
    private String failReason;

    /**
     * 登录时间
     */
    @Column(name = "login_time")
    private LocalDateTime loginTime;

    /**
     * 登出时间
     */
    @Column(name = "logout_time")
    private LocalDateTime logoutTime;
} 