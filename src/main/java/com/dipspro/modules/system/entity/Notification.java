package com.dipspro.modules.system.entity;

import com.dipspro.modules.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-07-31
 **/
@Entity
@Data
@Table(name = "sys_notification")
public class Notification extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // 用户ID
    @Column(name = "`user_id`", nullable = false)
    private Long userId;

    // 通知类型
    @Column(name = "`type`")
    private String type;

    // 通知标题
    @Column(name = "`title`")
    private String title;

    // 详细内容
    @Column(name = "`details`")
    private String details;

    // 跳转路径
    @Column(name = "`path`")
    private String path;

    @Column(name = "`status`", nullable = false)
    private String status;
}
