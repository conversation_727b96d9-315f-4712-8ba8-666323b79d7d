package com.dipspro.modules.billing.exception;

/**
 * 发票相关异常
 *
 * <AUTHOR> Pro
 * @since 1.0.0
 */
public class InvoiceException extends RuntimeException {

    public InvoiceException(String message) {
        super(message);
    }

    public InvoiceException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 发票不存在异常
     */
    public static class InvoiceNotFoundException extends InvoiceException {
        public InvoiceNotFoundException(Long invoiceId) {
            super("发票不存在: " + invoiceId);
        }
    }

    /**
     * 发票状态异常
     */
    public static class InvoiceStatusException extends InvoiceException {
        public InvoiceStatusException(String message) {
            super(message);
        }
    }

    /**
     * 无权限访问发票异常
     */
    public static class InvoiceAccessDeniedException extends InvoiceException {
        public InvoiceAccessDeniedException() {
            super("无权限访问该发票");
        }

        public InvoiceAccessDeniedException(String message) {
            super(message);
        }
    }

    /**
     * 发票文件异常
     */
    public static class InvoiceFileException extends InvoiceException {
        public InvoiceFileException(String message) {
            super(message);
        }

        public InvoiceFileException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    /**
     * 交易已开票异常
     */
    public static class TransactionAlreadyInvoicedException extends InvoiceException {
        public TransactionAlreadyInvoicedException(String transactionNo) {
            super("交易已申请发票: " + transactionNo);
        }
    }

    /**
     * 交易不可开票异常
     */
    public static class TransactionNotInvoiceableException extends InvoiceException {
        public TransactionNotInvoiceableException(String transactionNo) {
            super("交易不可开票: " + transactionNo);
        }
    }

    /**
     * 发票金额异常
     */
    public static class InvoiceAmountException extends InvoiceException {
        public InvoiceAmountException(String message) {
            super(message);
        }
    }

    /**
     * 发票申请数据异常
     */
    public static class InvoiceApplicationException extends InvoiceException {
        public InvoiceApplicationException(String message) {
            super(message);
        }
    }

    /**
     * 发票文件未找到异常
     */
    public static class InvoiceFileNotFoundException extends InvoiceException {
        public InvoiceFileNotFoundException(String message) {
            super(message);
        }
    }

    /**
     * 发票未完成异常
     */
    public static class InvoiceNotCompletedException extends InvoiceException {
        public InvoiceNotCompletedException(String message) {
            super(message);
        }
    }

    /**
     * 发票文件上传异常
     */
    public static class InvoiceFileUploadException extends InvoiceException {
        public InvoiceFileUploadException(String message) {
            super(message);
        }
    }

    /**
     * 发票验证异常
     */
    public static class InvoiceValidationException extends InvoiceException {
        public InvoiceValidationException(String message) {
            super(message);
        }
    }

    /**
     * 发票系统异常
     */
    public static class InvoiceSystemException extends InvoiceException {
        public InvoiceSystemException(String message) {
            super(message);
        }
    }
}