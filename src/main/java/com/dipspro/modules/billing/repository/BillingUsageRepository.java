package com.dipspro.modules.billing.repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.dipspro.modules.billing.entity.BillingUsageRecord;

/**
 * 计费使用记录数据访问层
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface BillingUsageRepository extends JpaRepository<BillingUsageRecord, Long> {

    /**
     * 根据消息ID查询计费记录
     * 
     * @param messageId 消息ID
     * @return 计费记录
     */
    Optional<BillingUsageRecord> findByMessageId(UUID messageId);

    /**
     * 根据消息ID列表批量查询计费记录
     * 
     * @param messageIds 消息ID列表
     * @return 计费记录列表
     */
    List<BillingUsageRecord> findByMessageIdIn(List<UUID> messageIds);

    /**
     * 根据用户ID分页查询计费记录
     * 
     * @param userId   用户ID
     * @param pageable 分页参数
     * @return 计费记录分页列表
     */
    Page<BillingUsageRecord> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);

    /**
     * 根据用户ID和时间范围查询计费记录
     * 
     * @param userId    用户ID
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param pageable  分页参数
     * @return 计费记录分页列表
     */
    Page<BillingUsageRecord> findByUserIdAndCreatedAtBetweenOrderByCreatedAtDesc(
            Long userId, LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * 根据会话ID查询计费记录
     * 
     * @param conversationId 会话ID
     * @return 计费记录列表
     */
    List<BillingUsageRecord> findByConversationIdOrderByCreatedAtAsc(UUID conversationId);

    /**
     * 根据用户ID和状态查询计费记录
     * 
     * @param userId   用户ID
     * @param status   计费状态
     * @param pageable 分页参数
     * @return 计费记录分页列表
     */
    Page<BillingUsageRecord> findByUserIdAndStatusOrderByCreatedAtDesc(
            Long userId, String status, Pageable pageable);

    /**
     * 统计用户的总Token消费
     * 
     * @param userId 用户ID
     * @return 总Token数量
     */
    @Query("SELECT COALESCE(SUM(br.totalTokens), 0) FROM BillingUsageRecord br " +
            "WHERE br.userId = :userId AND br.status = 'SUCCESS'")
    Long sumTotalTokensByUserId(@Param("userId") Long userId);

    /**
     * 统计用户的总费用支出
     * 
     * @param userId 用户ID
     * @return 总费用
     */
    @Query("SELECT COALESCE(SUM(br.totalCost), 0) FROM BillingUsageRecord br " +
            "WHERE br.userId = :userId AND br.status = 'SUCCESS'")
    BigDecimal sumTotalCostByUserId(@Param("userId") Long userId);

    /**
     * 统计用户在指定时间范围内的Token消费
     * 
     * @param userId    用户ID
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return Token统计信息 [totalTokens, inputTokens, outputTokens, conversationCount]
     */
    @Query("SELECT " +
            "COALESCE(SUM(br.totalTokens), 0) as totalTokens, " +
            "COALESCE(SUM(br.inputTokens), 0) as inputTokens, " +
            "COALESCE(SUM(br.outputTokens), 0) as outputTokens, " +
            "COUNT(DISTINCT br.conversationId) as conversationCount " +
            "FROM BillingUsageRecord br " +
            "WHERE br.userId = :userId AND br.status = 'SUCCESS' " +
            "AND br.createdAt BETWEEN :startDate AND :endDate")
    Object[] getTokenStatisticsByUserIdAndDateRange(
            @Param("userId") Long userId,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);

    /**
     * 根据消息ID列表统计轮次Token用量
     * 
     * @param messageIds 消息ID列表
     * @return Token统计信息 [totalInputTokens, totalOutputTokens, totalCost,
     *         completedCount, totalCount]
     */
    @Query("SELECT " +
            "COALESCE(SUM(CASE WHEN br.status = 'SUCCESS' THEN br.inputTokens ELSE 0 END), 0) as totalInputTokens, " +
            "COALESCE(SUM(CASE WHEN br.status = 'SUCCESS' THEN br.outputTokens ELSE 0 END), 0) as totalOutputTokens, " +
            "COALESCE(SUM(CASE WHEN br.status = 'SUCCESS' THEN br.totalCost ELSE 0 END), 0) as totalCost, " +
            "COUNT(CASE WHEN br.status = 'SUCCESS' THEN 1 END) as completedCount, " +
            "COUNT(br) as totalCount " +
            "FROM BillingUsageRecord br " +
            "WHERE br.messageId IN :messageIds")
    Object[] getRoundTokenStatistics(@Param("messageIds") List<UUID> messageIds);

    /**
     * 检查轮次中所有消息的计费状态
     * 
     * @param messageIds 消息ID列表
     * @return 状态统计 [pendingCount, successCount, failedCount]
     */
    @Query("SELECT " +
            "COUNT(CASE WHEN br.status = 'PENDING' THEN 1 END) as pendingCount, " +
            "COUNT(CASE WHEN br.status = 'SUCCESS' THEN 1 END) as successCount, " +
            "COUNT(CASE WHEN br.status = 'FAILED' THEN 1 END) as failedCount " +
            "FROM BillingUsageRecord br " +
            "WHERE br.messageId IN :messageIds")
    Object[] getRoundBillingStatus(@Param("messageIds") List<UUID> messageIds);

    /**
     * 查询未完成计费的记录
     * 
     * @param pageable 分页参数
     * @return 未完成的计费记录
     */
    @Query("SELECT br FROM BillingUsageRecord br " +
            "WHERE br.status = 'PENDING' " +
            "ORDER BY br.createdAt ASC")
    Page<BillingUsageRecord> findPendingRecords(Pageable pageable);

    /**
     * 查询可申诉的记录
     * 
     * @param userId   用户ID
     * @param pageable 分页参数
     * @return 可申诉的记录列表
     */
    @Query("SELECT br FROM BillingUsageRecord br " +
            "WHERE br.userId = :userId " +
            "AND br.status = 'SUCCESS' " +
            "AND br.billingStatus = 'BILLED' " +
            "AND (br.isAppealed IS NULL OR br.isAppealed = false) " +
            "ORDER BY br.createdAt DESC")
    Page<BillingUsageRecord> findAppealableRecords(@Param("userId") Long userId, Pageable pageable);

    /**
     * 统计用户的会话数量
     * 
     * @param userId 用户ID
     * @return 会话数量
     */
    @Query("SELECT COUNT(DISTINCT br.conversationId) FROM BillingUsageRecord br " +
            "WHERE br.userId = :userId AND br.status = 'SUCCESS'")
    Long countConversationsByUserId(@Param("userId") Long userId);

    /**
     * 查询最近的计费记录用于监控
     * 
     * @param limit 限制数量
     * @return 最近的计费记录
     */
    @Query("SELECT br FROM BillingUsageRecord br " +
            "ORDER BY br.createdAt DESC LIMIT :limit")
    List<BillingUsageRecord> findRecentRecords(@Param("limit") int limit);
}