package com.dipspro.modules.billing.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.dipspro.modules.billing.entity.IndustryTenantPricing;

/**
 * 行业租户定价数据访问接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface IndustryTenantPricingRepository extends JpaRepository<IndustryTenantPricing, Long> {

        /**
         * 根据行业类型和租户类型查询定价列表
         *
         * @param industryType 行业类型
         * @param tenantType   租户类型
         * @return 定价列表
         */
        List<IndustryTenantPricing> findByIndustryTypeAndTenantTypeOrderBySortOrderAsc(String industryType,
                        String tenantType);

        /**
         * 根据行业类型和租户类型查询可用的定价列表
         *
         * @param industryType 行业类型
         * @param tenantType   租户类型
         * @return 可用的定价列表
         */
        @Query("SELECT itp FROM IndustryTenantPricing itp WHERE itp.industryType = :industryType AND itp.tenantType = :tenantType AND itp.isAvailable = true ORDER BY itp.sortOrder ASC, itp.id ASC")
        List<IndustryTenantPricing> findAvailablePricingByIndustryAndTenantType(
                        @Param("industryType") String industryType,
                        @Param("tenantType") String tenantType);

        /**
         * 根据行业类型查询定价列表
         *
         * @param industryType 行业类型
         * @return 定价列表
         */
        List<IndustryTenantPricing> findByIndustryTypeOrderBySortOrderAsc(String industryType);

        /**
         * 根据租户类型查询定价列表
         *
         * @param tenantType 租户类型
         * @return 定价列表
         */
        List<IndustryTenantPricing> findByTenantTypeOrderBySortOrderAsc(String tenantType);

        /**
         * 查询所有可用的定价，按排序权重排序
         *
         * @return 可用的定价列表
         */
        @Query("SELECT itp FROM IndustryTenantPricing itp WHERE itp.isAvailable = true ORDER BY itp.sortOrder ASC, itp.id ASC")
        List<IndustryTenantPricing> findAllAvailablePricingOrderBySortOrder();

        /**
         * 查询所有定价（包括不可用的），按排序权重排序
         *
         * @return 所有定价列表
         */
        @Query("SELECT itp FROM IndustryTenantPricing itp ORDER BY itp.sortOrder ASC, itp.id ASC")
        List<IndustryTenantPricing> findAllPricingOrderBySortOrder();

        /**
         * 检查指定行业类型和租户类型是否存在定价配置
         *
         * @param industryType 行业类型
         * @param tenantType   租户类型
         * @return 是否存在
         */
        boolean existsByIndustryTypeAndTenantType(String industryType, String tenantType);

        /**
         * 根据套餐名称、行业类型和租户类型查询定价
         *
         * @param packageName  套餐名称
         * @param industryType 行业类型
         * @param tenantType   租户类型
         * @return 定价信息
         */
        IndustryTenantPricing findByPackageNameAndIndustryTypeAndTenantType(String packageName, String industryType,
                        String tenantType);
}