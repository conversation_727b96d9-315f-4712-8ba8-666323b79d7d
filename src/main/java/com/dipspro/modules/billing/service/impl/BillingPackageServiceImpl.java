package com.dipspro.modules.billing.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dipspro.modules.billing.entity.BillingPackage;
import com.dipspro.modules.billing.repository.BillingPackageRepository;
import com.dipspro.modules.billing.repository.UserBalanceRepository;
import com.dipspro.modules.billing.service.BillingPackageService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 计费套餐配置服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class BillingPackageServiceImpl implements BillingPackageService {

    private final BillingPackageRepository billingPackageRepository;
    private final UserBalanceRepository userBalanceRepository;

    /**
     * 创建计费套餐
     * 
     * 管理员通过此方法创建新的计费套餐配置：
     * 1. 配置验证：检查套餐的所有配置参数是否有效
     * 2. 唯一性检查：确保套餐名称在系统中唯一
     * 3. 套餐创建：创建完整的套餐配置记录
     * 4. 数据持久化：保存到数据库并返回完整记录
     * 
     * 套餐配置包含：
     * - 套餐名称和描述
     * - 输入Token和输出Token的单价
     * - 免费Token额度和每日限制
     * - 单次请求最大Token数
     * - 套餐激活状态和排序顺序
     * 
     * @param billingPackage 要创建的套餐对象，包含所有必要配置
     * @return 创建成功的套餐记录
     * @throws IllegalArgumentException 当套餐配置无效或名称重复时抛出
     */
    @Override
    public BillingPackage createPackage(BillingPackage billingPackage) {
        log.info("创建计费套餐: {}", billingPackage.getName());

        // 第一步：验证套餐配置的完整性和有效性
        String validationError = validatePackageConfiguration(billingPackage);
        if (validationError != null) {
            throw new IllegalArgumentException(validationError);
        }

        // 第二步：检查套餐名称是否已存在，确保唯一性
        if (isPackageNameExists(billingPackage.getName(), null)) {
            throw new IllegalArgumentException("套餐名称已存在: " + billingPackage.getName());
        }

        // 注意：当前实体设计中没有isDefault字段，使用sortOrder=0作为默认套餐标识

        // 第三步：保存套餐到数据库
        BillingPackage savedPackage = billingPackageRepository.save(billingPackage);
        log.info("成功创建计费套餐: ID={}, Name={}", savedPackage.getId(), savedPackage.getName());

        return savedPackage;
    }

    /**
     * 更新计费套餐
     * 
     * 管理员通过此方法更新现有的计费套餐配置：
     * 1. 存在性检查：验证要更新的套餐是否存在
     * 2. 配置验证：检查新的套餐配置参数是否有效
     * 3. 唯一性检查：确保新的套餐名称不与其他套餐冲突
     * 4. 字段更新：更新套餐的所有可修改字段
     * 5. 数据持久化：保存更新后的套餐配置
     * 
     * 可更新的字段包括：
     * - 套餐名称和描述
     * - Token价格配置
     * - 免费Token和限制配置
     * - 激活状态和排序顺序
     * 
     * @param id             要更新的套餐ID
     * @param billingPackage 包含新配置的套餐对象
     * @return 更新后的套餐记录
     * @throws IllegalArgumentException 当套餐不存在、配置无效或名称冲突时抛出
     */
    @Override
    public BillingPackage updatePackage(Long id, BillingPackage billingPackage) {
        log.info("更新计费套餐: ID={}", id);

        // 第一步：检查套餐是否存在
        BillingPackage existingPackage = billingPackageRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("套餐不存在: " + id));

        // 第二步：验证新的套餐配置
        String validationError = validatePackageConfiguration(billingPackage);
        if (validationError != null) {
            throw new IllegalArgumentException(validationError);
        }

        // 第三步：检查套餐名称是否已存在（排除当前套餐）
        if (isPackageNameExists(billingPackage.getName(), id)) {
            throw new IllegalArgumentException("套餐名称已存在: " + billingPackage.getName());
        }

        // 第四步：更新所有可修改的字段
        existingPackage.setName(billingPackage.getName());
        existingPackage.setDescription(billingPackage.getDescription());
        existingPackage.setInputTokenPrice(billingPackage.getInputTokenPrice());
        existingPackage.setOutputTokenPrice(billingPackage.getOutputTokenPrice());
        existingPackage.setFreeTokens(billingPackage.getFreeTokens());
        existingPackage.setMaxTokensPerRequest(billingPackage.getMaxTokensPerRequest());
        existingPackage.setDailyTokenLimit(billingPackage.getDailyTokenLimit());
        existingPackage.setIsActive(billingPackage.getIsActive());
        existingPackage.setSortOrder(billingPackage.getSortOrder());

        // 第五步：保存更新后的套餐
        BillingPackage savedPackage = billingPackageRepository.save(existingPackage);
        log.info("成功更新计费套餐: ID={}, Name={}", savedPackage.getId(), savedPackage.getName());

        return savedPackage;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<BillingPackage> getPackageById(Long id) {
        return billingPackageRepository.findById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<BillingPackage> getPackageByName(String name) {
        return billingPackageRepository.findByName(name);
    }

    @Override
    @Transactional(readOnly = true)
    public List<BillingPackage> getActivePackages() {
        return billingPackageRepository.findActivePackagesOrderBySortOrder();
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<BillingPackage> getDefaultPackage() {
        return billingPackageRepository.findDefaultPackage();
    }

    @Override
    @Transactional(readOnly = true)
    public Page<BillingPackage> getPackages(Pageable pageable) {
        return billingPackageRepository.findAllByOrderBySortOrderAscIdAsc(pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<BillingPackage> getPackagesByStatus(Boolean isActive, Pageable pageable) {
        return billingPackageRepository.findByIsActiveOrderBySortOrderAscIdAsc(isActive, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<BillingPackage> searchPackagesByName(String name, Pageable pageable) {
        return billingPackageRepository.findByNameContainingIgnoreCaseOrderBySortOrderAscIdAsc(name, pageable);
    }

    @Override
    public boolean activatePackage(Long id) {
        log.info("激活套餐: ID={}", id);

        Optional<BillingPackage> packageOpt = billingPackageRepository.findById(id);
        if (packageOpt.isEmpty()) {
            log.warn("套餐不存在: ID={}", id);
            return false;
        }

        BillingPackage billingPackage = packageOpt.get();
        billingPackage.setIsActive(true);
        billingPackageRepository.save(billingPackage);

        log.info("成功激活套餐: ID={}, Name={}", id, billingPackage.getName());
        return true;
    }

    @Override
    public boolean deactivatePackage(Long id) {
        log.info("停用套餐: ID={}", id);

        Optional<BillingPackage> packageOpt = billingPackageRepository.findById(id);
        if (packageOpt.isEmpty()) {
            log.warn("套餐不存在: ID={}", id);
            return false;
        }

        BillingPackage billingPackage = packageOpt.get();

        // 检查是否有用户正在使用该套餐
        long usageCount = getPackageUsageCount(id);
        if (usageCount > 0) {
            log.warn("套餐正在被使用，不能停用: ID={}, 使用用户数={}", id, usageCount);
            return false;
        }

        billingPackage.setIsActive(false);
        billingPackageRepository.save(billingPackage);

        log.info("成功停用套餐: ID={}, Name={}", id, billingPackage.getName());
        return true;
    }

    @Override
    public boolean deletePackage(Long id) {
        log.info("删除套餐: ID={}", id);

        Optional<BillingPackage> packageOpt = billingPackageRepository.findById(id);
        if (packageOpt.isEmpty()) {
            log.warn("套餐不存在: ID={}", id);
            return false;
        }

        BillingPackage billingPackage = packageOpt.get();

        // 检查是否有用户正在使用该套餐
        long usageCount = getPackageUsageCount(id);
        if (usageCount > 0) {
            log.warn("套餐正在被使用，不能删除: ID={}, 使用用户数={}", id, usageCount);
            return false;
        }

        billingPackageRepository.deleteById(id);
        log.info("成功删除套餐: ID={}, Name={}", id, billingPackage.getName());
        return true;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isPackageNameExists(String name, Long excludeId) {
        if (excludeId == null) {
            return billingPackageRepository.existsByName(name);
        } else {
            return billingPackageRepository.existsByNameAndIdNot(name, excludeId);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<BillingPackage> getPackagesByPriceRange(BigDecimal minInputPrice, BigDecimal maxInputPrice,
            Boolean isActive) {
        return billingPackageRepository.findByInputTokenPriceRangeAndIsActive(minInputPrice, maxInputPrice, isActive);
    }

    @Override
    @Transactional(readOnly = true)
    public List<BillingPackage> getPackagesWithFreeTokens(Boolean isActive) {
        return billingPackageRepository.findPackagesWithFreeTokens(isActive);
    }

    @Override
    public boolean updatePackageSortOrder(Long id, Integer sortOrder) {
        log.info("更新套餐排序: ID={}, SortOrder={}", id, sortOrder);

        Optional<BillingPackage> packageOpt = billingPackageRepository.findById(id);
        if (packageOpt.isEmpty()) {
            log.warn("套餐不存在: ID={}", id);
            return false;
        }

        BillingPackage billingPackage = packageOpt.get();
        billingPackage.setSortOrder(sortOrder);
        billingPackageRepository.save(billingPackage);

        log.info("成功更新套餐排序: ID={}, SortOrder={}", id, sortOrder);
        return true;
    }

    @Override
    public int batchUpdatePackageStatus(List<Long> ids, Boolean isActive) {
        log.info("批量更新套餐状态: IDs={}, IsActive={}", ids, isActive);

        if (ids == null || ids.isEmpty()) {
            return 0;
        }

        int updatedCount = 0;
        for (Long id : ids) {
            int result = billingPackageRepository.updateActiveStatus(id, isActive);
            updatedCount += result;
        }

        log.info("批量更新套餐状态完成: 更新数量={}", updatedCount);
        return updatedCount;
    }

    @Override
    @Transactional(readOnly = true)
    public long getPackageUsageCount(Long packageId) {
        return userBalanceRepository.countByPackageId(packageId);
    }

    @Override
    @Transactional(readOnly = true)
    public String validatePackageConfiguration(BillingPackage billingPackage) {
        if (billingPackage == null) {
            return "套餐配置不能为空";
        }

        if (billingPackage.getName() == null || billingPackage.getName().trim().isEmpty()) {
            return "套餐名称不能为空";
        }

        if (billingPackage.getInputTokenPrice() == null
                || billingPackage.getInputTokenPrice().compareTo(BigDecimal.ZERO) < 0) {
            return "输入Token价格不能为空且不能为负数";
        }

        if (billingPackage.getOutputTokenPrice() == null
                || billingPackage.getOutputTokenPrice().compareTo(BigDecimal.ZERO) < 0) {
            return "输出Token价格不能为空且不能为负数";
        }

        if (billingPackage.getFreeTokens() == null || billingPackage.getFreeTokens() < 0) {
            return "免费Token数量不能为空且不能为负数";
        }

        if (billingPackage.getSortOrder() == null || billingPackage.getSortOrder() < 0) {
            return "排序权重不能为空且不能为负数";
        }

        return null; // 验证通过
    }

    @Override
    @Transactional(readOnly = true)
    public BigDecimal calculateTokenCost(Long packageId, Long inputTokens, Long outputTokens) {
        Optional<BillingPackage> packageOpt = billingPackageRepository.findById(packageId);
        if (packageOpt.isEmpty()) {
            throw new IllegalArgumentException("套餐不存在: " + packageId);
        }

        BillingPackage billingPackage = packageOpt.get();

        BigDecimal inputCost = billingPackage.getInputTokenPrice()
                .multiply(BigDecimal.valueOf(inputTokens != null ? inputTokens : 0));
        BigDecimal outputCost = billingPackage.getOutputTokenPrice()
                .multiply(BigDecimal.valueOf(outputTokens != null ? outputTokens : 0));

        return inputCost.add(outputCost);
    }

    @Override
    @Transactional(readOnly = true)
    public List<BillingPackage> getRecommendedPackages(Long userId) {
        // 简单实现：返回激活的套餐，按排序权重排序
        // 后续可以根据用户使用情况进行智能推荐
        log.info("获取用户推荐套餐: UserId={}", userId);
        return getActivePackages();
    }

    /**
     * 设置默认套餐
     * 
     * 管理员通过此方法设置指定套餐为系统默认套餐：
     * 1. 存在性检查：验证要设置的套餐是否存在且激活
     * 2. 取消现有默认：将当前默认套餐的sortOrder重置
     * 3. 设置新默认：将指定套餐的sortOrder设为0（表示默认）
     * 4. 数据持久化：保存更新后的套餐配置
     * 
     * 注意：在当前设计中，使用sortOrder=0来标识默认套餐
     * 
     * @param packageId 要设置为默认的套餐ID
     * @return 是否设置成功
     */
    @Override
    public boolean setDefaultPackage(Long packageId) {
        log.info("设置默认套餐: PackageId={}", packageId);

        // 第一步：检查套餐是否存在且激活
        Optional<BillingPackage> packageOpt = billingPackageRepository.findById(packageId);
        if (packageOpt.isEmpty()) {
            log.warn("套餐不存在: PackageId={}", packageId);
            return false;
        }

        BillingPackage billingPackage = packageOpt.get();
        if (!billingPackage.getIsActive()) {
            log.warn("套餐未激活，不能设置为默认: PackageId={}", packageId);
            return false;
        }

        // 第二步：取消现有的默认套餐（将sortOrder=0的套餐重置为正常值）
        Optional<BillingPackage> currentDefaultOpt = getDefaultPackage();
        if (currentDefaultOpt.isPresent()) {
            BillingPackage currentDefault = currentDefaultOpt.get();
            if (!currentDefault.getId().equals(packageId)) {
                currentDefault.setSortOrder(999); // 重置为普通排序值
                billingPackageRepository.save(currentDefault);
                log.info("取消原默认套餐: PackageId={}, Name={}",
                        currentDefault.getId(), currentDefault.getName());
            }
        }

        // 第三步：设置新的默认套餐
        billingPackage.setSortOrder(0); // sortOrder=0表示默认套餐
        billingPackageRepository.save(billingPackage);

        log.info("成功设置默认套餐: PackageId={}, Name={}", packageId, billingPackage.getName());
        return true;
    }
}