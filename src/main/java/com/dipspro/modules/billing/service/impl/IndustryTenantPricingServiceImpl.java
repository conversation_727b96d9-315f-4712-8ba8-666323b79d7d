package com.dipspro.modules.billing.service.impl;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dipspro.modules.billing.entity.IndustryTenantPricing;
import com.dipspro.modules.billing.repository.IndustryTenantPricingRepository;
import com.dipspro.modules.billing.service.IndustryTenantPricingService;
import com.dipspro.modules.tenant.dto.TenantResponseDto;
import com.dipspro.modules.tenant.service.TenantService;
import com.dipspro.security.util.SecurityUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 行业租户定价服务实现
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Transactional
@Slf4j
public class IndustryTenantPricingServiceImpl implements IndustryTenantPricingService {

    @Autowired
    private IndustryTenantPricingRepository pricingRepository;

    @Autowired
    private TenantService tenantService;

    @Override
    @Transactional(readOnly = true)
    public List<IndustryTenantPricing> getPricingByIndustryAndTenantType(String industryType, String tenantType) {
        log.debug("根据行业类型和租户类型获取套餐定价列表: industryType={}, tenantType={}", industryType, tenantType);
        // 返回所有套餐（包括不可用的），前端会根据isAvailable字段处理显示
        return pricingRepository.findByIndustryTypeAndTenantTypeOrderBySortOrderAsc(industryType, tenantType);
    }

    @Override
    @Transactional(readOnly = true)
    public List<IndustryTenantPricing> getCurrentUserPricing() {
        log.debug("根据当前用户的租户信息获取套餐定价列表");

        // 获取当前用户的租户ID
        Long tenantId = SecurityUtils.getCurrentTenantId();
        if (tenantId == null) {
            log.warn("当前用户没有租户信息，返回空列表");
            return List.of();
        }

        // 获取租户信息
        try {
            TenantResponseDto tenant = tenantService.getTenantById(tenantId);
            String industryType = tenant.getIndustryType();
            String tenantType = tenant.getTenantType();

            if (industryType == null || tenantType == null) {
                log.warn("租户缺少行业类型或租户类型信息: tenantId={}, industryType={}, tenantType={}",
                        tenantId, industryType, tenantType);
                return List.of();
            }

            return getPricingByIndustryAndTenantType(industryType, tenantType);
        } catch (Exception e) {
            log.error("获取租户信息失败: tenantId={}", tenantId, e);
            return List.of();
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<IndustryTenantPricing> getPricingById(Long id) {
        log.debug("根据ID获取定价信息: id={}", id);
        return pricingRepository.findById(id);
    }

    @Override
    public IndustryTenantPricing createPricing(IndustryTenantPricing pricing) {
        log.debug("创建定价信息: {}", pricing);
        return pricingRepository.save(pricing);
    }

    @Override
    public IndustryTenantPricing updatePricing(Long id, IndustryTenantPricing pricing) {
        log.debug("更新定价信息: id={}, pricing={}", id, pricing);

        Optional<IndustryTenantPricing> existingOpt = pricingRepository.findById(id);
        if (existingOpt.isEmpty()) {
            throw new IllegalArgumentException("定价信息不存在: id=" + id);
        }

        IndustryTenantPricing existing = existingOpt.get();

        // 更新字段
        if (pricing.getIndustryType() != null) {
            existing.setIndustryType(pricing.getIndustryType());
        }
        if (pricing.getTenantType() != null) {
            existing.setTenantType(pricing.getTenantType());
        }
        if (pricing.getPackageName() != null) {
            existing.setPackageName(pricing.getPackageName());
        }
        if (pricing.getPackageDescription() != null) {
            existing.setPackageDescription(pricing.getPackageDescription());
        }
        if (pricing.getPrice() != null) {
            existing.setPrice(pricing.getPrice());
        }
        if (pricing.getOriginalPrice() != null) {
            existing.setOriginalPrice(pricing.getOriginalPrice());
        }
        if (pricing.getDiscountText() != null) {
            existing.setDiscountText(pricing.getDiscountText());
        }
        if (pricing.getDiscountEndDate() != null) {
            existing.setDiscountEndDate(pricing.getDiscountEndDate());
        }
        if (pricing.getPeriod() != null) {
            existing.setPeriod(pricing.getPeriod());
        }
        if (pricing.getIsAvailable() != null) {
            existing.setIsAvailable(pricing.getIsAvailable());
        }
        if (pricing.getIsPopular() != null) {
            existing.setIsPopular(pricing.getIsPopular());
        }
        if (pricing.getSortOrder() != null) {
            existing.setSortOrder(pricing.getSortOrder());
        }
        if (pricing.getFeatures() != null) {
            existing.setFeatures(pricing.getFeatures());
        }

        return pricingRepository.save(existing);
    }

    @Override
    public void deletePricing(Long id) {
        log.debug("删除定价信息: id={}", id);
        pricingRepository.deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<IndustryTenantPricing> getAllAvailablePricing() {
        log.debug("获取所有可用的定价列表");
        return pricingRepository.findAllAvailablePricingOrderBySortOrder();
    }

    @Override
    @Transactional(readOnly = true)
    public List<IndustryTenantPricing> getAllPricing() {
        log.debug("获取所有定价列表（包括不可用的）");
        return pricingRepository.findAllPricingOrderBySortOrder();
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<IndustryTenantPricing> getPricingByPackageName(String packageName, String industryType,
            String tenantType) {
        log.debug("根据套餐名称获取定价信息: packageName={}, industryType={}, tenantType={}",
                packageName, industryType, tenantType);

        IndustryTenantPricing pricing = pricingRepository.findByPackageNameAndIndustryTypeAndTenantType(
                packageName, industryType, tenantType);
        return Optional.ofNullable(pricing);
    }
}