package com.dipspro.modules.billing.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.dipspro.modules.billing.service.TokenCalculatorService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.knuddels.jtokkit.Encodings;
import com.knuddels.jtokkit.api.Encoding;
import com.knuddels.jtokkit.api.EncodingRegistry;
import com.knuddels.jtokkit.api.EncodingType;

import lombok.extern.slf4j.Slf4j;

/**
 * Token计算服务实现类
 * 
 * 基于JTokkit库实现的Token计算服务，提供统一的Token计算功能。
 * 使用CL100K_BASE编码格式，与具体AI模型无关，确保计算结果的一致性。
 * 
 * 主要特性：
 * - 统一编码格式，与模型无关
 * - 内置缓存机制提升计算性能
 * - 异步计算避免阻塞主线程
 * - 批量计算提升处理效率
 * - 详细的统计信息和监控
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Slf4j
public class TokenCalculatorServiceImpl implements TokenCalculatorService {

    private final EncodingRegistry encodingRegistry;
    private final ObjectMapper objectMapper;
    private final Executor tokenCalculatorExecutor;

    // Token计算结果缓存，缓存1小时
    private final Cache<String, Long> tokenCache;

    // 统计信息
    private final AtomicLong totalCalculations = new AtomicLong(0);
    private final AtomicLong cacheHits = new AtomicLong(0);
    private final AtomicLong totalCalculationTime = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);

    /**
     * 构造函数 - 初始化Token计算服务
     * 
     * 初始化JTokkit编码注册表、缓存、线程池等核心组件
     */
    public TokenCalculatorServiceImpl() {
        this.encodingRegistry = Encodings.newDefaultEncodingRegistry();
        this.objectMapper = new ObjectMapper();
        this.tokenCalculatorExecutor = Executors.newFixedThreadPool(4, r -> {
            Thread t = new Thread(r, "token-calculator-");
            t.setDaemon(true);
            return t;
        });

        // 初始化缓存，1小时过期，最多缓存10000条记录
        this.tokenCache = CacheBuilder.newBuilder()
                .maximumSize(10000)
                .expireAfterWrite(1, TimeUnit.HOURS)
                .recordStats()
                .build();
    }

    /**
     * 计算输入Token数量
     * 
     * 精确计算用户输入内容的Token数量，用于计费和统计。
     * 使用统一的编码格式进行计算，与具体AI模型无关。
     * 
     * @param content 输入内容，不能为null或空字符串
     * @param model   AI模型名称，保留参数兼容性但不影响计算逻辑
     * @return Token数量，如果输入为空则返回0
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    @Override
    public long calculateInputTokens(String content, String model) {
        return calculateTokensInternal(content, "INPUT");
    }

    /**
     * 计算输出Token数量
     * 
     * 精确计算AI模型输出内容的Token数量，用于计费和统计。
     * 使用统一的编码格式进行计算，与具体AI模型无关。
     * 
     * @param content 输出内容，AI模型生成的响应文本
     * @param model   AI模型名称，保留参数兼容性但不影响计算逻辑
     * @return Token数量，精确到个位数
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    @Override
    public long calculateOutputTokens(String content, String model) {
        return calculateTokensInternal(content, "OUTPUT");
    }

    /**
     * 计算思维链Token数量
     * 
     * 计算AI模型思维链（intermediate steps）的Token数量。
     * 思维链是AI模型在生成响应过程中的中间推理步骤，通常以JSON格式存储。
     * 
     * @param thoughtChain 思维链内容，通常为JSON格式的推理步骤
     * @param model        AI模型名称，保留参数兼容性但不影响计算逻辑
     * @return Token数量，如果思维链为空则返回0
     * @throws IllegalArgumentException 当参数格式错误时抛出
     */
    @Override
    public long calculateThoughtChainTokens(String thoughtChain, String model) {
        if (thoughtChain == null || thoughtChain.trim().isEmpty()) {
            return 0L;
        }

        try {
            // 尝试解析JSON格式的思维链内容
            JsonNode jsonNode = objectMapper.readTree(thoughtChain);
            String textContent = extractTextFromJson(jsonNode);
            return calculateTokensInternal(textContent, "THOUGHT_CHAIN");
        } catch (Exception e) {
            log.warn("思维链内容不是有效的JSON格式，直接计算: {}", e.getMessage());
            // 如果不是JSON格式，直接计算原始内容
            return calculateTokensInternal(thoughtChain, "THOUGHT_CHAIN");
        }
    }

    /**
     * 批量计算Token数量
     * 
     * 异步批量计算多个文本内容的Token数量，提升计算效率。
     * 适用于需要同时计算大量文本Token的场景，如批量数据处理、历史数据迁移等。
     * 
     * @param requests 计算请求列表，包含内容、模型、计算类型等信息
     * @return 异步计算结果，包含每个请求对应的Token数量
     * @throws IllegalArgumentException 当请求列表为空或包含无效请求时抛出
     */
    @Override
    public CompletableFuture<List<TokenCalculationResult>> calculateTokensBatch(
            List<TokenCalculationRequest> requests) {
        if (requests == null || requests.isEmpty()) {
            throw new IllegalArgumentException("批量计算请求列表不能为空");
        }

        return CompletableFuture.supplyAsync(() -> {
            List<TokenCalculationResult> results = new ArrayList<>();

            for (TokenCalculationRequest request : requests) {
                try {
                    long startTime = System.currentTimeMillis();
                    String cacheKey = generateCacheKey(request.getContent(), request.getType());

                    Long cachedResult = tokenCache.getIfPresent(cacheKey);
                    boolean fromCache = cachedResult != null;
                    long tokenCount;

                    if (fromCache) {
                        tokenCount = cachedResult;
                        cacheHits.incrementAndGet();
                    } else {
                        tokenCount = calculateTokensInternal(request.getContent(), request.getType());
                        tokenCache.put(cacheKey, tokenCount);
                    }

                    long calculationTime = System.currentTimeMillis() - startTime;

                    TokenCalculationResult result = new TokenCalculationResult(
                            request.getContent(), request.getModel(), request.getType(),
                            tokenCount, fromCache, calculationTime);
                    results.add(result);

                } catch (Exception e) {
                    log.error("批量计算Token失败: {}", e.getMessage(), e);
                    errorCount.incrementAndGet();
                    // 添加错误结果
                    TokenCalculationResult errorResult = new TokenCalculationResult(
                            request.getContent(), request.getModel(), request.getType(),
                            0L, false, 0L);
                    results.add(errorResult);
                }
            }

            return results;
        }, tokenCalculatorExecutor);
    }

    /**
     * 异步计算Token数量
     * 
     * 异步计算指定内容的Token数量，不阻塞调用线程。
     * 适用于大文本内容的Token计算，避免阻塞主线程影响用户体验。
     * 
     * @param content  待计算的文本内容
     * @param model    AI模型名称
     * @param callback 计算完成后的回调函数，接收Token数量作为参数
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    @Override
    @Async
    public void calculateTokensAsync(String content, String model, Consumer<Long> callback) {
        try {
            long tokenCount = calculateTokensInternal(content, "ASYNC");
            callback.accept(tokenCount);
        } catch (Exception e) {
            log.error("异步计算Token失败: {}", e.getMessage(), e);
            errorCount.incrementAndGet();
            callback.accept(0L);
        }
    }

    /**
     * 预估Token数量
     * 
     * 快速预估文本内容的Token数量，用于用户界面显示和余额检查。
     * 相比精确计算，预估方法速度更快但精度稍低，适用于实时显示场景。
     * 
     * @param content 待预估的文本内容
     * @param model   AI模型名称，保留参数兼容性但不影响计算逻辑
     * @return 预估的Token数量，可能存在±10%的误差
     */
    @Override
    public long estimateTokens(String content, String model) {
        if (content == null || content.trim().isEmpty()) {
            return 0L;
        }

        // 快速预估算法：基于字符数的统计模型
        int charCount = content.length();

        // 不同语言的Token密度不同
        double tokensPerChar;
        if (containsChinese(content)) {
            // 中文字符通常1个字符约等于1个Token
            tokensPerChar = 1.0;
        } else {
            // 英文字符通常4个字符约等于1个Token
            tokensPerChar = 0.25;
        }

        long estimatedTokens = Math.round(charCount * tokensPerChar);

        log.debug("Token预估: Content长度={}, 预估Token数={}", charCount, estimatedTokens);

        return estimatedTokens;
    }

    /**
     * 获取Token计算统计信息
     * 
     * 获取Token计算服务的运行统计信息，用于监控和性能分析。
     * 包括计算次数、缓存命中率、平均计算时间等关键指标。
     * 
     * @return 包含各项统计指标的Map对象
     */
    @Override
    public Map<String, Object> getCalculationStatistics() {
        Map<String, Object> stats = new HashMap<>();

        long totalCalc = totalCalculations.get();
        long cacheHit = cacheHits.get();
        long totalTime = totalCalculationTime.get();

        stats.put("totalCalculations", totalCalc);
        stats.put("cacheHits", cacheHit);
        stats.put("cacheHitRate", totalCalc > 0 ? (double) cacheHit / totalCalc : 0.0);
        stats.put("averageCalculationTimeMs", totalCalc > 0 ? (double) totalTime / totalCalc : 0.0);
        stats.put("errorCount", errorCount.get());
        stats.put("cacheSize", tokenCache.size());
        stats.put("cacheStats", tokenCache.stats());

        return stats;
    }

    /**
     * 清除Token计算缓存
     * 
     * 清除所有Token计算缓存，释放内存空间。
     * 适用于内存清理、缓存更新、性能优化等场景。
     * 
     * @param model 模型名称，保留参数兼容性但不影响功能
     * @return 清除的缓存条目数量
     */
    @Override
    public int clearCalculationCache(String model) {
        long sizeBefore = tokenCache.size();
        tokenCache.invalidateAll();
        log.info("已清除所有Token计算缓存，清除条目数: {}", sizeBefore);
        return (int) sizeBefore;
    }

    /**
     * 验证模型是否支持
     * 
     * Token计算与模型无关，所有模型都支持。
     * 保留此方法仅为API兼容性。
     * 
     * @param model 模型名称
     * @return 始终返回true
     */
    @Override
    public boolean isSupportedModel(String model) {
        return true; // Token计算与模型无关
    }

    /**
     * 获取支持的模型列表
     * 
     * 由于Token计算与模型无关，返回空列表。
     * 保留此方法仅为API兼容性。
     * 
     * @return 空的模型列表
     */
    @Override
    public List<String> getSupportedModels() {
        return new ArrayList<>(); // Token计算与模型无关
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 内部Token计算方法
     * 
     * 所有Token计算的核心实现，包含缓存逻辑和统计记录
     * 使用统一的编码格式，与具体AI模型无关
     */
    private long calculateTokensInternal(String content, String type) {
        long startTime = System.currentTimeMillis();

        try {
            // 参数验证
            if (content == null || content.trim().isEmpty()) {
                return 0L;
            }

            // 检查缓存
            String cacheKey = generateCacheKey(content, type);
            Long cachedResult = tokenCache.getIfPresent(cacheKey);

            if (cachedResult != null) {
                cacheHits.incrementAndGet();
                totalCalculations.incrementAndGet();
                return cachedResult;
            }

            // 执行实际的Token计算 - 使用统一编码
            Encoding encoding = encodingRegistry.getEncoding(EncodingType.CL100K_BASE);
            var tokens = encoding.encode(content);
            long tokenCount = tokens.size();

            // 缓存结果
            tokenCache.put(cacheKey, tokenCount);

            // 更新统计信息
            totalCalculations.incrementAndGet();
            long calculationTime = System.currentTimeMillis() - startTime;
            totalCalculationTime.addAndGet(calculationTime);

            log.debug("Token计算完成: Content长度={}, TokenCount={}, Type={}, Time={}ms",
                    content.length(), tokenCount, type, calculationTime);

            return tokenCount;

        } catch (Exception e) {
            errorCount.incrementAndGet();
            log.error("Token计算失败: Type={}, Content长度={}, Error={}",
                    type, content != null ? content.length() : 0, e.getMessage(), e);
            throw new RuntimeException("Token计算失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成缓存键
     */
    private String generateCacheKey(String content, String type) {
        return String.format("%s|%s|%d", type,
                Integer.toHexString(content.hashCode()), content.length());
    }

    /**
     * 从JSON中提取文本内容
     */
    private String extractTextFromJson(JsonNode jsonNode) {
        StringBuilder textBuilder = new StringBuilder();
        extractTextRecursively(jsonNode, textBuilder);
        return textBuilder.toString();
    }

    /**
     * 递归提取JSON中的文本内容
     */
    private void extractTextRecursively(JsonNode node, StringBuilder textBuilder) {
        if (node.isTextual()) {
            textBuilder.append(node.asText()).append(" ");
        } else if (node.isArray()) {
            for (JsonNode item : node) {
                extractTextRecursively(item, textBuilder);
            }
        } else if (node.isObject()) {
            for (JsonNode value : node) {
                extractTextRecursively(value, textBuilder);
            }
        }
    }

    /**
     * 检查内容是否包含中文字符
     */
    private boolean containsChinese(String content) {
        return content.chars()
                .anyMatch(c -> Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS ||
                        Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A ||
                        Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B);
    }

}