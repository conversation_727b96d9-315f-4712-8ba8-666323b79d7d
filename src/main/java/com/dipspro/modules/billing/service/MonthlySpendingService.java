package com.dipspro.modules.billing.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import com.dipspro.modules.billing.entity.UserMonthlySpending;

/**
 * 月消费跟踪服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface MonthlySpendingService {

    /**
     * 更新用户月消费金额
     * 
     * @param userId    用户ID
     * @param packageId 套餐ID
     * @param amount    消费金额
     * @param monthYear 月份 (格式: 2024-01)
     * @return 更新后的月消费记录
     */
    UserMonthlySpending updateMonthlySpending(Long userId, Long packageId, BigDecimal amount, String monthYear);

    /**
     * 获取用户当月消费记录
     * 
     * @param userId    用户ID
     * @param monthYear 月份 (格式: 2024-01)
     * @return 月消费记录
     */
    Optional<UserMonthlySpending> getCurrentMonthSpending(Long userId, String monthYear);

    /**
     * 检查用户当月是否达标
     * 
     * @param userId    用户ID
     * @param monthYear 月份 (格式: 2024-01)
     * @return 是否达标
     */
    boolean isQualifiedForMonth(Long userId, String monthYear);

    /**
     * 获取用户历史月消费记录
     * 
     * @param userId 用户ID
     * @param limit  记录数量限制
     * @return 历史记录列表
     */
    List<UserMonthlySpending> getUserMonthlyHistory(Long userId, int limit);

    /**
     * 初始化或重置月消费记录
     * 
     * @param userId    用户ID
     * @param packageId 套餐ID
     * @param monthYear 月份 (格式: 2024-01)
     * @return 初始化的记录
     */
    UserMonthlySpending initializeMonthlyRecord(Long userId, Long packageId, String monthYear);

    /**
     * 重新计算所有用户的达标状态
     * 
     * @param monthYear 月份 (格式: 2024-01)
     * @return 更新的记录数量
     */
    int recalculateQualificationStatus(String monthYear);

    /**
     * 处理套餐切换对月消费的影响
     * 
     * @param userId       用户ID
     * @param oldPackageId 旧套餐ID
     * @param newPackageId 新套餐ID
     * @param monthYear    月份 (格式: 2024-01)
     * @return 更新后的记录
     */
    UserMonthlySpending handlePackageSwitch(Long userId, Long oldPackageId, Long newPackageId, String monthYear);

    /**
     * 获取当前月份字符串
     * 
     * @return 当前月份 (格式: 2024-01)
     */
    String getCurrentMonthYear();

    /**
     * 清理历史数据
     * 
     * @param keepMonths 保留月数
     * @return 删除的记录数量
     */
    int cleanupHistoryData(int keepMonths);

    /**
     * 获取月消费统计报告
     * 
     * @param monthYear 月份 (格式: 2024-01)
     * @return 统计报告数据
     */
    List<Object[]> getMonthlySpendingReport(String monthYear);

    /**
     * 批量更新用户月消费
     * 
     * @param updates 批量更新数据
     * @return 处理结果
     */
    int batchUpdateMonthlySpending(List<UserMonthlySpending> updates);

    // ==================== 管理端功能 ====================

    /**
     * 设置套餐最低消费要求
     * 
     * @param packageId    套餐ID
     * @param minimumSpend 最低消费金额
     */
    void setPackageMinimumSpend(Long packageId, BigDecimal minimumSpend);

    /**
     * 获取用户消费统计
     * 
     * @param monthYear 月份 (格式: 2024-01)
     * @return 消费统计信息
     */
    com.dipspro.modules.billing.dto.SpendingStatsDto getSpendingStats(String monthYear);

    /**
     * 获取用户月度资格状态
     * 
     * @param monthYear 月份 (格式: 2024-01)
     * @param qualified 达标状态筛选
     * @param packageId 套餐ID筛选
     * @param userId    用户ID筛选
     * @param pageable  分页参数
     * @return 用户资格状态分页结果
     */
    org.springframework.data.domain.Page<com.dipspro.modules.billing.dto.UserQualificationDto> getUserMonthlyQualification(
            String monthYear, Boolean qualified, Long packageId, Long userId,
            org.springframework.data.domain.Pageable pageable);
}