package com.dipspro.modules.billing.dto;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 充值预设金额DTO
 * 
 * 用于返回系统配置的预设充值金额信息，包含金额、
 * 赠送奖励和排序等信息。
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class RechargePresetDto {

    /**
     * 预设ID
     */
    private Long id;

    /**
     * 充值金额
     */
    private BigDecimal amount;

    /**
     * 赠送金额
     */
    private BigDecimal bonusAmount;

    /**
     * 标题（如"推荐充值"）
     */
    private String title;

    /**
     * 描述（如"充值100送20"）
     */
    private String description;

    /**
     * 是否推荐
     */
    private Boolean recommended;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 标签（如"热门"、"优惠"）
     */
    private String tag;

    /**
     * 折扣率（赠送金额比例）
     */
    private BigDecimal discountRate;

    /**
     * 获取实际到账金额（充值金额+赠送金额）
     * 
     * @return 实际到账金额
     */
    public BigDecimal getTotalAmount() {
        BigDecimal base = amount != null ? amount : BigDecimal.ZERO;
        BigDecimal bonus = bonusAmount != null ? bonusAmount : BigDecimal.ZERO;
        return base.add(bonus);
    }

    /**
     * 获取格式化的充值金额
     * 
     * @return 格式化的金额字符串
     */
    public String getFormattedAmount() {
        if (amount == null) {
            return "¥0.00";
        }
        return "¥" + amount.toString();
    }

    /**
     * 获取格式化的赠送金额
     * 
     * @return 格式化的赠送金额字符串
     */
    public String getFormattedBonusAmount() {
        if (bonusAmount == null || bonusAmount.compareTo(BigDecimal.ZERO) == 0) {
            return "";
        }
        return "送¥" + bonusAmount.toString();
    }

    /**
     * 获取格式化的总金额
     * 
     * @return 格式化的总金额字符串
     */
    public String getFormattedTotalAmount() {
        return "¥" + getTotalAmount().toString();
    }

    /**
     * 检查是否有赠送
     * 
     * @return 是否有赠送
     */
    public boolean hasBonus() {
        return bonusAmount != null && bonusAmount.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 检查是否为推荐项
     * 
     * @return 是否推荐
     */
    public boolean isRecommended() {
        return Boolean.TRUE.equals(recommended);
    }

    /**
     * 检查是否启用
     * 
     * @return 是否启用
     */
    public boolean isEnabled() {
        return Boolean.TRUE.equals(enabled);
    }

    /**
     * 获取折扣百分比文本
     * 
     * @return 折扣百分比文本（如"20%"）
     */
    public String getDiscountPercentage() {
        if (discountRate == null || amount == null || amount.compareTo(BigDecimal.ZERO) == 0) {
            return "";
        }

        BigDecimal percentage = discountRate.multiply(new BigDecimal("100"));
        return percentage.intValue() + "%";
    }

    /**
     * 获取充值描述文本
     * 
     * @return 充值描述文本
     */
    public String getRechargeDescription() {
        if (description != null && !description.trim().isEmpty()) {
            return description;
        }

        if (hasBonus()) {
            return "充值" + getFormattedAmount() + "，" + getFormattedBonusAmount();
        }

        return "充值" + getFormattedAmount();
    }
}