package com.dipspro.modules.billing.dto.analytics;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 收入分层统计DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class RevenueLayerStatsDto {

    /**
     * 统计月份
     */
    private String month;

    /**
     * 总收入
     */
    private BigDecimal totalRevenue;

    /**
     * 总用户数
     */
    private Long totalUsers;

    /**
     * 平均每用户收入(ARPU)
     */
    private BigDecimal averageRevenuePerUser;

    /**
     * 平均收入
     */
    private BigDecimal averageRevenue;

    /**
     * 收入分层详情（新增）
     */
    private List<RevenueLayerDetail> layerDetails;

    /**
     * 收入分层
     */
    private List<RevenueLayer> revenueLayers;

    /**
     * 套餐收入分布
     */
    private List<PackageRevenueDetail> packageRevenueDetails;

    /**
     * 收入趋势分析
     */
    private RevenueTrendAnalysis revenueTrend;

    /**
     * 分析时间
     */
    private LocalDateTime analysisTime;

    public RevenueLayerStatsDto() {
    }

    public RevenueLayerStatsDto(String month, BigDecimal totalRevenue, Long totalUsers,
            BigDecimal averageRevenuePerUser, BigDecimal averageRevenue, List<RevenueLayerDetail> layerDetails,
            List<RevenueLayer> revenueLayers, List<PackageRevenueDetail> packageRevenueDetails,
            RevenueTrendAnalysis revenueTrend, LocalDateTime analysisTime) {
        this.month = month;
        this.totalRevenue = totalRevenue;
        this.totalUsers = totalUsers;
        this.averageRevenuePerUser = averageRevenuePerUser;
        this.averageRevenue = averageRevenue;
        this.layerDetails = layerDetails;
        this.revenueLayers = revenueLayers;
        this.packageRevenueDetails = packageRevenueDetails;
        this.revenueTrend = revenueTrend;
        this.analysisTime = analysisTime;
    }

    // Getters and setters
    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public BigDecimal getTotalRevenue() {
        return totalRevenue;
    }

    public void setTotalRevenue(BigDecimal totalRevenue) {
        this.totalRevenue = totalRevenue;
    }

    public Long getTotalUsers() {
        return totalUsers;
    }

    public void setTotalUsers(Long totalUsers) {
        this.totalUsers = totalUsers;
    }

    public BigDecimal getAverageRevenuePerUser() {
        return averageRevenuePerUser;
    }

    public void setAverageRevenuePerUser(BigDecimal averageRevenuePerUser) {
        this.averageRevenuePerUser = averageRevenuePerUser;
    }

    public BigDecimal getAverageRevenue() {
        return averageRevenue;
    }

    public void setAverageRevenue(BigDecimal averageRevenue) {
        this.averageRevenue = averageRevenue;
    }

    public List<RevenueLayerDetail> getLayerDetails() {
        return layerDetails;
    }

    public void setLayerDetails(List<RevenueLayerDetail> layerDetails) {
        this.layerDetails = layerDetails;
    }

    public List<RevenueLayer> getRevenueLayers() {
        return revenueLayers;
    }

    public void setRevenueLayers(List<RevenueLayer> revenueLayers) {
        this.revenueLayers = revenueLayers;
    }

    public List<PackageRevenueDetail> getPackageRevenueDetails() {
        return packageRevenueDetails;
    }

    public void setPackageRevenueDetails(List<PackageRevenueDetail> packageRevenueDetails) {
        this.packageRevenueDetails = packageRevenueDetails;
    }

    public RevenueTrendAnalysis getRevenueTrend() {
        return revenueTrend;
    }

    public void setRevenueTrend(RevenueTrendAnalysis revenueTrend) {
        this.revenueTrend = revenueTrend;
    }

    public LocalDateTime getAnalysisTime() {
        return analysisTime;
    }

    public void setAnalysisTime(LocalDateTime analysisTime) {
        this.analysisTime = analysisTime;
    }

    /**
     * 收入分层详情（新增的内部类）
     */
    public static class RevenueLayerDetail {

        /**
         * 分层名称
         */
        private String layerName;

        /**
         * 最小金额
         */
        private BigDecimal minAmount;

        /**
         * 最大金额
         */
        private BigDecimal maxAmount;

        /**
         * 用户数
         */
        private Long userCount;

        /**
         * 总收入
         */
        private BigDecimal totalRevenue;

        /**
         * 平均收入
         */
        private BigDecimal averageRevenue;

        /**
         * 收入百分比
         */
        private BigDecimal revenuePercentage;

        /**
         * 用户百分比
         */
        private BigDecimal userPercentage;

        public RevenueLayerDetail() {
        }

        public RevenueLayerDetail(String layerName, BigDecimal minAmount, BigDecimal maxAmount, Long userCount,
                BigDecimal totalRevenue, BigDecimal averageRevenue, BigDecimal revenuePercentage,
                BigDecimal userPercentage) {
            this.layerName = layerName;
            this.minAmount = minAmount;
            this.maxAmount = maxAmount;
            this.userCount = userCount;
            this.totalRevenue = totalRevenue;
            this.averageRevenue = averageRevenue;
            this.revenuePercentage = revenuePercentage;
            this.userPercentage = userPercentage;
        }

        // Getters and setters
        public String getLayerName() {
            return layerName;
        }

        public void setLayerName(String layerName) {
            this.layerName = layerName;
        }

        public BigDecimal getMinAmount() {
            return minAmount;
        }

        public void setMinAmount(BigDecimal minAmount) {
            this.minAmount = minAmount;
        }

        public BigDecimal getMaxAmount() {
            return maxAmount;
        }

        public void setMaxAmount(BigDecimal maxAmount) {
            this.maxAmount = maxAmount;
        }

        public Long getUserCount() {
            return userCount;
        }

        public void setUserCount(Long userCount) {
            this.userCount = userCount;
        }

        public BigDecimal getTotalRevenue() {
            return totalRevenue;
        }

        public void setTotalRevenue(BigDecimal totalRevenue) {
            this.totalRevenue = totalRevenue;
        }

        public BigDecimal getAverageRevenue() {
            return averageRevenue;
        }

        public void setAverageRevenue(BigDecimal averageRevenue) {
            this.averageRevenue = averageRevenue;
        }

        public BigDecimal getRevenuePercentage() {
            return revenuePercentage;
        }

        public void setRevenuePercentage(BigDecimal revenuePercentage) {
            this.revenuePercentage = revenuePercentage;
        }

        public BigDecimal getUserPercentage() {
            return userPercentage;
        }

        public void setUserPercentage(BigDecimal userPercentage) {
            this.userPercentage = userPercentage;
        }
    }

    /**
     * 收入分层
     */
    public static class RevenueLayer {

        private String layerName;
        private BigDecimal minAmount;
        private BigDecimal maxAmount;
        private Long userCount;
        private BigDecimal totalRevenue;
        private BigDecimal averageRevenue;
        private BigDecimal revenuePercentage;
        private BigDecimal userPercentage;

        public RevenueLayer() {
        }

        public RevenueLayer(String layerName, BigDecimal minAmount, BigDecimal maxAmount, Long userCount,
                BigDecimal totalRevenue, BigDecimal averageRevenue, BigDecimal revenuePercentage,
                BigDecimal userPercentage) {
            this.layerName = layerName;
            this.minAmount = minAmount;
            this.maxAmount = maxAmount;
            this.userCount = userCount;
            this.totalRevenue = totalRevenue;
            this.averageRevenue = averageRevenue;
            this.revenuePercentage = revenuePercentage;
            this.userPercentage = userPercentage;
        }

        // Getters and setters
        public String getLayerName() {
            return layerName;
        }

        public void setLayerName(String layerName) {
            this.layerName = layerName;
        }

        public BigDecimal getMinAmount() {
            return minAmount;
        }

        public void setMinAmount(BigDecimal minAmount) {
            this.minAmount = minAmount;
        }

        public BigDecimal getMaxAmount() {
            return maxAmount;
        }

        public void setMaxAmount(BigDecimal maxAmount) {
            this.maxAmount = maxAmount;
        }

        public Long getUserCount() {
            return userCount;
        }

        public void setUserCount(Long userCount) {
            this.userCount = userCount;
        }

        public BigDecimal getTotalRevenue() {
            return totalRevenue;
        }

        public void setTotalRevenue(BigDecimal totalRevenue) {
            this.totalRevenue = totalRevenue;
        }

        public BigDecimal getAverageRevenue() {
            return averageRevenue;
        }

        public void setAverageRevenue(BigDecimal averageRevenue) {
            this.averageRevenue = averageRevenue;
        }

        public BigDecimal getRevenuePercentage() {
            return revenuePercentage;
        }

        public void setRevenuePercentage(BigDecimal revenuePercentage) {
            this.revenuePercentage = revenuePercentage;
        }

        public BigDecimal getUserPercentage() {
            return userPercentage;
        }

        public void setUserPercentage(BigDecimal userPercentage) {
            this.userPercentage = userPercentage;
        }
    }

    /**
     * 套餐收入详情
     */
    public static class PackageRevenueDetail {

        private Long packageId;
        private String packageName;
        private Long userCount;
        private BigDecimal totalRevenue;
        private BigDecimal averageRevenuePerUser;
        private BigDecimal revenuePercentage;

        public PackageRevenueDetail() {
        }

        public PackageRevenueDetail(Long packageId, String packageName, Long userCount, BigDecimal totalRevenue,
                BigDecimal averageRevenuePerUser, BigDecimal revenuePercentage) {
            this.packageId = packageId;
            this.packageName = packageName;
            this.userCount = userCount;
            this.totalRevenue = totalRevenue;
            this.averageRevenuePerUser = averageRevenuePerUser;
            this.revenuePercentage = revenuePercentage;
        }

        // Getters and setters
        public Long getPackageId() {
            return packageId;
        }

        public void setPackageId(Long packageId) {
            this.packageId = packageId;
        }

        public String getPackageName() {
            return packageName;
        }

        public void setPackageName(String packageName) {
            this.packageName = packageName;
        }

        public Long getUserCount() {
            return userCount;
        }

        public void setUserCount(Long userCount) {
            this.userCount = userCount;
        }

        public BigDecimal getTotalRevenue() {
            return totalRevenue;
        }

        public void setTotalRevenue(BigDecimal totalRevenue) {
            this.totalRevenue = totalRevenue;
        }

        public BigDecimal getAverageRevenuePerUser() {
            return averageRevenuePerUser;
        }

        public void setAverageRevenuePerUser(BigDecimal averageRevenuePerUser) {
            this.averageRevenuePerUser = averageRevenuePerUser;
        }

        public BigDecimal getRevenuePercentage() {
            return revenuePercentage;
        }

        public void setRevenuePercentage(BigDecimal revenuePercentage) {
            this.revenuePercentage = revenuePercentage;
        }
    }

    /**
     * 收入趋势分析
     */
    public static class RevenueTrendAnalysis {

        private BigDecimal monthOverMonthGrowth;
        private BigDecimal yearOverYearGrowth;
        private String trend;
        private List<String> insights;

        public RevenueTrendAnalysis() {
        }

        public RevenueTrendAnalysis(BigDecimal monthOverMonthGrowth, BigDecimal yearOverYearGrowth, String trend,
                List<String> insights) {
            this.monthOverMonthGrowth = monthOverMonthGrowth;
            this.yearOverYearGrowth = yearOverYearGrowth;
            this.trend = trend;
            this.insights = insights;
        }

        // Getters and setters
        public BigDecimal getMonthOverMonthGrowth() {
            return monthOverMonthGrowth;
        }

        public void setMonthOverMonthGrowth(BigDecimal monthOverMonthGrowth) {
            this.monthOverMonthGrowth = monthOverMonthGrowth;
        }

        public BigDecimal getYearOverYearGrowth() {
            return yearOverYearGrowth;
        }

        public void setYearOverYearGrowth(BigDecimal yearOverYearGrowth) {
            this.yearOverYearGrowth = yearOverYearGrowth;
        }

        public String getTrend() {
            return trend;
        }

        public void setTrend(String trend) {
            this.trend = trend;
        }

        public List<String> getInsights() {
            return insights;
        }

        public void setInsights(List<String> insights) {
            this.insights = insights;
        }
    }
}