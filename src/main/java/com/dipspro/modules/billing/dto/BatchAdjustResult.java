package com.dipspro.modules.billing.dto;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 批量调整结果DTO
 * 
 * 用于返回批量操作的结果统计信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class BatchAdjustResult {

    /**
     * 成功处理的用户数量
     */
    private Integer successCount;

    /**
     * 失败处理的用户数量
     */
    private Integer failCount;

    /**
     * 总用户数量
     */
    private Integer totalCount;

    /**
     * 成功处理的用户ID列表
     */
    private List<Long> successUserIds;

    /**
     * 失败处理的用户ID列表
     */
    private List<Long> failUserIds;

    /**
     * 错误信息列表
     */
    private List<String> errorMessages;

    /**
     * 批量操作摘要
     */
    private String summary;

    /**
     * 获取成功率
     * 
     * @return 成功率百分比
     */
    public double getSuccessRate() {
        if (totalCount == null || totalCount == 0) {
            return 0.0;
        }
        return (double) successCount / totalCount * 100;
    }

    /**
     * 检查是否全部成功
     * 
     * @return 是否全部成功
     */
    public boolean isAllSuccess() {
        return failCount != null && failCount == 0;
    }

    /**
     * 检查是否全部失败
     * 
     * @return 是否全部失败
     */
    public boolean isAllFailed() {
        return successCount != null && successCount == 0;
    }

    /**
     * 获取操作状态描述
     * 
     * @return 状态描述
     */
    public String getStatusDescription() {
        if (isAllSuccess()) {
            return "全部成功";
        } else if (isAllFailed()) {
            return "全部失败";
        } else {
            return "部分成功";
        }
    }
}