package com.dipspro.modules.billing.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import com.dipspro.constant.BillingConstants;

/**
 * 使用记录DTO
 * 
 * 用于返回用户的Token使用记录信息，包含对话详情、
 * Token消费、费用计算等完整信息。
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class UsageRecordDto {

    /**
     * 记录ID
     */
    private Long id;

    /**
     * 消息ID
     */
    private UUID messageId;

    /**
     * 对话ID
     */
    private UUID conversationId;

    /**
     * 输入Token数量
     */
    private Long inputTokens;

    /**
     * 输出Token数量
     */
    private Long outputTokens;

    /**
     * 思维链Token数量
     */
    private Long thoughtChainTokens;

    /**
     * 总Token数量
     */
    private Long totalTokens;

    /**
     * 输入费用
     */
    private BigDecimal inputCost;

    /**
     * 输出费用
     */
    private BigDecimal outputCost;

    /**
     * 总费用
     */
    private BigDecimal totalCost;

    /**
     * 状态：SUCCESS, FAILED, PENDING
     */
    private String status;

    /**
     * 计费状态：BILLED, UNBILLED, REFUNDED
     */
    private String billingStatus;

    /**
     * 计费类型：TOKEN_BASED, FAILED, MANUAL, NO_CHARGE_*
     */
    private String billingType;
    /**
     * 扣除来源：FREE_TOKENS_ONLY, GIFT_BALANCE_ONLY, RECHARGED_BALANCE_ONLY, MIXED_DEDUCTION
     */
    private String deductionSource;

    /**
     * 使用的套餐ID
     */
    private Long packageId;

    /**
     * 计费时输入Token单价
     */
    private BigDecimal inputTokenPrice;

    /**
     * 计费时输出Token单价
     */
    private BigDecimal outputTokenPrice;

    /**
     * 本次计费使用的可视化折扣系数
     */
    private BigDecimal visualDiscountFactor;

    /**
     * 是否已申诉
     */
    private Boolean isAppealed;

    /**
     * 申诉状态
     */
    private String appealStatus;

    /**
     * 请求开始时间
     */
    private LocalDateTime requestTime;

    /**
     * 响应结束时间
     */
    private LocalDateTime responseTime;

    /**
     * 处理时长（毫秒）
     */
    private Long durationMs;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 检查是否计费成功
     * 
     * @return 是否计费成功
     */
    public boolean isBilledSuccessfully() {
        return "SUCCESS".equals(status) && "BILLED".equals(billingStatus);
    }

    /**
     * 检查是否可以申诉
     * 
     * @return 是否可以申诉
     */
    public boolean canAppeal() {
        return isBilledSuccessfully() && !Boolean.TRUE.equals(isAppealed);
    }

    /**
     * 格式化总费用显示
     * 
     * @return 格式化的费用字符串
     */
    public String getFormattedTotalCost() {
        return totalCost != null ? "¥" + totalCost.toString() : "¥0.00";
    }

    /**
     * 格式化Token数量显示
     * 
     * @return 格式化的Token数量字符串
     */
    public String getFormattedTotalTokens() {
        if (totalTokens == null || totalTokens == 0) {
            return "0 tokens";
        }

        long tokens = totalTokens;
        if (tokens < 1000) {
            return tokens + " tokens";
        } else if (tokens < 1000000) {
            return String.format("%.1fK tokens", tokens / 1000.0);
        } else {
            return String.format("%.1fM tokens", tokens / 1000000.0);
        }
    }

    /**
     * 格式化处理时长显示
     * 
     * @return 格式化的时长字符串
     */
    public String getFormattedDuration() {
        if (durationMs == null || durationMs == 0) {
            return "0ms";
        }

        if (durationMs < 1000) {
            return durationMs + "ms";
        } else if (durationMs < 60000) {
            return String.format("%.1fs", durationMs / 1000.0);
        } else {
            return String.format("%.1fm", durationMs / 60000.0);
        }
    }

    /**
     * 获取状态显示文本
     * 
     * @return 状态显示文本
     */
    public String getStatusDisplay() {
        if ("SUCCESS".equals(status)) {
            return "成功";
        } else if ("FAILED".equals(status)) {
            return "失败";
        } else if ("PENDING".equals(status)) {
            return "处理中";
        }
        return status;
    }

    /**
     * 获取计费状态显示文本
     * 
     * @return 计费状态显示文本
     */
    public String getBillingStatusDisplay() {
        if ("BILLED".equals(billingStatus)) {
            return "已计费";
        } else if ("UNBILLED".equals(billingStatus)) {
            return "未计费";
        } else if ("REFUNDED".equals(billingStatus)) {
            return "已退费";
        }
        return billingStatus;
    }

    /**
     * 获取计费类型显示文本
     * 
     * @return 计费类型显示文本
     */
    public String getBillingTypeDisplay() {
        return BillingConstants.getBillingTypeText(billingType);
    }

    /**
     * 获取扣除来源显示文本
     * 
     * @return 扣除来源显示文本
     */
    public String getDeductionSourceDisplay() {
        return BillingConstants.getDeductionSourceText(deductionSource);
    }

    /**
     * 获取组合显示文本（计费类型 + 扣除来源）
     * 
     * @return 组合显示文本
     */
    public String getCombinedDisplay() {
        String billingDisplay = getBillingTypeDisplay();
        String deductionDisplay = getDeductionSourceDisplay();
        
        if (deductionDisplay != null && !deductionDisplay.trim().isEmpty()) {
            return billingDisplay + " (" + deductionDisplay + ")";
        }
        return billingDisplay;
    }
}