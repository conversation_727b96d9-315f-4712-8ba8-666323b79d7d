package com.dipspro.modules.billing.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 申诉创建DTO
 * 
 * 用于用户提交申诉时的请求参数，包含申诉的基本信息和要求。
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppealCreateDto {

    /**
     * 用户ID
     * 系统自动设置，不需要前端传递
     */
    private Long userId;

    /**
     * 使用记录ID
     * 申诉针对的具体计费记录
     */
    @NotNull(message = "使用记录ID不能为空")
    private Long usageRecordId;

    /**
     * 申诉原因
     * 选择或填写申诉的具体原因
     */
    @NotBlank(message = "申诉原因不能为空")
    @Size(max = 200, message = "申诉原因不能超过200个字符")
    private String reason;

    /**
     * 申诉详细描述
     * 详细说明申诉的具体情况和背景
     */
    @Size(max = 2000, message = "申诉描述不能超过2000个字符")
    private String userDescription;

    /**
     * 验证申诉信息是否完整
     * 
     * @return 验证结果描述
     */
    public String validateAppealInfo() {
        if (usageRecordId == null) {
            return "使用记录ID不能为空";
        }

        if (reason == null || reason.trim().isEmpty()) {
            return "申诉原因不能为空";
        }

        if (reason.length() > 200) {
            return "申诉原因不能超过200个字符";
        }

        if (userDescription != null && userDescription.length() > 2000) {
            return "申诉描述不能超过2000个字符";
        }

        return "验证通过";
    }

    /**
     * 获取申诉简要信息
     * 
     * @return 申诉简要描述
     */
    public String getAppealSummary() {
        return "申诉原因: " + reason;
    }
}