package com.dipspro.modules.billing.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * Token费用计算请求DTO
 * 
 * 用于计算指定Token数量对应的费用。
 * 支持输入Token和输出Token分别计费。
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class TokenCostCalculateDto {

    /**
     * 用户ID（可选，如果提供则使用用户的套餐价格）
     */
    private Long userId;

    /**
     * 输入Token数量
     */
    @NotNull(message = "输入Token数量不能为空")
    @PositiveOrZero(message = "输入Token数量不能为负数")
    private Long inputTokens;

    /**
     * 输出Token数量
     */
    @NotNull(message = "输出Token数量不能为空")
    @PositiveOrZero(message = "输出Token数量不能为负数")
    private Long outputTokens;

    /**
     * 思维链Token数量（可选）
     * 用于支持模型的思维链Token计算
     */
    @PositiveOrZero(message = "思维链Token数量不能为负数")
    private Long thoughtChainTokens;

    /**
     * Agent类型（可选）
     * 用于计算折扣，如"visual"表示可视化Agent
     */
    private String agentType;

    /**
     * 计算总Token数量
     * 
     * @return 总Token数量
     */
    public Long getTotalTokens() {
        long total = (inputTokens != null ? inputTokens : 0L) +
                (outputTokens != null ? outputTokens : 0L);
        if (thoughtChainTokens != null) {
            total += thoughtChainTokens;
        }
        return total;
    }

    /**
     * 检查Token数量是否有效
     * 
     * @return 是否有效
     */
    public boolean isValidTokens() {
        return getTotalTokens() > 0;
    }

    /**
     * 格式化Token数量显示
     * 
     * @return 格式化的Token数量字符串
     */
    public String getFormattedTokens() {
        long total = getTotalTokens();
        if (total < 1000) {
            return total + " tokens";
        } else if (total < 1000000) {
            return String.format("%.1fK tokens", total / 1000.0);
        } else {
            return String.format("%.1fM tokens", total / 1000000.0);
        }
    }
}