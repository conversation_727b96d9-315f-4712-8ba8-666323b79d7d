package com.dipspro.modules.billing.entity;

import java.math.BigDecimal;
import java.time.Instant;

import org.hibernate.annotations.CreationTimestamp;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 发票交易关联实体
 *
 * <AUTHOR> Pro
 * @since 1.0.0
 */
@Entity
@Table(name = "b_billing_invoice_transactions")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceTransaction {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "invoice_id", nullable = false)
    private Invoice invoice;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "transaction_id", nullable = false)
    private BillingTransaction transaction;

    @Column(name = "transaction_amount", precision = 15, scale = 2, nullable = false)
    private BigDecimal transactionAmount;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private Instant createdAt;

    /**
     * 构造函数
     *
     * @param invoice           发票
     * @param transaction       交易
     * @param transactionAmount 交易金额
     */
    public InvoiceTransaction(Invoice invoice, BillingTransaction transaction, BigDecimal transactionAmount) {
        this.invoice = invoice;
        this.transaction = transaction;
        this.transactionAmount = transactionAmount;
    }

    /**
     * 获取交易ID
     *
     * @return 交易ID
     */
    public Long getTransactionId() {
        return transaction != null ? transaction.getId() : null;
    }

    /**
     * 获取交易单号
     *
     * @return 交易单号
     */
    public String getTransactionNo() {
        return transaction != null ? transaction.getTransactionNo() : null;
    }

    /**
     * 获取交易描述
     *
     * @return 交易描述
     */
    public String getTransactionDescription() {
        return transaction != null ? transaction.getDescription() : null;
    }

    /**
     * 获取交易时间
     *
     * @return 交易时间
     */
    public java.time.LocalDateTime getTransactionTime() {
        return transaction != null ? transaction.getCreatedAt() : null;
    }
}