package com.dipspro.modules.billing.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import org.hibernate.annotations.Comment;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 计费套餐配置实体
 * 对应数据库表：b_billing_packages
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "b_billing_packages")
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@Comment("计费套餐配置表")
public class BillingPackage {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("主键ID")
    private Long id;

    /**
     * 套餐名称
     */
    @Column(name = "name", length = 100, nullable = false)
    @NotBlank(message = "套餐名称不能为空")
    @Comment("套餐名称")
    private String name;

    /**
     * 套餐描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    @Comment("套餐描述")
    private String description;

    /**
     * 输入Token单价（每千Token价格）
     */
    @Column(name = "input_token_price", precision = 10, scale = 6, nullable = false)
    @NotNull(message = "输入Token单价不能为空")
    @PositiveOrZero(message = "输入Token单价不能为负数")
    @Comment("输入Token单价（每千Token价格）")
    private BigDecimal inputTokenPrice;

    /**
     * 输出Token单价（每千Token价格）
     */
    @Column(name = "output_token_price", precision = 10, scale = 6, nullable = false)
    @NotNull(message = "输出Token单价不能为空")
    @PositiveOrZero(message = "输出Token单价不能为负数")
    @Comment("输出Token单价（每千Token价格）")
    private BigDecimal outputTokenPrice;

    /**
     * 新用户免费Token数量
     */
    @Column(name = "free_tokens")
    @PositiveOrZero(message = "免费Token数量不能为负数")
    @Comment("新用户免费Token数量")
    private Long freeTokens = 0L;

    /**
     * 单次请求最大Token限制，0表示无限制
     */
    @Column(name = "max_tokens_per_request")
    @PositiveOrZero(message = "单次请求Token限制不能为负数")
    @Comment("单次请求最大Token限制，0表示无限制")
    private Long maxTokensPerRequest = 0L;

    /**
     * 每日Token使用限制，0表示无限制
     */
    @Column(name = "daily_token_limit")
    @PositiveOrZero(message = "每日Token限制不能为负数")
    @Comment("每日Token使用限制，0表示无限制")
    private Long dailyTokenLimit = 0L;

    /**
     * 是否激活
     */
    @Column(name = "is_active")
    @Comment("是否激活")
    private Boolean isActive = true;

    /**
     * 是否为默认套餐
     */
    @Column(name = "is_default")
    @Comment("是否为默认套餐")
    private Boolean isDefault = false;

    /**
     * 套餐特性描述
     */
    @Column(name = "features", columnDefinition = "TEXT")
    @Comment("套餐特性描述")
    private String features;

    /**
     * 套餐限制说明
     */
    @Column(name = "limitations", columnDefinition = "TEXT")
    @Comment("套餐限制说明")
    private String limitations;

    /**
     * 排序权重
     */
    @Column(name = "sort_order")
    @Comment("排序权重")
    private Integer sortOrder = 0;

    /**
     * 最低月消费要求（人民币）
     */
    @Column(name = "monthly_minimum_spend", precision = 10, scale = 2)
    @PositiveOrZero(message = "最低月消费要求不能为负数")
    @Comment("最低月消费要求（人民币）")
    private BigDecimal monthlyMinimumSpend = BigDecimal.ZERO;

    /**
     * 套餐等级：BASIC/STANDARD/PREMIUM
     */
    @Column(name = "package_level", length = 20)
    @Comment("套餐等级：BASIC/STANDARD/PREMIUM")
    private String packageLevel = "BASIC";

    /**
     * 可视化Agent输出Token折扣系数
     */
    @Column(name = "visual_discount_factor", precision = 3, scale = 3, nullable = false)
    @NotNull(message = "可视化折扣系数不能为空")
    @PositiveOrZero(message = "可视化折扣系数不能为负数")
    @Comment("可视化Agent输出Token折扣系数，1.000表示无折扣，0.100表示90%折扣")
    private BigDecimal visualDiscountFactor = new BigDecimal("1.000");

    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false, updatable = false)
    @Comment("创建时间")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    @Comment("更新时间")
    private LocalDateTime updatedAt;

    /**
     * 创建时自动设置创建时间和更新时间
     */
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createdAt = now;
        this.updatedAt = now;
    }

    /**
     * 更新时自动设置更新时间
     */
    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}