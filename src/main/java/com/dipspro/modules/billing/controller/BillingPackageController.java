package com.dipspro.modules.billing.controller;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.modules.billing.dto.BillingPackageDto;
import com.dipspro.modules.billing.dto.PackageCreateDto;
import com.dipspro.modules.billing.dto.PackageUpdateDto;
import com.dipspro.modules.billing.entity.BillingPackage;
import com.dipspro.modules.billing.service.BillingPackageService;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 计费套餐管理控制器
 * 
 * 提供套餐相关的REST API接口，包括：
 * - 用户端：查看可用套餐、套餐详情
 * - 管理端：套餐创建、更新、管理
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/billing/packages")
@RequiredArgsConstructor
@Validated
public class BillingPackageController {

    private final BillingPackageService billingPackageService;

    /**
     * 获取可用套餐列表（用户端）
     * 
     * 返回所有激活状态的套餐，按照排序权重排列。
     * 用户可以查看套餐价格、特性等信息进行选择。
     * 
     * @return 激活的套餐列表
     */
    @GetMapping
    public ApiResponse<List<BillingPackageDto>> getAvailablePackages() {
        log.info("获取可用套餐列表");

        List<BillingPackage> packages = billingPackageService.getActivePackages();
        List<BillingPackageDto> packageDtos = packages.stream()
                .map(this::convertToPackageDto)
                .toList();

        log.info("返回 {} 个可用套餐", packageDtos.size());
        return ApiResponse.success(packageDtos, "套餐列表获取成功");
    }

    /**
     * 获取套餐详情
     * 
     * 根据套餐ID获取详细的套餐信息，包括价格配置、
     * 特性描述、使用限制等完整信息。
     * 
     * @param id 套餐ID
     * @return 套餐详情
     */
    @GetMapping("/{id}")
    public ApiResponse<BillingPackageDto> getPackageDetail(@PathVariable @NotNull Long id) {
        log.info("获取套餐详情: PackageId={}", id);

        var packageOpt = billingPackageService.getPackageById(id);
        if (packageOpt.isEmpty()) {
            log.warn("套餐不存在: PackageId={}", id);
            return ApiResponse.error("套餐不存在");
        }

        BillingPackageDto packageDto = convertToPackageDto(packageOpt.get());

        log.info("套餐详情获取成功: PackageId={}, Name={}", id, packageDto.getName());
        return ApiResponse.success(packageDto, "套餐详情获取成功");
    }

    /**
     * 获取默认套餐
     * 
     * 返回系统默认套餐，通常用于新用户初始化或套餐重置。
     * 
     * @return 默认套餐信息
     */
    @GetMapping("/default")
    public ApiResponse<BillingPackageDto> getDefaultPackage() {
        log.info("获取默认套餐");

        var packageOpt = billingPackageService.getDefaultPackage();
        if (packageOpt.isEmpty()) {
            log.warn("未配置默认套餐");
            return ApiResponse.error("未配置默认套餐");
        }

        BillingPackageDto packageDto = convertToPackageDto(packageOpt.get());

        log.info("默认套餐获取成功: PackageId={}, Name={}", packageDto.getId(), packageDto.getName());
        return ApiResponse.success(packageDto, "默认套餐获取成功");
    }

    /**
     * 分页查询套餐（管理端）
     * 
     * 管理员可以分页查询所有套餐，包括已停用的套餐。
     * 支持按状态筛选和名称搜索。
     * 
     * @param page   页码，从0开始
     * @param size   每页大小，默认20，最大100
     * @param status 状态筛选：true(激活)、false(停用)、null(全部)
     * @param name   套餐名称搜索关键词
     * @return 分页的套餐列表
     */
    @GetMapping("/admin/list")
    public ApiResponse<Page<BillingPackageDto>> getPackagesForAdmin(
            @RequestParam(defaultValue = "0") @Min(0) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size,
            @RequestParam(required = false) Boolean status,
            @RequestParam(required = false) String name) {

        log.info("管理员查询套餐: page={}, size={}, status={}, name={}", page, size, status, name);

        Pageable pageable = PageRequest.of(page, size);
        Page<BillingPackage> packages;

        if (name != null && !name.trim().isEmpty()) {
            // 按名称搜索
            packages = billingPackageService.searchPackagesByName(name.trim(), pageable);
        } else if (status != null) {
            // 按状态筛选
            packages = billingPackageService.getPackagesByStatus(status, pageable);
        } else {
            // 查询全部
            packages = billingPackageService.getPackages(pageable);
        }

        Page<BillingPackageDto> packageDtos = packages.map(this::convertToPackageDto);

        log.info("管理员套餐查询完成: TotalPackages={}", packages.getTotalElements());
        return ApiResponse.success(packageDtos, "套餐查询成功");
    }

    /**
     * 创建套餐（管理端）
     * 
     * 管理员创建新的计费套餐，需要配置价格、特性、限制等信息。
     * 创建前会进行配置验证，确保套餐信息完整有效。
     * 
     * @param createDto 套餐创建参数
     * @return 创建的套餐信息
     */
    @PostMapping("/admin")
    public ApiResponse<BillingPackageDto> createPackage(@Valid @RequestBody PackageCreateDto createDto) {
        log.info("创建套餐: Name={}, InputPrice={}, OutputPrice={}",
                createDto.getName(), createDto.getInputTokenPrice(), createDto.getOutputTokenPrice());

        // 转换DTO为实体
        BillingPackage billingPackage = convertToPackageEntity(createDto);

        // 创建套餐
        BillingPackage createdPackage = billingPackageService.createPackage(billingPackage);
        BillingPackageDto packageDto = convertToPackageDto(createdPackage);

        log.info("套餐创建成功: PackageId={}, Name={}", createdPackage.getId(), createdPackage.getName());
        return ApiResponse.success(packageDto, "套餐创建成功");
    }

    /**
     * 更新套餐（管理端）
     * 
     * 管理员更新现有套餐的配置信息。
     * 更新前会验证套餐是否存在以及配置的有效性。
     * 
     * @param id        套餐ID
     * @param updateDto 套餐更新参数
     * @return 更新后的套餐信息
     */
    @PutMapping("/admin/{id}")
    public ApiResponse<BillingPackageDto> updatePackage(
            @PathVariable @NotNull Long id,
            @Valid @RequestBody PackageUpdateDto updateDto) {

        log.info("更新套餐: PackageId={}, Name={}", id, updateDto.getName());

        // 转换DTO为实体
        BillingPackage billingPackage = convertToPackageEntity(updateDto);

        // 更新套餐
        BillingPackage updatedPackage = billingPackageService.updatePackage(id, billingPackage);
        BillingPackageDto packageDto = convertToPackageDto(updatedPackage);

        log.info("套餐更新成功: PackageId={}, Name={}", updatedPackage.getId(), updatedPackage.getName());
        return ApiResponse.success(packageDto, "套餐更新成功");
    }

    /**
     * 激活套餐（管理端）
     * 
     * 管理员激活指定套餐，使其对用户可见和可选择。
     * 
     * @param id 套餐ID
     * @return 操作结果
     */
    @PostMapping("/admin/{id}/activate")
    public ApiResponse<Object> activatePackage(@PathVariable @NotNull Long id) {
        log.info("激活套餐: PackageId={}", id);

        boolean success = billingPackageService.activatePackage(id);
        if (success) {
            log.info("套餐激活成功: PackageId={}", id);
            return ApiResponse.success("套餐激活成功");
        } else {
            log.warn("套餐激活失败: PackageId={}", id);
            return ApiResponse.error("套餐激活失败");
        }
    }

    /**
     * 停用套餐（管理端）
     * 
     * 管理员停用指定套餐，停用后用户将无法选择该套餐。
     * 已使用该套餐的用户不受影响，继续使用原套餐。
     * 
     * @param id 套餐ID
     * @return 操作结果
     */
    @PostMapping("/admin/{id}/deactivate")
    public ApiResponse<Object> deactivatePackage(@PathVariable @NotNull Long id) {
        log.info("停用套餐: PackageId={}", id);

        boolean success = billingPackageService.deactivatePackage(id);
        if (success) {
            log.info("套餐停用成功: PackageId={}", id);
            return ApiResponse.success("套餐停用成功");
        } else {
            log.warn("套餐停用失败: PackageId={}", id);
            return ApiResponse.error("套餐停用失败");
        }
    }

    /**
     * 设置默认套餐（管理端）
     * 
     * 管理员设置指定套餐为系统默认套餐。
     * 新用户注册时将自动分配该套餐。
     * 
     * @param id 套餐ID
     * @return 操作结果
     */
    @PostMapping("/admin/{id}/set-default")
    public ApiResponse<Object> setDefaultPackage(@PathVariable @NotNull Long id) {
        log.info("设置默认套餐: PackageId={}", id);

        boolean success = billingPackageService.setDefaultPackage(id);
        if (success) {
            log.info("默认套餐设置成功: PackageId={}", id);
            return ApiResponse.success("默认套餐设置成功");
        } else {
            log.warn("默认套餐设置失败: PackageId={}", id);
            return ApiResponse.error("默认套餐设置失败");
        }
    }

    /**
     * 转换BillingPackage实体为DTO
     * 
     * @param billingPackage 套餐实体
     * @return 套餐DTO
     */
    private BillingPackageDto convertToPackageDto(BillingPackage billingPackage) {
        return new BillingPackageDto()
                .setId(billingPackage.getId())
                .setName(billingPackage.getName())
                .setDescription(billingPackage.getDescription())
                .setInputTokenPrice(billingPackage.getInputTokenPrice())
                .setOutputTokenPrice(billingPackage.getOutputTokenPrice())
                .setFreeTokens(billingPackage.getFreeTokens())
                .setMaxTokensPerRequest(billingPackage.getMaxTokensPerRequest())
                .setDailyTokenLimit(billingPackage.getDailyTokenLimit())
                .setIsActive(billingPackage.getIsActive())
                .setSortOrder(billingPackage.getSortOrder())
                .setCreatedAt(billingPackage.getCreatedAt())
                .setUpdatedAt(billingPackage.getUpdatedAt());
    }

    /**
     * 转换创建DTO为实体
     * 
     * @param createDto 创建DTO
     * @return 套餐实体
     */
    private BillingPackage convertToPackageEntity(PackageCreateDto createDto) {
        BillingPackage billingPackage = new BillingPackage();
        billingPackage.setName(createDto.getName());
        billingPackage.setDescription(createDto.getDescription());
        billingPackage.setInputTokenPrice(createDto.getInputTokenPrice());
        billingPackage.setOutputTokenPrice(createDto.getOutputTokenPrice());
        billingPackage.setFreeTokens(createDto.getFreeTokens());
        billingPackage.setIsActive(createDto.getIsActive());
        billingPackage.setIsDefault(createDto.getIsDefault());
        billingPackage.setSortOrder(createDto.getSortOrder());
        billingPackage.setFeatures(createDto.getFeatures());
        billingPackage.setLimitations(createDto.getLimitations());
        return billingPackage;
    }

    /**
     * 转换更新DTO为实体
     * 
     * @param updateDto 更新DTO
     * @return 套餐实体
     */
    private BillingPackage convertToPackageEntity(PackageUpdateDto updateDto) {
        BillingPackage billingPackage = new BillingPackage();
        billingPackage.setName(updateDto.getName());
        billingPackage.setDescription(updateDto.getDescription());
        billingPackage.setInputTokenPrice(updateDto.getInputTokenPrice());
        billingPackage.setOutputTokenPrice(updateDto.getOutputTokenPrice());
        billingPackage.setFreeTokens(updateDto.getFreeTokens());
        billingPackage.setIsActive(updateDto.getIsActive());
        billingPackage.setIsDefault(updateDto.getIsDefault());
        billingPackage.setSortOrder(updateDto.getSortOrder());
        billingPackage.setFeatures(updateDto.getFeatures());
        billingPackage.setLimitations(updateDto.getLimitations());
        return billingPackage;
    }
}