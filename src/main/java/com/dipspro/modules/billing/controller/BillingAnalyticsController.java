package com.dipspro.modules.billing.controller;

import java.math.BigDecimal;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.modules.billing.dto.analytics.AbnormalSwitchMonitorDto;
import com.dipspro.modules.billing.dto.analytics.OverviewDataDto;
import com.dipspro.modules.billing.dto.analytics.PackageROIAnalysisDto;
import com.dipspro.modules.billing.dto.analytics.PackageSwitchRateStatsDto;
import com.dipspro.modules.billing.dto.analytics.PackageUsageTrendDto;
import com.dipspro.modules.billing.dto.analytics.QualificationRateAnalysisDto;
import com.dipspro.modules.billing.dto.analytics.RealTimeMetricsDto;
import com.dipspro.modules.billing.dto.analytics.RevenueLayerStatsDto;
import com.dipspro.modules.billing.dto.analytics.UserLifetimeValueDto;
import com.dipspro.modules.billing.service.BillingAnalyticsService;

/**
 * 计费分析控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/billing/analytics")
public class BillingAnalyticsController {

    private static final Logger logger = LoggerFactory.getLogger(BillingAnalyticsController.class);

    @Autowired
    private BillingAnalyticsService billingAnalyticsService;

    // ==================== 新增前端兼容接口 ====================

    /**
     * 获取实时监控数据 - 前端兼容接口
     */
    @GetMapping("/real-time")
    public ApiResponse<Map<String, Object>> getRealTimeData() {
        try {
            logger.info("获取实时监控数据");
            RealTimeMetricsDto metrics = billingAnalyticsService.getRealTimeMetrics();

            // 转换为前端期望的格式
            Map<String, Object> result = new HashMap<>();
            result.put("todayRevenue",
                    metrics.getTodayRevenue() != null ? metrics.getTodayRevenue().doubleValue() : 0.0);
            result.put("yesterdayRevenue", 0.0); // 可以从历史数据获取
            result.put("onlineUsers", metrics.getOnlineUsers() != null ? metrics.getOnlineUsers() : 0L);
            result.put("peakUsers", metrics.getTodayActiveUsers() != null ? metrics.getTodayActiveUsers() : 0L);

            // 系统负载
            if (metrics.getSystemLoad() != null) {
                BigDecimal cpu = metrics.getSystemLoad().getCpuUsage();
                BigDecimal memory = metrics.getSystemLoad().getMemoryUsage();
                double systemLoad = 0.0;
                if (cpu != null && memory != null) {
                    systemLoad = (cpu.doubleValue() + memory.doubleValue()) / 2;
                }
                result.put("systemLoad", systemLoad);
            } else {
                result.put("systemLoad", 0.0);
            }

            // 告警数量
            result.put("alertCount", metrics.getAlerts() != null ? metrics.getAlerts().size() : 0);

            return ApiResponse.success(result);
        } catch (Exception e) {
            logger.error("获取实时监控数据失败", e);
            return ApiResponse.error("获取实时监控数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取概览数据 - 前端兼容接口
     */
    @GetMapping("/overview")
    public ApiResponse<Map<String, Object>> getOverviewData(@RequestParam String timeRange) {
        try {
            logger.info("获取概览数据 - 时间范围: {}", timeRange);

            // 调用service获取概览数据
            OverviewDataDto overviewData = billingAnalyticsService.getOverviewData(timeRange);

            // 转换为前端期望的格式
            Map<String, Object> result = new HashMap<>();

            // 总收入
            result.put("totalRevenue",
                    overviewData.getTotalRevenue() != null ? overviewData.getTotalRevenue().doubleValue() : 0.0);

            // 活跃用户
            result.put("activeUsers",
                    overviewData.getActiveUsers() != null ? overviewData.getActiveUsers() : 0L);

            // 套餐订阅数量
            result.put("packageSubscriptions",
                    overviewData.getPackageSubscriptions() != null ? overviewData.getPackageSubscriptions() : 0L);

            // 达标率
            result.put("qualificationRate",
                    overviewData.getQualificationRate() != null ? overviewData.getQualificationRate().doubleValue() : 0.0);

            // 趋势数据
            if (overviewData.getRevenueTrend() != null) {
                Map<String, Object> revenueTrend = new HashMap<>();
                revenueTrend.put("type", overviewData.getRevenueTrend().getType());
                revenueTrend.put("percentage", overviewData.getRevenueTrend().getPercentage());
                result.put("revenueTrend", revenueTrend);
            } else {
                Map<String, Object> revenueTrend = new HashMap<>();
                revenueTrend.put("type", "stable");
                revenueTrend.put("percentage", 0.0);
                result.put("revenueTrend", revenueTrend);
            }

            if (overviewData.getUsersTrend() != null) {
                Map<String, Object> usersTrend = new HashMap<>();
                usersTrend.put("type", overviewData.getUsersTrend().getType());
                usersTrend.put("percentage", overviewData.getUsersTrend().getPercentage());
                result.put("usersTrend", usersTrend);
            } else {
                Map<String, Object> usersTrend = new HashMap<>();
                usersTrend.put("type", "stable");
                usersTrend.put("percentage", 0.0);
                result.put("usersTrend", usersTrend);
            }

            if (overviewData.getPackagesTrend() != null) {
                Map<String, Object> packagesTrend = new HashMap<>();
                packagesTrend.put("type", overviewData.getPackagesTrend().getType());
                packagesTrend.put("percentage", overviewData.getPackagesTrend().getPercentage());
                result.put("packagesTrend", packagesTrend);
            } else {
                Map<String, Object> packagesTrend = new HashMap<>();
                packagesTrend.put("type", "stable");
                packagesTrend.put("percentage", 0.0);
                result.put("packagesTrend", packagesTrend);
            }

            if (overviewData.getQualificationTrend() != null) {
                Map<String, Object> qualificationTrend = new HashMap<>();
                qualificationTrend.put("type", overviewData.getQualificationTrend().getType());
                qualificationTrend.put("percentage", overviewData.getQualificationTrend().getPercentage());
                result.put("qualificationTrend", qualificationTrend);
            } else {
                Map<String, Object> qualificationTrend = new HashMap<>();
                qualificationTrend.put("type", "stable");
                qualificationTrend.put("percentage", 0.0);
                result.put("qualificationTrend", qualificationTrend);
            }

            // 消费统计数据
            result.put("totalUsageCount",
                    overviewData.getTotalUsageCount() != null ? overviewData.getTotalUsageCount() : 0L);
            result.put("successUsageCount",
                    overviewData.getSuccessUsageCount() != null ? overviewData.getSuccessUsageCount() : 0L);
            result.put("averageUsageCost",
                    overviewData.getAverageUsageCost() != null ? overviewData.getAverageUsageCost().doubleValue() : 0.0);
            result.put("averageUsageTokens",
                    overviewData.getAverageUsageTokens() != null ? overviewData.getAverageUsageTokens() : 0L);
            result.put("consumingUsers",
                    overviewData.getConsumingUsers() != null ? overviewData.getConsumingUsers() : 0L);

            // 消费统计趋势数据
            if (overviewData.getUsageCountTrend() != null) {
                Map<String, Object> usageCountTrend = new HashMap<>();
                usageCountTrend.put("type", overviewData.getUsageCountTrend().getType());
                usageCountTrend.put("percentage", overviewData.getUsageCountTrend().getPercentage());
                result.put("usageCountTrend", usageCountTrend);
            } else {
                Map<String, Object> usageCountTrend = new HashMap<>();
                usageCountTrend.put("type", "stable");
                usageCountTrend.put("percentage", 0.0);
                result.put("usageCountTrend", usageCountTrend);
            }

            if (overviewData.getAverageCostTrend() != null) {
                Map<String, Object> averageCostTrend = new HashMap<>();
                averageCostTrend.put("type", overviewData.getAverageCostTrend().getType());
                averageCostTrend.put("percentage", overviewData.getAverageCostTrend().getPercentage());
                result.put("averageCostTrend", averageCostTrend);
            } else {
                Map<String, Object> averageCostTrend = new HashMap<>();
                averageCostTrend.put("type", "stable");
                averageCostTrend.put("percentage", 0.0);
                result.put("averageCostTrend", averageCostTrend);
            }

            return ApiResponse.success(result);
        } catch (Exception e) {
            logger.error("获取概览数据失败", e);
            return ApiResponse.error("获取概览数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取收入趋势数据 - 前端兼容接口
     */
    @GetMapping("/revenue-trend")
    public ApiResponse<Map<String, Object>> getRevenueTrend(
            @RequestParam String type,
            @RequestParam String timeRange) {
        try {
            logger.info("获取收入趋势 - 类型: {}, 时间范围: {}", type, timeRange);

            // 返回空数据，等待后续实现真实数据查询
            Map<String, Object> result = new HashMap<>();
            result.put("dates", new ArrayList<>());
            result.put("values", new ArrayList<>());

            return ApiResponse.success(result);
        } catch (Exception e) {
            logger.error("获取收入趋势失败", e);
            return ApiResponse.error("获取收入趋势失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户分层数据 - 前端兼容接口
     */
    @GetMapping("/user-layer")
    public ApiResponse<Map<String, Object>> getUserLayerData(@RequestParam String timeRange) {
        try {
            logger.info("获取用户分层数据 - 时间范围: {}", timeRange);

            // 使用现有的收入分层统计
            YearMonth month = YearMonth.now();
            RevenueLayerStatsDto revenueStats = null;

            try {
                revenueStats = billingAnalyticsService.getRevenueLayerStats(month);
            } catch (Exception e) {
                logger.warn("获取收入分层统计失败，使用默认值", e);
            }

            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> layers = new ArrayList<>();

            if (revenueStats != null && revenueStats.getLayerDetails() != null) {
                for (RevenueLayerStatsDto.RevenueLayerDetail layer : revenueStats.getLayerDetails()) {
                    Map<String, Object> layerData = new HashMap<>();
                    layerData.put("name", layer.getLayerName());
                    layerData.put("value", layer.getUserCount() != null ? layer.getUserCount() : 0L);

                    // 计算百分比
                    long totalUsers = revenueStats.getLayerDetails().stream()
                            .mapToLong(l -> l.getUserCount() != null ? l.getUserCount() : 0L)
                            .sum();
                    double percentage = totalUsers > 0
                            ? (layer.getUserCount() != null ? layer.getUserCount() : 0L) * 100.0 / totalUsers
                            : 0.0;
                    layerData.put("percentage", percentage);

                    layers.add(layerData);
                }
            }

            result.put("layers", layers);

            return ApiResponse.success(result);
        } catch (Exception e) {
            logger.error("获取用户分层数据失败", e);
            return ApiResponse.error("获取用户分层数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取资格达标分析 - 前端兼容接口
     */
    @GetMapping("/qualification")
    public ApiResponse<Map<String, Object>> getQualificationAnalysis(@RequestParam String timeRange) {
        try {
            logger.info("获取资格达标分析 - 时间范围: {}", timeRange);

            YearMonth month = YearMonth.now();
            QualificationRateAnalysisDto qualificationStats = billingAnalyticsService
                    .getQualificationRateAnalysis(month);

            Map<String, Object> result = new HashMap<>();

            // 达标率数据
            Map<String, Object> qualificationRate = new HashMap<>();
            qualificationRate.put("dates", new ArrayList<>());
            qualificationRate.put("rates", new ArrayList<>());
            result.put("qualificationRate", qualificationRate);

            // 消费分布数据
            Map<String, Object> consumptionDistribution = new HashMap<>();
            consumptionDistribution.put("ranges", new ArrayList<>());
            consumptionDistribution.put("counts", new ArrayList<>());
            result.put("consumptionDistribution", consumptionDistribution);

            return ApiResponse.success(result);
        } catch (Exception e) {
            logger.error("获取资格达标分析失败", e);
            return ApiResponse.error("获取资格达标分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取套餐ROI分析 - 前端兼容接口
     */
    @GetMapping("/package-roi")
    public ApiResponse<Map<String, Object>> getPackageROIAnalysis(@RequestParam String timeRange) {
        try {
            logger.info("获取套餐ROI分析 - 时间范围: {}", timeRange);

            // 返回空数据，等待后续实现真实数据查询
            Map<String, Object> result = new HashMap<>();
            result.put("packages", new ArrayList<>());

            return ApiResponse.success(result);
        } catch (Exception e) {
            logger.error("获取套餐ROI分析失败", e);
            return ApiResponse.error("获取套餐ROI分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户列表 - 前端兼容接口
     */
    @GetMapping("/users")
    public ApiResponse<Map<String, Object>> getUserList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String packageType,
            @RequestParam(required = false) String timeRange) {
        try {
            logger.info("获取用户列表 - 页码: {}, 大小: {}, 关键词: {}, 套餐类型: {}", page, size, keyword, packageType);

            // 返回空数据，等待后续实现真实数据查询
            Map<String, Object> result = new HashMap<>();
            result.put("content", new ArrayList<>());
            result.put("totalElements", 0L);
            result.put("totalPages", 0);
            result.put("size", size);
            result.put("number", page - 1);

            return ApiResponse.success(result);
        } catch (Exception e) {
            logger.error("获取用户列表失败", e);
            return ApiResponse.error("获取用户列表失败: " + e.getMessage());
        }
    }

    /**
     * 导出分析报告 - 前端兼容接口
     */
    @GetMapping("/export")
    public ApiResponse<byte[]> exportAnalyticsReport(
            @RequestParam String timeRange,
            @RequestParam String format) {
        try {
            logger.info("导出分析报告 - 时间范围: {}, 格式: {}", timeRange, format);

            // 返回空数据，等待后续实现真实报告生成
            byte[] reportData = new byte[0];

            return ApiResponse.success(reportData);
        } catch (Exception e) {
            logger.error("导出分析报告失败", e);
            return ApiResponse.error("导出分析报告失败: " + e.getMessage());
        }
    }

    // ==================== 原有接口保持不变 ====================

    /**
     * 获取套餐切换率统计
     */
    @GetMapping("/package-switch-rate")
    public ApiResponse<PackageSwitchRateStatsDto> getPackageSwitchRateStats(
            @RequestParam String month) {
        try {
            logger.info("获取套餐切换率统计 - 月份: {}", month);
            YearMonth yearMonth = YearMonth.parse(month);
            PackageSwitchRateStatsDto result = billingAnalyticsService.getPackageSwitchRateStats(yearMonth);
            return ApiResponse.success(result);
        } catch (Exception e) {
            logger.error("获取套餐切换率统计失败", e);
            return ApiResponse.error("获取套餐切换率统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取资格达标率分析
     */
    @GetMapping("/qualification-rate")
    public ApiResponse<QualificationRateAnalysisDto> getQualificationRateAnalysis(
            @RequestParam String month) {
        try {
            logger.info("获取资格达标率分析 - 月份: {}", month);
            YearMonth yearMonth = YearMonth.parse(month);
            QualificationRateAnalysisDto result = billingAnalyticsService.getQualificationRateAnalysis(yearMonth);
            return ApiResponse.success(result);
        } catch (Exception e) {
            logger.error("获取资格达标率分析失败", e);
            return ApiResponse.error("获取资格达标率分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取收入分层统计
     */
    @GetMapping("/revenue-layer")
    public ApiResponse<RevenueLayerStatsDto> getRevenueLayerStats(
            @RequestParam String month) {
        try {
            logger.info("获取收入分层统计 - 月份: {}", month);
            YearMonth yearMonth = YearMonth.parse(month);
            RevenueLayerStatsDto result = billingAnalyticsService.getRevenueLayerStats(yearMonth);
            return ApiResponse.success(result);
        } catch (Exception e) {
            logger.error("获取收入分层统计失败", e);
            return ApiResponse.error("获取收入分层统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取异常切换监控
     */
    @GetMapping("/abnormal-switch-monitor")
    public ApiResponse<AbnormalSwitchMonitorDto> getAbnormalSwitchMonitor(
            @RequestParam(defaultValue = "24") int hours) {
        try {
            logger.info("获取异常切换监控 - 时间范围: {}小时", hours);
            AbnormalSwitchMonitorDto result = billingAnalyticsService.getAbnormalSwitchMonitor(hours);
            return ApiResponse.success(result);
        } catch (Exception e) {
            logger.error("获取异常切换监控失败", e);
            return ApiResponse.error("获取异常切换监控失败: " + e.getMessage());
        }
    }

    /**
     * 获取套餐使用趋势
     */
    @GetMapping("/package-usage-trend")
    public ApiResponse<PackageUsageTrendDto> getPackageUsageTrend(
            @RequestParam Long packageId,
            @RequestParam(defaultValue = "30") int days) {
        try {
            logger.info("获取套餐使用趋势 - 套餐ID: {}, 天数: {}", packageId, days);
            PackageUsageTrendDto result = billingAnalyticsService.getPackageUsageTrend(packageId, days);
            return ApiResponse.success(result);
        } catch (Exception e) {
            logger.error("获取套餐使用趋势失败", e);
            return ApiResponse.error("获取套餐使用趋势失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户生命周期价值
     */
    @GetMapping("/user-lifetime-value")
    public ApiResponse<UserLifetimeValueDto> getUserLifetimeValue(
            @RequestParam Long userId) {
        try {
            logger.info("获取用户生命周期价值 - 用户ID: {}", userId);
            UserLifetimeValueDto result = billingAnalyticsService.getUserLifetimeValue(userId);
            return ApiResponse.success(result);
        } catch (Exception e) {
            logger.error("获取用户生命周期价值失败", e);
            return ApiResponse.error("获取用户生命周期价值失败: " + e.getMessage());
        }
    }

    /**
     * 获取套餐ROI分析
     */
    @GetMapping("/package-roi-detail")
    public ApiResponse<PackageROIAnalysisDto> getPackageROIAnalysisDetail(
            @RequestParam Long packageId,
            @RequestParam String month) {
        try {
            logger.info("获取套餐ROI分析 - 套餐ID: {}, 月份: {}", packageId, month);
            YearMonth yearMonth = YearMonth.parse(month);
            PackageROIAnalysisDto result = billingAnalyticsService.getPackageROIAnalysis(packageId, yearMonth);
            return ApiResponse.success(result);
        } catch (Exception e) {
            logger.error("获取套餐ROI分析失败", e);
            return ApiResponse.error("获取套餐ROI分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取实时指标
     */
    @GetMapping("/real-time-metrics")
    public ApiResponse<RealTimeMetricsDto> getRealTimeMetrics() {
        try {
            logger.info("获取实时指标");
            RealTimeMetricsDto result = billingAnalyticsService.getRealTimeMetrics();
            return ApiResponse.success(result);
        } catch (Exception e) {
            logger.error("获取实时指标失败", e);
            return ApiResponse.error("获取实时指标失败: " + e.getMessage());
        }
    }

    /**
     * 获取综合分析报告
     */
    @GetMapping("/comprehensive-report")
    public ApiResponse<ComprehensiveAnalyticsReportDto> getComprehensiveAnalyticsReport(
            @RequestParam String month) {
        try {
            logger.info("获取综合分析报告 - 月份: {}", month);
            YearMonth yearMonth = YearMonth.parse(month);

            // 组合多个分析结果
            PackageSwitchRateStatsDto switchStats = billingAnalyticsService.getPackageSwitchRateStats(yearMonth);
            QualificationRateAnalysisDto qualificationStats = billingAnalyticsService
                    .getQualificationRateAnalysis(yearMonth);
            RevenueLayerStatsDto revenueStats = billingAnalyticsService.getRevenueLayerStats(yearMonth);
            RealTimeMetricsDto realTimeMetrics = billingAnalyticsService.getRealTimeMetrics();

            ComprehensiveAnalyticsReportDto report = new ComprehensiveAnalyticsReportDto();
            report.setMonth(month);
            report.setPackageSwitchStats(switchStats);
            report.setQualificationAnalysis(qualificationStats);
            report.setRevenueLayerStats(revenueStats);
            report.setRealTimeMetrics(realTimeMetrics);
            report.setGeneratedAt(realTimeMetrics.getTimestamp());

            return ApiResponse.success(report);
        } catch (Exception e) {
            logger.error("获取综合分析报告失败", e);
            return ApiResponse.error("获取综合分析报告失败: " + e.getMessage());
        }
    }

    /**
     * 综合分析报告DTO
     */
    public static class ComprehensiveAnalyticsReportDto {
        private String month;
        private PackageSwitchRateStatsDto packageSwitchStats;
        private QualificationRateAnalysisDto qualificationAnalysis;
        private RevenueLayerStatsDto revenueLayerStats;
        private RealTimeMetricsDto realTimeMetrics;
        private java.time.LocalDateTime generatedAt;

        // Getters and setters
        public String getMonth() {
            return month;
        }

        public void setMonth(String month) {
            this.month = month;
        }

        public PackageSwitchRateStatsDto getPackageSwitchStats() {
            return packageSwitchStats;
        }

        public void setPackageSwitchStats(PackageSwitchRateStatsDto packageSwitchStats) {
            this.packageSwitchStats = packageSwitchStats;
        }

        public QualificationRateAnalysisDto getQualificationAnalysis() {
            return qualificationAnalysis;
        }

        public void setQualificationAnalysis(QualificationRateAnalysisDto qualificationAnalysis) {
            this.qualificationAnalysis = qualificationAnalysis;
        }

        public RevenueLayerStatsDto getRevenueLayerStats() {
            return revenueLayerStats;
        }

        public void setRevenueLayerStats(RevenueLayerStatsDto revenueLayerStats) {
            this.revenueLayerStats = revenueLayerStats;
        }

        public RealTimeMetricsDto getRealTimeMetrics() {
            return realTimeMetrics;
        }

        public void setRealTimeMetrics(RealTimeMetricsDto realTimeMetrics) {
            this.realTimeMetrics = realTimeMetrics;
        }

        public java.time.LocalDateTime getGeneratedAt() {
            return generatedAt;
        }

        public void setGeneratedAt(java.time.LocalDateTime generatedAt) {
            this.generatedAt = generatedAt;
        }
    }
}