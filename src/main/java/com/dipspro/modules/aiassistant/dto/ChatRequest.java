package com.dipspro.modules.aiassistant.dto;

import lombok.Data;
import jakarta.validation.constraints.Size;
import com.fasterxml.jackson.databind.JsonNode;

/**
 * 聊天请求DTO
 */
@Data
public class ChatRequest {

    /**
     * 用户问题（当jsonInput为空时必填）
     */
    @Size(max = 4000, message = "问题长度不能超过4000字符")
    private String question;

    /**
     * 系统提示词（可选）
     */
    @Size(max = 2000, message = "系统提示词长度不能超过2000字符")
    private String systemPrompt;

    /**
     * 模型名称（可选）
     */
    private String model;

    /**
     * 温度参数（可选，0.0-2.0）
     */
    private Double temperature;

    /**
     * 最大token数（可选）
     */
    private Integer maxTokens;

    /**
     * 调试模式（可选，默认false）
     */
    private Boolean debugMode = false;

    /**
     * JSON对象输入（可选，如果提供则会转换为字符串作为问题内容）
     */
    private JsonNode jsonInput;
}