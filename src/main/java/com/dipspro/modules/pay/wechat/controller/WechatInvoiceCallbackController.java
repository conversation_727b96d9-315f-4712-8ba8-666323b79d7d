package com.dipspro.modules.pay.wechat.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.dipspro.modules.pay.wechat.service.WechatInvoiceCallbackService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 微信电子发票回调处理控制器
 * 
 * 处理微信支付电子发票相关的异步通知，包括：
 * - 开票通知（用户申请开票）
 * - 授权通知（用户授权获取发票信息）
 * - 开票结果通知（第三方开票平台完成开票）
 * 
 * 根据微信支付电子发票官方文档：
 * https://pay.weixin.qq.com/doc/v3/merchant/4012529457
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/pay/wechat/invoice/callback")
public class WechatInvoiceCallbackController {

    private static final Logger log = LoggerFactory.getLogger(WechatInvoiceCallbackController.class);

    private final WechatInvoiceCallbackService callbackService;
    private final ObjectMapper objectMapper;

    @Autowired
    public WechatInvoiceCallbackController(WechatInvoiceCallbackService callbackService) {
        this.callbackService = callbackService;
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 处理微信电子发票开票通知
     * 
     * 当用户在微信端申请开票时，微信会向此接口发送通知，包含：
     * - 商户订单号
     * - 用户填写的发票抬头信息
     * - 开票金额
     * - 用户openid等信息
     * 
     * @param requestBody 微信发送的通知内容
     * @param signature   微信签名（用于验证通知真实性）
     * @param serial      证书序列号
     * @param timestamp   时间戳
     * @param nonce       随机字符串
     * @return 响应微信的结果
     */
    @PostMapping("/invoice-request")
    public ResponseEntity<String> handleInvoiceRequest(
            @RequestBody String requestBody,
            @RequestHeader(value = "Wechatpay-Signature", required = false) String signature,
            @RequestHeader(value = "Wechatpay-Serial", required = false) String serial,
            @RequestHeader(value = "Wechatpay-Timestamp", required = false) String timestamp,
            @RequestHeader(value = "Wechatpay-Nonce", required = false) String nonce) {

        log.info("收到微信电子发票开票通知: Serial={}, Timestamp={}, Nonce={}",
                serial, timestamp, nonce);
        log.debug("开票通知内容: {}", requestBody);

        try {
            // 第一步：验证签名（如果提供了签名信息）
            if (signature != null && timestamp != null && nonce != null) {
                String verifyData = timestamp + "\n" + nonce + "\n" + requestBody + "\n";
                boolean signatureValid = callbackService.verifyCallbackSignature(verifyData, signature);

                if (!signatureValid) {
                    log.error("微信电子发票回调签名验证失败: Serial={}, Signature={}", serial, signature);
                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                            .body("{\"code\":\"FAIL\",\"message\":\"签名验证失败\"}");
                }
            }

            // 第二步：解析并处理开票请求
            JsonNode jsonNode = objectMapper.readTree(requestBody);
            boolean processed = callbackService.handleInvoiceRequest(jsonNode);

            if (processed) {
                log.info("微信电子发票开票通知处理成功: Serial={}", serial);
                return ResponseEntity.ok("{\"code\":\"SUCCESS\",\"message\":\"处理成功\"}");
            } else {
                log.error("微信电子发票开票通知处理失败: Serial={}", serial);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("{\"code\":\"FAIL\",\"message\":\"处理失败\"}");
            }

        } catch (Exception e) {
            log.error("处理微信电子发票开票通知异常: Serial={}", serial, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("{\"code\":\"FAIL\",\"message\":\"系统异常\"}");
        }
    }

    /**
     * 处理微信电子发票授权通知
     * 
     * 当用户授权商户获取其发票相关信息时，微信会向此接口发送通知。
     * 
     * @param requestBody 微信发送的授权通知内容
     * @param signature   微信签名
     * @param serial      证书序列号
     * @param timestamp   时间戳
     * @param nonce       随机字符串
     * @return 响应微信的结果
     */
    @PostMapping("/auth-notify")
    public ResponseEntity<String> handleAuthNotify(
            @RequestBody String requestBody,
            @RequestHeader(value = "Wechatpay-Signature", required = false) String signature,
            @RequestHeader(value = "Wechatpay-Serial", required = false) String serial,
            @RequestHeader(value = "Wechatpay-Timestamp", required = false) String timestamp,
            @RequestHeader(value = "Wechatpay-Nonce", required = false) String nonce) {

        log.info("收到微信电子发票授权通知: Serial={}, Timestamp={}, Nonce={}",
                serial, timestamp, nonce);
        log.debug("授权通知内容: {}", requestBody);

        try {
            // 验证签名
            if (signature != null && timestamp != null && nonce != null) {
                String verifyData = timestamp + "\n" + nonce + "\n" + requestBody + "\n";
                boolean signatureValid = callbackService.verifyCallbackSignature(verifyData, signature);

                if (!signatureValid) {
                    log.error("微信电子发票授权通知签名验证失败: Serial={}", serial);
                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                            .body("{\"code\":\"FAIL\",\"message\":\"签名验证失败\"}");
                }
            }

            // 处理授权通知
            JsonNode jsonNode = objectMapper.readTree(requestBody);
            boolean processed = callbackService.handleAuthNotify(jsonNode);

            if (processed) {
                log.info("微信电子发票授权通知处理成功: Serial={}", serial);
                return ResponseEntity.ok("{\"code\":\"SUCCESS\",\"message\":\"授权处理成功\"}");
            } else {
                log.error("微信电子发票授权通知处理失败: Serial={}", serial);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("{\"code\":\"FAIL\",\"message\":\"授权处理失败\"}");
            }

        } catch (Exception e) {
            log.error("处理微信电子发票授权通知异常: Serial={}", serial, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("{\"code\":\"FAIL\",\"message\":\"系统异常\"}");
        }
    }

    /**
     * 处理微信电子发票开票结果通知
     * 
     * 当第三方开票平台完成开票后，微信会向此接口发送开票结果通知。
     * 
     * @param requestBody 微信发送的开票结果通知内容
     * @param signature   微信签名
     * @param serial      证书序列号
     * @param timestamp   时间戳
     * @param nonce       随机字符串
     * @return 响应微信的结果
     */
    @PostMapping("/invoice-result")
    public ResponseEntity<String> handleInvoiceResult(
            @RequestBody String requestBody,
            @RequestHeader(value = "Wechatpay-Signature", required = false) String signature,
            @RequestHeader(value = "Wechatpay-Serial", required = false) String serial,
            @RequestHeader(value = "Wechatpay-Timestamp", required = false) String timestamp,
            @RequestHeader(value = "Wechatpay-Nonce", required = false) String nonce) {

        log.info("收到微信电子发票开票结果通知: Serial={}, Timestamp={}, Nonce={}",
                serial, timestamp, nonce);
        log.debug("开票结果通知内容: {}", requestBody);

        try {
            // 验证签名
            if (signature != null && timestamp != null && nonce != null) {
                String verifyData = timestamp + "\n" + nonce + "\n" + requestBody + "\n";
                boolean signatureValid = callbackService.verifyCallbackSignature(verifyData, signature);

                if (!signatureValid) {
                    log.error("微信电子发票开票结果通知签名验证失败: Serial={}", serial);
                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                            .body("{\"code\":\"FAIL\",\"message\":\"签名验证失败\"}");
                }
            }

            // 处理开票结果
            JsonNode jsonNode = objectMapper.readTree(requestBody);
            boolean processed = callbackService.handleInvoiceResult(jsonNode);

            if (processed) {
                log.info("微信电子发票开票结果通知处理成功: Serial={}", serial);
                return ResponseEntity.ok("{\"code\":\"SUCCESS\",\"message\":\"开票结果处理成功\"}");
            } else {
                log.error("微信电子发票开票结果通知处理失败: Serial={}", serial);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("{\"code\":\"FAIL\",\"message\":\"开票结果处理失败\"}");
            }

        } catch (Exception e) {
            log.error("处理微信电子发票开票结果通知异常: Serial={}", serial, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("{\"code\":\"FAIL\",\"message\":\"系统异常\"}");
        }
    }

    /**
     * 通用回调处理接口
     * 
     * 如果微信的回调类型在一个统一接口中处理，可以使用此接口。
     * 根据回调内容的event_type字段来区分不同的通知类型。
     * 
     * @param requestBody 微信发送的通知内容
     * @param signature   微信签名
     * @param serial      证书序列号
     * @param timestamp   时间戳
     * @param nonce       随机字符串
     * @return 响应微信的结果
     */
    @PostMapping("/notify")
    public ResponseEntity<String> handleGeneralNotify(
            @RequestBody String requestBody,
            @RequestHeader(value = "Wechatpay-Signature", required = false) String signature,
            @RequestHeader(value = "Wechatpay-Serial", required = false) String serial,
            @RequestHeader(value = "Wechatpay-Timestamp", required = false) String timestamp,
            @RequestHeader(value = "Wechatpay-Nonce", required = false) String nonce) {

        log.info("收到微信电子发票通用通知: Serial={}, Timestamp={}, Nonce={}",
                serial, timestamp, nonce);

        // 打印完整的回调数据
        log.info("完整微信电子发票回调数据: Serial={}, RequestBody={}", serial, requestBody);
        log.info("完整微信电子发票回调头信息: Serial={}, Signature={}, Timestamp={}, Nonce={}",
                serial, signature, timestamp, nonce);

        try {
            // 验证签名
            if (signature != null && timestamp != null && nonce != null) {
                String verifyData = timestamp + "\n" + nonce + "\n" + requestBody + "\n";
                boolean signatureValid = callbackService.verifyCallbackSignature(verifyData, signature);

                if (!signatureValid) {
                    log.error("微信电子发票通用通知签名验证失败: Serial={}", serial);
                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                            .body("{\"code\":\"FAIL\",\"message\":\"签名验证失败\"}");
                }
            }

            // 解析通知类型并分发处理
            JsonNode jsonNode = objectMapper.readTree(requestBody);
            String eventType = jsonNode.path("event_type").asText();

            log.info("解析到微信电子发票通知类型: Serial={}, EventType={}", serial, eventType);

            boolean processed = switch (eventType) {
                case "INVOICE_REQUEST" -> {
                    log.info("处理微信电子发票开票请求通知: Serial={}", serial);
                    yield callbackService.handleInvoiceRequest(jsonNode);
                }
                case "AUTH_NOTIFY" -> {
                    log.info("处理微信电子发票授权通知: Serial={}", serial);
                    yield callbackService.handleAuthNotify(jsonNode);
                }
                case "INVOICE_RESULT" -> {
                    log.info("处理微信电子发票开票结果通知: Serial={}", serial);
                    yield callbackService.handleInvoiceResult(jsonNode);
                }
                case "FAPIAO.CARD_INSERTED" -> {
                    log.info("处理微信电子发票插入卡包通知: Serial={}", serial);
                    yield handleCardInserted(jsonNode);
                }
                case "FAPIAO.USER_APPLIED" -> {
                    log.info("处理微信电子发票用户申请通知: Serial={}", serial);
                    yield handleUserApplied(jsonNode);
                }
                default -> {
                    log.warn("未知的微信电子发票通知类型: EventType={}, Serial={}", eventType, serial);
                    // 对于未知类型，先返回成功避免微信重复发送，但记录详细信息用于调试
                    log.info("未知通知类型的完整数据: EventType={}, Data={}", eventType, requestBody);
                    yield true; // 改为返回true，避免微信重复发送
                }
            };

            if (processed) {
                log.info("微信电子发票通用通知处理成功: Serial={}, EventType={}", serial, eventType);
                return ResponseEntity.ok("{\"code\":\"SUCCESS\",\"message\":\"通知处理成功\"}");
            } else {
                log.error("微信电子发票通用通知处理失败: Serial={}, EventType={}", serial, eventType);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("{\"code\":\"FAIL\",\"message\":\"通知处理失败\"}");
            }

        } catch (Exception e) {
            log.error("处理微信电子发票通用通知异常: Serial={}", serial, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("{\"code\":\"FAIL\",\"message\":\"系统异常\"}");
        }
    }

    /**
     * 健康检查接口
     * 
     * 用于微信平台验证回调地址的可用性
     * 
     * @return 成功响应
     */
    @PostMapping("/health")
    public ResponseEntity<String> healthCheck() {
        log.debug("微信电子发票回调健康检查");
        return ResponseEntity.ok("{\"code\":\"SUCCESS\",\"message\":\"服务正常\"}");
    }

    /**
     * 处理微信电子发票插入卡包通知
     * 
     * 当发票成功插入用户微信卡包后，微信会发送此通知
     * 
     * @param callbackData 回调数据
     * @return 是否处理成功
     */
    private boolean handleCardInserted(JsonNode callbackData) {
        try {
            String outTradeNo = callbackData.path("out_trade_no").asText();
            String cardId = callbackData.path("card_id").asText();
            String insertTime = callbackData.path("insert_time").asText();

            log.info("[WECHAT-INVOICE-CALLBACK] 处理发票插入卡包通知: outTradeNo={}, cardId={}, insertTime={}",
                    outTradeNo, cardId, insertTime);

            // 这里可以更新发票记录的卡包状态
            // 暂时返回成功，后续可以扩展具体业务逻辑

            return true;
        } catch (Exception e) {
            log.error("[WECHAT-INVOICE-CALLBACK] 处理发票插入卡包通知失败", e);
            return false;
        }
    }

    /**
     * 处理微信电子发票用户申请通知
     * 
     * 当用户在微信端申请开票时，微信会发送此通知
     * 
     * @param callbackData 回调数据
     * @return 是否处理成功
     */
    private boolean handleUserApplied(JsonNode callbackData) {
        try {
            String outTradeNo = callbackData.path("out_trade_no").asText();
            String invoiceCode = callbackData.path("invoice_code").asText();
            String invoiceDate = callbackData.path("invoice_date").asText();
            String invoiceType = callbackData.path("invoice_type").asText();
            String invoiceContent = callbackData.path("invoice_content").asText();
            String invoiceAmount = callbackData.path("invoice_amount").asText();
            String invoiceTitle = callbackData.path("invoice_title").asText();
            String invoiceTaxpayerId = callbackData.path("invoice_taxpayer_id").asText();
            String invoicePhone = callbackData.path("invoice_phone").asText();
            String invoiceAddress = callbackData.path("invoice_address").asText();
            String invoiceBank = callbackData.path("invoice_bank").asText();
            String invoiceAccount = callbackData.path("invoice_account").asText();
            String invoiceRemark = callbackData.path("invoice_remark").asText();
            String invoiceStatus = callbackData.path("invoice_status").asText();
            String invoiceApplyTime = callbackData.path("invoice_apply_time").asText();

            log.info(
                    "[WECHAT-INVOICE-CALLBACK] 处理微信电子发票用户申请通知: outTradeNo={}, invoiceCode={}, invoiceDate={}, invoiceType={}, invoiceContent={}, invoiceAmount={}, invoiceTitle={}, invoiceTaxpayerId={}, invoicePhone={}, invoiceAddress={}, invoiceBank={}, invoiceAccount={}, invoiceRemark={}, invoiceStatus={}, invoiceApplyTime={}",
                    outTradeNo, invoiceCode, invoiceDate, invoiceType, invoiceContent, invoiceAmount, invoiceTitle,
                    invoiceTaxpayerId, invoicePhone, invoiceAddress, invoiceBank, invoiceAccount, invoiceRemark,
                    invoiceStatus, invoiceApplyTime);

            // 这里可以更新发票记录的申请状态或保存发票信息
            // 暂时返回成功，后续可以扩展具体业务逻辑

            return true;
        } catch (Exception e) {
            log.error("[WECHAT-INVOICE-CALLBACK] 处理微信电子发票用户申请通知失败", e);
            return false;
        }
    }
}