package com.dipspro.modules.tenant.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 租户配置CSV导入数据传输对象
 */
@Data
public class TenantConfigCsvImportDto {

    /**
     * id (MongoDB风格的ID)
     */
    private String id;

    /**
     * 租户（医美机构）唯一码
     */
    @NotBlank(message = "租户唯一码不能为空")
    @Size(max = 64, message = "租户唯一码长度不能超过64个字符")
    private String usci;

    /**
     * 租户名称
     */
    @NotBlank(message = "租户名称不能为空")
    @Size(min = 2, max = 128, message = "租户名称长度必须在2-128个字符之间")
    private String name;

    /**
     * 租户（医美机构）联系方式
     */
    @Size(max = 32, message = "联系方式长度不能超过32个字符")
    private String telephone;

    /**
     * 租户（医美机构）行政区
     */
    @Size(max = 128, message = "行政区长度不能超过128个字符")
    private String region;

    /**
     * 租户（医美机构）的地址
     */
    @Size(max = 512, message = "地址长度不能超过512个字符")
    private String address;

    /**
     * 省份（从region解析）
     */
    private String province;

    /**
     * 城市（从region解析）
     */
    private String city;

    /**
     * 行政区（从region解析）
     */
    private String district;
}