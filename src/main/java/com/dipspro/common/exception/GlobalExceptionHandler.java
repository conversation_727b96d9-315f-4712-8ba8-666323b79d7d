package com.dipspro.common.exception;

import java.io.IOException;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.dipspro.common.dto.ApiResponse;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理认证异常
     */
    @ExceptionHandler(AuthenticationException.class)
    public ResponseEntity<ApiResponse<Object>> handleAuthenticationException(AuthenticationException e) {
        log.error("认证异常: {}", e.getMessage());
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(ApiResponse.unauthorized("认证失败，请重新登录"));
    }

    /**
     * 处理凭证错误异常
     */
    @ExceptionHandler(BadCredentialsException.class)
    public ResponseEntity<ApiResponse<Object>> handleBadCredentialsException(BadCredentialsException e) {
        log.error("凭证错误: {}", e.getMessage());
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(ApiResponse.unauthorized("用户名或密码错误"));
    }

    /**
     * 处理访问拒绝异常
     */
    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<ApiResponse<Object>> handleAccessDeniedException(AccessDeniedException e) {
        log.error("访问拒绝: {}", e.getMessage());
        return ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(ApiResponse.error("访问被拒绝，权限不足"));
    }

    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<Object>> handleValidationException(MethodArgumentNotValidException e) {
        StringBuilder errorMsg = new StringBuilder();
        for (FieldError error : e.getBindingResult().getFieldErrors()) {
            errorMsg.append(error.getField()).append(": ").append(error.getDefaultMessage()).append("; ");
        }
        log.error("参数验证失败: {}", errorMsg.toString());
        return ResponseEntity.badRequest()
                .body(ApiResponse.error("参数验证失败: " + errorMsg.toString()));
    }

    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<ApiResponse<Object>> handleBindException(BindException e) {
        StringBuilder errorMsg = new StringBuilder();
        for (FieldError error : e.getBindingResult().getFieldErrors()) {
            errorMsg.append(error.getField()).append(": ").append(error.getDefaultMessage()).append("; ");
        }
        log.error("数据绑定失败: {}", errorMsg.toString());
        return ResponseEntity.badRequest()
                .body(ApiResponse.error("数据绑定失败: " + errorMsg.toString()));
    }

    /**
     * 处理业务异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<ApiResponse<Object>> handleRuntimeException(RuntimeException e) {
        log.error("业务异常: {}", e.getMessage(), e);
        return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage()));
    }

    /**
     * 处理IO异常（包括SSE Broken pipe）
     */
    @ExceptionHandler(IOException.class)
    public ResponseEntity<?> handleIOException(IOException e, HttpServletRequest request) {
        // 检查是否为SSE请求
        if (isSseRequest(request)) {
            // 对于SSE请求，记录日志但不返回响应（避免序列化错误）
            if (isBrokenPipeError(e)) {
                log.debug("SSE连接断开 (Broken pipe): {}", e.getMessage());
            } else {
                log.warn("SSE连接IO异常: {}", e.getMessage());
            }
            // 返回空响应，让Spring处理
            return null;
        }

        // 非SSE请求的正常处理
        log.error("IO异常: {}", e.getMessage(), e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("网络连接异常"));
    }

    /**
     * 处理其他异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<?> handleException(Exception e, HttpServletRequest request) {
        // 检查是否为SSE请求
        if (isSseRequest(request)) {
            // 对于SSE请求，记录日志但不返回响应
            log.error("SSE请求系统异常: {}", e.getMessage(), e);
            return null;
        }

        log.error("系统异常: {}", e.getMessage(), e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统内部错误"));
    }

    /**
     * 检查是否为SSE请求
     */
    private boolean isSseRequest(HttpServletRequest request) {
        if (request == null) {
            return false;
        }

        String accept = request.getHeader("Accept");
        String contentType = request.getHeader("Content-Type");
        String uri = request.getRequestURI();

        return (accept != null && accept.contains("text/event-stream")) ||
                (contentType != null && contentType.contains("text/event-stream")) ||
                (uri != null && uri.contains("/sse/")) ||
                (uri != null && uri.contains("/stream"));
    }

    /**
     * 检查是否为Broken pipe错误
     */
    private boolean isBrokenPipeError(IOException e) {
        String message = e.getMessage();
        if (message == null) {
            return false;
        }

        String lowerMessage = message.toLowerCase();
        return lowerMessage.contains("broken pipe") ||
                lowerMessage.contains("connection reset") ||
                lowerMessage.contains("connection aborted") ||
                lowerMessage.contains("socket closed");
    }
} 