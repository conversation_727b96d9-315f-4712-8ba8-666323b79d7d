package com.dipspro.constant;

/**
 * 租户相关常量
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class TenantConstant {

    /**
     * 行业类型常量
     */
    public static class IndustryType {
        /** 共用 */
        public static final String SHARE = "SHARE";
        /** 医美行业 */
        public static final String MEDICAL_BEAUTY = "MEDICAL_BEAUTY";
        /** 地产行业 */
        public static final String REAL_ESTATE = "REAL_ESTATE";
    }

    /**
     * 租户类型常量
     */
    public static class TenantType {
        /** 机构 */
        public static final String ORGANIZATION = "ORGANIZATION";
        /** 机构集团 */
        public static final String ORGANIZATION_GROUP = "ORGANIZATION_GROUP";
        /** 厂家 */
        public static final String MANUFACTURER = "MANUFACTURER";
        /** 消费者 */
        public static final String CONSUMER = "CONSUMER";
    }

    /**
     * 行业类型描述映射
     * 
     * @param industryType 行业类型代码
     * @return 行业类型描述
     */
    public static String getIndustryTypeText(String industryType) {
        if (industryType == null) {
            return "";
        }
        switch (industryType) {
            case IndustryType.MEDICAL_BEAUTY:
                return "医美";
            case IndustryType.REAL_ESTATE:
                return "地产";
            default:
                return "";
        }
    }

    /**
     * 租户类型描述映射
     * 
     * @param tenantType 租户类型代码
     * @return 租户类型描述
     */
    public static String getTenantTypeText(String tenantType) {
        if (tenantType == null) {
            return "";
        }
        switch (tenantType) {
            case TenantType.ORGANIZATION:
                return "机构";
            case TenantType.ORGANIZATION_GROUP:
                return "机构集团";
            case TenantType.MANUFACTURER:
                return "厂家";
            case TenantType.CONSUMER:
                return "消费者";
            default:
                return "";
        }
    }
}