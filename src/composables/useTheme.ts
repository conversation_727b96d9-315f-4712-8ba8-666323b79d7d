import { ref, computed, watch, onMounted } from 'vue'
import { themeManager } from '@/utils/themeManager'
import type { IndustryType } from '@/utils/tenant-constants'
import { useUserStore } from '@/store/modules/user'

/**
 * 主题管理组合式函数
 */
export function useTheme() {
  const userStore = useUserStore()
  
  const currentTheme = ref(themeManager.getCurrentTheme())
  const currentColorScheme = ref(themeManager.getCurrentColorScheme())
  const availableThemes = ref(themeManager.getAvailableThemes())
  
  // 计算当前主题信息
  const themeInfo = computed(() => {
    return availableThemes.value.find(theme => theme.value === currentTheme.value)
  })
  
  // 检查是否为行业推荐主题
  const isIndustryTheme = computed(() => {
    return themeInfo.value?.industry !== undefined
  })
  
  // 获取行业推荐主题
  const getIndustryTheme = (industryType: IndustryType) => {
    return themeManager.getRecommendedTheme(industryType)
  }
  
  // 应用主题
  const applyTheme = (themeName: string, colorScheme?: 'light' | 'dark') => {
    themeManager.applyTheme(themeName, colorScheme)
    currentTheme.value = themeName
    if (colorScheme) {
      currentColorScheme.value = colorScheme
    }
  }
  
  // 切换明暗模式
  const toggleColorScheme = () => {
    themeManager.toggleColorScheme()
    currentColorScheme.value = themeManager.getCurrentColorScheme()
  }
  
  // 自动应用主题
  const autoApplyTheme = (industryType?: IndustryType, userPreference?: string) => {
    themeManager.autoApplyTheme(industryType, userPreference)
    currentTheme.value = themeManager.getCurrentTheme()
    currentColorScheme.value = themeManager.getCurrentColorScheme()
  }
  
  // 预加载主题
  const preloadTheme = (themeName: string) => {
    themeManager.preloadTheme(themeName)
  }
  
  // 监听用户信息变化，自动应用行业主题
  watch(() => userStore.userInfo, (userInfo) => {
    if (userInfo?.tenant?.industryType) {
      const recommendedTheme = getIndustryTheme(userInfo.tenant.industryType)
      if (recommendedTheme !== currentTheme.value) {
        // 只有在用户没有手动选择主题时才自动应用
        const hasCustomTheme = localStorage.getItem('theme')
        if (!hasCustomTheme) {
          autoApplyTheme(userInfo.tenant.industryType)
        }
      }
    }
  }, { immediate: true })
  
  // 组件挂载时初始化
  onMounted(() => {
    currentTheme.value = themeManager.getCurrentTheme()
    currentColorScheme.value = themeManager.getCurrentColorScheme()
  })
  
  return {
    // 状态
    currentTheme: readonly(currentTheme),
    currentColorScheme: readonly(currentColorScheme),
    availableThemes: readonly(availableThemes),
    themeInfo: readonly(themeInfo),
    isIndustryTheme: readonly(isIndustryTheme),
    
    // 方法
    applyTheme,
    toggleColorScheme,
    autoApplyTheme,
    getIndustryTheme,
    preloadTheme
  }
}

/**
 * 主题样式类组合式函数
 */
export function useThemeClasses() {
  const { currentTheme, currentColorScheme } = useTheme()
  
  const themeClasses = computed(() => {
    return [
      `theme-${currentTheme.value}`,
      currentColorScheme.value
    ]
  })
  
  const getThemeClass = (prefix: string = '') => {
    const base = prefix ? `${prefix}-` : ''
    return `${base}theme-${currentTheme.value}`
  }
  
  const getColorSchemeClass = (prefix: string = '') => {
    const base = prefix ? `${prefix}-` : ''
    return `${base}${currentColorScheme.value}`
  }
  
  return {
    themeClasses: readonly(themeClasses),
    getThemeClass,
    getColorSchemeClass
  }
}

/**
 * 响应式主题变量组合式函数
 */
export function useThemeVariables() {
  const { currentTheme, currentColorScheme } = useTheme()
  
  // 获取CSS变量值
  const getCSSVariable = (variableName: string): string => {
    return getComputedStyle(document.documentElement)
      .getPropertyValue(variableName)
      .trim()
  }
  
  // 设置CSS变量值
  const setCSSVariable = (variableName: string, value: string): void => {
    document.documentElement.style.setProperty(variableName, value)
  }
  
  // 获取主题色彩
  const getThemeColors = () => {
    return {
      primary: getCSSVariable('--primary'),
      secondary: getCSSVariable('--secondary'),
      accent: getCSSVariable('--accent'),
      background: getCSSVariable('--background'),
      foreground: getCSSVariable('--foreground'),
      border: getCSSVariable('--border'),
      muted: getCSSVariable('--muted')
    }
  }
  
  // 获取行业特定变量
  const getIndustryVariables = () => {
    if (currentTheme.value === 'medical-beauty') {
      return {
        gradientStart: getCSSVariable('--medical-gradient-start'),
        gradientEnd: getCSSVariable('--medical-gradient-end'),
        cardBg: getCSSVariable('--medical-card-bg'),
        border: getCSSVariable('--medical-border')
      }
    } else if (currentTheme.value === 'real-estate') {
      return {
        gradientStart: getCSSVariable('--estate-gradient-start'),
        gradientEnd: getCSSVariable('--estate-gradient-end'),
        cardBg: getCSSVariable('--estate-card-bg'),
        border: getCSSVariable('--estate-border')
      }
    }
    return {}
  }
  
  return {
    getCSSVariable,
    setCSSVariable,
    getThemeColors,
    getIndustryVariables
  }
}

/**
 * 主题过渡动画组合式函数
 */
export function useThemeTransition() {
  const isTransitioning = ref(false)
  
  const startThemeTransition = async (newTheme: string, colorScheme?: 'light' | 'dark') => {
    if (isTransitioning.value) return
    
    isTransitioning.value = true
    
    // 添加过渡类
    document.documentElement.classList.add('theme-transitioning')
    
    try {
      // 应用新主题
      themeManager.applyTheme(newTheme, colorScheme)
      
      // 等待过渡完成
      await new Promise(resolve => setTimeout(resolve, 300))
    } finally {
      // 移除过渡类
      document.documentElement.classList.remove('theme-transitioning')
      isTransitioning.value = false
    }
  }
  
  return {
    isTransitioning: readonly(isTransitioning),
    startThemeTransition
  }
}

// 添加主题过渡样式
const themeTransitionStyle = `
.theme-transitioning * {
  transition: background-color 0.3s ease, 
              border-color 0.3s ease, 
              color 0.3s ease !important;
}
`

// 注入样式
if (typeof document !== 'undefined') {
  const style = document.createElement('style')
  style.textContent = themeTransitionStyle
  document.head.appendChild(style)
}