import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { 
  createPageTransition, 
  createStaggerAnimation, 
  createParallaxEffect, 
  createScrollAnimation,
  getAnimationsByIndustry,
  type PageTransitionConfig,
  type CardAnimationConfig,
  type ButtonAnimationConfig 
} from '@/utils/animations/pageTransitions'
import type { IndustryType } from '@/utils/tenant-constants'

/**
 * 页面转换动画组合式函数
 */
export function usePageTransition(industryType?: IndustryType) {
  const transitionConfig = ref<PageTransitionConfig>()
  
  onMounted(() => {
    if (industryType) {
      const animations = getAnimationsByIndustry(industryType)
      transitionConfig.value = animations.page
    }
  })
  
  const getTransition = () => {
    if (!transitionConfig.value) return {}
    return createPageTransition(transitionConfig.value)
  }
  
  return {
    transitionConfig: readonly(transitionConfig),
    getTransition
  }
}

/**
 * 卡片动画组合式函数
 */
export function useCardAnimation(industryType?: IndustryType) {
  const animationConfig = ref<CardAnimationConfig>()
  const isAnimating = ref(false)
  
  onMounted(() => {
    if (industryType) {
      const animations = getAnimationsByIndustry(industryType)
      animationConfig.value = animations.card
    }
  })
  
  const animateCards = async (selector: string) => {
    if (!animationConfig.value || isAnimating.value) return
    
    await nextTick()
    const elements = document.querySelectorAll(selector)
    if (elements.length === 0) return
    
    isAnimating.value = true
    createStaggerAnimation(elements, animationConfig.value)
    
    // 等待动画完成
    const totalDuration = elements.length * animationConfig.value.stagger + 1000
    setTimeout(() => {
      isAnimating.value = false
    }, totalDuration)
  }
  
  const getCardClasses = () => {
    if (!animationConfig.value) return []
    return [
      animationConfig.value.hover,
      animationConfig.value.click
    ]
  }
  
  return {
    animationConfig: readonly(animationConfig),
    isAnimating: readonly(isAnimating),
    animateCards,
    getCardClasses
  }
}

/**
 * 按钮动画组合式函数
 */
export function useButtonAnimation(industryType?: IndustryType) {
  const animationConfig = ref<ButtonAnimationConfig>()
  
  onMounted(() => {
    if (industryType) {
      const animations = getAnimationsByIndustry(industryType)
      animationConfig.value = animations.button
    }
  })
  
  const getButtonClasses = (isLoading: boolean = false) => {
    if (!animationConfig.value) return []
    
    const classes = [
      animationConfig.value.hover,
      animationConfig.value.click
    ]
    
    if (isLoading) {
      classes.push(animationConfig.value.loading)
    }
    
    return classes
  }
  
  return {
    animationConfig: readonly(animationConfig),
    getButtonClasses
  }
}

/**
 * 滚动动画组合式函数
 */
export function useScrollAnimation() {
  const observers = ref<IntersectionObserver[]>([])
  const isReducedMotion = ref(false)
  
  onMounted(() => {
    // 检测用户是否偏好减少动画
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    isReducedMotion.value = mediaQuery.matches
    
    mediaQuery.addEventListener('change', (e) => {
      isReducedMotion.value = e.matches
    })
  })
  
  onUnmounted(() => {
    // 清理所有观察器
    observers.value.forEach(observer => observer.disconnect())
    observers.value = []
  })
  
  const observeElement = (
    element: HTMLElement, 
    animationClass: string, 
    threshold: number = 0.1
  ) => {
    if (isReducedMotion.value) {
      // 如果用户偏好减少动画，直接添加最终状态的类
      element.classList.add(animationClass)
      return
    }
    
    const observer = createScrollAnimation(element, animationClass, threshold)
    observers.value.push(observer)
    return observer
  }
  
  const observeElements = (
    selector: string, 
    animationClass: string, 
    threshold: number = 0.1
  ) => {
    const elements = document.querySelectorAll<HTMLElement>(selector)
    elements.forEach(element => {
      observeElement(element, animationClass, threshold)
    })
  }
  
  return {
    isReducedMotion: readonly(isReducedMotion),
    observeElement,
    observeElements
  }
}

/**
 * 视差滚动组合式函数
 */
export function useParallax() {
  const parallaxElements = ref<Map<HTMLElement, IntersectionObserver>>(new Map())
  const isReducedMotion = ref(false)
  
  onMounted(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    isReducedMotion.value = mediaQuery.matches
    
    mediaQuery.addEventListener('change', (e) => {
      isReducedMotion.value = e.matches
      if (e.matches) {
        // 如果启用了减少动画，清除所有视差效果
        clearAllParallax()
      }
    })
  })
  
  onUnmounted(() => {
    clearAllParallax()
  })
  
  const addParallax = (element: HTMLElement, speed: number = 0.5) => {
    if (isReducedMotion.value) return
    
    const observer = createParallaxEffect(element, speed)
    parallaxElements.value.set(element, observer)
    return observer
  }
  
  const removeParallax = (element: HTMLElement) => {
    const observer = parallaxElements.value.get(element)
    if (observer) {
      observer.disconnect()
      parallaxElements.value.delete(element)
    }
  }
  
  const clearAllParallax = () => {
    parallaxElements.value.forEach((observer, element) => {
      observer.disconnect()
      // 重置元素transform
      element.style.transform = ''
    })
    parallaxElements.value.clear()
  }
  
  return {
    isReducedMotion: readonly(isReducedMotion),
    addParallax,
    removeParallax,
    clearAllParallax
  }
}

/**
 * 计数动画组合式函数
 */
export function useCountUp() {
  const animateNumber = (
    element: HTMLElement,
    from: number,
    to: number,
    duration: number = 2000,
    formatter?: (value: number) => string
  ) => {
    const startTime = performance.now()
    const difference = to - from
    
    const updateNumber = (currentTime: number) => {
      const elapsedTime = currentTime - startTime
      const progress = Math.min(elapsedTime / duration, 1)
      
      // 使用缓动函数
      const easedProgress = easeOutCubic(progress)
      const currentValue = from + (difference * easedProgress)
      
      const displayValue = formatter 
        ? formatter(currentValue) 
        : Math.round(currentValue).toString()
      
      element.textContent = displayValue
      
      if (progress < 1) {
        requestAnimationFrame(updateNumber)
      }
    }
    
    requestAnimationFrame(updateNumber)
  }
  
  // 缓动函数
  const easeOutCubic = (t: number): number => {
    return 1 - Math.pow(1 - t, 3)
  }
  
  return {
    animateNumber
  }
}

/**
 * 元素进入动画组合式函数
 */
export function useEnterAnimation() {
  const animatedElements = ref<Set<HTMLElement>>(new Set())
  
  const addEnterAnimation = (
    element: HTMLElement,
    animationClass: string,
    delay: number = 0
  ) => {
    if (animatedElements.value.has(element)) return
    
    const applyAnimation = () => {
      element.style.opacity = '0'
      element.style.transform = 'translateY(20px)'
      
      setTimeout(() => {
        element.style.transition = 'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)'
        element.style.opacity = '1'
        element.style.transform = 'translateY(0)'
        element.classList.add(animationClass)
        
        animatedElements.value.add(element)
      }, delay)
    }
    
    // 检查元素是否在视口中
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          applyAnimation()
          observer.unobserve(entry.target)
        }
      })
    }, {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    })
    
    observer.observe(element)
    return observer
  }
  
  const addBatchEnterAnimation = (
    selector: string,
    animationClass: string,
    staggerDelay: number = 100
  ) => {
    const elements = document.querySelectorAll<HTMLElement>(selector)
    elements.forEach((element, index) => {
      addEnterAnimation(element, animationClass, index * staggerDelay)
    })
  }
  
  return {
    addEnterAnimation,
    addBatchEnterAnimation
  }
}

/**
 * 交互反馈动画组合式函数
 */
export function useInteractionFeedback() {
  const createRipple = (event: MouseEvent, element: HTMLElement) => {
    const rect = element.getBoundingClientRect()
    const size = Math.max(rect.width, rect.height)
    const x = event.clientX - rect.left - size / 2
    const y = event.clientY - rect.top - size / 2
    
    const ripple = document.createElement('div')
    ripple.style.cssText = `
      position: absolute;
      width: ${size}px;
      height: ${size}px;
      left: ${x}px;
      top: ${y}px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      transform: scale(0);
      animation: ripple-effect 0.6s ease-out;
      pointer-events: none;
      z-index: 1000;
    `
    
    element.style.position = 'relative'
    element.style.overflow = 'hidden'
    element.appendChild(ripple)
    
    setTimeout(() => {
      ripple.remove()
    }, 600)
  }
  
  const addClickFeedback = (element: HTMLElement) => {
    element.addEventListener('click', (event) => {
      createRipple(event, element)
      
      // 添加点击缩放效果
      element.style.transform = 'scale(0.98)'
      setTimeout(() => {
        element.style.transform = ''
      }, 100)
    })
  }
  
  const addHoverFeedback = (element: HTMLElement, scale: number = 1.02) => {
    element.addEventListener('mouseenter', () => {
      element.style.transform = `scale(${scale})`
    })
    
    element.addEventListener('mouseleave', () => {
      element.style.transform = ''
    })
  }
  
  return {
    createRipple,
    addClickFeedback,
    addHoverFeedback
  }
}

// 导出统一的动画工具集
export function useAnimations(industryType?: IndustryType) {
  const pageTransition = usePageTransition(industryType)
  const cardAnimation = useCardAnimation(industryType)
  const buttonAnimation = useButtonAnimation(industryType)
  const scrollAnimation = useScrollAnimation()
  const parallax = useParallax()
  const countUp = useCountUp()
  const enterAnimation = useEnterAnimation()
  const interactionFeedback = useInteractionFeedback()
  
  return {
    pageTransition,
    cardAnimation,
    buttonAnimation,
    scrollAnimation,
    parallax,
    countUp,
    enterAnimation,
    interactionFeedback
  }
}