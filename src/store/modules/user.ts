import type { Settings } from '#/global'
import type { LoginRequest, LoginResponse } from '@/api/modules/auth'
import apiAuth from '@/api/modules/auth'
import apiUser from '@/api/modules/user'
import router from '@/router'
import settingsDefault from '@/settings'
import { roleRouteMap } from '@/settings'
import eventBus from '@/utils/eventBus'
import { diffTwoObj, mergeWithoutUndefinedProps } from '@/utils/object'
import storage from '@/utils/storage'
import { cloneDeep } from 'es-toolkit'
import useMenuStore from './menu'
import useRouteStore from './route'
import useSettingsStore from './settings'
import useTabbarStore from './tabbar'
import apiOnboarding from '@/api/modules/onboarding'

/**
 * 根据用户角色计算跳转路径
 * @param roles 用户角色数组
 * @returns 跳转路径，如果没有匹配的角色则返回默认首页路径
 */
function calculateRoleRedirectPath(roles: string[]): string {
  // console.log('🎯 [角色跳转] 开始计算角色跳转路径')
  // console.log('📋 [角色跳转] 输入角色数组:', roles)
  // console.log('🗺️ [角色跳转] 可用角色映射表:', roleRouteMap)

  if (!roles || roles.length === 0) {
    // console.log('⚠️ [角色跳转] 角色数组为空或未定义')
    // console.log('🏠 [角色跳转] 返回默认首页路径: /')
    return '/'
  }

  // console.log(`📊 [角色跳转] 角色数组长度: ${roles.length}`)

  // 遍历所有角色，找到第一个匹配的映射
  for (let i = 0; i < roles.length; i++) {
    const role = roles[i]
    // console.log(`🔍 [角色跳转] 检查角色 [${i}]: "${role}"`)

    const redirectRoute = roleRouteMap[role]
    if (redirectRoute) {
      // console.log(`✅ [角色跳转] 找到匹配的角色映射`)
      // console.log(`   - 角色代码: "${role}"`)
      // console.log(`   - 映射路径: "${redirectRoute}"`)
      // console.log(`🚀 [角色跳转] 返回角色对应路径: ${redirectRoute}`)
      return redirectRoute
    } else {
      // console.log(`❌ [角色跳转] 角色 "${role}" 在映射表中未找到`)
    }
  }

  // console.log('⚠️ [角色跳转] 所有角色都未找到匹配的映射')
  // console.log('🏠 [角色跳转] 返回默认首页路径: /')
  return '/'
}

const useUserStore = defineStore(
  // 唯一ID
  'user',
  () => {
    const settingsStore = useSettingsStore()
    const tabbarStore = useTabbarStore()
    const routeStore = useRouteStore()
    const menuStore = useMenuStore()

    const account = ref(storage.local.get('account') ?? '')
    const token = ref(storage.local.get('token') ?? '')
    const refreshToken = ref(storage.local.get('refreshToken') ?? '')
    const avatar = ref(storage.local.get('avatar') ?? '')
    const userId = ref<number | null>(Number(storage.local.get('userId')) || null)
    const username = ref(storage.local.get('username') ?? '')
    const realName = ref(storage.local.get('realName') ?? '')
    const tenantId = ref<number | null>(Number(storage.local.get('tenantId')) || null)
    const tenantName = ref(storage.local.get('tenantName') ?? '')
    const permissions = ref<string[]>([])
    const firstLoginCompleted = ref(storage.local.get('firstLoginCompleted') === 'true')
    const userAgreementAccepted = ref(storage.local.get('userAgreementAccepted') === 'true')
    const industryType = ref(storage.local.get('tenantIndustryType') ?? '')
    const tenantType = ref(storage.local.get('tenantType') ?? '')
    const roles = ref<string[]>(storage.local.get('roles') as string[] ?? [])
    const perms = ref<string[]>(storage.local.get('perms') as string[] ?? [])
    const vertexId = ref(storage.local.get('vertexId') ?? '')
    const orgCategoryTierId = ref(storage.local.get('orgCategoryTierId') ?? '')
    const deptId = ref(storage.local.get('deptId') ?? '')
    const roleId = ref(storage.local.get('roleId') ?? '') // yoda

    const isLogin = computed(() => {
      if (token.value) {
        return true
      }
      return false
    })

    // 登录
    async function login(data: {
      account: string
      password: string
    }) {
      // console.log('🚀 [用户Store] 开始执行登录请求')
      // console.log('📝 [用户Store] 登录账号:', data.account)

      try {
        const loginData: LoginRequest = {
          loginId: data.account,
          password: data.password,
        }
        // console.log('📡 [用户Store] 发送登录API请求...')
        const res = await apiAuth.login(loginData)
        // console.log('📥 [用户Store] 收到登录API响应')

        // 根据实际响应结构访问数据
        const responseData = res.data.data || res.data
        // console.log('🔍 [用户Store] 解析响应数据结构完成')
        // console.log('👤 [用户Store] 后端返回的用户信息:')
        // console.log(`   - 用户ID: ${responseData.userId}`)
        // console.log(`   - 用户名: ${responseData.username}`)
        // console.log(`   - 真实姓名: ${responseData.realName}`)
        // console.log(`   - 租户ID: ${responseData.tenantId}`)
        // console.log(`   - 租户名称: ${responseData.tenantName}`)
        // console.log(`   - 用户角色: ${JSON.stringify(responseData.roles)}`)
        // console.log(`   - 用户权限: ${JSON.stringify(responseData.perms)}`)

        // console.log('💾 [用户Store] 开始保存用户信息到本地存储...')
        // 存储用户信息
        storage.local.set('account', data.account)
        storage.local.set('token', responseData.accessToken)
        storage.local.set('refreshToken', responseData.refreshToken)
        storage.local.set('avatar', responseData.avatar)
        storage.local.set('userId', String(responseData.userId))
        storage.local.set('username', responseData.username)
        storage.local.set('realName', responseData.realName)
        storage.local.set('tenantId', String(responseData.tenantId))
        storage.local.set('tenantName', responseData.tenantName)
        storage.local.set('roles', responseData.roles)
        storage.local.set('perms', responseData.perms)

        // 存储引导相关信息 - 确保正确处理布尔值
        const isFirstLoginCompleted = Boolean(responseData.firstLoginCompleted)
        const isUserAgreementAccepted = Boolean(responseData.userAgreementAccepted)
        storage.local.set('firstLoginCompleted', String(isFirstLoginCompleted))
        storage.local.set('userAgreementAccepted', String(isUserAgreementAccepted))
        storage.local.set('tenantIndustryType', responseData.industryType || '')
        storage.local.set('tenantType', responseData.tenantType || '')

        // console.log('🔐 [用户Store] 后端返回的 firstLoginCompleted:', responseData.firstLoginCompleted)
        // console.log('🔐 [用户Store] 处理后的 isFirstLoginCompleted:', isFirstLoginCompleted)

        // console.log('📊 [用户Store] 开始更新响应式状态...')
        // 更新状态
        account.value = data.account
        token.value = responseData.accessToken
        refreshToken.value = responseData.refreshToken
        avatar.value = responseData.avatar
        userId.value = responseData.userId
        username.value = responseData.username
        realName.value = responseData.realName
        tenantId.value = responseData.tenantId
        tenantName.value = responseData.tenantName
        firstLoginCompleted.value = isFirstLoginCompleted
        userAgreementAccepted.value = isUserAgreementAccepted
        industryType.value = responseData.industryType
        tenantType.value = responseData.tenantType
        roles.value = responseData.roles
        perms.value = responseData.perms

        await setUserInfo(responseData.username)

        // console.log('✅ [用户Store] 用户登录处理完成')
        // console.log('🎭 [用户Store] 最终设置的用户角色:', JSON.stringify(roles.value))

        return res.data
      }
      catch (error) {
        console.error('❌ [用户Store] 登录失败:', error)
        throw error
      }
    }

    async function setUserInfo(username: string) {
      const userInfo = await apiAuth.getUserInfo(username)
      storage.local.set('vertexId', userInfo.data.vertexId)
      storage.local.set('orgCategoryTierId', userInfo.data.orgCategoryTierId)
      storage.local.set('deptId', userInfo.data.deptId)
      storage.local.set('roleId', userInfo.data.roleId) // yoda

      vertexId.value = userInfo.data.vertexId
      orgCategoryTierId.value = userInfo.data.orgCategoryTierId
      deptId.value = userInfo.data.deptId
      roleId.value = userInfo.data.roleId
    }

    // 手动登出
    function logout(redirect = router.currentRoute.value.fullPath) {
      // 此处仅清除计算属性 isLogin 中判断登录状态过期的变量，以保证在弹出登录窗口模式下页面展示依旧正常
      storage.local.remove('token')
      token.value = ''
      router.push({
        name: 'login',
        query: {
          ...(redirect !== settingsStore.settings.home.fullPath && router.currentRoute.value.name !== 'login' && { redirect }),
        },
      }).then(logoutCleanStatus)
    }
    // 请求登出
    function requestLogout() {
      // 此处仅清除计算属性 isLogin 中判断登录状态过期的变量，以保证在弹出登录窗口模式下页面展示依旧正常
      storage.local.remove('token')
      token.value = ''
      if (settingsStore.settings.app.loginExpiredMode === 'redirect') {
        router.push({
          name: 'login',
          query: {
            ...(
              router.currentRoute.value.fullPath !== settingsStore.settings.home.fullPath
              && router.currentRoute.value.name !== 'login'
              && {
                redirect: router.currentRoute.value.fullPath,
              }
            ),
          },
        }).then(logoutCleanStatus)
      }
      else {
        eventBus.emit('global-login-again-visible')
      }
    }
    // 登出后清除状态
    function logoutCleanStatus() {
      storage.local.remove('account')
      storage.local.remove('refreshToken')
      storage.local.remove('avatar')
      storage.local.remove('userId')
      storage.local.remove('username')
      storage.local.remove('realName')
      storage.local.remove('tenantId')
      storage.local.remove('tenantName')
      storage.local.remove('roles')
      storage.local.remove('perms')
      storage.local.remove('vertexId')
      storage.local.remove('orgCategoryTierId')
      storage.local.remove('deptId')
      storage.local.remove('roleId')

      // 清除引导相关信息
      storage.local.remove('firstLoginCompleted')
      storage.local.remove('userAgreementAccepted')
      storage.local.remove('tenantIndustryType')
      storage.local.remove('tenantType')
      account.value = ''
      refreshToken.value = ''
      avatar.value = ''
      userId.value = null
      username.value = ''
      realName.value = ''
      tenantId.value = null
      tenantName.value = ''
      firstLoginCompleted.value = false
      userAgreementAccepted.value = false
      industryType.value = ''
      tenantType.value = ''
      permissions.value = []
      roles.value = []
      perms.value = []
      vertexId.value = ''
      orgCategoryTierId.value = ''
      deptId.value = ''
      roleId.value = ''

      settingsStore.updateSettings({}, true)
      tabbarStore.clean()
      routeStore.removeRoutes()
      menuStore.setActived(0)
    }

    // 获取权限
    async function getPermissions() {
      const res = await apiUser.permission()
      permissions.value = res.data.permissions
    }

    async function rolesAndPerms() {
      if (userId.value) {
        const res = await apiAuth.getRolesAndPerms(userId.value);
        const responseData = res.data

        storage.local.set('roles', responseData.roles)
        storage.local.set('perms', responseData.perms)

        roles.value = responseData.roles
        perms.value = responseData.perms
      }
    }

    // 修改密码
    async function editPassword(data: {
      password: string
      newPassword: string
    }) {
      await apiUser.passwordEdit(data)
    }

    // 框架已将可提供给用户配置的选项提取出来，请勿新增其他选项，不需要的选项可以在这里注释掉
    const preferences = ref<Settings.all>({
      app: {
        themeSync: settingsDefault.app.themeSync,
        colorScheme: settingsDefault.app.colorScheme,
        lightTheme: settingsDefault.app.lightTheme,
        darkTheme: settingsDefault.app.darkTheme,
        radius: settingsDefault.app.radius,
        enableColorAmblyopiaMode: settingsDefault.app.enableColorAmblyopiaMode,
        enableProgress: settingsDefault.app.enableProgress,
        defaultLang: settingsDefault.app.defaultLang,
      },
      menu: {
        mode: settingsDefault.menu.mode,
        style: settingsDefault.menu.style,
        mainMenuClickMode: settingsDefault.menu.mainMenuClickMode,
        subMenuUniqueOpened: settingsDefault.menu.subMenuUniqueOpened,
        subMenuCollapse: settingsDefault.menu.subMenuCollapse,
        subMenuAutoCollapse: settingsDefault.menu.subMenuAutoCollapse,
        enableSubMenuCollapseButton: settingsDefault.menu.enableSubMenuCollapseButton,
      },
      layout: {
        widthMode: settingsDefault.layout.widthMode,
        widthModeScope: settingsDefault.layout.widthModeScope,
      },
      mainPage: {
        enableTransition: settingsDefault.mainPage.enableTransition,
        transitionMode: settingsDefault.mainPage.transitionMode,
      },
      topbar: {
        mode: settingsDefault.topbar.mode,
        switchTabbarAndToolbar: settingsDefault.topbar.switchTabbarAndToolbar,
      },
      tabbar: {
        style: settingsDefault.tabbar.style,
        enableIcon: settingsDefault.tabbar.enableIcon,
        dblclickAction: settingsDefault.tabbar.dblclickAction,
        enableMemory: settingsDefault.tabbar.enableMemory,
      },
      toolbar: {
        breadcrumb: settingsDefault.toolbar.breadcrumb,
        navSearch: settingsDefault.toolbar.navSearch,
        fullscreen: settingsDefault.toolbar.fullscreen,
        pageReload: settingsDefault.toolbar.pageReload,
        colorScheme: settingsDefault.toolbar.colorScheme,
        layout: settingsDefault.toolbar.layout,
      },
      breadcrumb: {
        style: settingsDefault.breadcrumb.style,
        enableMainMenu: settingsDefault.breadcrumb.enableMainMenu,
      },
    })
    // isPreferencesUpdating 用于防止循环更新
    let isPreferencesUpdating = false
    watch(() => settingsStore.settings, (val) => {
      if (!settingsStore.settings.userPreferences.enable) {
        return
      }
      if (!isPreferencesUpdating) {
        isPreferencesUpdating = true
        preferences.value = mergeWithoutUndefinedProps(val, preferences.value)
      }
      else {
        isPreferencesUpdating = false
      }
    }, {
      deep: true,
    })
    watch(preferences, (val) => {
      if (!settingsStore.settings.userPreferences.enable) {
        return
      }
      if (!isPreferencesUpdating) {
        isPreferencesUpdating = true
        settingsStore.updateSettings(cloneDeep(val))
      }
      else {
        isPreferencesUpdating = false
      }
      updatePreferences(cloneDeep(val))
    }, {
      deep: true,
    })
    // isPreferencesInited 用于防止初始化时触发更新
    let isPreferencesInited = false
    // 获取偏好设置
    async function getPreferences() {
      let data: Settings.all = {}
      if (settingsStore.settings.userPreferences.storageTo === 'local') {
        if (storage.local.has('userPreferences')) {
          data = JSON.parse(storage.local.get('userPreferences') as string)[account.value] || {}
        }
      }
      else if (settingsStore.settings.userPreferences.storageTo === 'server') {
        const res = await apiUser.preferences()
        data = JSON.parse(res.data.preferences || '{}') as Settings.all
      }
      preferences.value = mergeWithoutUndefinedProps(data, preferences.value)
    }
    // 更新偏好设置
    async function updatePreferences(data: Settings.all = {}) {
      if (!isPreferencesInited) {
        isPreferencesInited = true
        return
      }
      if (!isLogin.value) {
        return
      }
      data = diffTwoObj(settingsDefault, data)
      if (settingsStore.settings.userPreferences.storageTo === 'local') {
        const userPreferencesData = storage.local.has('userPreferences') ? JSON.parse(storage.local.get('userPreferences') as string) : {}
        userPreferencesData[account.value] = data
        storage.local.set('userPreferences', JSON.stringify(userPreferencesData))
      }
      else if (settingsStore.settings.userPreferences.storageTo === 'server') {
        await apiUser.preferencesEdit(JSON.stringify(data))
      }
    }

    // 完成引导页面
    async function completeOnboarding() {
      try {
        await apiOnboarding.completeOnboarding();
        firstLoginCompleted.value = true;
        storage.local.set('firstLoginCompleted', 'true');
      } catch (error) {
        console.error('Failed to mark onboarding as complete:', error);
        throw error;
      }
    }

    // 接受用户协议
    async function acceptUserAgreement() {
      try {
        await apiUser.acceptUserAgreement();
        userAgreementAccepted.value = true;
        storage.local.set('userAgreementAccepted', 'true');
      } catch (error) {
        console.error('Failed to accept user agreement:', error);
        throw error;
      }
    }

    // 重新同步用户引导状态（从后端获取最新状态）
    async function syncOnboardingStatus() {
      try {
        //console.log('🔄 [用户状态] 开始同步引导状态...');

        // 通过重新登录获取最新的用户状态（使用当前 token 刷新）
        if (token.value && refreshToken.value) {
          const refreshRes = await apiAuth.refreshToken(refreshToken.value);
          const responseData = refreshRes.data.data || refreshRes.data;

          // 更新引导状态
          const latestFirstLoginCompleted = responseData.firstLoginCompleted || false;
          //console.log('🔄 [用户状态] 后端最新引导状态:', latestFirstLoginCompleted);
          //console.log('🔄 [用户状态] 前端当前引导状态:', firstLoginCompleted.value);

          if (latestFirstLoginCompleted !== firstLoginCompleted.value) {
            //console.log('🔄 [用户状态] 状态不一致，更新前端状态');
            firstLoginCompleted.value = latestFirstLoginCompleted;
            storage.local.set('firstLoginCompleted', String(latestFirstLoginCompleted));
          } else {
            //console.log('🔄 [用户状态] 状态一致，无需更新');
          }
        }
      } catch (error) {
        console.error('❌ [用户状态] 同步引导状态失败:', error);
        // 即使同步失败也不抛出错误，避免影响其他流程
      }
    }

    return {
      account,
      token,
      refreshToken,
      avatar,
      userId,
      username,
      realName,
      tenantId,
      tenantName,
      permissions,
      firstLoginCompleted,
      userAgreementAccepted,
      industryType,
      tenantType,
      roles,
      perms,
      vertexId,
      orgCategoryTierId,
      deptId,
      roleId,
      isLogin,
      login,
      logout,
      requestLogout,
      logoutCleanStatus,
      getPermissions,
      rolesAndPerms,
      editPassword,
      preferences,
      getPreferences,
      updatePreferences,
      completeOnboarding,
      acceptUserAgreement,
      syncOnboardingStatus,
      calculateRoleRedirectPath: () => calculateRoleRedirectPath(roles.value),
    }
  },
)
export { useUserStore }

export default useUserStore
