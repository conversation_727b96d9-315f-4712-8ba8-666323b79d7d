/**
 * 计费相关常量定义
 */

import type { BillingType, DeductionSource } from '../types/billing'

/**
 * 计费类型显示文本映射
 */
export const BILLING_TYPE_DISPLAY_MAP: Record<BillingType, string> = {
  TOKEN_BASED: '正常计费',
  FAILED: '失败记录',
  MANUAL: '手动计费',
  NO_CHARGE_INSUFFICIENT_BALANCE: '余额不足(免费)',
  NO_CHARGE_NO_RESULT: '无结果(免费)',
  NO_CHARGE_TEMPLATE_NO_DATA: '模板无数据(免费)',
  NO_CHARGE_SYSTEM_MESSAGE: '系统消息(免费)',
}

/**
 * 计费类型颜色映射
 */
export const BILLING_TYPE_COLOR_MAP: Record<BillingType, string> = {
  TOKEN_BASED: 'blue',
  FAILED: 'red',
  MANUAL: 'orange',
  NO_CHARGE_INSUFFICIENT_BALANCE: 'green',
  NO_CHARGE_NO_RESULT: 'green',
  NO_CHARGE_TEMPLATE_NO_DATA: 'green',
  NO_CHARGE_SYSTEM_MESSAGE: 'green',
}

/**
 * 扣除来源显示文本映射
 */
export const DEDUCTION_SOURCE_DISPLAY_MAP: Record<DeductionSource, string> = {
  FREE_TOKENS_ONLY: '仅免费Token',
  GIFT_BALANCE_ONLY: '仅赠送余额',
  RECHARGED_BALANCE_ONLY: '仅充值余额',
  MIXED_DEDUCTION: '混合扣除',
}

/**
 * 扣除来源颜色映射
 */
export const DEDUCTION_SOURCE_COLOR_MAP: Record<DeductionSource, string> = {
  FREE_TOKENS_ONLY: 'cyan',
  GIFT_BALANCE_ONLY: 'purple',
  RECHARGED_BALANCE_ONLY: 'blue',
  MIXED_DEDUCTION: 'orange',
}

/**
 * 计费类型常量类
 */
export class BillingConstants {
  /**
   * 获取计费类型显示文本
   * @param billingType 计费类型
   * @returns 显示文本
   */
  static getBillingTypeDisplay(billingType: string | null | undefined): string {
    if (!billingType) {
      return '未知类型'
    }
    const type = billingType as BillingType
    return BILLING_TYPE_DISPLAY_MAP[type] || billingType
  }

  /**
   * 获取计费类型颜色
   * @param billingType 计费类型
   * @returns 颜色值
   */
  static getBillingTypeColor(billingType: string | null | undefined): string {
    if (!billingType) {
      return 'default'
    }
    const type = billingType as BillingType
    if (billingType?.startsWith('NO_CHARGE_')) {
      return 'green'
    }
    return BILLING_TYPE_COLOR_MAP[type] || 'default'
  }

  /**
   * 检查是否为免费计费类型
   * @param billingType 计费类型
   * @returns 是否为免费类型
   */
  static isNoChargeType(billingType: string): boolean {
    return billingType?.startsWith('NO_CHARGE_') || false
  }

  /**
   * 获取所有计费类型
   * @returns 计费类型数组
   */
  static getAllBillingTypes(): BillingType[] {
    return Object.keys(BILLING_TYPE_DISPLAY_MAP) as BillingType[]
  }

  /**
   * 获取计费类型选项（用于下拉选择）
   * @returns 选项数组
   */
  static getBillingTypeOptions() {
    return Object.entries(BILLING_TYPE_DISPLAY_MAP).map(([value, label]) => ({
      value,
      label,
    }))
  }

  /**
   * 获取扣除来源显示文本
   * @param deductionSource 扣除来源
   * @returns 显示文本
   */
  static getDeductionSourceDisplay(deductionSource: string | null | undefined): string {
    if (!deductionSource) {
      return ''
    }
    const source = deductionSource as DeductionSource
    return DEDUCTION_SOURCE_DISPLAY_MAP[source] || deductionSource
  }

  /**
   * 获取扣除来源颜色
   * @param deductionSource 扣除来源
   * @returns 颜色值
   */
  static getDeductionSourceColor(deductionSource: string | null | undefined): string {
    if (!deductionSource) {
      return 'default'
    }
    const source = deductionSource as DeductionSource
    return DEDUCTION_SOURCE_COLOR_MAP[source] || 'default'
  }

  /**
   * 获取组合显示文本（计费类型 + 扣除来源）
   * @param billingType 计费类型
   * @param deductionSource 扣除来源
   * @returns 组合显示文本
   */
  static getCombinedDisplay(billingType: string | null | undefined, deductionSource: string | null | undefined): string {
    const billingDisplay = this.getBillingTypeDisplay(billingType)
    const deductionDisplay = this.getDeductionSourceDisplay(deductionSource)

    if (deductionDisplay && deductionDisplay.trim()) {
      return `${billingDisplay} (${deductionDisplay})`
    }
    return billingDisplay
  }

  /**
   * 获取所有扣除来源类型
   * @returns 扣除来源类型数组
   */
  static getAllDeductionSources(): DeductionSource[] {
    return Object.keys(DEDUCTION_SOURCE_DISPLAY_MAP) as DeductionSource[]
  }

  /**
   * 获取扣除来源选项（用于下拉选择）
   * @returns 选项数组
   */
  static getDeductionSourceOptions() {
    return Object.entries(DEDUCTION_SOURCE_DISPLAY_MAP).map(([value, label]) => ({
      value,
      label,
    }))
  }
}
