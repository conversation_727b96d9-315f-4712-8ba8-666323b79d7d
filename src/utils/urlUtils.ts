/**
 * URL 工具类
 * 主要用于处理URL相关的操作，包括协议转换等
 */

/**
 * 将HTTP URL转换为HTTPS URL
 * 用于解决Mixed Content问题（HTTPS页面加载HTTP资源被浏览器阻止）
 *
 * @param url 原始URL
 * @returns 转换后的HTTPS URL
 */
export function convertToHttps(url: string): string {
  if (!url) {
    return url
  }

  // 如果URL已经是HTTPS，直接返回
  if (url.startsWith('https://')) {
    return url
  }

  // 如果是HTTP，转换为HTTPS
  if (url.startsWith('http://')) {
    return url.replace('http://', 'https://')
  }

  // 如果是相对URL或其他格式，直接返回
  return url
}

/**
 * 确保URL使用HTTPS协议
 * 同 convertToHttps 的别名函数，语义更清晰
 */
export function ensureHttps(url: string): string {
  return convertToHttps(url)
}

/**
 * 检查URL是否为HTTPS协议
 *
 * @param url 要检查的URL
 * @returns 是否为HTTPS
 */
export function isHttpsUrl(url: string): boolean {
  return Boolean(url && url.startsWith('https://'))
}

/**
 * 检查URL是否为HTTP协议
 *
 * @param url 要检查的URL
 * @returns 是否为HTTP
 */
export function isHttpUrl(url: string): boolean {
  return Boolean(url && url.startsWith('http://'))
}

/**
 * 获取URL的协议部分
 *
 * @param url 原始URL
 * @returns 协议部分（如 'https:', 'http:'）
 */
export function getUrlProtocol(url: string): string {
  try {
    const urlObj = new URL(url)
    return urlObj.protocol
  } catch {
    return ''
  }
}

/**
 * 检查当前页面是否为HTTPS
 *
 * @returns 当前页面是否使用HTTPS
 */
export function isCurrentPageHttps(): boolean {
  return window.location.protocol === 'https:'
}

/**
 * 智能转换URL协议
 * 如果当前页面是HTTPS，则将HTTP URL转换为HTTPS
 * 如果当前页面是HTTP，则保持原样
 *
 * @param url 原始URL
 * @returns 转换后的URL
 */
export function smartConvertUrl(url: string): string {
  if (isCurrentPageHttps() && isHttpUrl(url)) {
    return convertToHttps(url)
  }
  return url
}
