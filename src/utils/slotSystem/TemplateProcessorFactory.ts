/**
 * 模板处理器工厂类
 * 负责创建和管理不同类型的模板处理器实例
 */

import type { Ref } from 'vue'
import type { BaseTemplateProcessor } from './BaseTemplateProcessor'
import type { SlotChangeParams, SlotStateUpdateResult, TemplateInitParams } from './types'

// 模板处理器构造函数类型
type ProcessorConstructor = new (templateName: string) => BaseTemplateProcessor

// 模板处理器注册表
const processorRegistry = new Map<string, ProcessorConstructor>()

// 模板处理器实例缓存
const processorCache = new Map<string, BaseTemplateProcessor>()

export class TemplateProcessorFactory {
  /**
   * 注册模板处理器
   * @param templateName 模板名称
   * @param ProcessorClass 处理器类
   */
  static registerProcessor(templateName: string, ProcessorClass: ProcessorConstructor): void {
    processorRegistry.set(templateName, ProcessorClass)
    //console.log(`Template processor registered: ${templateName}`)
  }

  /**
   * 获取模板处理器实例
   * @param templateName 模板名称
   * @returns 模板处理器实例或null
   */
  static getProcessor(templateName: string | Ref<string>): BaseTemplateProcessor | null {
    const name = unref(templateName)

    if (!name) {
      console.warn('Template name is required')
      return null
    }

    // 先从缓存中查找
    if (processorCache.has(name)) {
      return processorCache.get(name)!
    }

    // 从注册表中查找处理器类
    const ProcessorClass = processorRegistry.get(name)
    if (!ProcessorClass) {
      console.warn(`No processor found for template: ${name}`)
      return null
    }

    // 创建新实例并缓存
    const processor = new ProcessorClass(name)
    processorCache.set(name, processor)

    return processor
  }

  /**
   * 初始化模板插槽
   * @param params 初始化参数
   * @returns Promise<SlotStateUpdateResult>
   */
  static async initializeTemplate(params: TemplateInitParams): Promise<SlotStateUpdateResult> {
    const templateName = unref(params.templateName)
    const processor = this.getProcessor(templateName)

    if (!processor) {
      // 如果没有找到对应的处理器，返回默认的插槽状态
      return this.getDefaultSlotState(params)
    }

    try {
      // 设置模板内容和标准参数
      const templateContent = unref(params.templateContent)
      const standardParam = unref(params.standardParam)
      const initialSlotDefinitions = unref(params.initialSlotDefinitions)
      const initialSlotValues = unref(params.initialSlotValues)

      processor.setTemplateContent(templateContent)
      if (standardParam) {
        processor.setStandardParam(standardParam)
      }

      return await processor.initializeSlots({
        templateName,
        templateContent,
        initialSlotDefinitions,
        initialSlotValues,
        standardParam,
      })
    }
    catch (error) {
      console.error(`Failed to initialize template ${templateName}:`, error)

      // 如果初始化失败，返回默认的插槽状态
      return this.getDefaultSlotState(params)
    }
  }

  /**
   * 处理插槽值变化
   * @param templateName 模板名称
   * @param params 插槽变化参数
   * @returns Promise<SlotStateUpdateResult | null>
   */
  static async handleSlotChange(
    templateName: string | Ref<string>,
    params: SlotChangeParams,
  ): Promise<SlotStateUpdateResult | null> {
    const name = unref(templateName)
    const processor = this.getProcessor(name)

    if (!processor) {
      console.warn(`No processor found for template: ${name}`)
      return null
    }

    try {
      // 处理可能的 Ref 类型参数
      const processedParams = {
        ...params,
        standardParam: unref(params.standardParam),
        onSlotValueChange: unref(params.onSlotValueChange),
      }

      return await processor.handleSlotChange(processedParams)
    }
    catch (error) {
      console.error(`Failed to handle slot change for template ${name}:`, error)
      return null
    }
  }

  /**
   * 获取默认插槽状态，用于未注册的模板
   * @param params 初始化参数
   * @returns 默认插槽状态
   */
  private static getDefaultSlotState(params: TemplateInitParams): SlotStateUpdateResult {
    return {
      definitions: { ...unref(params.initialSlotDefinitions) || {} },
      values: { ...unref(params.initialSlotValues) || {} },
    }
  }

  /**
   * 从模板内容中解析插槽名称
   * @param templateContent 模板内容
   * @returns 插槽名称数组
   */
  private static parseSlotNames(templateContent: string | Ref<string>): string[] {
    const content = unref(templateContent)
    if (!content) {
      return []
    }

    const slotMatches = content.match(/\{\{([^{}]+)\}\}/g) || []
    return slotMatches.map(match => match.replace(/[{}]/g, '').trim())
  }

  /**
   * 清除处理器缓存
   * @param templateName 模板名称，如果不提供则清除所有缓存
   */
  static clearCache(templateName?: string): void {
    if (templateName) {
      processorCache.delete(templateName)
    }
    else {
      processorCache.clear()
    }
  }

  /**
   * 获取已注册的模板列表
   * @returns 模板名称数组
   */
  static getRegisteredTemplates(): string[] {
    return Array.from(processorRegistry.keys())
  }

  /**
   * 检查模板是否已注册
   * @param templateName 模板名称
   * @returns 是否已注册
   */
  static isTemplateRegistered(templateName: string): boolean {
    return processorRegistry.has(templateName)
  }
}
