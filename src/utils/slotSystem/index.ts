/**
 * 插槽系统主入口
 * 提供统一的API接口
 */

import type {
  SlotChangeParams,
  SlotDefinition,
  SlotDefinitions,
  SlotStateUpdateResult,
  SlotValue,
  SlotValues,
  TemplateInitParams,
} from './types'
import { IndustryQueryProcessor } from './processors/IndustryQueryProcessor'
import { VisualProcessor } from './processors/VisualProcessor'
import { TemplateProcessorFactory } from './TemplateProcessorFactory'

// 自动注册内置的模板处理器
function registerBuiltinProcessors() {
  // 注册行业数查询处理器
  TemplateProcessorFactory.registerProcessor('行业数查询', IndustryQueryProcessor)
  TemplateProcessorFactory.registerProcessor('可视化查询', VisualProcessor)

  // 可以在这里注册更多的内置处理器
  // TemplateProcessorFactory.registerProcessor('其他模板', OtherProcessor)
}

// 初始化插槽系统
function initializeSlotSystem() {
  registerBuiltinProcessors()
  //console.log('Slot system initialized with built-in processors')
}

// 插槽系统API类
export class SlotSystemAPI {
  private static initialized = false

  /**
   * 确保系统已初始化
   */
  private static ensureInitialized() {
    if (!this.initialized) {
      initializeSlotSystem()
      this.initialized = true
    }
  }

  /**
   * 注册自定义模板处理器
   * @param templateName 模板名称
   * @param ProcessorClass 处理器类
   */
  static registerProcessor(
    templateName: string,
    ProcessorClass: new (templateName: string) => any,
  ): void {
    this.ensureInitialized()
    TemplateProcessorFactory.registerProcessor(templateName, ProcessorClass)
  }

  /**
   * 初始化模板插槽
   * @param params 初始化参数
   * @returns Promise<SlotStateUpdateResult>
   */
  static async initializeTemplate(params: TemplateInitParams): Promise<SlotStateUpdateResult> {
    // 确保系统已初始化,并注册模板处理器
    this.ensureInitialized()
    // 调用模板处理器的初始化方法
    try {
      return await TemplateProcessorFactory.initializeTemplate(params)
    }
    catch (error) {
      console.error('Failed to initialize template:', error)
      // 返回默认的插槽状态
      return {
        definitions: { ...params.initialSlotDefinitions || {} },
        values: { ...params.initialSlotValues || {} },
      }
    }
  }

  /**
   * 处理插槽值变化
   * @param templateName 模板名称
   * @param params 插槽变化参数
   * @returns Promise<SlotStateUpdateResult | null>
   */
  static async handleSlotChange(
    templateName: string,
    params: SlotChangeParams,
  ): Promise<SlotStateUpdateResult | null> {
    this.ensureInitialized()

    try {
      return await TemplateProcessorFactory.handleSlotChange(templateName, params)
    }
    catch (error) {
      console.error('Failed to handle slot change:', error)
      return null
    }
  }

  /**
   * 获取已注册的模板列表
   * @returns 模板名称数组
   */
  static getRegisteredTemplates(): string[] {
    this.ensureInitialized()
    return TemplateProcessorFactory.getRegisteredTemplates()
  }

  /**
   * 检查模板是否已注册
   * @param templateName 模板名称
   * @returns 是否已注册
   */
  static isTemplateRegistered(templateName: string): boolean {
    this.ensureInitialized()
    return TemplateProcessorFactory.isTemplateRegistered(templateName)
  }

  /**
   * 清除处理器缓存
   * @param templateName 模板名称，如果不提供则清除所有缓存
   */
  static clearCache(templateName?: string): void {
    this.ensureInitialized()
    TemplateProcessorFactory.clearCache(templateName)
  }

  /**
   * 验证插槽值
   * @param slotName 插槽名称
   * @param value 插槽值
   * @param definition 插槽定义
   * @returns 验证结果，返回错误信息或null
   */
  static validateSlotValue(
    slotName: string,
    value: any,
    definition: SlotDefinition,
  ): string | null {
    if (definition.required && (value === undefined || value === null || value === '')) {
      return `${definition.label || slotName}是必填项`
    }

    if (definition.validation) {
      const { pattern, message, min, max } = definition.validation

      if (pattern && typeof value === 'string' && !pattern.test(value)) {
        return message || `${definition.label || slotName}格式不正确`
      }

      if (typeof value === 'string') {
        if (min !== undefined && value.length < min) {
          return `${definition.label || slotName}长度不能少于${min}个字符`
        }
        if (max !== undefined && value.length > max) {
          return `${definition.label || slotName}长度不能超过${max}个字符`
        }
      }

      if (typeof value === 'number') {
        if (min !== undefined && value < min) {
          return `${definition.label || slotName}不能小于${min}`
        }
        if (max !== undefined && value > max) {
          return `${definition.label || slotName}不能大于${max}`
        }
      }
    }

    return null
  }

  /**
   * 从模板内容中解析插槽名称
   * @param templateContent 模板内容
   * @returns 插槽名称数组
   */
  private static parseSlotNames(templateContent: string): string[] {
    if (!templateContent) {
      return []
    }

    const slotMatches = templateContent.match(/\{\{([^{}]+)\}\}/g) || []
    return slotMatches.map(match => match.replace(/[{}]/g, '').trim())
  }
}

// 导出类型和API
export {
  SlotSystemAPI as default,
  type SlotChangeParams,
  type SlotDefinition,
  type SlotDefinitions,
  type SlotStateUpdateResult,
  type SlotValue,
  type SlotValues,
  type TemplateInitParams,
}

// 便捷的函数式API
export const {
  registerProcessor,
  initializeTemplate,
  handleSlotChange,
  getRegisteredTemplates,
  isTemplateRegistered,
  clearCache,
  validateSlotValue,
} = SlotSystemAPI
