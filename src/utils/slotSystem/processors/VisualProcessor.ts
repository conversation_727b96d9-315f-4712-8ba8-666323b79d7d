import {BaseTemplateProcessor} from "@/utils/slotSystem/BaseTemplateProcessor.ts";
import type {SlotChangeParams, SlotStateUpdateResult, SlotValues, TemplateInitParams} from "@/utils/slotSystem";
import {request} from "@/api";

export class VisualProcessor extends BaseTemplateProcessor {

  async initializeSlots(params: TemplateInitParams): Promise<SlotStateUpdateResult> {
    const {initialSlotDefinitions = {}} = params
    const definitions = this.deepClone(initialSlotDefinitions)
    const values: SlotValues = {}

    if (definitions.index_name) {
      const options = await this.fetchIndexNameOptions()
      if (options) {
        definitions.index_name.options = options
      }
    }

    return {
      definitions,
      values,
    }
  }

  private async fetchIndexNameOptions(): Promise<Array<{ label: string, value: string }> | null> {
    try {
      const url = '/api/route/sp/gpt/loadAllAnaDimensions'
      const response = await request({
        url,
        method: 'get',
      })

      if (response.status === 1 && response.data) {
        return response.data.map((item: any) => ({
          label: item,
          value: item,
        }))
      }

      return null
    } catch (error) {
      console.error('fetchIndexNameOptions error: ', error)
      return null
    }
  }

  async handleSlotChange(params: SlotChangeParams): Promise<SlotStateUpdateResult | null> {
    return null
  }

}
