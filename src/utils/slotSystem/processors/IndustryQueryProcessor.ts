/**
 * 行业数查询模板处理器
 * 实现行业相关的插槽初始化和联动逻辑
 */

import type {
  SlotChangeParams,
  SlotDefinition,
  SlotDefinitions,
  SlotStateUpdateResult,
  SlotValues,
  TemplateInitParams,
} from '../types'
import { request } from '@/api'
import { BaseTemplateProcessor } from '../BaseTemplateProcessor'

// API端点配置
const API_ENDPOINTS = {
  dimensions: '/api/route/ana_dips/industryMark/listDimensions',
  indexNames: '/api/route/ana_dips/industryMark/listIndices',
  cities: '/api/route/ana_dips/industryMark/listCities',
}

// 静态选项配置
const STATIC_OPTIONS = {
  ownerTypes: [
    { value: '准业主', label: '准业主' },
    { value: '准业主1', label: '准业主1' },
    { value: '准业主2', label: '准业主2' },
    { value: '准业主3', label: '准业主3' },
    { value: '磨合期', label: '磨合期' },
    { value: '磨合期1', label: '磨合期1' },
    { value: '磨合期2', label: '磨合期2' },
    { value: '稳定期', label: '稳定期' },
    { value: '老业主', label: '老业主' },
    { value: '老业主1', label: '老业主1' },
    { value: '老业主2', label: '老业主2' },
    { value: '老业主3', label: '老业主3' },
  ],
  dataPeriods: [
    { value: '2020', label: '2020年度' },
    { value: '2021', label: '2021年度' },
    { value: '2022', label: '2022年度' },
    { value: '2023Q1', label: '2023年一季度' },
    { value: '2023Q2', label: '2023年二季度' },
    { value: '2023H', label: '2023半年度' },
    { value: '2023Q3', label: '2023年三季度' },
    { value: '2023TQ', label: '2023年三季度累计' },
    { value: '2023', label: '2023年度' },
    { value: '2024Q1', label: '2024年一季度' },
    { value: '2024Q2', label: '2024年二季度' },
    { value: '2024H', label: '2024半年度' },
    { value: '2024Q3', label: '2024年三季度' },
    { value: '2024TQ', label: '2024年三季度累计' },
    { value: '2024', label: '2024年度' },
  ],
}

export class IndustryQueryProcessor extends BaseTemplateProcessor {
  constructor() {
    super('行业数查询')
  }

  /**
   * 初始化插槽定义和值
   */
  async initializeSlots(params: TemplateInitParams): Promise<SlotStateUpdateResult> {
    const { initialSlotDefinitions = {} } = params

    // 深拷贝初始定义
    const definitions = this.deepClone(initialSlotDefinitions)
    const values: SlotValues = {}

    // 处理静态选项
    await this.initializeStaticOptions(definitions)

    // 处理动态选项
    await this.initializeDynamicOptions(definitions)

    return {
      definitions,
      values,
    }
  }

  /**
   * 处理插槽值变化时的联动逻辑
   */
  async handleSlotChange(params: SlotChangeParams): Promise<SlotStateUpdateResult | null> {
    const { changedSlotName, newValue, currentSlotValues, currentSlotDefinitions, onSlotValueChange } = params

    switch (changedSlotName) {
      case 'dimensions':
        return this.handleDimensionsChange(newValue, currentSlotValues, currentSlotDefinitions, onSlotValueChange)
      // 默认情况下，不处理其他插槽的联动逻辑
      default:
        return null
    }
  }

  /**
   * 初始化静态选项
   */
  private async initializeStaticOptions(definitions: SlotDefinitions): Promise<void> {
    // 设置业主类型选项
    if (definitions.ownerTypes) {
      definitions.ownerTypes.options = STATIC_OPTIONS.ownerTypes
    }

    // 设置数据期间选项
    if (definitions.dataPeriods) {
      definitions.dataPeriods.options = STATIC_OPTIONS.dataPeriods
    }
  }

  /**
   * 初始化动态选项
   */
  private async initializeDynamicOptions(definitions: SlotDefinitions): Promise<void> {
    // 获取维度选项
    if (definitions.dimensions) {
      const dimensionsOptions = await this.fetchDimensionsOptions()
      if (dimensionsOptions) {
        definitions.dimensions.options = dimensionsOptions
      }
    }

    // 获取指标名称选项
    if (definitions.indexNames) {
      const indexNamesOptions = await this.fetchIndexNamesOptions()
      if (indexNamesOptions) {
        definitions.indexNames.options = indexNamesOptions
      }
    }

    // 获取城市选项
    if (definitions.cities) {
      const citiesOptions = await this.fetchCitiesOptions()
      if (citiesOptions) {
        definitions.cities.options = citiesOptions
      }
    }
  }

  /**
   * 处理维度变化
   */
  private handleDimensionsChange(
    dimensionsValue: any,
    currentSlotValues: SlotValues,
    currentSlotDefinitions: SlotDefinitions,
    onSlotValueChange: (slotName: string, value: any) => void,
  ): SlotStateUpdateResult {
    // 创建更新状态对象
    const updateState: SlotStateUpdateResult = {
      values: { ...currentSlotValues },
      definitions: this.deepClone(currentSlotDefinitions),
    }

    // 重置相关字段值
    updateState.values.ownerTypes = ''
    updateState.values.cities = ''

    // 回调函数
    onSlotValueChange('ownerTypes', '')
    onSlotValueChange('cities', '')

    const ownerTypeDef = updateState.definitions.ownerTypes
    const citiesDef = updateState.definitions.cities

    if (!ownerTypeDef || !citiesDef) {
      return updateState
    }

    // 处理业主类型维度
    const hasOwnerType = Array.isArray(dimensionsValue)
      ? dimensionsValue.includes('业主类型')
      : String(dimensionsValue).includes('业主类型')

    const hasNode = Array.isArray(dimensionsValue)
      ? dimensionsValue.includes('节点')
      : String(dimensionsValue).includes('节点')

    const hasCity = Array.isArray(dimensionsValue)
      ? dimensionsValue.includes('城市')
      : String(dimensionsValue).includes('城市')

    // 设置业主类型选项
    if (hasOwnerType) {
      ownerTypeDef.required = true
      ownerTypeDef.disabled = false
      ownerTypeDef.options = [
        { value: '准业主', label: '准业主' },
        { value: '磨合期', label: '磨合期' },
        { value: '稳定期', label: '稳定期' },
        { value: '老业主', label: '老业主' },
      ]
    }
    else if (hasNode) {
      // 设置节点选项
      ownerTypeDef.required = true
      ownerTypeDef.disabled = false
      ownerTypeDef.options = [
        { value: '准业主1', label: '准业主1' },
        { value: '准业主2', label: '准业主2' },
        { value: '准业主3', label: '准业主3' },
        { value: '磨合期1', label: '磨合期1' },
        { value: '磨合期2', label: '磨合期2' },
        { value: '稳定期', label: '稳定期' },
        { value: '老业主1', label: '老业主1' },
        { value: '老业主2', label: '老业主2' },
        { value: '老业主3', label: '老业主3' },
      ]
    }
    else {
      // 当既不是业主类型也不是节点时，禁用业主类型选择
      ownerTypeDef.required = false
      ownerTypeDef.disabled = true
      ownerTypeDef.options = []
    }

    // 设置城市选项状态
    citiesDef.required = hasCity
    citiesDef.disabled = !hasCity

    //console.log('IndustryQueryProcessor - handleDimensionsChange:', updateState)
    return updateState
  }

  /**
   * 获取维度选项
   */
  private async fetchDimensionsOptions(): Promise<Array<{ label: string, value: string }> | null> {
    try {
      const url = `${API_ENDPOINTS.dimensions}?customerName='保利'`
      // 使用 request 请求
      const response = await request({
        url,
        method: 'get',
      })
      if (response.status === 1 && response.data) {
        return response.data
      }
      else {
        return null
      }
    }
    catch (error) {
      console.error('获取维度选项失败:', error)
      return null
    }
  }

  /**
   * 获取指标名称选项
   */
  private async fetchIndexNamesOptions(): Promise<Array<{ label: string, value: string }> | null> {
    try {
      // const response = await this.makeRequest({ url: API_ENDPOINTS.indexNames })
      const url = API_ENDPOINTS.indexNames
      const response = await request({
        url,
        method: 'get',
      })

      if (response.status === 1 && response.data) {
        return response.data.map((item: any) => ({
          label: item,
          value: item,
        }))
      }

      return null
    }
    catch (error) {
      console.error('获取指标名称选项失败:', error)
      return null
    }
  }

  /**
   * 获取城市选项
   */
  private async fetchCitiesOptions(): Promise<Array<{ label: string, value: string }> | null> {
    try {
      const url = API_ENDPOINTS.cities
      const response = await request({
        url,
        method: 'get',
      })

      if (response.status === 1 && response.data) {
        return response.data.map((item: any) => ({
          label: item,
          value: item,
        }))
      }

      return null
    }
    catch (error) {
      console.error('获取城市选项失败:', error)
      return null
    }
  }
}
