import type { TokenCalculationRequest, TokenCalculationResponse } from '@/types/billing'

/**
 * 批量请求项
 */
interface BatchRequestItem {
  id: string
  request: TokenCalculationRequest
  resolve: (data: TokenCalculationResponse | null) => void
  reject: (error: any) => void
  timestamp: number
}

/**
 * 批量请求管理器
 */
export class BatchRequestManager {
  private batchQueue: BatchRequestItem[] = []
  private batchTimer: NodeJS.Timeout | null = null
  private readonly batchDelay: number
  private readonly maxBatchSize: number
  private requestCounter = 0

  constructor(batchDelay: number = 100, maxBatchSize: number = 10) {
    this.batchDelay = batchDelay
    this.maxBatchSize = maxBatchSize
  }

  /**
   * 添加请求到批量队列
   */
  addRequest(
    request: TokenCalculationRequest,
    apiCall: (requests: TokenCalculationRequest[]) => Promise<TokenCalculationResponse[]>,
  ): Promise<TokenCalculationResponse | null> {
    return new Promise((resolve, reject) => {
      const requestId = `req-${++this.requestCounter}-${Date.now()}`

      const batchItem: BatchRequestItem = {
        id: requestId,
        request,
        resolve,
        reject,
        timestamp: Date.now(),
      }

      this.batchQueue.push(batchItem)
      //console.log(`📝 [批量请求] 添加请求到队列: ${requestId}, 队列长度: ${this.batchQueue.length}`)

      // 如果队列已满，立即处理
      if (this.batchQueue.length >= this.maxBatchSize) {
        //console.log(`🚀 [批量请求] 队列已满，立即处理 ${this.batchQueue.length} 个请求`)
        this.processBatch(apiCall)
        return
      }

      // 设置批量处理定时器
      if (!this.batchTimer) {
        this.batchTimer = setTimeout(() => {
          if (this.batchQueue.length > 0) {
            //console.log(`⏰ [批量请求] 定时器触发，处理 ${this.batchQueue.length} 个请求`)
            this.processBatch(apiCall)
          }
        }, this.batchDelay)
      }
    })
  }

  /**
   * 处理批量请求
   */
  private async processBatch(
    apiCall: (requests: TokenCalculationRequest[]) => Promise<TokenCalculationResponse[]>,
  ): Promise<void> {
    if (this.batchQueue.length === 0) {
      return
    }

    // 清除定时器
    if (this.batchTimer) {
      clearTimeout(this.batchTimer)
      this.batchTimer = null
    }

    // 获取当前批次的请求
    const currentBatch = [...this.batchQueue]
    this.batchQueue = []

    //console.log(`🔄 [批量请求] 开始处理批次，包含 ${currentBatch.length} 个请求`)

    try {
      // 提取请求参数
      const requests = currentBatch.map(item => item.request)

      // 调用批量API
      const responses = await apiCall(requests)

      // 分发响应结果
      currentBatch.forEach((item, index) => {
        const response = responses[index]
        if (response) {
          //console.log(`✅ [批量请求] 请求 ${item.id} 处理成功`)
          item.resolve(response)
        }
        else {
          console.warn(`⚠️ [批量请求] 请求 ${item.id} 无响应数据`)
          item.resolve(null)
        }
      })

      //console.log(`🎉 [批量请求] 批次处理完成，成功处理 ${currentBatch.length} 个请求`)
    }
    catch (error) {
      console.error(`❌ [批量请求] 批次处理失败:`, error)

      // 所有请求都标记为失败
      currentBatch.forEach((item) => {
        console.error(`❌ [批量请求] 请求 ${item.id} 处理失败`)
        item.reject(error)
      })
    }
  }

  /**
   * 清空队列
   */
  clear(): void {
    if (this.batchTimer) {
      clearTimeout(this.batchTimer)
      this.batchTimer = null
    }

    // 拒绝所有等待中的请求
    this.batchQueue.forEach((item) => {
      item.reject(new Error('批量请求管理器已清空'))
    })

    this.batchQueue = []
    //console.log('🗑️ [批量请求] 队列已清空')
  }

  /**
   * 获取统计信息
   */
  getStats(): {
    queueLength: number
    hasPendingTimer: boolean
    totalProcessed: number
  } {
    return {
      queueLength: this.batchQueue.length,
      hasPendingTimer: this.batchTimer !== null,
      totalProcessed: this.requestCounter,
    }
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.clear()
  }
}

// 创建单例批量请求管理器
export const tokenCalculationBatchManager = new BatchRequestManager(100, 5)

export type { BatchRequestItem }
