/**
 * 分页钩子函数
 */
import { computed, reactive, ref, watch, type Ref } from 'vue'

export interface PaginationConfig {
  currentPage: number
  pageSize: number
  total: number
}

export interface UsePaginationOptions {
  initialPageSize?: number
  pageSizes?: number[]
  layout?: string
  background?: boolean
  small?: boolean
}

export interface UsePaginationReturn {
  pagination: PaginationConfig
  paginatedData: <T>(data: T[]) => T[]
  handleCurrentChange: (page: number) => void
  handleSizeChange: (size: number) => void
  resetPagination: () => void
  updateTotal: (total: number) => void
  getCurrentPageRange: () => { start: number; end: number }
}

/**
 * 分页钩子函数
 */
export function usePagination(
  options: UsePaginationOptions = {}
): UsePaginationReturn {
  const {
    initialPageSize = 20,
    pageSizes = [10, 20, 50, 100],
    layout = 'total, sizes, prev, pager, next, jumper',
    background = true,
    small = false
  } = options

  // 分页配置
  const pagination = reactive<PaginationConfig>({
    currentPage: 1,
    pageSize: initialPageSize,
    total: 0
  })

  /**
   * 对数据进行分页处理
   */
  function paginatedData<T>(data: T[]): T[] {
    const start = (pagination.currentPage - 1) * pagination.pageSize
    const end = start + pagination.pageSize
    return data.slice(start, end)
  }

  /**
   * 处理页码变化
   */
  function handleCurrentChange(page: number) {
    pagination.currentPage = page
  }

  /**
   * 处理每页条数变化
   */
  function handleSizeChange(size: number) {
    pagination.pageSize = size
    pagination.currentPage = 1 // 重置到第一页
  }

  /**
   * 重置分页
   */
  function resetPagination() {
    pagination.currentPage = 1
    pagination.total = 0
  }

  /**
   * 更新总数
   */
  function updateTotal(total: number) {
    pagination.total = total
  }

  /**
   * 获取当前页的数据范围
   */
  function getCurrentPageRange(): { start: number; end: number } {
    const start = (pagination.currentPage - 1) * pagination.pageSize
    const end = start + pagination.pageSize
    return { start, end }
  }

  return {
    pagination,
    paginatedData,
    handleCurrentChange,
    handleSizeChange,
    resetPagination,
    updateTotal,
    getCurrentPageRange
  }
}

/**
 * 获取默认分页配置
 */
export function getDefaultPaginationConfig(): UsePaginationOptions {
  return {
    initialPageSize: 20,
    pageSizes: [10, 20, 50, 100],
    layout: 'total, sizes, prev, pager, next, jumper',
    background: true,
    small: false
  }
}

/**
 * 计算分页信息
 */
export function calculatePaginationInfo(currentPage: number, pageSize: number, total: number) {
  const totalPages = Math.ceil(total / pageSize)
  const start = (currentPage - 1) * pageSize + 1
  const end = Math.min(currentPage * pageSize, total)

  return {
    totalPages,
    start,
    end,
    hasNext: currentPage < totalPages,
    hasPrev: currentPage > 1
  }
}
