<script setup lang="ts">
import { computed } from 'vue'
import { usePagination, type UsePaginationOptions } from './usePagination'

interface Props {
  pagination?: {
    currentPage: number
    pageSize: number
    total: number
    pageSizes?: number[]
    layout?: string
    background?: boolean
    small?: boolean
  }
  currentPage?: number
  pageSize?: number
  total?: number
  pageSizes?: number[]
  layout?: string
  background?: boolean
  small?: boolean
  hideOnSinglePage?: boolean
  clazz?: string
}

const props = withDefaults(defineProps<Props>(), {
  currentPage: 1,
  pageSize: 20,
  total: 0,
  pageSizes: () => [10, 20, 50, 100],
  layout: 'total, sizes, prev, pager, next, jumper',
  background: true,
  small: false,
  hideOnSinglePage: false,
  clazz: 'research-pagination'
})

// 计算属性：获取实际的分页配置
const actualCurrentPage = computed(() => props.pagination?.currentPage ?? props.currentPage)
const actualPageSize = computed(() => props.pagination?.pageSize ?? props.pageSize)
const actualTotal = computed(() => props.pagination?.total ?? props.total)
const actualPageSizes = computed(() => props.pagination?.pageSizes ?? props.pageSizes)
const actualLayout = computed(() => props.pagination?.layout ?? props.layout)
const actualBackground = computed(() => props.pagination?.background ?? props.background)
const actualSmall = computed(() => props.pagination?.small ?? props.small)

const emit = defineEmits<{
  'current-change': [page: number]
  'size-change': [size: number]
}>()

// 计算属性：是否显示分页
const shouldShowPagination = computed(() => {
  if (props.hideOnSinglePage) {
    const totalPages = Math.ceil(actualTotal.value / actualPageSize.value)
    return totalPages > 1
  }
  return true
})

// 处理页码变化
function handleCurrentChange(page: number) {
  emit('current-change', page)
}

// 处理每页条数变化
function handleSizeChange(size: number) {
  emit('size-change', size)
}
</script>

<template>
  <div
    v-if="shouldShowPagination"
    :class="['pagination-wrapper', clazz]"
  >
    <el-pagination
      :current-page="actualCurrentPage"
      :page-size="actualPageSize"
      :total="actualTotal"
      :page-sizes="actualPageSizes"
      :layout="actualLayout"
      :background="actualBackground"
      :small="actualSmall"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<style scoped>
.pagination-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-top: 20px;
}

.research-pagination :deep(.el-pagination) {
  --el-pagination-bg-color: transparent;
  --el-pagination-text-color: #606266;
  --el-pagination-border-radius: 6px;
}

.research-pagination :deep(.el-pagination .el-pager li) {
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  margin: 0 2px;
}

.research-pagination :deep(.el-pagination .el-pager li.is-active) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border-color: #667eea;
}

.research-pagination :deep(.el-pagination .btn-prev),
.research-pagination :deep(.el-pagination .btn-next) {
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
}

.research-pagination :deep(.el-pagination .btn-prev:hover),
.research-pagination :deep(.el-pagination .btn-next:hover) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border-color: #667eea;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pagination-wrapper {
    padding: 15px 10px;
  }

  .research-pagination :deep(.el-pagination) {
    font-size: 12px;
  }

  .research-pagination :deep(.el-pagination .el-pager li) {
    min-width: 28px;
    height: 28px;
    line-height: 28px;
  }
}

@media (max-width: 480px) {
  .pagination-wrapper {
    padding: 10px 5px;
  }

  .research-pagination :deep(.el-pagination .el-pagination__sizes),
  .research-pagination :deep(.el-pagination .el-pagination__jump) {
    display: none;
  }
}
</style>
