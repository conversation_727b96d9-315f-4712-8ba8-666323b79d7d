# Research 分页组件

## 🎯 推荐使用 usePagination 钩子函数

为了避免重复代码和更好的组合式API体验，推荐使用 `usePagination` 钩子函数来管理分页相关的逻辑。

## 组件使用

### PaginationComponent 组件

```vue
<template>
  <div>
    <!-- 基本使用 -->
    <PaginationComponent 
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="total"
    />
    
    <!-- 自定义配置 -->
    <PaginationComponent 
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :background="true"
      :small="false"
      :hide-on-single-page="true"
    />
    
    <!-- 紧凑模式 -->
    <PaginationComponent 
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="total"
      :small="true"
      layout="prev, pager, next"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { PaginationComponent } from '@/utils/research/pagination'

const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(100)
</script>
```

## 🎯 推荐使用 usePagination 钩子函数

### 基本使用

```vue
<template>
  <div>
    <!-- 数据表格 -->
    <el-table :data="paginatedTableData">
      <!-- 表格列定义 -->
    </el-table>
    
    <!-- 分页组件 -->
    <PaginationComponent 
      v-model:current-page="pagination.currentPage"
      v-model:page-size="pagination.pageSize"
      :total="pagination.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { usePagination, PaginationComponent } from '@/utils/research/pagination'

// 原始数据
const tableData = ref([])

// 使用分页钩子
const {
  pagination,
  paginatedData,
  handleCurrentChange,
  handleSizeChange,
  resetPagination,
  updateTotal
} = usePagination({
  initialPageSize: 20,
  pageSizes: [10, 20, 50, 100]
})

// 分页后的数据
const paginatedTableData = computed(() => paginatedData(tableData.value))

// 加载数据时更新总数
function loadData() {
  // 加载数据逻辑
  updateTotal(tableData.value.length)
}
</script>
```

### 高级使用

```vue
<script setup>
import { ref, computed, watch } from 'vue'
import { usePagination, PaginationComponent } from '@/utils/research/pagination'

// 原始数据和过滤条件
const allData = ref([])
const searchKeyword = ref('')

// 过滤后的数据
const filteredData = computed(() => {
  if (!searchKeyword.value) return allData.value
  return allData.value.filter(item => 
    item.name.includes(searchKeyword.value)
  )
})

// 使用分页钩子
const {
  pagination,
  paginatedData,
  handleCurrentChange,
  handleSizeChange,
  resetPagination,
  updateTotal,
  getCurrentPageRange
} = usePagination({
  initialPageSize: 20
})

// 分页后的数据
const paginatedTableData = computed(() => paginatedData(filteredData.value))

// 监听过滤条件变化，重置分页
watch(searchKeyword, () => {
  resetPagination()
  updateTotal(filteredData.value.length)
})

// 监听过滤数据变化，更新总数
watch(filteredData, (newData) => {
  updateTotal(newData.length)
}, { immediate: true })

// 获取当前页范围信息
function getCurrentInfo() {
  const { start, end } = getCurrentPageRange()
  console.log(`显示第 ${start + 1} - ${Math.min(end, filteredData.value.length)} 条，共 ${filteredData.value.length} 条`)
}
</script>
```

## API 参考

### usePagination 钩子函数

#### 参数

```typescript
interface UsePaginationOptions {
  initialPageSize?: number    // 初始每页条数，默认 20
  pageSizes?: number[]       // 每页条数选项，默认 [10, 20, 50, 100]
  layout?: string           // 分页布局，默认 'total, sizes, prev, pager, next, jumper'
  background?: boolean      // 是否显示背景色，默认 true
  small?: boolean          // 是否使用小型分页，默认 false
}
```

#### 返回值

```typescript
interface UsePaginationReturn {
  pagination: PaginationConfig              // 分页配置对象
  paginatedData: <T>(data: T[]) => T[]     // 数据分页函数
  handleCurrentChange: (page: number) => void    // 页码变化处理
  handleSizeChange: (size: number) => void       // 每页条数变化处理
  resetPagination: () => void                    // 重置分页
  updateTotal: (total: number) => void           // 更新总数
  getCurrentPageRange: () => { start: number; end: number }  // 获取当前页范围
}
```

### PaginationComponent 组件

#### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| currentPage | number | 1 | 当前页码 |
| pageSize | number | 20 | 每页条数 |
| total | number | 0 | 总条数 |
| pageSizes | number[] | [10, 20, 50, 100] | 每页条数选项 |
| layout | string | 'total, sizes, prev, pager, next, jumper' | 分页布局 |
| background | boolean | true | 是否显示背景色 |
| small | boolean | false | 是否使用小型分页 |
| hideOnSinglePage | boolean | false | 只有一页时是否隐藏 |
| class | string | 'research-pagination' | 自定义样式类 |

#### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:currentPage | page: number | 当前页码更新 |
| update:pageSize | size: number | 每页条数更新 |
| current-change | page: number | 页码变化 |
| size-change | size: number | 每页条数变化 |

## 工具函数使用（向后兼容）

```typescript
import {
  getDefaultPaginationConfig,
  calculatePaginationInfo
} from '@/utils/research/pagination'

// 获取默认配置
const defaultConfig = getDefaultPaginationConfig()

// 计算分页信息
const paginationInfo = calculatePaginationInfo(2, 20, 100)
// 返回: { totalPages: 5, start: 21, end: 40, hasNext: true, hasPrev: true }
```

## 最佳实践

1. **优先使用钩子函数**：`usePagination` 提供了完整的分页逻辑管理
2. **数据过滤时重置分页**：当搜索或筛选条件变化时，调用 `resetPagination()`
3. **及时更新总数**：数据变化时使用 `updateTotal()` 更新总条数
4. **响应式设计**：组件已内置响应式样式，适配移动端
5. **性能优化**：对于大量数据，建议使用服务端分页而非前端分页

## 样式定制

组件提供了统一的样式，支持通过 CSS 变量进行定制：

```css
.research-pagination {
  --el-pagination-bg-color: transparent;
  --el-pagination-text-color: #606266;
  --el-pagination-border-radius: 6px;
}
```