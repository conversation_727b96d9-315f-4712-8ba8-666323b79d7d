/**
 * 日期范围选择器钩子函数
 */
import { computed, ref, watch, type Ref } from 'vue'

export interface DateShortcut {
  text: string
  value: () => [Date, Date]
}

export interface UseDateRangeOptions {
  initPeriod?: string
  defaultToLastWeek?: boolean
  shortcuts?: DateShortcut[]
}

export interface UseDateRangeReturn {
  internalValue: Ref<[string, string] | null>
  dateShortcuts: Ref<DateShortcut[]>
  handleChange: (value: any) => void
  initializeDateRange: () => [string, string] | null
  formatDateTime: (date: Date, format?: string) => string
  getLastWeekRange: () => [string, string]
  getLastMonthRange: () => [string, string]
  getLastThreeMonthsRange: () => [string, string]
}

/**
 * 格式化日期时间为字符串
 */
export function formatDateTime(date: Date, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 获取默认的日期快捷选项
 */
export function getDefaultDateShortcuts(): DateShortcut[] {
  return [
    {
      text: '昨天',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24)
        return [start, end]
      },
    },
    {
      text: '最近一周',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        return [start, end]
      },
    },
    {
      text: '最近一个月',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        return [start, end]
      },
    },
    {
      text: '最近三个月',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
        return [start, end]
      },
    },
  ]
}

/**
 * 获取最近一周的日期范围
 */
export function getLastWeekRange(): [string, string] {
  const end = new Date()
  const start = new Date()
  start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)

  return [
    formatDateTime(start),
    formatDateTime(end)
  ]
}

/**
 * 获取最近一个月的日期范围
 */
export function getLastMonthRange(): [string, string] {
  const end = new Date()
  const start = new Date()
  start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)

  return [
    formatDateTime(start),
    formatDateTime(end)
  ]
}

/**
 * 获取最近三个月的日期范围
 */
export function getLastThreeMonthsRange(): [string, string] {
  const end = new Date()
  const start = new Date()
  start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)

  return [
    formatDateTime(start),
    formatDateTime(end)
  ]
}

/**
 * 日期范围选择器钩子函数
 */
export function useDateRange(
  modelValue: Ref<[string, string] | null>,
  emit: ((evt: 'update:modelValue', value: [string, string] | null) => void) & ((evt: 'change', value: [string, string] | null) => void),
  options: UseDateRangeOptions = {}
): UseDateRangeReturn {
  const {
    initPeriod,
    defaultToLastWeek = true,
    shortcuts
  } = options

  // 内部值
  const internalValue = ref<[string, string] | null>(modelValue.value || null)

  // 日期快捷选项
  const dateShortcuts = computed(() => shortcuts || getDefaultDateShortcuts())

  /**
   * 根据参数初始化日期范围
   */
  function initializeDateRange(): [string, string] | null {
    if (initPeriod) {
      const period = initPeriod
      let start: Date
      let end: Date

      if (period.length === 4) {
        // 年份格式：'2025'
        const year = parseInt(period)
        if (isNaN(year)) return null

        start = new Date(year, 0, 1) // 1月1日
        end = new Date(year, 11, 31, 23, 59, 59) // 12月31日
      } else if (period.length === 7 && period.includes('-')) {
        // 年月格式：'2025-06'
        const [yearStr, monthStr] = period.split('-')
        const year = parseInt(yearStr)
        const month = parseInt(monthStr)

        if (isNaN(year) || isNaN(month) || month < 1 || month > 12) return null

        start = new Date(year, month - 1, 1) // 月份第一天
        end = new Date(year, month, 0, 23, 59, 59) // 月份最后一天
      } else if (period.length === 10 && period.split('-').length === 3) {
        // 年月日格式：'2025-01-12'
        const [yearStr, monthStr, dayStr] = period.split('-')
        const year = parseInt(yearStr)
        const month = parseInt(monthStr)
        const day = parseInt(dayStr)

        if (isNaN(year) || isNaN(month) || isNaN(day) ||
          month < 1 || month > 12 || day < 1 || day > 31) return null

        start = new Date(year, month - 1, day) // 指定日期开始
        end = new Date(year, month - 1, day, 23, 59, 59) // 指定日期结束
      } else {
        return null
      }

      return [
        formatDateTime(start),
        formatDateTime(end)
      ]
    } else if (defaultToLastWeek) {
      // 默认最近一周
      return getLastWeekRange()
    }

    return null
  }

  /**
   * 处理值变化
   */
  function handleChange(value: any) {
    const typedValue = value as [string, string] | null
    internalValue.value = typedValue
    emit('update:modelValue', typedValue)
    emit('change', typedValue)
  }

  // 初始化
  if (!modelValue.value) {
    const initialValue = initializeDateRange()
    if (initialValue) {
      internalValue.value = initialValue
      emit('update:modelValue', initialValue)
    }
  }

  // 监听外部值变化
  watch(modelValue, (newValue) => {
    internalValue.value = newValue || null
  }, { immediate: true })

  return {
    internalValue,
    dateShortcuts,
    handleChange,
    initializeDateRange,
    formatDateTime,
    getLastWeekRange,
    getLastMonthRange,
    getLastThreeMonthsRange
  }
}
