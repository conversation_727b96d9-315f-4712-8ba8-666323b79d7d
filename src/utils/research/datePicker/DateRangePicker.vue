<script setup lang="ts">
import { toRef } from 'vue'
import { useDateRange, type DateShortcut } from './useDateRange'

interface Props {
  modelValue?: [string, string] | null
  placeholder?: [string, string]
  format?: string
  valueFormat?: string
  rangeSeparator?: string
  shortcuts?: DateShortcut[]
  initPeriod?: string // 初始化时间段参数，如：'2025-06' 或 '2025-01' 或 '2025'
  defaultToLastWeek?: boolean // 是否默认最近一周
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: () => ['开始时间', '结束时间'],
  format: 'YYYY-MM-DD HH:mm:ss',
  valueFormat: 'YYYY-MM-DD HH:mm:ss',
  rangeSeparator: '至',
  defaultToLastWeek: true,
  class: 'form-date-picker'
})

const emit = defineEmits<{
  'update:modelValue': [value: [string, string] | null]
  'change': [value: [string, string] | null]
}>()

// 使用日期范围钩子函数
const {
  internalValue,
  dateShortcuts,
  handleChange
} = useDateRange(
  toRef(() => props.modelValue || null),
  emit,
  {
    initPeriod: props.initPeriod,
    defaultToLastWeek: props.defaultToLastWeek,
    shortcuts: props.shortcuts
  }
)
</script>

<template>
  <el-date-picker
    :model-value="internalValue as any"
    type="datetimerange"
    :range-separator="rangeSeparator"
    :start-placeholder="placeholder[0]"
    :end-placeholder="placeholder[1]"
    :format="format"
    :value-format="valueFormat"
    :shortcuts="dateShortcuts"
    :class="class"
    @update:model-value="handleChange"
  />
</template>
