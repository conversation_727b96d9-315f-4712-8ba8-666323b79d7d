# Research 日期选择器组件

这个模块提供了一个可复用的日期范围选择器组件和相关的钩子函数。

## 🎯 推荐使用 useDateRange 钩子函数

为了避免重复代码和更好的组合式API体验，推荐使用 `useDateRange` 钩子函数来管理日期范围相关的逻辑。

## 组件使用

### DateRangePicker 组件

```vue
<template>
  <div>
    <!-- 基本使用 -->
    <DateRangePicker v-model="dateRange" />
    
    <!-- 自定义初始化为6月份 -->
    <DateRangePicker 
      v-model="dateRange" 
      init-period="2025-06"
    />
    
    <!-- 自定义初始化为2025年 -->
    <DateRangePicker 
      v-model="dateRange" 
      init-period="2025"
    />
    
    <!-- 自定义初始化为特定日期 -->
    <DateRangePicker 
      v-model="dateRange" 
      init-period="2025-01-12"
    />
    
    <!-- 不使用默认最近一周 -->
    <DateRangePicker 
      v-model="dateRange" 
      :default-to-last-week="false"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { DateRangePicker } from '@/utils/research'

const dateRange = ref(null)
</script>
```

### 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| modelValue | `[string, string] \| null` | `null` | 绑定值 |
| placeholder | `[string, string]` | `['开始时间', '结束时间']` | 占位符文本 |
| format | `string` | `'YYYY-MM-DD HH:mm:ss'` | 显示格式 |
| valueFormat | `string` | `'YYYY-MM-DD HH:mm:ss'` | 值格式 |
| rangeSeparator | `string` | `'至'` | 范围分隔符 |
| shortcuts | `DateShortcut[]` | 默认快捷选项 | 自定义快捷选项 |
| initPeriod | `string` | `undefined` | 初始化时间段参数 |
| defaultToLastWeek | `boolean` | `true` | 是否默认最近一周 |
| class | `string` | `'form-date-picker'` | CSS 类名 |

### 组件事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | `value: [string, string] \| null` | 值更新事件 |
| change | `value: [string, string] \| null` | 值变化事件 |

## useDateRange 钩子函数使用（推荐）

```vue
<template>
  <div>
    <el-date-picker
      :model-value="internalValue"
      type="datetimerange"
      :shortcuts="dateShortcuts"
      @update:model-value="handleChange"
    />
    
    <el-button @click="setToLastMonth">设置为最近一个月</el-button>
    <el-button @click="setToCustomPeriod">设置为2025年6月</el-button>
  </div>
</template>

<script setup>
import { ref, toRef } from 'vue'
import { useDateRange } from '@/utils/research'

const modelValue = ref(null)

const emit = defineEmits(['update:modelValue', 'change'])

// 使用日期范围钩子函数
const {
  internalValue,
  dateShortcuts,
  handleChange,
  initializeDateRange,
  getLastMonthRange,
  formatDateTime
} = useDateRange(
  toRef(() => modelValue.value),
  emit,
  {
    initPeriod: '2025-06', // 初始化为2025年6月
    defaultToLastWeek: true
  }
)

// 设置为最近一个月
function setToLastMonth() {
  const range = getLastMonthRange()
  handleChange(range)
}

// 设置为自定义时间段
function setToCustomPeriod() {
  // 可以动态调用初始化方法
  const range = initializeDateRange()
  if (range) {
    handleChange(range)
  }
}
</script>
```

## 工具函数使用（向后兼容）

```typescript
import {
  getDefaultDateShortcuts,
  initializeDateRange, // @deprecated 建议使用 useDateRange 钩子
  formatDateTime,
  getLastWeekRange,
  getLastMonthRange,
  getLastThreeMonthsRange
} from '@/utils/research'

// 获取默认快捷选项
const shortcuts = getDefaultDateShortcuts()

// 初始化日期范围（已废弃，建议使用钩子函数）
const range1 = initializeDateRange('2025-06') // 2025年6月
const range2 = initializeDateRange('2025') // 2025年整年
const range3 = initializeDateRange('2025-01-12') // 2025年1月12日
const range4 = initializeDateRange() // 默认最近一周

// 格式化日期
const formatted = formatDateTime(new Date())

// 获取预设范围
const lastWeek = getLastWeekRange()
const lastMonth = getLastMonthRange()
const lastThreeMonths = getLastThreeMonthsRange()
```

## initPeriod 参数格式

- `'2025'` - 2025年整年（1月1日 00:00:00 至 12月31日 23:59:59）
- `'2025-06'` - 2025年6月（6月1日 00:00:00 至 6月30日 23:59:59）
- `'2025-01-12'` - 2025年1月12日（当天 00:00:00 至 23:59:59）

## 默认快捷选项

- 昨天
- 最近一周
- 最近一个月
- 最近三个月

## 注意事项

1. 组件会自动根据 `initPeriod` 或 `defaultToLastWeek` 设置初始值
2. 如果同时设置了 `initPeriod` 和 `defaultToLastWeek`，优先使用 `initPeriod`
3. 日期格式默认为 `YYYY-MM-DD HH:mm:ss`，可通过 `format` 和 `valueFormat` 自定义
4. 组件内置了表单验证和错误处理