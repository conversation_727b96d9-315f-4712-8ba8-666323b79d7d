import type { IndustryType } from '@/utils/tenant-constants'

/**
 * 页面转换动画配置
 */
export interface PageTransitionConfig {
  enter: string
  leave: string
  duration: number
  easing: string
  delay?: number
}

/**
 * 页面转换动画映射
 */
export const pageTransitions: Record<string, PageTransitionConfig> = {
  'medical-beauty': {
    enter: 'fade-slide-up',
    leave: 'fade-slide-down',
    duration: 800,
    easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    delay: 100
  },
  'real-estate': {
    enter: 'fade-scale',
    leave: 'fade-scale-out',
    duration: 600,
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
    delay: 50
  },
  'default': {
    enter: 'fade',
    leave: 'fade-out',
    duration: 400,
    easing: 'ease-in-out'
  }
}

/**
 * 卡片动画配置
 */
export interface CardAnimationConfig {
  hover: string
  click: string
  enter: string
  stagger: number
}

export const cardAnimations: Record<string, CardAnimationConfig> = {
  'medical-beauty': {
    hover: 'medical-card-hover',
    click: 'medical-card-click',
    enter: 'medical-card-enter',
    stagger: 150
  },
  'real-estate': {
    hover: 'estate-card-hover',
    click: 'estate-card-click',
    enter: 'estate-card-enter',
    stagger: 100
  },
  'default': {
    hover: 'default-card-hover',
    click: 'default-card-click',
    enter: 'default-card-enter',
    stagger: 120
  }
}

/**
 * 按钮动画配置
 */
export interface ButtonAnimationConfig {
  hover: string
  click: string
  loading: string
}

export const buttonAnimations: Record<string, ButtonAnimationConfig> = {
  'medical-beauty': {
    hover: 'medical-button-hover',
    click: 'medical-button-click',
    loading: 'medical-button-loading'
  },
  'real-estate': {
    hover: 'estate-button-hover',
    click: 'estate-button-click',
    loading: 'estate-button-loading'
  },
  'default': {
    hover: 'default-button-hover',
    click: 'default-button-click',
    loading: 'default-button-loading'
  }
}

/**
 * 获取主题对应的页面转换配置
 */
export function getPageTransition(theme: string): PageTransitionConfig {
  return pageTransitions[theme] || pageTransitions.default
}

/**
 * 获取主题对应的卡片动画配置
 */
export function getCardAnimation(theme: string): CardAnimationConfig {
  return cardAnimations[theme] || cardAnimations.default
}

/**
 * 获取主题对应的按钮动画配置
 */
export function getButtonAnimation(theme: string): ButtonAnimationConfig {
  return buttonAnimations[theme] || buttonAnimations.default
}

/**
 * 根据行业类型获取动画配置
 */
export function getAnimationsByIndustry(industryType: IndustryType) {
  const themeMap: Record<IndustryType, string> = {
    MEDICAL_BEAUTY: 'medical-beauty',
    REAL_ESTATE: 'real-estate',
    GENERAL: 'default'
  }
  
  const theme = themeMap[industryType] || 'default'
  
  return {
    page: getPageTransition(theme),
    card: getCardAnimation(theme),
    button: getButtonAnimation(theme)
  }
}

/**
 * 创建页面转换函数
 */
export function createPageTransition(config: PageTransitionConfig) {
  return {
    name: config.enter,
    mode: 'out-in' as const,
    duration: config.duration,
    
    beforeEnter(el: Element) {
      const htmlEl = el as HTMLElement
      htmlEl.style.opacity = '0'
      htmlEl.style.transform = getTransformByAnimation(config.enter)
    },
    
    enter(el: Element, done: () => void) {
      const htmlEl = el as HTMLElement
      htmlEl.style.transition = `all ${config.duration}ms ${config.easing}`
      
      // 触发重排
      htmlEl.offsetHeight
      
      setTimeout(() => {
        htmlEl.style.opacity = '1'
        htmlEl.style.transform = 'none'
        
        setTimeout(done, config.duration)
      }, config.delay || 0)
    },
    
    beforeLeave(el: Element) {
      const htmlEl = el as HTMLElement
      htmlEl.style.transition = `all ${config.duration}ms ${config.easing}`
    },
    
    leave(el: Element, done: () => void) {
      const htmlEl = el as HTMLElement
      htmlEl.style.opacity = '0'
      htmlEl.style.transform = getTransformByAnimation(config.leave)
      
      setTimeout(done, config.duration)
    }
  }
}

/**
 * 根据动画名称获取transform值
 */
function getTransformByAnimation(animation: string): string {
  const transforms: Record<string, string> = {
    'fade-slide-up': 'translateY(30px)',
    'fade-slide-down': 'translateY(-30px)',
    'fade-scale': 'scale(0.95)',
    'fade-scale-out': 'scale(1.05)',
    'fade': 'none'
  }
  
  return transforms[animation] || 'none'
}

/**
 * 创建交错动画
 */
export function createStaggerAnimation(elements: NodeListOf<Element>, config: CardAnimationConfig) {
  elements.forEach((el, index) => {
    const htmlEl = el as HTMLElement
    const delay = index * config.stagger
    
    htmlEl.style.animationDelay = `${delay}ms`
    htmlEl.classList.add(config.enter)
    
    // 监听动画结束
    htmlEl.addEventListener('animationend', () => {
      htmlEl.classList.remove(config.enter)
    }, { once: true })
  })
}

/**
 * 视差滚动动画
 */
export function createParallaxEffect(element: HTMLElement, speed: number = 0.5) {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const handleScroll = () => {
          const scrolled = window.pageYOffset
          const rate = scrolled * speed
          element.style.transform = `translateY(${rate}px)`
        }
        
        window.addEventListener('scroll', handleScroll)
        
        // 清理函数
        return () => {
          window.removeEventListener('scroll', handleScroll)
        }
      }
    })
  })
  
  observer.observe(element)
  return observer
}

/**
 * 元素进入视口动画
 */
export function createScrollAnimation(
  element: HTMLElement, 
  animationClass: string, 
  threshold: number = 0.1
) {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add(animationClass)
        observer.unobserve(entry.target)
      }
    })
  }, {
    threshold,
    rootMargin: '0px 0px -100px 0px'
  })
  
  observer.observe(element)
  return observer
}