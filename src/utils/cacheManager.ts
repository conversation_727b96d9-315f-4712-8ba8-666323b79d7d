import type { TokenCalculationRequest, TokenCalculationResponse } from '@/types/billing'
import type { TokenCostPreview } from '@/types/packages'

/**
 * 缓存项接口
 */
interface CacheItem<T> {
  key: string
  data: T
  timestamp: number
  expireTime: number
}

/**
 * 缓存管理器类
 */
class CacheManager<T> {
  private cache = new Map<string, CacheItem<T>>()
  private readonly ttl: number
  private readonly maxSize: number
  private cleanupTimer: NodeJS.Timeout | null = null

  constructor(ttl: number = 300000, maxSize: number = 100) { // 默认5分钟缓存，最多100个项目
    this.ttl = ttl
    this.maxSize = maxSize
    this.startCleanupTimer()
  }

  /**
   * 生成缓存键
   */
  generateKey(request: TokenCalculationRequest): string {
    const thoughtChain = request.thoughtChainTokens || 0
    return `${request.inputTokens}:${request.outputTokens}:${thoughtChain}`
  }

  /**
   * 生成带用户和套餐信息的缓存键
   */
  generateKeyWithContext(request: TokenCalculationRequest, packageId?: number, userId?: string): string {
    const thoughtChain = request.thoughtChainTokens || 0
    const pkg = packageId || 'default'
    const user = userId || 'anonymous'
    return `${request.inputTokens}:${request.outputTokens}:${thoughtChain}:${pkg}:${user}`
  }

  /**
   * 生成预览缓存键
   */
  generatePreviewKey(packageId: number, inputTokens: number, outputTokens: number): string {
    return `preview:${packageId}:${inputTokens}:${outputTokens}`
  }

  /**
   * 设置缓存
   */
  set(key: string, data: T): void {
    const now = Date.now()
    const expireTime = now + this.ttl

    // 如果缓存已满，删除最老的项目
    if (this.cache.size >= this.maxSize) {
      this.removeOldest()
    }

    this.cache.set(key, {
      key,
      data,
      timestamp: now,
      expireTime,
    })
  }

  /**
   * 获取缓存
   */
  get(key: string): T | null {
    const item = this.cache.get(key)

    if (!item) {
      return null
    }

    // 检查是否过期
    if (Date.now() > item.expireTime) {
      this.cache.delete(key)
      return null
    }

    return item.data
  }

  /**
   * 删除指定缓存
   */
  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.cache.clear()
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): {
    size: number
    maxSize: number
    hitRate: number
    oldestItem: number | null
  } {
    const items = Array.from(this.cache.values())
    const oldestTimestamp = items.length > 0
      ? Math.min(...items.map(item => item.timestamp))
      : null

    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: 0, // 需要在使用时计算
      oldestItem: oldestTimestamp,
    }
  }

  /**
   * 清理过期缓存
   */
  private cleanup(): void {
    const now = Date.now()
    const expiredKeys: string[] = []

    for (const [key, item] of this.cache.entries()) {
      if (now > item.expireTime) {
        expiredKeys.push(key)
      }
    }

    expiredKeys.forEach(key => this.cache.delete(key))

    if (expiredKeys.length > 0) {
      //console.log(`🗑️ [缓存管理器] 清理了 ${expiredKeys.length} 个过期缓存项`)
    }
  }

  /**
   * 删除最老的缓存项
   */
  private removeOldest(): void {
    let oldestKey: string | null = null
    let oldestTimestamp = Infinity

    for (const [key, item] of this.cache.entries()) {
      if (item.timestamp < oldestTimestamp) {
        oldestTimestamp = item.timestamp
        oldestKey = key
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey)
      //console.log(`🗑️ [缓存管理器] 删除最老的缓存项: ${oldestKey}`)
    }
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    // 每10分钟清理一次过期缓存
    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, 10 * 60 * 1000)
  }

  /**
   * 停止清理定时器
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }
    this.clear()
  }
}

// 创建单例缓存管理器实例
export const tokenCalculationCacheManager = new CacheManager<TokenCalculationResponse>(300000, 100)
export const tokenPreviewCacheManager = new CacheManager<TokenCostPreview>(300000, 50)

export { CacheManager }
export type { CacheItem }
