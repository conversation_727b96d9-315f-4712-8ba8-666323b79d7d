// 租户分类常量定义

/**
 * 行业类型常量
 */
export const INDUSTRY_TYPES = {
  SHARE: 'SHARE',
  MEDICAL_BEAUTY: 'MEDICAL_BEAUTY',
  REAL_ESTATE: 'REAL_ESTATE',
} as const

/**
 * 租户类型常量
 */
export const TENANT_TYPES = {
  ORGANIZATION: 'ORGANIZATION',
  ORGANIZATION_GROUP: 'ORGANIZATION_GROUP',
  MANUFACTURER: 'MANUFACTURER',
  CONSUMER: 'CONSUMER',
} as const

/**
 * 行业类型选项列表
 */
export const INDUSTRY_TYPE_OPTIONS = [
  { label: '共用', value: INDUSTRY_TYPES.SHARE },
  { label: '医美', value: INDUSTRY_TYPES.MEDICAL_BEAUTY },
  { label: '地产', value: INDUSTRY_TYPES.REAL_ESTATE },
]

/**
 * 租户类型选项列表
 */
export const TENANT_TYPE_OPTIONS = [
  { label: '机构', value: TENANT_TYPES.ORGANIZATION },
  { label: '机构集团', value: TENANT_TYPES.ORGANIZATION_GROUP },
  { label: '厂家', value: TENANT_TYPES.MANUFACTURER },
  { label: '消费者', value: TENANT_TYPES.CONSUMER },
]

/**
 * 根据行业类型获取对应的租户类型选项
 * @param industryType 行业类型
 * @returns 租户类型选项列表
 */
export function getTenantTypesByIndustry(industryType?: string): typeof TENANT_TYPE_OPTIONS {
  // 目前所有行业都支持所有租户类型
  return TENANT_TYPE_OPTIONS
}

/**
 * 获取行业类型描述
 * @param industryType 行业类型代码
 * @returns 行业类型描述
 */
export function getIndustryTypeText(industryType?: string): string {
  if (!industryType) return ''

  switch (industryType) {
    case INDUSTRY_TYPES.SHARE:
      return '共用'
    case INDUSTRY_TYPES.MEDICAL_BEAUTY:
      return '医美'
    case INDUSTRY_TYPES.REAL_ESTATE:
      return '地产'
    default:
      return ''
  }
}

/**
 * 获取租户类型描述
 * @param tenantType 租户类型代码
 * @returns 租户类型描述
 */
export function getTenantTypeText(tenantType?: string): string {
  if (!tenantType) return ''

  switch (tenantType) {
    case TENANT_TYPES.ORGANIZATION:
      return '机构'
    case TENANT_TYPES.ORGANIZATION_GROUP:
      return '机构集团'
    case TENANT_TYPES.MANUFACTURER:
      return '厂家'
    case TENANT_TYPES.CONSUMER:
      return '消费者'
    default:
      return ''
  }
}

export type IndustryType = typeof INDUSTRY_TYPES[keyof typeof INDUSTRY_TYPES]
export type TenantType = typeof TENANT_TYPES[keyof typeof TENANT_TYPES]
