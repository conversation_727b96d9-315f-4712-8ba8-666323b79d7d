import type { IndustryType, TenantType } from '@/utils/tenant-constants'

/**
 * 主题管理器
 * 根据用户行业类型和租户类型动态应用主题
 */
export class ThemeManager {
  private static instance: ThemeManager
  private currentTheme: string = 'default'
  private currentColorScheme: 'light' | 'dark' = 'light'

  private constructor() {}

  static getInstance(): ThemeManager {
    if (!ThemeManager.instance) {
      ThemeManager.instance = new ThemeManager()
    }
    return ThemeManager.instance
  }

  /**
   * 根据行业类型获取推荐主题
   */
  getRecommendedTheme(industryType: IndustryType): string {
    const themeMap: Record<IndustryType, string> = {
      MEDICAL_BEAUTY: 'medical-beauty',
      REAL_ESTATE: 'real-estate',
      GENERAL: 'default'
    }
    return themeMap[industryType] || 'default'
  }

  /**
   * 应用主题
   */
  applyTheme(themeName: string, colorScheme: 'light' | 'dark' = 'light') {
    const html = document.documentElement
    
    // 移除之前的主题类
    html.classList.remove(`theme-${this.currentTheme}`)
    html.classList.remove(this.currentColorScheme === 'dark' ? 'dark' : 'light')
    
    // 应用新主题
    html.setAttribute('data-theme', themeName)
    html.classList.add(`theme-${themeName}`)
    html.classList.add(colorScheme)
    
    this.currentTheme = themeName
    this.currentColorScheme = colorScheme
    
    // 保存到本地存储
    localStorage.setItem('theme', themeName)
    localStorage.setItem('colorScheme', colorScheme)
  }

  /**
   * 切换明暗模式
   */
  toggleColorScheme() {
    const newScheme = this.currentColorScheme === 'light' ? 'dark' : 'light'
    this.applyTheme(this.currentTheme, newScheme)
  }

  /**
   * 根据用户信息自动应用主题
   */
  autoApplyTheme(industryType?: IndustryType, userPreference?: string) {
    // 优先使用用户自定义主题
    if (userPreference) {
      this.applyTheme(userPreference, this.currentColorScheme)
      return
    }

    // 根据行业类型应用推荐主题
    if (industryType) {
      const recommendedTheme = this.getRecommendedTheme(industryType)
      this.applyTheme(recommendedTheme, this.currentColorScheme)
      return
    }

    // 使用默认主题
    this.applyTheme('default', this.currentColorScheme)
  }

  /**
   * 初始化主题
   */
  initialize() {
    // 从本地存储读取主题设置
    const savedTheme = localStorage.getItem('theme') || 'default'
    const savedColorScheme = (localStorage.getItem('colorScheme') as 'light' | 'dark') || 'light'
    
    // 检测系统主题偏好
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
    const initialColorScheme = savedColorScheme === 'light' ? 'light' : (prefersDark ? 'dark' : 'light')
    
    this.applyTheme(savedTheme, initialColorScheme)
    
    // 监听系统主题变化
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (!localStorage.getItem('colorScheme')) {
        this.applyTheme(this.currentTheme, e.matches ? 'dark' : 'light')
      }
    })
  }

  /**
   * 获取当前主题
   */
  getCurrentTheme(): string {
    return this.currentTheme
  }

  /**
   * 获取当前色彩模式
   */
  getCurrentColorScheme(): 'light' | 'dark' {
    return this.currentColorScheme
  }

  /**
   * 获取可用主题列表
   */
  getAvailableThemes(): Array<{ value: string; label: string; industry?: IndustryType }> {
    return [
      { value: 'default', label: '默认主题' },
      { value: 'medical-beauty', label: '医美主题', industry: 'MEDICAL_BEAUTY' },
      { value: 'real-estate', label: '地产主题', industry: 'REAL_ESTATE' },
      { value: 'blue', label: '蓝色主题' },
      { value: 'green', label: '绿色主题' },
      { value: 'orange', label: '橙色主题' },
      { value: 'rose', label: '玫瑰主题' },
      { value: 'red', label: '红色主题' },
      { value: 'yellow', label: '黄色主题' },
      { value: 'violet', label: '紫色主题' }
    ]
  }

  /**
   * 预加载主题样式
   */
  preloadTheme(themeName: string) {
    // 创建临时元素来预加载主题CSS变量
    const testElement = document.createElement('div')
    testElement.setAttribute('data-theme', themeName)
    testElement.style.position = 'absolute'
    testElement.style.visibility = 'hidden'
    document.body.appendChild(testElement)
    
    // 触发样式计算
    window.getComputedStyle(testElement).getPropertyValue('--primary')
    
    // 清理
    document.body.removeChild(testElement)
  }
}

// 导出单例实例
export const themeManager = ThemeManager.getInstance()