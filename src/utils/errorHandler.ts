/**
 * 错误类型枚举
 */
export enum ErrorType {
  NETWORK_ERROR = 'network',
  SERVER_ERROR = 'server',
  CALCULATION_ERROR = 'calculation',
  TIMEOUT_ERROR = 'timeout',
  VALIDATION_ERROR = 'validation',
  UNKNOWN_ERROR = 'unknown',
}

/**
 * 错误处理配置
 */
interface ErrorConfig {
  type: ErrorType
  message: string
  retryable: boolean
  maxRetries: number
  userMessage: string
  actions?: ErrorAction[]
}

/**
 * 错误处理动作
 */
interface ErrorAction {
  label: string
  action: () => void | Promise<void>
  type: 'primary' | 'default' | 'danger'
}

/**
 * 重试配置
 */
interface RetryConfig {
  maxRetries: number
  baseDelay: number
  maxDelay: number
  backoffMultiplier: number
}

/**
 * 错误分类器
 */
export function classifyError(error: any): ErrorConfig {
  // 网络错误
  if (error.code === 'NETWORK_ERROR' || error.message?.includes('网络')) {
    return {
      type: ErrorType.NETWORK_ERROR,
      message: error.message || '网络连接异常',
      retryable: true,
      maxRetries: 3,
      userMessage: '网络连接不稳定，请检查网络后重试',
      actions: [
        {
          label: '重试',
          action: () => window.location.reload(),
          type: 'primary',
        },
      ],
    }
  }

  // 服务器错误 (5xx)
  if (error.status >= 500 && error.status < 600) {
    return {
      type: ErrorType.SERVER_ERROR,
      message: error.message || '服务器内部错误',
      retryable: true,
      maxRetries: 1,
      userMessage: '服务器暂时不可用，请稍后重试',
      actions: [
        {
          label: '重试',
          action: async () => {
            await new Promise(resolve => setTimeout(resolve, 1000))
          },
          type: 'primary',
        },
      ],
    }
  }

  // 客户端错误 (4xx)
  if (error.status >= 400 && error.status < 500) {
    return {
      type: ErrorType.VALIDATION_ERROR,
      message: error.message || '请求参数错误',
      retryable: false,
      maxRetries: 0,
      userMessage: error.message || '请求参数有误，请检查后重试',
    }
  }

  // 超时错误
  if (error.code === 'TIMEOUT' || error.message?.includes('timeout')) {
    return {
      type: ErrorType.TIMEOUT_ERROR,
      message: error.message || '请求超时',
      retryable: true,
      maxRetries: 2,
      userMessage: '请求超时，网络可能较慢，请重试',
      actions: [
        {
          label: '重试',
          action: async () => {
            await new Promise(resolve => setTimeout(resolve, 500))
          },
          type: 'primary',
        },
      ],
    }
  }

  // 计算错误
  if (error.message?.includes('计算') || error.message?.includes('token')) {
    return {
      type: ErrorType.CALCULATION_ERROR,
      message: error.message || 'Token计算失败',
      retryable: true,
      maxRetries: 1,
      userMessage: 'Token费用计算失败，请重试',
      actions: [
        {
          label: '重试',
          action: async () => {
            // 可以在这里添加特定的重试逻辑
          },
          type: 'primary',
        },
        {
          label: '使用预估值',
          action: () => {
            // 可以提供一个预估值作为备选
          },
          type: 'default',
        },
      ],
    }
  }

  // 未知错误
  return {
    type: ErrorType.UNKNOWN_ERROR,
    message: error.message || '未知错误',
    retryable: false,
    maxRetries: 0,
    userMessage: '出现未知错误，请联系技术支持',
    actions: [
      {
        label: '刷新页面',
        action: () => window.location.reload(),
        type: 'default',
      },
    ],
  }
}

/**
 * 指数退避重试函数
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  config: RetryConfig = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffMultiplier: 2,
  },
): Promise<T> {
  let lastError: any

  for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
    try {
      const result = await fn()
      if (attempt > 0) {
        //console.log(`✅ [重试成功] 第 ${attempt} 次重试成功`)
      }
      return result
    }
    catch (error) {
      lastError = error

      if (attempt === config.maxRetries) {
        console.error(`❌ [重试失败] 已达到最大重试次数 ${config.maxRetries}`)
        break
      }

      const errorConfig = classifyError(error)
      if (!errorConfig.retryable) {
        //console.log(`⏹️ [重试中止] 错误不可重试: ${errorConfig.type}`)
        break
      }

      const delay = Math.min(
        config.baseDelay * config.backoffMultiplier ** attempt,
        config.maxDelay,
      )

      //console.log(`⏳ [重试等待] 第 ${attempt + 1} 次重试，等待 ${delay}ms`)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }

  throw lastError
}

/**
 * 增强的错误处理器
 */
export class EnhancedErrorHandler {
  private retryQueue = new Map<string, number>()

  /**
   * 处理错误并决定是否重试
   */
  async handleError<T>(
    errorKey: string,
    fn: () => Promise<T>,
    onError?: (config: ErrorConfig) => void,
    onRetry?: (attempt: number) => void,
  ): Promise<T | null> {
    try {
      const result = await fn()
      // 成功时清除重试计数
      this.retryQueue.delete(errorKey)
      return result
    }
    catch (error) {
      const errorConfig = classifyError(error)
      const currentRetries = this.retryQueue.get(errorKey) || 0

      // 通知错误处理回调
      if (onError) {
        onError(errorConfig)
      }

      // 检查是否可以重试
      if (errorConfig.retryable && currentRetries < errorConfig.maxRetries) {
        this.retryQueue.set(errorKey, currentRetries + 1)

        // 通知重试回调
        if (onRetry) {
          onRetry(currentRetries + 1)
        }

        // 执行重试
        try {
          return await retryWithBackoff(fn, {
            maxRetries: errorConfig.maxRetries - currentRetries,
            baseDelay: 1000,
            maxDelay: 5000,
            backoffMultiplier: 2,
          })
        }
        catch (retryError) {
          // 重试也失败了
          this.retryQueue.delete(errorKey)
          console.error(`❌ [最终失败] ${errorKey}:`, retryError)
          if (onError) {
            onError(classifyError(retryError))
          }
          return null
        }
      }
      else {
        // 不可重试或已达到最大重试次数
        this.retryQueue.delete(errorKey)
        console.error(`❌ [处理失败] ${errorKey}:`, error)
        return null
      }
    }
  }

  /**
   * 获取错误的重试次数
   */
  getRetryCount(errorKey: string): number {
    return this.retryQueue.get(errorKey) || 0
  }

  /**
   * 清除指定错误的重试记录
   */
  clearRetryCount(errorKey: string): void {
    this.retryQueue.delete(errorKey)
  }

  /**
   * 清除所有重试记录
   */
  clearAllRetries(): void {
    this.retryQueue.clear()
  }
}

// 创建单例错误处理器
export const errorHandler = new EnhancedErrorHandler()

// 错误提示工具函数
export function showErrorMessage(config: ErrorConfig): void {
  // 这里可以集成具体的UI框架显示错误信息
  console.error(`🚨 [用户错误] ${config.userMessage}`)
}

export type { ErrorAction, ErrorConfig, RetryConfig }
