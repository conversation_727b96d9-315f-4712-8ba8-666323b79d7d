import 'dayjs/locale/zh-cn'

/**
 * 对话工具模块
 * 提供轮次识别、轮询控制等聊天相关的工具函数
 *
 * <AUTHOR>
 * @since 1.0.0
 */

export interface ChatMessage {
  id?: string
  role: 'user' | 'assistant' | string
  content?: string
  createdAt?: string
  loading?: boolean
  error?: boolean
  intermediateSteps?: any[]
  roundSequence?: number
  messageOrder?: number

  // 新增：支持多类型内容列表
  list?: Array<{
    type: 'text' | 'suggestion' | 'file' | 'collapsible'
    content: any
  }>

  [key: string]: any
}

export interface RoundInfo {
  roundSequence: number
  conversationId: string
  messages: ChatMessage[]
  isLastRoundMessage: boolean
}

export interface TokenUsageState {
  status: 'idle' | 'loading' | 'success' | 'error'
  data: any | null
  error: string | null
  retryCount: number
  pollTimer: number | null
}

/**
 * 识别对话中的轮次信息
 * 一个轮次包含一个用户问题和对应的所有助手回复
 */
export function identifyRounds(messages: ChatMessage[]): Map<number, RoundInfo> {
  const rounds = new Map<number, RoundInfo>()

  if (!messages || messages.length === 0) {
    return rounds
  }

  let currentRound = 1
  let currentRoundMessages: ChatMessage[] = []

  for (let i = 0; i < messages.length; i++) {
    const message = messages[i]
    const role = typeof message.role === 'string' ? message.role.toLowerCase() : 'assistant'
    const isUser = role === 'user' || role.includes('user')

    // 如果是用户消息且不是第一条消息，则开始新轮次
    if (isUser && currentRoundMessages.length > 0) {
      // 保存当前轮次
      if (currentRoundMessages.length > 0) {
        rounds.set(currentRound, {
          roundSequence: currentRound,
          conversationId: message.conversationId || '',
          messages: [...currentRoundMessages],
          isLastRoundMessage: true,
        })
      }

      // 开始新轮次
      currentRound++
      currentRoundMessages = []
    }

    // 添加消息到当前轮次
    currentRoundMessages.push({
      ...message,
      roundSequence: currentRound,
      messageOrder: currentRoundMessages.length + 1,
    })
  }

  // 保存最后一个轮次
  if (currentRoundMessages.length > 0) {
    rounds.set(currentRound, {
      roundSequence: currentRound,
      conversationId: currentRoundMessages[0].conversationId || '',
      messages: [...currentRoundMessages],
      isLastRoundMessage: true,
    })
  }

  return rounds
}

/**
 * 判断消息是否为轮次中的最后一条助手消息
 */
export function isLastAssistantMessageInRound(message: ChatMessage, allMessages: ChatMessage[]): boolean {
  const rounds = identifyRounds(allMessages)
  const messageRound = rounds.get(message.roundSequence || 1)

  if (!messageRound) {
    return false
  }

  const assistantMessages = messageRound.messages.filter(m =>
    m.role === 'assistant' || m.role === 'ASSISTANT',
  )

  return assistantMessages.length > 0
    && assistantMessages[assistantMessages.length - 1].id === message.id
}

/**
 * 创建 Token 用量状态管理器
 */
export function createTokenStateManager(initialState?: Partial<TokenUsageState>): {
  state: TokenUsageState
  setState: (updates: Partial<TokenUsageState>) => void
  reset: () => void
} {
  const defaultState: TokenUsageState = {
    status: 'idle',
    data: null,
    error: null,
    retryCount: 0,
    pollTimer: null,
  }

  let state = { ...defaultState, ...initialState }

  const setState = (updates: Partial<TokenUsageState>) => {
    state = { ...state, ...updates }
  }

  const reset = () => {
    if (state.pollTimer) {
      clearTimeout(state.pollTimer)
    }
    state = { ...defaultState }
  }

  return { state, setState, reset }
}

/**
 * 创建智能轮询控制器
 */
export function createTokenUsagePoller() {
  let pollTimer: number | null = null
  let isPolling = false
  let retryCount = 0
  const maxRetries = 12 // 最多轮询12次（60秒）
  const pollInterval = 5000 // 5秒间隔

  const stop = () => {
    if (pollTimer) {
      clearTimeout(pollTimer)
      pollTimer = null
    }
    isPolling = false
    retryCount = 0
  }

  const start = (
    fetchFunction: () => Promise<any>,
    onSuccess: (data: any) => void,
    onError: (error: any) => void,
    onUpdate?: (data: any) => void,
  ) => {
    if (isPolling) {
      return
    }

    isPolling = true
    retryCount = 0

    const poll = async () => {
      try {
        const data = await fetchFunction()

        if (data && data.status === 'SUCCESS') {
          // 计算完成，停止轮询
          stop()
          onSuccess(data)
        }
        else if (data && data.status === 'FAILED') {
          // 计算失败，停止轮询
          stop()
          onError(new Error(data.errorMessage || 'Token计算失败'))
        }
        else if (data && data.status === 'PENDING') {
          // 仍在计算中，继续轮询
          retryCount++
          if (onUpdate) {
            onUpdate(data)
          }

          if (retryCount >= maxRetries) {
            // 超过最大重试次数
            stop()
            onError(new Error('Token计算超时，请稍后刷新查看'))
          }
          else {
            // 继续轮询
            pollTimer = window.setTimeout(poll, pollInterval)
          }
        }
      }
      catch (error) {
        retryCount++
        if (retryCount >= maxRetries) {
          stop()
          onError(error)
        }
        else {
          // 继续重试
          pollTimer = window.setTimeout(poll, pollInterval)
        }
      }
    }

    poll()
  }

  const isActive = () => isPolling

  const cleanup = () => {
    stop()
  }

  return { start, stop, isActive, cleanup }
}

/**
 * 循环加载消息功能（保持与现有代码兼容）
 */
export const LOADING_MESSAGES = [
  '正在思考中...',
  '正在分析问题...',
  '正在准备回答...',
  '正在整理思路...',
  '即将完成回答...',
]

export function createCyclicLoadingMessage(updateCallback: (message: string) => void) {
  let currentIndex = 0
  let intervalId: number | null = null
  let isActive = false

  const start = () => {
    if (isActive) {
      return
    }

    isActive = true
    currentIndex = 0

    const cycle = () => {
      updateCallback(LOADING_MESSAGES[currentIndex])
      currentIndex = (currentIndex + 1) % LOADING_MESSAGES.length
      intervalId = window.setTimeout(cycle, 2000) // 每2秒切换一次
    }

    cycle()
  }

  const stop = () => {
    if (intervalId) {
      clearTimeout(intervalId)
      intervalId = null
    }
    isActive = false
  }

  const cleanup = () => {
    stop()
  }

  return { start, stop, isActive: () => isActive, cleanup }
}

/**
 * 格式化 Token 用量显示
 */
export function formatTokenUsage(data: any): string {
  if (!data) {
    return ''
  }

  const { inputTokens = 0, outputTokens = 0, totalCost = 0 } = data

  return `输入: ${inputTokens?.toLocaleString()} | 输出: ${outputTokens?.toLocaleString()} | 费用: ¥${totalCost?.toFixed(4)}`
}

/**
 * 格式化费用显示
 */
export function formatCost(cost: number): string {
  if (cost === 0) {
    return '¥0.0000'
  }
  if (cost < 0.0001) {
    return '< ¥0.0001'
  }
  return `¥${cost.toFixed(4)}`
}

/**
 * 获取随机加载消息
 * 从预定义的加载消息列表中随机选择一条
 */
export function getRandomLoadingMessage(): string {
  const randomIndex = Math.floor(Math.random() * LOADING_MESSAGES.length)
  return LOADING_MESSAGES[randomIndex]
}

/**
 * 验证字符串是否为有效的 UUID 格式
 * @param str 要验证的字符串
 * @returns 是否为有效的 UUID
 */
export function isValidUUID(str: string): boolean {
  if (!str || typeof str !== 'string') {
    return false
  }

  // UUID v4 格式：xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(str)
}

/**
 * 验证 Token 查询参数的有效性
 * @param conversationId 对话ID
 * @param roundSequence 轮次序号
 * @returns 验证结果对象
 */
export function validateTokenQueryParams(conversationId: string | undefined, roundSequence: number | undefined): {
  isValid: boolean
  error?: string
} {
  //console.log('🔍 [参数验证] 开始验证Token查询参数:', {
    conversationId,
    roundSequence,
    conversationIdType: typeof conversationId,
    roundSequenceType: typeof roundSequence,
  })

  // 检查 conversationId
  if (!conversationId) {
    //console.log('❌ [参数验证] 对话ID为空:', { conversationId })
    return {
      isValid: false,
      error: '对话ID不能为空',
    }
  }

  //console.log('✅ [参数验证] 对话ID非空，检查格式:', { conversationId })

  // 只接受UUID格式的会话ID，拒绝临时会话ID
  const isUUIDFormat = isValidUUID(conversationId)
  //console.log('🔍 [参数验证] UUID格式验证:', {
    conversationId,
    isUUIDFormat,
    isTempConversation: conversationId === 'temp-conversation',
  })

  if (!isUUIDFormat) {
    //console.log('❌ [参数验证] 对话ID格式无效:', {
      conversationId,
      expectedFormat: 'UUID',
      actualFormat: '非UUID格式',
      isTemporary: conversationId === 'temp-conversation',
    })
    return {
      isValid: false,
      error: `对话ID格式无效，需要UUID格式: ${conversationId}`,
    }
  }

  //console.log('✅ [参数验证] 对话ID格式有效，检查轮次序号')

  // 检查 roundSequence
  if (roundSequence === undefined || roundSequence === null) {
    //console.log('❌ [参数验证] 轮次序号为空:', {
      roundSequence,
      isUndefined: roundSequence === undefined,
      isNull: roundSequence === null,
    })
    return {
      isValid: false,
      error: '轮次序号不能为空',
    }
  }

  //console.log('✅ [参数验证] 轮次序号非空，检查数值有效性:', {
    roundSequence,
    isInteger: Number.isInteger(roundSequence),
    isPositive: roundSequence > 0,
  })

  if (!Number.isInteger(roundSequence) || roundSequence <= 0) {
    //console.log('❌ [参数验证] 轮次序号无效:', {
      roundSequence,
      isInteger: Number.isInteger(roundSequence),
      isPositive: roundSequence > 0,
      expectedCondition: '必须是大于0的整数',
    })
    return {
      isValid: false,
      error: `轮次序号必须是大于0的整数: ${roundSequence}`,
    }
  }

  //console.log('✅ [参数验证] 所有参数验证通过:', {
    conversationId,
    roundSequence,
    result: 'VALID',
  })

  return {
    isValid: true,
  }
}

/**
 * 从消息历史中提取真实的会话ID
 * 如果当前会话ID是临时ID（如'temp-conversation'），尝试从最新的消息中获取真实的会话ID
 * @param currentConversationId 当前会话ID
 * @param messages 消息历史数组
 * @returns 真实的会话ID，如果无法获取则返回null
 */
export function extractRealConversationId(
  currentConversationId: string | undefined,
  messages: ChatMessage[],
): string | null {
  //console.log('🔍 [ID提取] 开始提取真实会话ID:', {
    currentConversationId,
    messagesCount: messages ? messages.length : 0,
    currentIdType: typeof currentConversationId,
  })

  // 如果当前会话ID已经是有效的UUID，直接返回
  if (currentConversationId && isValidUUID(currentConversationId)) {
    //console.log('✅ [ID提取] 当前会话ID已是有效UUID，直接返回:', {
      conversationId: currentConversationId,
    })
    return currentConversationId
  }

  //console.log('⚠️ [ID提取] 当前会话ID无效或为临时ID，尝试从消息中提取:', {
    currentConversationId,
    isTemporary: currentConversationId === 'temp-conversation',
    isUndefined: currentConversationId === undefined,
    isNull: currentConversationId === null,
  })

  // 如果是临时会话ID或无效ID，尝试从消息中获取真实ID
  if (!messages || messages.length === 0) {
    //console.log('❌ [ID提取] 消息历史为空，无法提取会话ID:', {
      messages,
      messagesLength: messages ? messages.length : 'undefined',
    })
    return null
  }

  //console.log('🔍 [ID提取] 开始遍历消息寻找有效会话ID:', {
    totalMessages: messages.length,
    messageIds: messages.map(m => m.id),
    messageConversationIds: messages.map(m => ({
      messageId: m.id,
      conversationId: m.conversationId,
      isValidUUID: m.conversationId ? isValidUUID(m.conversationId) : false,
    })),
  })

  // 从最新的消息开始查找真实的会话ID
  for (let i = messages.length - 1; i >= 0; i--) {
    const message = messages[i]
    //console.log(`🔍 [ID提取] 检查消息 ${i + 1}/${messages.length}:`, {
      messageId: message.id,
      conversationId: message.conversationId,
      isValidUUID: message.conversationId ? isValidUUID(message.conversationId) : false,
      messageIndex: i,
    })

    if (message.conversationId && isValidUUID(message.conversationId)) {
      //console.log('✅ [ID提取] 从消息中成功提取到真实会话ID:', {
        messageId: message.id,
        messageIndex: i,
        extractedConversationId: message.conversationId,
        originalConversationId: currentConversationId,
      })
      return message.conversationId
    }
  }

  console.warn('❌ [ID提取] 无法从消息历史中提取到有效的会话ID:', {
    currentConversationId,
    messagesCount: messages.length,
    messageIds: messages.map(m => m.id),
    searchedConversationIds: messages.map(m => m.conversationId),
    allConversationIdsInvalid: messages.every(m => !m.conversationId || !isValidUUID(m.conversationId)),
  })
  return null
}
