// 医美行业专用样式
.theme-medical-beauty {
  // 渐变背景
  .medical-gradient-bg {
    background: linear-gradient(135deg, 
      hsl(var(--medical-gradient-start)), 
      hsl(var(--medical-gradient-end)));
    background-attachment: fixed;
  }
  
  // 卡片样式
  .medical-card {
    background: hsl(var(--medical-card-bg));
    border: 1px solid hsl(var(--medical-border));
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(329, 100%, 65%, 0.1);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, 
        hsl(var(--medical-gradient-start)), 
        hsl(var(--medical-gradient-end)));
    }
    
    &:hover {
      transform: translateY(-8px) scale(1.02);
      box-shadow: 0 20px 40px rgba(329, 100%, 65%, 0.2);
    }
  }
  
  // 按钮样式
  .medical-button {
    background: linear-gradient(45deg, 
      hsl(var(--primary)), 
      hsl(var(--accent)));
    border: none;
    border-radius: 24px;
    color: hsl(var(--primary-foreground));
    font-weight: 600;
    padding: 12px 28px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.2), 
        transparent);
      transition: left 0.5s;
    }
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(329, 100%, 65%, 0.3);
      
      &::before {
        left: 100%;
      }
    }
    
    &:active {
      transform: translateY(0);
    }
  }
  
  // 医美特色装饰元素
  .medical-ornament {
    position: relative;
    
    &::after {
      content: '✨';
      position: absolute;
      top: -10px;
      right: -10px;
      font-size: 18px;
      opacity: 0.6;
      animation: sparkle 2s ease-in-out infinite;
    }
  }
  
  // 表单输入框样式
  .medical-input {
    border: 2px solid hsl(var(--medical-border));
    border-radius: 12px;
    padding: 12px 16px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
    
    &:focus {
      border-color: hsl(var(--primary));
      box-shadow: 0 0 0 3px rgba(329, 100%, 65%, 0.1);
      background: rgba(255, 255, 255, 1);
    }
  }
  
  // 步骤指示器
  .medical-steps {
    .step {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: hsl(var(--muted));
      border: 3px solid hsl(var(--medical-border));
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      transition: all 0.4s ease;
      
      &.active {
        background: linear-gradient(135deg, 
          hsl(var(--medical-gradient-start)), 
          hsl(var(--medical-gradient-end)));
        border-color: hsl(var(--primary));
        color: white;
        transform: scale(1.1);
      }
      
      &.completed {
        background: hsl(var(--primary));
        border-color: hsl(var(--primary));
        color: white;
        
        &::after {
          content: '✓';
          position: absolute;
          font-weight: bold;
        }
      }
    }
    
    .step-connector {
      height: 3px;
      background: hsl(var(--medical-border));
      position: relative;
      
      &.completed {
        background: linear-gradient(90deg, 
          hsl(var(--medical-gradient-start)), 
          hsl(var(--medical-gradient-end)));
      }
    }
  }
  
  // 价格卡片
  .medical-pricing-card {
    background: hsl(var(--medical-card-bg));
    border: 2px solid hsl(var(--medical-border));
    border-radius: 20px;
    padding: 24px;
    position: relative;
    transition: all 0.3s ease;
    
    &.featured {
      border-color: hsl(var(--primary));
      transform: scale(1.05);
      
      &::before {
        content: '推荐';
        position: absolute;
        top: -12px;
        left: 50%;
        transform: translateX(-50%);
        background: linear-gradient(45deg, 
          hsl(var(--primary)), 
          hsl(var(--accent)));
        color: white;
        padding: 6px 16px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
      }
    }
    
    &:hover {
      border-color: hsl(var(--primary));
      box-shadow: 0 12px 35px rgba(329, 100%, 65%, 0.15);
    }
  }
  
  // 动画定义
  @keyframes sparkle {
    0%, 100% { 
      opacity: 0.6; 
      transform: scale(1) rotate(0deg); 
    }
    50% { 
      opacity: 1; 
      transform: scale(1.1) rotate(180deg); 
    }
  }
  
  // 响应式调整
  @media (max-width: 768px) {
    .medical-card {
      border-radius: 12px;
      padding: 16px;
      
      &:hover {
        transform: translateY(-4px) scale(1.01);
      }
    }
    
    .medical-button {
      border-radius: 20px;
      padding: 10px 24px;
      font-size: 14px;
    }
    
    .medical-pricing-card {
      border-radius: 16px;
      padding: 20px;
      
      &.featured {
        transform: scale(1.02);
      }
    }
  }
  
  // 暗色模式调整
  &.dark {
    .medical-card {
      box-shadow: 0 8px 32px rgba(329, 80%, 55%, 0.2);
      
      &:hover {
        box-shadow: 0 20px 40px rgba(329, 80%, 55%, 0.3);
      }
    }
    
    .medical-input {
      background: rgba(0, 0, 0, 0.3);
      
      &:focus {
        background: rgba(0, 0, 0, 0.5);
        box-shadow: 0 0 0 3px rgba(329, 80%, 55%, 0.2);
      }
    }
  }
}