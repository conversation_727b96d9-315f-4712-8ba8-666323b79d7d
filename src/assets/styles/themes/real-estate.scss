// 地产行业专用样式
.theme-real-estate {
  // 商务风格渐变
  .estate-gradient-bg {
    background: linear-gradient(135deg,
      hsl(var(--estate-gradient-start)),
      hsl(var(--estate-gradient-end)));
    background-attachment: fixed;
  }
  
  // 专业卡片样式
  .estate-card {
    background: hsl(var(--estate-card-bg));
    border: 1px solid hsl(var(--estate-border));
    border-radius: 8px;
    box-shadow: 0 4px 24px rgba(210, 100%, 50%, 0.08);
    transition: all 0.3s ease-in-out;
    position: relative;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, 
        hsl(var(--estate-gradient-start)), 
        hsl(var(--estate-gradient-end)));
    }
    
    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 32px rgba(210, 100%, 50%, 0.15);
      border-color: hsl(var(--primary));
    }
  }
  
  // 商务按钮样式
  .estate-button {
    background: hsl(var(--primary));
    border: none;
    border-radius: 6px;
    color: hsl(var(--primary-foreground));
    font-weight: 600;
    padding: 12px 24px;
    transition: all 0.2s ease-in-out;
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.15), 
        transparent);
      transition: left 0.4s;
    }
    
    &:hover {
      background: hsl(var(--estate-gradient-end));
      transform: translateY(-1px);
      box-shadow: 0 6px 20px rgba(210, 100%, 50%, 0.2);
      
      &::before {
        left: 100%;
      }
    }
    
    &:active {
      transform: translateY(0);
    }
  }
  
  // 地产专业装饰元素
  .estate-ornament {
    position: relative;
    
    &::after {
      content: '🏢';
      position: absolute;
      top: -8px;
      right: -8px;
      font-size: 16px;
      opacity: 0.7;
      animation: building-pulse 3s ease-in-out infinite;
    }
  }
  
  // 表单输入框样式
  .estate-input {
    border: 1px solid hsl(var(--estate-border));
    border-radius: 6px;
    padding: 12px 16px;
    transition: all 0.2s ease;
    background: rgba(255, 255, 255, 0.9);
    
    &:focus {
      border-color: hsl(var(--primary));
      box-shadow: 0 0 0 2px rgba(210, 100%, 50%, 0.1);
      background: rgba(255, 255, 255, 1);
    }
  }
  
  // 步骤指示器
  .estate-steps {
    .step {
      width: 36px;
      height: 36px;
      border-radius: 6px;
      background: hsl(var(--muted));
      border: 2px solid hsl(var(--estate-border));
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      transition: all 0.3s ease;
      font-weight: 600;
      
      &.active {
        background: hsl(var(--primary));
        border-color: hsl(var(--primary));
        color: white;
        transform: scale(1.05);
      }
      
      &.completed {
        background: hsl(var(--primary));
        border-color: hsl(var(--primary));
        color: white;
        
        &::after {
          content: '✓';
          position: absolute;
          font-weight: bold;
        }
      }
    }
    
    .step-connector {
      height: 2px;
      background: hsl(var(--estate-border));
      position: relative;
      
      &.completed {
        background: hsl(var(--primary));
      }
    }
  }
  
  // 价格卡片
  .estate-pricing-card {
    background: hsl(var(--estate-card-bg));
    border: 1px solid hsl(var(--estate-border));
    border-radius: 12px;
    padding: 24px;
    position: relative;
    transition: all 0.3s ease;
    
    &.featured {
      border-color: hsl(var(--primary));
      border-width: 2px;
      transform: scale(1.03);
      
      &::before {
        content: '推荐';
        position: absolute;
        top: -10px;
        left: 50%;
        transform: translateX(-50%);
        background: hsl(var(--primary));
        color: white;
        padding: 4px 12px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
      }
    }
    
    &:hover {
      border-color: hsl(var(--primary));
      box-shadow: 0 8px 28px rgba(210, 100%, 50%, 0.12);
    }
  }
  
  // 数据展示卡片
  .estate-data-card {
    background: hsl(var(--estate-card-bg));
    border: 1px solid hsl(var(--estate-border));
    border-radius: 8px;
    padding: 20px;
    position: relative;
    
    .data-value {
      font-size: 24px;
      font-weight: 700;
      color: hsl(var(--primary));
      margin-bottom: 4px;
    }
    
    .data-label {
      font-size: 14px;
      color: hsl(var(--muted-foreground));
      font-weight: 500;
    }
    
    .data-trend {
      position: absolute;
      top: 16px;
      right: 16px;
      font-size: 12px;
      padding: 2px 6px;
      border-radius: 4px;
      
      &.positive {
        background: rgba(34, 197, 94, 0.1);
        color: rgb(34, 197, 94);
      }
      
      &.negative {
        background: rgba(239, 68, 68, 0.1);
        color: rgb(239, 68, 68);
      }
    }
  }
  
  // 专业图表容器
  .estate-chart-container {
    background: hsl(var(--estate-card-bg));
    border: 1px solid hsl(var(--estate-border));
    border-radius: 12px;
    padding: 24px;
    position: relative;
    
    .chart-title {
      font-size: 16px;
      font-weight: 600;
      color: hsl(var(--foreground));
      margin-bottom: 16px;
    }
    
    .chart-legend {
      display: flex;
      gap: 16px;
      margin-top: 12px;
      
      .legend-item {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 12px;
        
        .legend-color {
          width: 12px;
          height: 12px;
          border-radius: 2px;
        }
      }
    }
  }
  
  // 动画定义
  @keyframes building-pulse {
    0%, 100% { 
      opacity: 0.7; 
      transform: scale(1); 
    }
    50% { 
      opacity: 1; 
      transform: scale(1.05); 
    }
  }
  
  // 数据加载动画
  @keyframes data-load {
    0% { 
      transform: scaleY(0); 
      opacity: 0; 
    }
    100% { 
      transform: scaleY(1); 
      opacity: 1; 
    }
  }
  
  .estate-data-enter {
    animation: data-load 0.6s ease-out;
    transform-origin: bottom;
  }
  
  // 响应式调整
  @media (max-width: 768px) {
    .estate-card {
      border-radius: 6px;
      padding: 16px;
      
      &:hover {
        transform: translateY(-2px);
      }
    }
    
    .estate-button {
      padding: 10px 20px;
      font-size: 14px;
    }
    
    .estate-pricing-card {
      border-radius: 10px;
      padding: 20px;
      
      &.featured {
        transform: scale(1.01);
      }
    }
    
    .estate-data-card {
      padding: 16px;
      
      .data-value {
        font-size: 20px;
      }
    }
    
    .estate-chart-container {
      padding: 20px;
      
      .chart-legend {
        flex-wrap: wrap;
        gap: 12px;
      }
    }
  }
  
  // 暗色模式调整
  &.dark {
    .estate-card {
      box-shadow: 0 4px 24px rgba(210, 80%, 55%, 0.15);
      
      &:hover {
        box-shadow: 0 12px 32px rgba(210, 80%, 55%, 0.25);
      }
    }
    
    .estate-input {
      background: rgba(0, 0, 0, 0.2);
      
      &:focus {
        background: rgba(0, 0, 0, 0.4);
        box-shadow: 0 0 0 2px rgba(210, 80%, 55%, 0.2);
      }
    }
    
    .estate-data-card {
      .data-trend {
        &.positive {
          background: rgba(34, 197, 94, 0.2);
        }
        
        &.negative {
          background: rgba(239, 68, 68, 0.2);
        }
      }
    }
  }
}