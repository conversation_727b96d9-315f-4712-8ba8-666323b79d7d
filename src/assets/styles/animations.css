/* 页面转换动画 */
@keyframes fade-slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-slide-down {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-30px);
  }
}

@keyframes fade-scale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fade-scale-out {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(1.05);
  }
}

@keyframes fade {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* 医美卡片动画 */
@keyframes medical-card-enter {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.medical-card-hover {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.medical-card-hover:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(329, 100%, 65%, 0.2);
}

.medical-card-click {
  transition: transform 0.1s ease;
}

.medical-card-click:active {
  transform: scale(0.98);
}

.medical-card-enter {
  animation: medical-card-enter 0.6s ease-out forwards;
}

/* 地产卡片动画 */
@keyframes estate-card-enter {
  from {
    opacity: 0;
    transform: translateY(16px) scale(0.99);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.estate-card-hover {
  transition: all 0.3s ease-in-out;
}

.estate-card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(210, 100%, 50%, 0.15);
}

.estate-card-click {
  transition: transform 0.08s ease;
}

.estate-card-click:active {
  transform: scale(0.97);
}

.estate-card-enter {
  animation: estate-card-enter 0.5s ease-out forwards;
}

/* 默认卡片动画 */
@keyframes default-card-enter {
  from {
    opacity: 0;
    transform: translateY(12px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.default-card-hover {
  transition: all 0.2s ease;
}

.default-card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.default-card-click {
  transition: transform 0.1s ease;
}

.default-card-click:active {
  transform: scale(0.99);
}

.default-card-enter {
  animation: default-card-enter 0.4s ease-out forwards;
}

/* 医美按钮动画 */
@keyframes medical-button-loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.medical-button-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.medical-button-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.medical-button-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(329, 100%, 65%, 0.3);
}

.medical-button-hover:hover::before {
  left: 100%;
}

.medical-button-click {
  transition: transform 0.1s ease;
}

.medical-button-click:active {
  transform: translateY(0);
}

.medical-button-loading::after {
  content: '';
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: medical-button-loading 1s linear infinite;
  margin-left: 8px;
}

/* 地产按钮动画 */
@keyframes estate-button-loading {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.5;
  }
}

.estate-button-hover {
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.estate-button-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
  transition: left 0.4s;
}

.estate-button-hover:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(210, 100%, 50%, 0.2);
}

.estate-button-hover:hover::before {
  left: 100%;
}

.estate-button-click {
  transition: transform 0.08s ease;
}

.estate-button-click:active {
  transform: translateY(0);
}

.estate-button-loading::after {
  content: '...';
  animation: estate-button-loading 1.5s ease-in-out infinite;
  margin-left: 4px;
}

/* 默认按钮动画 */
.default-button-hover {
  transition: all 0.2s ease;
}

.default-button-hover:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.default-button-click {
  transition: transform 0.1s ease;
}

.default-button-click:active {
  transform: translateY(0);
}

.default-button-loading::after {
  content: '';
  display: inline-block;
  width: 14px;
  height: 14px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: medical-button-loading 1s linear infinite;
  margin-left: 6px;
}

/* 按钮点击波纹效果 */
.button-ripple {
  position: relative;
  overflow: hidden;
}

.button-ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.button-ripple:active::after {
  width: 300px;
  height: 300px;
}

/* 输入框焦点动画 */
.input-focus {
  position: relative;
}

.input-focus::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: hsl(var(--primary));
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.input-focus:focus-within::after {
  width: 100%;
}

/* 步骤指示器动画 */
@keyframes step-activate {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1.05);
  }
}

@keyframes step-complete {
  0% {
    transform: scale(1.05);
  }
  50% {
    transform: scale(1.15);
  }
  100% {
    transform: scale(1);
  }
}

.step-activate {
  animation: step-activate 0.4s ease-out;
}

.step-complete {
  animation: step-complete 0.5s ease-out;
}

/* 进度条动画 */
@keyframes progress-fill {
  from {
    width: 0%;
  }
}

.progress-fill {
  animation: progress-fill 0.8s ease-out;
}

/* 数据计数动画 */
@keyframes count-up {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.count-up {
  animation: count-up 0.6s ease-out;
}

/* 图表出现动画 */
@keyframes chart-draw {
  from {
    stroke-dashoffset: 1000;
  }
  to {
    stroke-dashoffset: 0;
  }
}

.chart-draw {
  stroke-dasharray: 1000;
  animation: chart-draw 2s ease-out forwards;
}

/* 弹出框动画 */
@keyframes modal-slide-up {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-slide-up {
  animation: modal-slide-up 0.3s ease-out;
}

/* 通知动画 */
@keyframes notification-slide-in {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes notification-slide-out {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(100%);
  }
}

.notification-slide-in {
  animation: notification-slide-in 0.4s ease-out;
}

.notification-slide-out {
  animation: notification-slide-out 0.3s ease-in;
}

/* 加载骨架屏动画 */
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-loading {
  background: linear-gradient(90deg, 
    hsl(var(--muted)) 25%, 
    hsl(var(--accent)) 50%, 
    hsl(var(--muted)) 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* 视差滚动优化 */
.parallax-element {
  will-change: transform;
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .parallax-element {
    transform: none !important;
  }
}

/* 高性能动画类 */
.gpu-accelerated {
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 交错动画延迟 */
.stagger-1 { animation-delay: 0ms; }
.stagger-2 { animation-delay: 100ms; }
.stagger-3 { animation-delay: 200ms; }
.stagger-4 { animation-delay: 300ms; }
.stagger-5 { animation-delay: 400ms; }
.stagger-6 { animation-delay: 500ms; }