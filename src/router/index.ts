import pinia from '@/store'
import useSettingsStore from '@/store/modules/settings'
import { loadingFadeOut } from 'virtual:app-loading'
import { createRouter, createWebHashHistory } from 'vue-router'
import setupExtensions from './extensions'
import setupGuards from './guards'
// 路由相关数据
import { constantRoutes, constantRoutesByFilesystem } from './routes'

const router = createRouter({
  history: createWebHashHistory(),
  routes: useSettingsStore(pinia).settings.app.routeBaseOn === 'filesystem' ? constantRoutesByFilesystem : constantRoutes,
})

setupGuards(router)
setupExtensions(router)

// 添加路由调试监听
router.beforeEach((to, from, next) => {
  //console.log('🎯 [路由] 路由跳转开始')
  //console.log('  - 从:', from.path)
  //console.log('  - 到:', to.path)
  //console.log('  - 完整路径:', to.fullPath)
  next()
})

router.afterEach((to, from) => {
  //console.log('✅ [路由] 路由跳转完成')
  //console.log('  - 从:', from.path)
  //console.log('  - 到:', to.path)
  //console.log('  - 最终路径:', to.fullPath)
})

router.onError((error) => {
  console.error('❌ [路由] 路由错误:', error)
})

router.isReady().then(() => {
  loadingFadeOut()
})

export default router
