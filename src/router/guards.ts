import type { UserTenantInfo } from '@/views/onboarding/utils/onboardingRouter'
import type { Router } from 'vue-router'
import useFavoritesStore from '@/store/modules/favorites'
import useIframeStore from '@/store/modules/iframe'
import useKeepAliveStore from '@/store/modules/keepAlive'
import useMenuStore from '@/store/modules/menu'
import useRouteStore from '@/store/modules/route'
import useSettingsStore from '@/store/modules/settings'
import useTabbarStore from '@/store/modules/tabbar'
import useUserStore from '@/store/modules/user'
import { getUserOnboardingPath, shouldShowOnboarding } from '@/views/onboarding/utils/onboardingRouter'
import { useNProgress } from '@vueuse/integrations/useNProgress'
import { asyncRoutes, asyncRoutesByFilesystem } from './routes'
import storage from '@/utils/storage'
import '@/assets/styles/nprogress.css'

function setupRoutes(router: Router) {
  router.beforeEach(async (to, _from, next) => {
    const settingsStore = useSettingsStore()
    const userStore = useUserStore()
    const routeStore = useRouteStore()
    const menuStore = useMenuStore()
    const tabbarStore = useTabbarStore()
    const favoritesStore = useFavoritesStore()

    // 添加调试日志
    //console.log(`🚀 [路由守卫] 开始 -> ${to.path} | 登录:${userStore.isLogin} | 白名单:${to?.meta?.whiteList} | 查询:${JSON.stringify(to.query)}`)

    // 是否已登录
    if (userStore.isLogin) {
      //console.log(`✅ [路由守卫] 已登录 | 路由生成:${routeStore.isGenerate}`)
      // 是否已根据权限动态生成并注册路由
      if (routeStore.isGenerate) {
        // 导航栏如果不是 single 模式，则需要根据 path 定位主导航的选中状态
        settingsStore.settings.menu.mode !== 'single' && menuStore.setActived(to.path)
        // 如果已登录状态下，进入登录页会强制跳转到角色对应页面或主页
        if (to.name === 'login') {
          // console.log('🔄 [路由守卫] 检测到已登录用户访问登录页，开始重定向处理')
          // console.log('👤 [路由守卫] 当前登录用户信息:')
          // console.log(`   - 用户ID: ${userStore.userId}`)
          // console.log(`   - 用户名: ${userStore.username}`)
          // console.log(`   - 用户角色: ${JSON.stringify(userStore.roles)}`)

          // 根据用户角色计算跳转路径
          const roleRedirectPath = userStore.calculateRoleRedirectPath()
          // console.log(`🎯 [路由守卫] 角色计算结果路径: ${roleRedirectPath}`)
          // console.log('🚀 [路由守卫] 执行重定向跳转...')

          next({
            path: roleRedirectPath,
            replace: true,
          })
        }
        // 如果未开启主页，但进入的是主页，则会进入第一个固定标签页或者侧边栏导航第一个模块
        else if (!settingsStore.settings.home.enable && to.fullPath === settingsStore.settings.home.fullPath) {
          //console.log('🔄 [路由守卫] 主页未开启，重定向')
          const firstPin = tabbarStore.list.find(v => v.isPin)
          if (settingsStore.settings.tabbar.enable && firstPin) {
            next({
              path: firstPin.fullPath,
              replace: true,
            })
          }
          else if (menuStore.sidebarMenus.length > 0) {
            next({
              path: menuStore.sidebarMenusFirstDeepestPath,
              replace: true,
            })
          }
          // 如果第一个固定标签页和侧边栏导航第一个模块均无法命中，则还是进入主页
          else {
            next()
          }
        }
        // 正常访问页面
        else {
          // 特殊处理：如果访问的是退出登录路由，直接执行退出登录
          if (to.path === '/logout') {
            //console.log('🚪 [路由守卫] 检测到退出登录路由，执行退出登录操作')
            userStore.logout()
            return
          }

          // 检查是否需要显示引导页面（仅在用户登录且有有效用户信息时）
          if (userStore.isLogin && isValidUserSession(userStore)) {
            //console.log('✅ [路由守卫] 正常访问，检查引导页面')
            await checkOnboardingFlow(to, next, userStore)
          } else {
            // 用户session无效，需要重新登录
            if (!isValidUserSession(userStore)) {
              //console.log('🚨 [路由守卫] Session无效，清除token并重定向')
              userStore.logout()
              return
            }
            next()
          }
        }
      }
      else {
        //console.log('🔄 [路由守卫] 开始生成路由')
        try {
          // 获取用户权限
          // settingsStore.settings.app.enablePermission && await userStore.getPermissions()
          await userStore.rolesAndPerms()
          // 获取用户偏好设置
          settingsStore.settings.userPreferences.enable && await userStore.getPreferences()
          // 复原固定标签页
          settingsStore.settings.tabbar.enable && await tabbarStore.recoveryStorage()
          // 复原收藏夹
          settingsStore.settings.toolbar.favorites && await favoritesStore.recoveryStorage()
          // 生成动态路由
          switch (settingsStore.settings.app.routeBaseOn) {
            case 'frontend':
              routeStore.generateRoutesAtFront(asyncRoutes)
              break
            case 'backend':
              await routeStore.generateRoutesAtBack()
              break
            case 'filesystem':
              routeStore.generateRoutesAtFilesystem(asyncRoutesByFilesystem)
              // 文件系统生成的路由，需要手动生成导航数据
              switch (settingsStore.settings.menu.baseOn) {
                case 'frontend':
                  menuStore.generateMenusAtFront()
                  break
                case 'backend':
                  await menuStore.generateMenusAtBack()
                  break
              }
              break
          }
          // 注册并记录路由数据
          // 记录的数据会在登出时会使用到，不使用 router.removeRoute 是考虑配置的路由可能不一定有设置 name ，则通过调用 router.addRoute() 返回的回调进行删除
          const removeRoutes: (() => void)[] = []
          routeStore.routes.forEach((route) => {
            if (!/^(?:https?:|mailto:|tel:)/.test(route.path)) {
              removeRoutes.push(router.addRoute(route))
            }
          })
          if (settingsStore.settings.app.routeBaseOn !== 'filesystem') {
            routeStore.systemRoutes.forEach((route) => {
              removeRoutes.push(router.addRoute(route))
            })
          }
          routeStore.setCurrentRemoveRoutes(removeRoutes)
          //console.log('✅ [路由守卫] 路由生成完成，重新进入当前路由')
        }
        catch (error) {
          console.error('❌ [路由守卫] 路由生成失败:', error)
          userStore.logout()
        }
        // 动态路由生成并注册后，重新进入当前路由
        next({
          path: to.path,
          query: to.query,
          replace: true,
        })
      }
    }
    else {
      // 特殊处理：如果访问的是退出登录路由，未登录状态下直接重定向到登录页
      if (to.path === '/logout') {
        //console.log('🚪 [路由守卫] 未登录状态访问退出登录路由，重定向到登录页')
        next({
          name: 'login',
          replace: true,
        })
        return
      }

      if (!to?.meta?.whiteList) {
        //console.log(`❌ [路由守卫] 未登录访问 ${to.path}，重定向到登录页`)
        next({
          name: 'login',
          query: {
            redirect: to.fullPath !== settingsStore.settings.home.fullPath ? to.fullPath : undefined,
          },
        })
      }
      else {
        //console.log(`✅ [路由守卫] 未登录访问白名单页面 ${to.path}`)
        next()
      }
    }

    //console.log(`🏁 [路由守卫] 完成: ${_from.path} -> ${to.fullPath}`)
  })
}

// 最大化
function setupMaximize(router: Router) {
  router.afterEach((to, from) => {
    const settingsStore = useSettingsStore()
    if (to.meta.maximize) {
      settingsStore.setMainPageMaximize(true)
    }
    else if (from.meta.exitMaximize) {
      settingsStore.setMainPageMaximize(false)
    }
  })
}

// 进度条
function setupProgress(router: Router) {
  const { isLoading } = useNProgress()
  router.beforeEach((_to, _from, next) => {
    const settingsStore = useSettingsStore()
    if (settingsStore.settings.app.enableProgress) {
      isLoading.value = true
    }
    next()
  })
  router.afterEach(() => {
    const settingsStore = useSettingsStore()
    if (settingsStore.settings.app.enableProgress) {
      isLoading.value = false
    }
  })
}

// 标题
function setupTitle(router: Router) {
  router.afterEach((to) => {
    const settingsStore = useSettingsStore()
    if (settingsStore.settings.app.routeBaseOn !== 'filesystem') {
      settingsStore.setTitle(to.matched?.at(-1)?.meta?.title ?? to.meta.title)
    }
    else {
      settingsStore.setTitle(to.meta.title)
    }
  })
}

// 页面缓存
function setupKeepAlive(router: Router) {
  router.afterEach((to, from) => {
    const keepAliveStore = useKeepAliveStore()
    const iframeStore = useIframeStore()
    if (to.fullPath !== from.fullPath) {
      /**
       * 处理普通页面的缓存
       */
      // 判断当前页面是否开启缓存，如果开启，则将当前页面的 name 信息存入 keep-alive 全局状态
      if (to.meta.cache && !to.meta.iframe) {
        const componentName = to.matched.at(-1)?.components?.default.name
        if (componentName) {
          keepAliveStore.add(componentName)
        }
        else {
          // turbo-console-disable-next-line
          console.warn('[Fantastic-admin] 该页面组件未设置组件名，会导致缓存失效，请检查')
        }
      }
      // 判断离开页面是否开启缓存，如果开启，则根据缓存规则判断是否需要清空 keep-alive 全局状态里离开页面的 name 信息
      if (from.meta.cache && !from.meta.iframe) {
        const componentName = from.matched.at(-1)?.components?.default.name
        if (componentName) {
          // 通过 meta.cache 判断针对哪些页面进行缓存
          switch (typeof from.meta.cache) {
            case 'string':
              if (from.meta.cache !== to.name) {
                keepAliveStore.remove(componentName)
              }
              break
            case 'object':
              if (!from.meta.cache.includes(to.name as string)) {
                keepAliveStore.remove(componentName)
              }
              break
          }
          // 通过 meta.noCache 判断针对哪些页面不需要进行缓存
          if (from.meta.noCache) {
            switch (typeof from.meta.noCache) {
              case 'string':
                if (from.meta.noCache === to.name) {
                  keepAliveStore.remove(componentName)
                }
                break
              case 'object':
                if (from.meta.noCache.includes(to.name as string)) {
                  keepAliveStore.remove(componentName)
                }
                break
            }
          }
          // 如果进入的是 reload 页面，则也将离开页面的缓存清空
          if (to.name === 'reload') {
            keepAliveStore.remove(componentName)
          }
        }
      }
      /**
       * 处理 iframe 页面的缓存
       */
      if (to.meta.iframe) {
        iframeStore.open({
          path: to.fullPath,
          src: (to.query.iframe as string) ?? to.meta.iframe,
          title: (to.query.title as string) ?? to.meta.title,
        })
      }
      if (from.meta.iframe) {
        if (from.meta.cache) {
          switch (typeof from.meta.cache) {
            case 'string':
              if (from.meta.cache !== to.name) {
                iframeStore.close(from.fullPath)
              }
              break
            case 'object':
              if (!from.meta.cache.includes(to.name as string)) {
                iframeStore.close(from.fullPath)
              }
              break
          }
          if (from.meta.noCache) {
            switch (typeof from.meta.noCache) {
              case 'string':
                if (from.meta.noCache === to.name) {
                  iframeStore.close(from.fullPath)
                }
                break
              case 'object':
                if (from.meta.noCache.includes(to.name as string)) {
                  iframeStore.close(from.fullPath)
                }
                break
            }
          }
          if (to.name === 'reload') {
            iframeStore.close(from.fullPath)
          }
        }
        else {
          iframeStore.close(from.fullPath)
        }
      }
    }
  })
}

// 引导页面检查
async function checkOnboardingFlow(to: any, next: any, userStore: any) {
  try {
    // 首先检查用户协议状态
    const needsUserAgreement = !userStore.userAgreementAccepted
    const isUserAgreementPage = to.path === '/user-agreement'

    //console.log(`🔍 [路由守卫] 用户协议检查 - 需要协议:${needsUserAgreement} 当前是协议页:${isUserAgreementPage} 路径:${to.path}`)

    if (needsUserAgreement && !isUserAgreementPage) {
      // 需要用户协议且不在协议页面 -> 重定向到协议页面
      //console.log('📋 [路由守卫] 用户未同意协议，重定向到协议页面')
      next({
        path: '/user-agreement',
        query: { redirect: to.fullPath },
        replace: true
      })
      return
    }

    if (!needsUserAgreement && isUserAgreementPage) {
      // 已同意协议但在协议页面 -> 重定向到目标页面或主页
      const redirectPath = to.query.redirect as string || '/'
      //console.log(`🔄 [路由守卫] 已同意协议，从协议页重定向到: ${redirectPath}`)
      next({ path: redirectPath, replace: true })
      return
    }

    // 如果当前在用户协议页面且需要协议，直接允许访问，不进行引导页面检查
    if (needsUserAgreement && isUserAgreementPage) {
      //console.log('✅ [路由守卫] 在用户协议页面，允许访问')
      next()
      return
    }

    // 用户协议检查通过，继续引导页面检查
    const needsOnboarding = shouldShowOnboarding(userStore)
    const isOnboardingPage = to.path.startsWith('/onboarding')

    //console.log(`🔍 [路由守卫] 引导检查 - 需要引导:${needsOnboarding} 当前是引导页:${isOnboardingPage} 路径:${to.path}`)

    if (needsOnboarding && !isOnboardingPage) {
      // 需要引导且不在引导页面 -> 重定向到引导页面
      // 特殊处理：如果是从 onboarding 页面跳转到充值页面，且包含 source=onboarding 参数，允许通过
      if (to.path === '/billing/recharge' && to.query?.source === 'onboarding') {
        //console.log('🎯 [路由守卫] 引导页->充值页，允许通过')
        next()
        return
      }

      // 获取用户特定的引导页面路径
      const userInfo: UserTenantInfo = {
        industryType: userStore.industryType,
        tenantType: userStore.tenantType,
      }
      const onboardingPath = getUserOnboardingPath(userInfo)

      //console.log(`🔎 [路由守卫] 首次登录 行业:${userInfo.industryType} 类型:${userInfo.tenantType} 重定向:${onboardingPath}`)
      next({ path: onboardingPath, replace: true })
    }
    else if (!needsOnboarding && isOnboardingPage) {
      // 不需要引导但在引导页面 -> 重定向到主页
      const settingsStore = useSettingsStore()
      const homePath = settingsStore.settings.home.fullPath
      //console.log(`🔄 [路由守卫] 已完成引导，从引导页重定向到主页: ${homePath}`)
      next({ path: homePath, replace: true })
    }
    else {
      // 其他情况正常通过
      //console.log('✅ [路由守卫] 引导检查通过，继续访问')
      next()
    }
  }
  catch (error) {
    console.error('❌ [路由守卫] 引导页面检查失败:', error)
    next()
  }
}

// 验证用户session是否有效
function isValidUserSession(userStore: any): boolean {
  // 检查基本的用户信息是否存在
  const hasUserId = userStore.userId && userStore.userId !== null
  const hasUsername = userStore.username && userStore.username.trim() !== ''
  const hasTenantId = userStore.tenantId && userStore.tenantId !== null

  const isValid = hasUserId && hasUsername && hasTenantId
  //console.log(`🔍 [路由守卫] Session验证 ID:${hasUserId} 用户名:${hasUsername} 租户:${hasTenantId} 结果:${isValid}`)

  return isValid
}

// 其他
function setupOther(router: Router) {
  router.afterEach(() => {
    document.documentElement.scrollTop = 0
  })
}

// Force TS server to re-evaluate by adding a comment
export default function setupGuards(router: Router) {
  setupRoutes(router)
  setupMaximize(router)
  setupProgress(router)
  setupTitle(router)
  setupKeepAlive(router)
  setupOther(router)
}
