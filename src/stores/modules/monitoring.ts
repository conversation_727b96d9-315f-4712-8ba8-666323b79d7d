import { defineStore } from 'pinia'
import { AgentManagementService } from '@/api/agentManagement'
import type {
  SystemMetrics,
  ChartData,
  SystemResources,
  LogEntry,
  Alert
} from '@/api/agentManagement'

interface MonitoringState {
  // 系统指标
  metrics: SystemMetrics
  metricsLoading: boolean
  metricsError: string | null
  lastMetricsUpdate: string | null

  // 图表数据
  executionTrendData: ChartData | null
  agentUsageData: ChartData | null
  responseTimeData: ChartData | null
  chartDataLoading: boolean
  chartDataError: string | null

  // 系统资源
  systemResources: SystemResources | null
  resourcesLoading: boolean
  resourcesError: string | null

  // 日志数据
  logs: LogEntry[]
  logsLoading: boolean
  logsError: string | null
  logFilters: {
    level?: string
    source?: string
    search?: string
  }

  // 告警数据
  alerts: Alert[]
  alertsLoading: boolean
  alertsError: string | null
  unreadAlertsCount: number

  // 实时数据配置
  realTimeEnabled: boolean
  updateInterval: number
  autoRefreshTimer: number | null

  // 历史数据
  historicalData: {
    timeRange: string
    dataPoints: number
    cacheExpiry: number
  }
}

export const useMonitoringStore = defineStore('monitoring', {
  state: (): MonitoringState => ({
    // 系统指标
    metrics: {
      totalExecutions: 0,
      successRate: 0,
      avgResponseTime: 0,
      activeUsers: 0,
      executionsTrend: 0,
      successRateTrend: 0,
      responseTimeTrend: 0,
      activeUsersTrend: 0
    },
    metricsLoading: false,
    metricsError: null,
    lastMetricsUpdate: null,

    // 图表数据
    executionTrendData: null,
    agentUsageData: null,
    responseTimeData: null,
    chartDataLoading: false,
    chartDataError: null,

    // 系统资源
    systemResources: null,
    resourcesLoading: false,
    resourcesError: null,

    // 日志数据
    logs: [],
    logsLoading: false,
    logsError: null,
    logFilters: {},

    // 告警数据
    alerts: [],
    alertsLoading: false,
    alertsError: null,
    unreadAlertsCount: 0,

    // 实时数据配置
    realTimeEnabled: false,
    updateInterval: 30000, // 30秒
    autoRefreshTimer: null,

    // 历史数据
    historicalData: {
      timeRange: '24h',
      dataPoints: 100,
      cacheExpiry: 5 * 60 * 1000 // 5分钟
    }
  }),

  getters: {
    // 指标相关
    isMetricsHealthy: (state) => {
      return state.metrics.successRate >= 95 &&
             state.metrics.avgResponseTime <= 1000
    },

    criticalAlerts: (state) => {
      return state.alerts.filter(alert =>
        alert.type === 'error' && !alert.dismissed
      )
    },

    warningAlerts: (state) => {
      return state.alerts.filter(alert =>
        alert.type === 'warning' && !alert.dismissed
      )
    },

    recentErrors: (state) => {
      return state.logs.filter(log =>
        log.level === 'ERROR' &&
        new Date(log.timestamp).getTime() > Date.now() - 24 * 60 * 60 * 1000
      )
    },

    systemHealthScore: (state) => {
      if (!state.systemResources) return 0

      const cpuScore = Math.max(0, 100 - state.systemResources.cpuUsage)
      const memoryScore = Math.max(0, 100 - state.systemResources.memoryUsage)
      const diskScore = Math.max(0, 100 - state.systemResources.diskUsage)

      return Math.round((cpuScore + memoryScore + diskScore) / 3)
    },

    // 趋势分析
    performanceTrend: (state) => {
      const trends = [
        state.metrics.executionsTrend,
        state.metrics.successRateTrend,
        -state.metrics.responseTimeTrend // 响应时间降低是好事
      ]

      const avgTrend = trends.reduce((sum, trend) => sum + trend, 0) / trends.length

      if (avgTrend > 5) return 'improving'
      if (avgTrend < -5) return 'declining'
      return 'stable'
    },

    // 数据统计
    logsStatistics: (state) => {
      const stats = {
        total: state.logs.length,
        error: 0,
        warn: 0,
        info: 0,
        debug: 0
      }

      state.logs.forEach(log => {
        stats[log.level.toLowerCase() as keyof typeof stats]++
      })

      return stats
    }
  },

  actions: {
    // ============ 系统指标 Actions ============

    async loadMetrics(timeRange = '24h') {
      if (this.metricsLoading) return

      this.metricsLoading = true
      this.metricsError = null

      try {
        const response = await AgentManagementService.getMetrics(timeRange)

        if (response.success && response.data) {
          this.metrics = response.data
          this.lastMetricsUpdate = new Date().toISOString()
        } else {
          throw new Error(response.message || '加载系统指标失败')
        }
      } catch (error) {
        this.metricsError = error instanceof Error ? error.message : '未知错误'
        console.error('Failed to load metrics:', error)

        // 设置默认数据以防止界面崩溃
        this.setDefaultMetrics()
      } finally {
        this.metricsLoading = false
      }
    },

    setDefaultMetrics() {
      this.metrics = {
        totalExecutions: 1250,
        successRate: 96.8,
        avgResponseTime: 850,
        activeUsers: 42,
        executionsTrend: 12.5,
        successRateTrend: 2.1,
        responseTimeTrend: -8.3,
        activeUsersTrend: 15.2
      }
    },

    // ============ 图表数据 Actions ============

    async loadChartData(timeRange = '24h') {
      this.chartDataLoading = true
      this.chartDataError = null

      try {
        const [executionTrend, agentUsage, responseTime] = await Promise.allSettled([
          AgentManagementService.getExecutionTrend(timeRange),
          AgentManagementService.getAgentUsage(timeRange),
          AgentManagementService.getResponseTimeDistribution(timeRange)
        ])

        // 执行趋势数据
        if (executionTrend.status === 'fulfilled' && executionTrend.value.success) {
          this.executionTrendData = executionTrend.value.data
        } else {
          this.executionTrendData = this.generateMockExecutionTrend()
        }

        // Agent 使用数据
        if (agentUsage.status === 'fulfilled' && agentUsage.value.success) {
          this.agentUsageData = agentUsage.value.data
        } else {
          this.agentUsageData = this.generateMockAgentUsage()
        }

        // 响应时间数据
        if (responseTime.status === 'fulfilled' && responseTime.value.success) {
          this.responseTimeData = responseTime.value.data
        } else {
          this.responseTimeData = this.generateMockResponseTime()
        }

      } catch (error) {
        this.chartDataError = error instanceof Error ? error.message : '加载图表数据失败'
        console.error('Failed to load chart data:', error)

        // 生成模拟数据
        this.generateMockChartData()
      } finally {
        this.chartDataLoading = false
      }
    },

    generateMockExecutionTrend(): ChartData {
      const hours = Array.from({ length: 24 }, (_, i) => {
        const date = new Date()
        date.setHours(date.getHours() - (23 - i))
        return date.getHours().toString().padStart(2, '0') + ':00'
      })

      return {
        labels: hours,
        datasets: [
          {
            label: '成功',
            data: hours.map(() => Math.floor(Math.random() * 50) + 20),
            borderColor: '#10b981',
            backgroundColor: '#10b981',
            fill: false
          },
          {
            label: '失败',
            data: hours.map(() => Math.floor(Math.random() * 5) + 1),
            borderColor: '#ef4444',
            backgroundColor: '#ef4444',
            fill: false
          }
        ]
      }
    },

    generateMockAgentUsage(): ChartData {
      return {
        labels: ['数据分析', '数据处理', '推理', '报告生成', '可视化'],
        datasets: [
          {
            label: 'Agent 使用次数',
            data: [320, 280, 180, 150, 120],
            backgroundColor: [
              '#3b82f6',
              '#10b981',
              '#f59e0b',
              '#ef4444',
              '#8b5cf6'
            ]
          }
        ]
      }
    },

    generateMockResponseTime(): ChartData {
      return {
        labels: ['<100ms', '100-500ms', '500ms-1s', '1-2s', '2s+'],
        datasets: [
          {
            label: '请求数量',
            data: [450, 320, 180, 80, 20],
            backgroundColor: '#3b82f6'
          }
        ]
      }
    },

    generateMockChartData() {
      this.executionTrendData = this.generateMockExecutionTrend()
      this.agentUsageData = this.generateMockAgentUsage()
      this.responseTimeData = this.generateMockResponseTime()
    },

    // ============ 系统资源 Actions ============

    async loadSystemResources() {
      this.resourcesLoading = true
      this.resourcesError = null

      try {
        const response = await AgentManagementService.getSystemResources()

        if (response.success && response.data) {
          this.systemResources = response.data
        } else {
          throw new Error(response.message || '加载系统资源失败')
        }
      } catch (error) {
        this.resourcesError = error instanceof Error ? error.message : '未知错误'
        console.error('Failed to load system resources:', error)

        // 设置模拟数据
        this.systemResources = {
          cpuUsage: Math.floor(Math.random() * 30) + 20,
          memoryUsage: Math.floor(Math.random() * 40) + 30,
          diskUsage: Math.floor(Math.random() * 20) + 50,
          timestamp: new Date().toISOString()
        }
      } finally {
        this.resourcesLoading = false
      }
    },

    // ============ 日志数据 Actions ============

    async loadRecentLogs(params?: { level?: string; limit?: number; source?: string }) {
      this.logsLoading = true
      this.logsError = null

      try {
        const response = await AgentManagementService.getRecentLogs({
          limit: 100,
          ...params,
          ...this.logFilters
        })

        if (response.success && response.data) {
          this.logs = response.data
        } else {
          throw new Error(response.message || '加载日志失败')
        }
      } catch (error) {
        this.logsError = error instanceof Error ? error.message : '未知错误'
        console.error('Failed to load logs:', error)

        // 生成模拟日志数据
        this.generateMockLogs()
      } finally {
        this.logsLoading = false
      }
    },

    generateMockLogs() {
      const levels = ['ERROR', 'WARN', 'INFO', 'DEBUG']
      const sources = ['AgentService', 'OrchestrationService', 'ChatController', 'SystemMonitor']
      const messages = [
        'Agent execution completed successfully',
        'Failed to connect to external API',
        'Memory usage threshold exceeded',
        'New user session started',
        'Database connection pool exhausted',
        'Cache miss for key: user_123',
        'Agent timeout after 30 seconds',
        'Configuration updated successfully'
      ]

      this.logs = Array.from({ length: 50 }, (_, i) => ({
        id: `log-${i}`,
        level: levels[Math.floor(Math.random() * levels.length)] as LogEntry['level'],
        message: messages[Math.floor(Math.random() * messages.length)],
        timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString(),
        source: sources[Math.floor(Math.random() * sources.length)],
        details: Math.random() > 0.7 ? {
          stackTrace: 'at com.dipspro.service.AgentService.execute(AgentService.java:123)',
          requestId: `req-${Math.random().toString(36).substr(2, 9)}`
        } : undefined
      })).sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
    },

    setLogFilters(filters: typeof this.logFilters) {
      this.logFilters = { ...this.logFilters, ...filters }
    },

    clearLogs() {
      this.logs = []
    },

    // ============ 告警数据 Actions ============

    async loadAlerts() {
      this.alertsLoading = true
      this.alertsError = null

      try {
        const response = await AgentManagementService.getAlerts()

        if (response.success && response.data) {
          this.alerts = response.data
          this.updateUnreadAlertsCount()
        } else {
          throw new Error(response.message || '加载告警失败')
        }
      } catch (error) {
        this.alertsError = error instanceof Error ? error.message : '未知错误'
        console.error('Failed to load alerts:', error)

        // 生成模拟告警数据
        this.generateMockAlerts()
      } finally {
        this.alertsLoading = false
      }
    },

    generateMockAlerts() {
      const alertTypes: Alert['type'][] = ['error', 'warning', 'info']
      const titles = [
        'Agent 执行失败',
        '内存使用率过高',
        '响应时间异常',
        '数据库连接异常',
        '系统负载过高',
        'API 限流警告'
      ]
      const messages = [
        'Agent "数据分析" 在处理请求时发生异常',
        '系统内存使用率已达到 85%，建议检查内存泄漏',
        '平均响应时间超过 2 秒，影响用户体验',
        '数据库连接池已满，新请求将被拒绝',
        'CPU 使用率持续超过 80%，建议扩容',
        'API 请求频率过高，已触发限流保护'
      ]

      this.alerts = Array.from({ length: 6 }, (_, i) => ({
        id: `alert-${i}`,
        type: alertTypes[Math.floor(Math.random() * alertTypes.length)],
        title: titles[i],
        message: messages[i],
        timestamp: new Date(Date.now() - Math.random() * 12 * 60 * 60 * 1000).toISOString(),
        dismissed: Math.random() > 0.7
      })).sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

      this.updateUnreadAlertsCount()
    },

    async dismissAlert(alertId: string) {
      try {
        const response = await AgentManagementService.dismissAlert(alertId)

        if (response.success) {
          const alert = this.alerts.find(a => a.id === alertId)
          if (alert) {
            alert.dismissed = true
            this.updateUnreadAlertsCount()
          }
        }
      } catch (error) {
        console.error('Failed to dismiss alert:', error)
      }
    },

    async dismissAllAlerts() {
      try {
        const response = await AgentManagementService.clearAlerts()

        if (response.success) {
          this.alerts.forEach(alert => {
            alert.dismissed = true
          })
          this.updateUnreadAlertsCount()
        }
      } catch (error) {
        console.error('Failed to dismiss all alerts:', error)
      }
    },

    updateUnreadAlertsCount() {
      this.unreadAlertsCount = this.alerts.filter(alert => !alert.dismissed).length
    },

    // ============ 实时数据管理 ============

    startRealTimeUpdates() {
      if (this.autoRefreshTimer) {
        this.stopRealTimeUpdates()
      }

      this.realTimeEnabled = true
      this.autoRefreshTimer = setInterval(() => {
        this.refreshAllData()
      }, this.updateInterval)
    },

    stopRealTimeUpdates() {
      if (this.autoRefreshTimer) {
        clearInterval(this.autoRefreshTimer)
        this.autoRefreshTimer = null
      }
      this.realTimeEnabled = false
    },

    setUpdateInterval(interval: number) {
      this.updateInterval = interval

      if (this.realTimeEnabled) {
        this.stopRealTimeUpdates()
        this.startRealTimeUpdates()
      }
    },

    async refreshAllData(timeRange = '24h') {
      try {
        await Promise.allSettled([
          this.loadMetrics(timeRange),
          this.loadChartData(timeRange),
          this.loadSystemResources(),
          this.loadRecentLogs(),
          this.loadAlerts()
        ])
      } catch (error) {
        console.error('Failed to refresh all monitoring data:', error)
      }
    },

    // ============ 数据导出 ============

    async exportMetrics(timeRange = '24h', format = 'json') {
      try {
        const data = {
          metrics: this.metrics,
          systemResources: this.systemResources,
          exportTime: new Date().toISOString(),
          timeRange
        }

        const content = format === 'json'
          ? JSON.stringify(data, null, 2)
          : this.convertToCSV(data)

        const blob = new Blob([content], {
          type: format === 'json' ? 'application/json' : 'text/csv'
        })
        const url = URL.createObjectURL(blob)

        const a = document.createElement('a')
        a.href = url
        a.download = `monitoring-data-${timeRange}.${format}`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)

        URL.revokeObjectURL(url)
      } catch (error) {
        console.error('Failed to export metrics:', error)
        throw error
      }
    },

    convertToCSV(data: any): string {
      // 简化的 CSV 转换
      const headers = Object.keys(data.metrics).join(',')
      const values = Object.values(data.metrics).join(',')
      return `${headers}\n${values}`
    },

    // ============ 清理和重置 ============

    clearAllErrors() {
      this.metricsError = null
      this.chartDataError = null
      this.resourcesError = null
      this.logsError = null
      this.alertsError = null
    },

    resetMonitoringData() {
      // 重置所有数据到初始状态
      this.metrics = {
        totalExecutions: 0,
        successRate: 0,
        avgResponseTime: 0,
        activeUsers: 0,
        executionsTrend: 0,
        successRateTrend: 0,
        responseTimeTrend: 0,
        activeUsersTrend: 0
      }
      this.executionTrendData = null
      this.agentUsageData = null
      this.responseTimeData = null
      this.systemResources = null
      this.logs = []
      this.alerts = []
      this.unreadAlertsCount = 0
      this.clearAllErrors()
    }
  }
})
