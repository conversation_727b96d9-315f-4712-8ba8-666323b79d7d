import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  Conversation,
  Message,
  ChainExecution,
  ChainStep,
  AgentExecution,
  ConnectionStatus,
  AgentChatSettings,
  InputSuggestion,
  SSEEvent,
  AgentFlow,
  ExecutionLog,
  ThinkingProcess
} from '@/types/agent'
import { agentChatApi } from '@/api/agentChat'

export const useAgentChatStore = defineStore('agentChat', () => {
  // 状态
  const conversations = ref<Conversation[]>([])
  const currentConversation = ref<Conversation | null>(null)
  const messages = ref<Message[]>([])
  const currentFlow = ref<AgentFlow | null>(null)
  const currentStep = ref<string | null>(null)
  const executionHistory = ref<AgentExecution[]>([])
  const activeExecution = ref<ChainExecution | null>(null)
  const activeAgents = ref<string[]>([])
  const isProcessing = ref(false)
  const connectionStatus = ref<ConnectionStatus>('disconnected')
  const inputSuggestions = ref<InputSuggestion[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 设置
  const settings = ref<AgentChatSettings>({
    autoScroll: true,
    showChainOfThought: true,
    enableSound: false,
    enableVoiceInput: true,
    theme: 'auto'
  })

  // 实时数据
  const executionLogs = ref<ExecutionLog[]>([])
  const thinkingChain = ref<ThinkingProcess[]>([])

  // 计算属性
  const hasActiveExecution = computed(() => {
    return activeExecution.value?.status === 'running' ||
           activeExecution.value?.status === 'paused'
  })

  const currentExecutionId = computed(() => {
    return activeExecution.value?.id
  })

  const unreadMessageCount = computed(() => {
    return conversations.value.reduce((count, conv) => {
      return count + (conv.messageCount || 0)
    }, 0)
  })

  // Actions

  /**
   * 加载对话列表
   */
  async function loadConversations() {
    try {
      loading.value = true
      error.value = null

            //console.log('=== DEBUG: Starting loadConversations ===')
      const response = await agentChatApi.getConversations()
      //console.log('=== DEBUG: Raw API response ===', response)
      //console.log('=== DEBUG: Response type ===', typeof response)
      //console.log('=== DEBUG: Response is array ===', Array.isArray(response))

      // 处理直接返回数组的情况
      let conversationsData: any[] = []
      if (Array.isArray(response)) {
        //console.log('=== DEBUG: Case 1 - Direct array ===')
        conversationsData = response
      } else if (response && response.data && Array.isArray(response.data)) {
        //console.log('=== DEBUG: Case 2 - Wrapped object ===')
        conversationsData = response.data
      } else if (response && response.success && response.data) {
        //console.log('=== DEBUG: Case 3 - Standard ApiResponse ===')
        conversationsData = response.data
      } else {
        //console.log('=== DEBUG: Case 4 - Unknown format ===', response)
      }

      //console.log('=== DEBUG: Final conversationsData ===', conversationsData)

      if (conversationsData.length > 0) {
        // 映射后端字段到前端字段
        const mappedConversations = conversationsData.map((conv: any) => ({
          id: conv.conversationId,
          title: conv.title,
          createdAt: conv.createdAt,
          updatedAt: conv.updatedAt,
          messageCount: conv.messageCount || 0,
          isArchived: conv.status === 'ARCHIVED',
          userId: conv.userId,
          status: conv.status?.toLowerCase() || 'active',
          conversationType: conv.conversationType,
          metadata: conv.metadata || {}
        }))
        //console.log('Mapped conversations:', mappedConversations)
        conversations.value = mappedConversations
        //console.log('Final conversations state:', conversations.value)
      } else {
        console.warn('No conversations data found:', conversationsData)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载对话列表失败'
      console.error('Failed to load conversations:', err)
    } finally {
      loading.value = false
    }
  }

  /**
   * 创建新对话
   */
  async function createConversation(title: string) {
    try {
      loading.value = true
      error.value = null

      const response = await agentChatApi.createConversation({ title })
      //console.log('=== DEBUG: Create conversation response ===', response)
      //console.log('=== DEBUG: Response type ===', typeof response)
      //console.log('=== DEBUG: Response keys ===', Object.keys(response || {}))

      // 由于 API 层已经返回了 response.data，所以 response 就是实际的数据
      // 检查响应格式
      let backendData: any = null

      if (response && (response as any).conversationId) {
        // 直接返回对话对象格式（当前实际情况）
        backendData = response
        //console.log('=== DEBUG: Using direct response ===', backendData)
      } else if (response && response.success && response.data) {
        // 标准 API 响应格式：{success: true, data: conversation}
        backendData = response.data
        //console.log('=== DEBUG: Using response.data ===', backendData)
      } else if (response && response.data && (response.data as any).conversationId) {
        // 嵌套格式
        backendData = response.data
        //console.log('=== DEBUG: Using nested response.data ===', backendData)
      } else {
        console.error('=== DEBUG: Unknown response format ===', response)
        throw new Error('未知的响应格式')
      }

      if (backendData && backendData.conversationId) {
        // 映射后端字段到前端字段，确保字段名一致
        const newConversation: Conversation = {
          id: backendData.conversationId,
          title: backendData.title,
          createdAt: backendData.createdAt,
          updatedAt: backendData.updatedAt,
          messageCount: backendData.messageCount || 0,
          isArchived: backendData.status === 'ARCHIVED',
          userId: backendData.userId,
          status: backendData.status?.toLowerCase() || 'active',
          conversationType: backendData.conversationType,
          metadata: backendData.metadata || {}
        }

        //console.log('=== DEBUG: Mapped new conversation ===', newConversation)

        conversations.value.unshift(newConversation)
        currentConversation.value = newConversation
        messages.value = []

        //console.log('=== DEBUG: Current conversation after create (final) ===', currentConversation.value)
        //console.log('=== DEBUG: Current conversation ID ===', currentConversation.value?.id)

        // 生成输入建议
        await generateInputSuggestions()
      } else {
        console.error('=== DEBUG: Invalid backend data ===', backendData)
        throw new Error('无效的后端数据格式')
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建对话失败'
      console.error('Failed to create conversation:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 设置当前对话
   */
  function setCurrentConversation(conversation: Conversation) {
    currentConversation.value = conversation
    messages.value = []
    activeExecution.value = null
    currentStep.value = null
    executionLogs.value = []
    thinkingChain.value = []
  }

  /**
   * 加载对话消息
   */
  async function loadMessages(conversationId: string) {
    try {
      loading.value = true
      error.value = null

      //console.log('=== DEBUG: Loading messages for conversation ===', conversationId)
      const response = await agentChatApi.getMessages(conversationId)
      //console.log('=== DEBUG: Raw messages response ===', response)

      // 处理不同的响应格式
      let messagesData: any[] = []
      if (Array.isArray(response)) {
        //console.log('=== DEBUG: Direct array response ===')
        messagesData = response
      } else if (response && response.data && Array.isArray(response.data)) {
        //console.log('=== DEBUG: Wrapped data response ===')
        messagesData = response.data
      } else if (response && response.success && response.data) {
        //console.log('=== DEBUG: Standard API response ===')
        messagesData = Array.isArray(response.data) ? response.data : []
      }

      //console.log('=== DEBUG: Processing messages data ===', messagesData)

      if (messagesData.length > 0) {
        // 映射后端消息格式到前端格式
        const mappedMessages = messagesData.map((msg: any) => ({
          id: msg.messageId,
          conversationId: msg.conversationId,
          content: msg.content,
          // 将 messageType 映射到 role，并转换为小写
          role: msg.messageType === 'USER' ? 'user' :
                msg.messageType === 'ASSISTANT' ? 'assistant' :
                msg.role || 'user',
          timestamp: msg.timestamp,
          metadata: {
            ...msg.metadata,
            sender: msg.sender,
            status: msg.status
          },
          chainOfThought: msg.chainOfThought,
          executionId: msg.executionId
        }))

        //console.log('=== DEBUG: Mapped messages ===', mappedMessages)
        messages.value = mappedMessages

        // 如果有正在执行的任务，恢复执行状态
        const lastMessage = mappedMessages[mappedMessages.length - 1]
        if (lastMessage?.executionId) {
          await loadExecutionStatus(lastMessage.executionId)
        }
      } else {
        //console.log('=== DEBUG: No messages found ===')
        messages.value = []
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载消息失败'
      console.error('Failed to load messages:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 发送消息
   */
  async function sendMessage(conversationId: string, content: string, files?: File[]) {
    // 先在外部定义 userMessage，确保在 catch 块中也能访问
    const userMessage: Message = {
      id: `temp_${Date.now()}`,
      conversationId,
      content,
      role: 'user',
      timestamp: new Date().toISOString(),
      isTemporary: true,  // 标记为临时状态，等待服务器响应
      metadata: {
        status: 'sending'  // 发送状态
      }
    }

    try {
      isProcessing.value = true
      error.value = null

      // 添加用户消息到本地状态
      messages.value.push(userMessage)

      // 发送消息到服务器
      const response = await agentChatApi.sendMessage(conversationId, {
        content,
        files: files || []
      })

      if (response.success && response.data) {
        const { message, execution } = response.data

        // 更新消息 ID 和状态
        const tempMessageIndex = messages.value.findIndex((m: Message) => m.id === userMessage.id)
        if (tempMessageIndex !== -1) {
          // 更新为服务器返回的消息，并移除临时状态
          messages.value[tempMessageIndex] = {
            ...message,
            isTemporary: false,
            metadata: {
              ...message.metadata,
              status: 'sent'  // 已发送状态
            }
          }
        }

        // 如果有执行信息，设置活动执行
        if (execution) {
          activeExecution.value = execution
          currentStep.value = execution.steps?.[0]?.id || null
        }

        // 更新对话的消息计数
        const conversation = conversations.value.find((c: Conversation) => c.id === conversationId)
        if (conversation) {
          conversation.messageCount = (conversation.messageCount || 0) + 1
          conversation.updatedAt = new Date().toISOString()
        }
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '发送消息失败'
      console.error('Failed to send message:', err)

      // 更新临时消息为错误状态
      const tempMessageIndex = messages.value.findIndex((m: Message) => m.id === userMessage.id)
      if (tempMessageIndex !== -1) {
        const currentMessage = messages.value[tempMessageIndex]
        messages.value.splice(tempMessageIndex, 1, {
          ...currentMessage,
          isTemporary: false,
          metadata: {
            ...currentMessage.metadata,
            status: 'error',
            error: err instanceof Error ? err.message : '发送失败'
          }
        })
      }

      throw err
    } finally {
      isProcessing.value = false
    }
  }

  /**
   * 删除对话
   */
  async function deleteConversation(conversationId: string) {
    try {
      loading.value = true
      error.value = null

      const response = await agentChatApi.deleteConversation(conversationId)
      if (response.success) {
        conversations.value = conversations.value.filter(c => c.id !== conversationId)

        // 如果删除的是当前对话，清空状态
        if (currentConversation.value?.id === conversationId) {
          currentConversation.value = null
          messages.value = []
          activeExecution.value = null
          currentStep.value = null
        }
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除对话失败'
      console.error('Failed to delete conversation:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 加载执行状态
   */
  async function loadExecutionStatus(executionId: string) {
    try {
      const response = await agentChatApi.getExecutionStatus(executionId)
      if (response.success && response.data) {
        activeExecution.value = response.data

        // 找到当前执行的步骤
        const runningStep = response.data.steps?.find(step => step.status === 'running')
        currentStep.value = runningStep?.id || null
      }
    } catch (err) {
      console.error('Failed to load execution status:', err)
    }
  }

  /**
   * 生成输入建议
   */
  async function generateInputSuggestions() {
    try {
      const response = await agentChatApi.getInputSuggestions()
      if (response.success && response.data) {
        inputSuggestions.value = response.data
      }
    } catch (err) {
      console.error('Failed to generate input suggestions:', err)
    }
  }

  /**
   * 处理 SSE 事件
   */
  function handleSseEvent(event: SSEEvent) {
    //console.log('Handling SSE event:', event)

    switch (event.type) {
      case 'execution_started':
        handleExecutionStarted(event.data)
        break

      case 'step_started':
        handleStepStarted(event.data)
        break

      case 'step_progress':
        handleStepProgress(event.data)
        break

      case 'step_completed':
        handleStepCompleted(event.data)
        break

      case 'step_failed':
        handleStepFailed(event.data)
        break

      case 'thinking_process':
        handleThinkingProcess(event.data)
        break

      case 'execution_completed':
        handleExecutionCompleted(event.data)
        break

      case 'execution_failed':
        handleExecutionFailed(event.data)
        break

      case 'execution_paused':
        handleExecutionPaused(event.data)
        break

      case 'execution_resumed':
        handleExecutionResumed(event.data)
        break

      case 'connection_status':
        handleConnectionStatus(event.data)
        break

      // Agent Chat 事件处理
      case 'CONNECTED':
        handleConnected(event.data)
        break

      case 'DISCONNECTED':
        handleDisconnected(event.data)
        break

      case 'HEARTBEAT':
        // 心跳消息，不需要特殊处理
        break

      case 'MESSAGE_RECEIVED':
        handleMessageReceived(event.data)
        break

      case 'AGENT_THINKING':
        handleAgentThinking(event)
        break

      case 'AGENT_PROGRESS':
        handleAgentProgress(event)
        break

      case 'AGENT_COMPLETED':
        handleAgentCompleted(event)
        break

      case 'AGENT_FAILED':
        handleAgentFailed(event)
        break

      case 'ORCHESTRATION_STARTED':
        handleOrchestrationStarted(event)
        break

      case 'ORCHESTRATION_PROGRESS':
        handleOrchestrationProgress(event)
        break

      case 'ORCHESTRATION_COMPLETED':
        handleOrchestrationCompleted(event)
        break

      case 'ORCHESTRATION_FAILED':
        handleOrchestrationFailed(event)
        break

      case 'ERROR':
        handleError(event.data)
        break

      default:
        //console.log('Unknown SSE event type:', event.type)
    }

    // 添加执行日志
    if (event.type !== 'connection_status') {
      const log: ExecutionLog = {
        timestamp: event.timestamp,
        level: 'INFO',
        agentName: event.data.agentName || 'System',
        message: `${event.type}: ${event.data.message || JSON.stringify(event.data)}`,
        data: event.data,
        executionId: event.executionId,
        stepId: event.stepId
      }
      executionLogs.value.push(log)
    }
  }

  function handleExecutionStarted(data: any) {
    if (activeExecution.value && activeExecution.value.id === data.executionId) {
      activeExecution.value.status = 'running'
      activeExecution.value.startTime = data.startTime
    }
    isProcessing.value = true
  }

  function handleStepStarted(data: any) {
    if (activeExecution.value && activeExecution.value.id === data.executionId) {
      currentStep.value = data.stepId

      // 更新步骤状态
      const step = activeExecution.value.steps?.find(s => s.id === data.stepId)
      if (step) {
        step.status = 'running'
        step.startTime = data.startTime
      }

      // 更新活动 Agent 列表
      if (data.agentName && !activeAgents.value.includes(data.agentName)) {
        activeAgents.value.push(data.agentName)
      }
    }
  }

  function handleStepProgress(data: any) {
    if (activeExecution.value && activeExecution.value.id === data.executionId) {
      const step = activeExecution.value.steps?.find(s => s.id === data.stepId)
      if (step) {
        step.progress = data.progress
      }
    }
  }

  function handleStepCompleted(data: any) {
    if (activeExecution.value && activeExecution.value.id === data.executionId) {
      const step = activeExecution.value.steps?.find(s => s.id === data.stepId)
      if (step) {
        step.status = 'completed'
        step.endTime = data.endTime
        step.duration = data.duration
        step.result = data.result
      }

      // 从活动 Agent 列表中移除
      if (data.agentName) {
        const index = activeAgents.value.indexOf(data.agentName)
        if (index > -1) {
          activeAgents.value.splice(index, 1)
        }
      }

      // 移动到下一步
      const nextStep = activeExecution.value.steps?.find(s => s.status === 'pending')
      currentStep.value = nextStep?.id || null
    }
  }

  function handleStepFailed(data: any) {
    if (activeExecution.value && activeExecution.value.id === data.executionId) {
      const step = activeExecution.value.steps?.find(s => s.id === data.stepId)
      if (step) {
        step.status = 'failed'
        step.endTime = data.endTime
        step.duration = data.duration
        step.error = data.error
      }

      // 从活动 Agent 列表中移除
      if (data.agentName) {
        const index = activeAgents.value.indexOf(data.agentName)
        if (index > -1) {
          activeAgents.value.splice(index, 1)
        }
      }
    }
  }

  function handleThinkingProcess(data: any) {
    const thinkingStep: ThinkingProcess = {
      agentName: data.agentName,
      timestamp: data.timestamp,
      content: data.content || data.analysis || '思考过程',
      analysis: data.analysis,
      reasoning: data.reasoning,
      confidence: data.confidence,
      data: data.data
    }
    thinkingChain.value.push(thinkingStep)
  }

  function handleExecutionCompleted(data: any) {
    if (activeExecution.value && activeExecution.value.id === data.executionId) {
      activeExecution.value.status = 'completed'
      activeExecution.value.endTime = data.endTime
      activeExecution.value.duration = data.duration
      activeExecution.value.result = data.result
    }

    isProcessing.value = false
    activeAgents.value = []
    currentStep.value = null

    // 添加助手回复消息
    if (data.assistantMessage && currentConversation.value) {
      const assistantMessage: Message = {
        id: data.messageId || `msg_${Date.now()}`,
        conversationId: currentConversation.value.id,
        content: data.assistantMessage,
        role: 'assistant',
        timestamp: data.endTime,
        executionId: data.executionId,
        chainOfThought: data.chainOfThought
      }
      messages.value.push(assistantMessage)
    }
  }

  function handleExecutionFailed(data: any) {
    if (activeExecution.value && activeExecution.value.id === data.executionId) {
      activeExecution.value.status = 'failed'
      activeExecution.value.endTime = data.endTime
      activeExecution.value.duration = data.duration
      activeExecution.value.error = data.error
    }

    isProcessing.value = false
    activeAgents.value = []
    currentStep.value = null

    error.value = data.error || '执行失败'
  }

  function handleExecutionPaused(data: any) {
    if (activeExecution.value && activeExecution.value.id === data.executionId) {
      activeExecution.value.status = 'paused'
    }
    isProcessing.value = false
  }

  function handleExecutionResumed(data: any) {
    if (activeExecution.value && activeExecution.value.id === data.executionId) {
      activeExecution.value.status = 'running'
    }
    isProcessing.value = true
  }

  function handleConnectionStatus(data: any) {
    connectionStatus.value = data.status
  }

  // Agent Chat 事件处理函数

  function handleConnected(data: any) {
    connectionStatus.value = 'connected'
    console.log('SSE连接已建立:', data)
  }

  function handleDisconnected(data: any) {
    connectionStatus.value = 'disconnected'
    console.log('SSE连接已断开:', data)
  }

  function handleMessageReceived(data: any) {
    console.log('消息已接收确认:', data)
    // 可以在这里更新用户消息的状态为"已接收"
  }

  function handleAgentThinking(event: SSEEvent) {
    console.log('Agent正在思考:', event)

    // 创建或更新思考过程
    const thinkingItem: ThinkingProcess = {
      id: `thinking_${Date.now()}`,
      agentName: event.data.agentName || 'Agent',
      content: event.data.thought || event.data.message || '正在思考...',
      timestamp: event.timestamp,
      status: 'thinking',
      type: 'thinking'
    }

    // 添加到思考链
    thinkingChain.value.push(thinkingItem)

    // 创建临时思考消息，包含当前所有思维链步骤
    if (currentConversation.value) {
      // 构建思维链内容展示
      const currentConvThinking = thinkingChain.value.filter(t =>
        t.agentName === thinkingItem.agentName || t.type === 'thinking'
      )

      const thinkingContent = currentConvThinking.length > 1
        ? `${thinkingItem.agentName}正在思考...\n\n最新进展：${thinkingItem.content}`
        : thinkingItem.content

      const thinkingMessage: Message = {
        id: `thinking_${currentConversation.value.id}`,
        conversationId: currentConversation.value.id,
        content: thinkingContent,
        role: 'assistant',
        timestamp: event.timestamp,
        metadata: {
          agentName: thinkingItem.agentName,
          status: 'thinking',
          isThinking: true
        },
        isTemporary: true, // 标记为临时消息
        chainOfThought: {
          steps: currentConvThinking.map(t => ({
            id: t.id || `step_${Date.now()}`,
            agentName: t.agentName,
            timestamp: t.timestamp,
            content: t.content,
            type: 'analysis' as const,
            confidence: t.data?.confidence,
            data: t.data
          })),
          summary: `${thinkingItem.agentName}正在分析问题，已进行${currentConvThinking.length}步思考`
        }
      }

      // 检查是否已经有思考消息，如果有则更新，否则添加
      const existingThinkingIndex = messages.value.findIndex(msg =>
        msg.metadata?.isThinking && msg.conversationId === currentConversation.value?.id
      )

      if (existingThinkingIndex >= 0) {
        messages.value[existingThinkingIndex] = thinkingMessage
      } else {
        messages.value.push(thinkingMessage)
      }
    }
  }

  function handleAgentProgress(event: SSEEvent) {
    console.log('Agent进度更新:', event)

    // 更新进度信息
    if (event.data.progress !== undefined) {
      // 可以在这里更新进度条或状态
    }
  }

  function handleAgentCompleted(event: SSEEvent) {
    console.log('Agent执行完成:', event)

    // 注意：Agent完成不立即移除思考消息，等待编排完成时统一处理
    // 这样可以保持思维链的连续性，直到整个对话流程结束
    console.log('Agent执行完成，等待编排完成以保留完整思维链')
  }

  function handleAgentFailed(event: SSEEvent) {
    console.log('Agent执行失败:', event)
    error.value = event.data.error || 'Agent执行失败'

    // Agent失败时保留思维链，等待编排失败处理
    // 这样用户可以看到失败前的思考过程
    console.log('Agent执行失败，保留思维链等待编排失败处理')
  }

  function handleOrchestrationStarted(event: SSEEvent) {
    console.log('编排开始:', event)
    isProcessing.value = true
  }

  function handleOrchestrationProgress(event: SSEEvent) {
    console.log('编排进度:', event)
    // 可以在这里更新整体进度
  }

  function handleOrchestrationCompleted(event: SSEEvent) {
    console.log('编排完成:', event)
    isProcessing.value = false

    // 获取当前的思维链内容
    let existingThinkingMessage: Message | null = null
    if (currentConversation.value) {
      const thinkingIndex = messages.value.findIndex(msg =>
        msg.metadata?.isThinking && msg.conversationId === currentConversation.value?.id
      )
      if (thinkingIndex >= 0) {
        existingThinkingMessage = messages.value[thinkingIndex]
        // 移除临时思考消息
        messages.value.splice(thinkingIndex, 1)
      }
    }

    // 创建最终结果消息，包含思维链内容
    if (event.data.finalResult && currentConversation.value) {
            // 构建完整的消息内容：思维链 + 最终结果
      const existingSteps = existingThinkingMessage?.chainOfThought
      const thinkingSteps = (Array.isArray(existingSteps) ? existingSteps : existingSteps?.steps) ||
                           thinkingChain.value.map(t => ({
                             id: t.id || `step_${Date.now()}`,
                             agentName: t.agentName,
                             timestamp: t.timestamp,
                             content: t.content,
                             type: 'analysis' as const,
                             confidence: t.data?.confidence,
                             data: t.data
                           }))

      const resultMessage: Message = {
        id: `result_${Date.now()}`,
        conversationId: currentConversation.value.id,
        content: event.data.finalResult,
        role: 'assistant',
        timestamp: event.timestamp,
        metadata: {
          agentName: existingThinkingMessage?.metadata?.agentName || 'AI Assistant',
          status: 'completed'
        },
        chainOfThought: thinkingSteps.length > 0 ? {
          steps: thinkingSteps,
          summary: `完成分析，共进行${thinkingSteps.length}步思考`,
          reasoning: '基于以上思考过程得出结论'
        } : undefined
      }
      messages.value.push(resultMessage)

      // 清空思维链，为下次对话做准备
      thinkingChain.value = []
    }
  }

  function handleOrchestrationFailed(event: SSEEvent) {
    console.log('编排失败:', event)
    isProcessing.value = false
    error.value = event.data.error || '编排执行失败'

    // 获取当前的思维链内容
    let existingThinkingMessage: Message | null = null
    if (currentConversation.value) {
      const thinkingIndex = messages.value.findIndex(msg =>
        msg.metadata?.isThinking && msg.conversationId === currentConversation.value?.id
      )
      if (thinkingIndex >= 0) {
        existingThinkingMessage = messages.value[thinkingIndex]
        // 移除临时思考消息
        messages.value.splice(thinkingIndex, 1)
      }
    }

    // 创建失败消息，包含思维链内容
    if (currentConversation.value) {
      const existingSteps = existingThinkingMessage?.chainOfThought
      const thinkingSteps = (Array.isArray(existingSteps) ? existingSteps : existingSteps?.steps) ||
                           thinkingChain.value.map(t => ({
                             id: t.id || `step_${Date.now()}`,
                             agentName: t.agentName,
                             timestamp: t.timestamp,
                             content: t.content,
                             type: 'analysis' as const,
                             confidence: t.data?.confidence,
                             data: t.data
                           }))

      const errorMessage: Message = {
        id: `error_${Date.now()}`,
        conversationId: currentConversation.value.id,
        content: `抱歉，处理过程中遇到错误：${event.data.error || '执行失败'}`,
        role: 'assistant',
        timestamp: event.timestamp,
        metadata: {
          agentName: existingThinkingMessage?.metadata?.agentName || 'AI Assistant',
          status: 'failed',
          error: event.data.error
        },
        chainOfThought: thinkingSteps.length > 0 ? {
          steps: thinkingSteps,
          summary: `执行失败前已进行${thinkingSteps.length}步思考`,
          reasoning: '思考过程已记录，但执行过程中遇到错误'
        } : undefined
      }
      messages.value.push(errorMessage)

      // 清空思维链
      thinkingChain.value = []
    }
  }

  function handleError(data: any) {
    console.error('SSE错误:', data)
    error.value = data.error || data.message || '发生未知错误'
  }

  /**
   * 设置连接状态
   */
  function setConnectionStatus(status: ConnectionStatus) {
    connectionStatus.value = status
  }

  /**
   * 更新设置
   */
  function updateSettings(newSettings: Partial<AgentChatSettings>) {
    settings.value = { ...settings.value, ...newSettings }

    // 持久化到本地存储
    localStorage.setItem('agentChatSettings', JSON.stringify(settings.value))
  }

  /**
   * 加载设置
   */
  function loadSettings() {
    const savedSettings = localStorage.getItem('agentChatSettings')
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings)
        settings.value = { ...settings.value, ...parsed }
      } catch (err) {
        console.error('Failed to parse saved settings:', err)
      }
    }
  }

  /**
   * 清空错误
   */
  function clearError() {
    error.value = null
  }

  /**
   * 重置状态
   */
  function resetState() {
    conversations.value = []
    currentConversation.value = null
    messages.value = []
    currentFlow.value = null
    currentStep.value = null
    executionHistory.value = []
    activeExecution.value = null
    activeAgents.value = []
    isProcessing.value = false
    connectionStatus.value = 'disconnected'
    inputSuggestions.value = []
    loading.value = false
    error.value = null
    executionLogs.value = []
    thinkingChain.value = []
  }

  // 初始化设置
  loadSettings()

  return {
    // 状态
    conversations,
    currentConversation,
    messages,
    currentFlow,
    currentStep,
    executionHistory,
    activeExecution,
    activeAgents,
    isProcessing,
    connectionStatus,
    inputSuggestions,
    loading,
    error,
    settings,
    executionLogs,
    thinkingChain,

    // 计算属性
    hasActiveExecution,
    currentExecutionId,
    unreadMessageCount,

    // Actions
    loadConversations,
    createConversation,
    setCurrentConversation,
    loadMessages,
    sendMessage,
    deleteConversation,
    loadExecutionStatus,
    generateInputSuggestions,
    handleSseEvent,
    setConnectionStatus,
    updateSettings,
    loadSettings,
    clearError,
    resetState
  }
})

export type AgentChatStore = ReturnType<typeof useAgentChatStore>
