import { defineStore } from 'pinia'
import { ConfigManagementService } from '@/api/configManagement'
import type {
  SystemConfig,
  AIConfig,
  PerformanceConfig,
  MonitoringConfig,
  SecurityConfig,
  BackupConfig,
  BackupSchedule,
  TestResult,
  ConfigValidationResult
} from '@/api/configManagement'

interface ConfigManagementState {
  // 各类配置数据
  systemConfig: SystemConfig | null
  aiConfig: AIConfig | null
  performanceConfig: PerformanceConfig | null
  monitoringConfig: MonitoringConfig | null
  securityConfig: SecurityConfig | null

  // 备份相关
  backupList: BackupConfig[]
  backupSchedule: BackupSchedule | null

  // 加载状态
  systemConfigLoading: boolean
  aiConfigLoading: boolean
  performanceConfigLoading: boolean
  monitoringConfigLoading: boolean
  securityConfigLoading: boolean
  backupLoading: boolean

  // 错误状态
  systemConfigError: string | null
  aiConfigError: string | null
  performanceConfigError: string | null
  monitoringConfigError: string | null
  securityConfigError: string | null
  backupError: string | null

  // 未保存的更改追踪
  unsavedChanges: Record<string, boolean>

  // 原始配置备份（用于比较和回滚）
  originalConfigs: {
    system: SystemConfig | null
    ai: AIConfig | null
    performance: PerformanceConfig | null
    monitoring: MonitoringConfig | null
    security: SecurityConfig | null
  }

  // 操作状态
  saving: boolean
  testing: boolean
  lastSaved: string | null

  // 系统信息
  systemInfo: Record<string, any> | null
  configStatus: Record<string, any> | null
}

export const useConfigManagementStore = defineStore('configManagement', {
  state: (): ConfigManagementState => ({
    // 各类配置数据
    systemConfig: null,
    aiConfig: null,
    performanceConfig: null,
    monitoringConfig: null,
    securityConfig: null,

    // 备份相关
    backupList: [],
    backupSchedule: null,

    // 加载状态
    systemConfigLoading: false,
    aiConfigLoading: false,
    performanceConfigLoading: false,
    monitoringConfigLoading: false,
    securityConfigLoading: false,
    backupLoading: false,

    // 错误状态
    systemConfigError: null,
    aiConfigError: null,
    performanceConfigError: null,
    monitoringConfigError: null,
    securityConfigError: null,
    backupError: null,

    // 未保存的更改追踪
    unsavedChanges: {
      system: false,
      ai: false,
      performance: false,
      monitoring: false,
      security: false
    },

    // 原始配置备份
    originalConfigs: {
      system: null,
      ai: null,
      performance: null,
      monitoring: null,
      security: null
    },

    // 操作状态
    saving: false,
    testing: false,
    lastSaved: null,

    // 系统信息
    systemInfo: null,
    configStatus: null
  }),

  getters: {
    hasAnyUnsavedChanges: (state) => {
      return Object.values(state.unsavedChanges).some(changed => changed)
    },

    getConfigByType: (state) => (type: string) => {
      switch (type) {
        case 'system': return state.systemConfig
        case 'ai': return state.aiConfig
        case 'performance': return state.performanceConfig
        case 'monitoring': return state.monitoringConfig
        case 'security': return state.securityConfig
        default: return null
      }
    },

    getLoadingByType: (state) => (type: string) => {
      switch (type) {
        case 'system': return state.systemConfigLoading
        case 'ai': return state.aiConfigLoading
        case 'performance': return state.performanceConfigLoading
        case 'monitoring': return state.monitoringConfigLoading
        case 'security': return state.securityConfigLoading
        case 'backup': return state.backupLoading
        default: return false
      }
    },

    getErrorByType: (state) => (type: string) => {
      switch (type) {
        case 'system': return state.systemConfigError
        case 'ai': return state.aiConfigError
        case 'performance': return state.performanceConfigError
        case 'monitoring': return state.monitoringConfigError
        case 'security': return state.securityConfigError
        case 'backup': return state.backupError
        default: return null
      }
    },

    recentBackups: (state) => {
      return state.backupList
        .filter(backup => backup.status === 'COMPLETED')
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, 5)
    },

    totalBackupSize: (state) => {
      return state.backupList
        .filter(backup => backup.status === 'COMPLETED')
        .reduce((total, backup) => total + backup.size, 0)
    }
  },

  actions: {
    // ============ 系统配置 Actions ============

    async loadSystemConfig() {
      this.systemConfigLoading = true
      this.systemConfigError = null

      try {
        const response = await ConfigManagementService.getSystemConfig()
        this.systemConfig = response
        this.originalConfigs.system = JSON.parse(JSON.stringify(response))
        this.unsavedChanges.system = false
      } catch (error) {
        this.systemConfigError = error instanceof Error ? error.message : '加载系统配置失败'
        console.error('Failed to load system config:', error)
      } finally {
        this.systemConfigLoading = false
      }
    },

    updateSystemConfig(config: SystemConfig) {
      this.systemConfig = config
      this.unsavedChanges.system = true
      this.systemConfigError = null
    },

    async saveSystemConfig() {
      if (!this.systemConfig) return

      this.systemConfigLoading = true
      this.systemConfigError = null

      try {
        const response = await ConfigManagementService.updateSystemConfig(this.systemConfig)
        this.systemConfig = response
        this.originalConfigs.system = JSON.parse(JSON.stringify(response))
        this.unsavedChanges.system = false
        this.lastSaved = new Date().toISOString()
      } catch (error) {
        this.systemConfigError = error instanceof Error ? error.message : '保存系统配置失败'
        throw error
      } finally {
        this.systemConfigLoading = false
      }
    },

    async resetSystemConfig() {
      this.systemConfigLoading = true
      this.systemConfigError = null

      try {
        const response = await ConfigManagementService.resetSystemConfig()
        this.systemConfig = response
        this.originalConfigs.system = JSON.parse(JSON.stringify(response))
        this.unsavedChanges.system = false
      } catch (error) {
        this.systemConfigError = error instanceof Error ? error.message : '重置系统配置失败'
        throw error
      } finally {
        this.systemConfigLoading = false
      }
    },

    // ============ AI 配置 Actions ============

    async loadAIConfig() {
      this.aiConfigLoading = true
      this.aiConfigError = null

      try {
        const response = await ConfigManagementService.getAIConfig()
        this.aiConfig = response
        this.originalConfigs.ai = JSON.parse(JSON.stringify(response))
        this.unsavedChanges.ai = false
      } catch (error) {
        this.aiConfigError = error instanceof Error ? error.message : '加载AI配置失败'
        console.error('Failed to load AI config:', error)
      } finally {
        this.aiConfigLoading = false
      }
    },

    updateAIConfig(config: AIConfig) {
      this.aiConfig = config
      this.unsavedChanges.ai = true
      this.aiConfigError = null
    },

    async saveAIConfig() {
      if (!this.aiConfig) return

      this.aiConfigLoading = true
      this.aiConfigError = null

      try {
        const response = await ConfigManagementService.updateAIConfig(this.aiConfig)
        this.aiConfig = response
        this.originalConfigs.ai = JSON.parse(JSON.stringify(response))
        this.unsavedChanges.ai = false
        this.lastSaved = new Date().toISOString()
      } catch (error) {
        this.aiConfigError = error instanceof Error ? error.message : '保存AI配置失败'
        throw error
      } finally {
        this.aiConfigLoading = false
      }
    },

    async testAIConnection(provider: string): Promise<TestResult[]> {
      this.testing = true

      try {
        const results = await ConfigManagementService.testAIConnection(provider)
        return results
      } catch (error) {
        console.error('AI connection test failed:', error)
        throw error
      } finally {
        this.testing = false
      }
    },

    // ============ 性能配置 Actions ============

    async loadPerformanceConfig() {
      this.performanceConfigLoading = true
      this.performanceConfigError = null

      try {
        const response = await ConfigManagementService.getPerformanceConfig()
        this.performanceConfig = response
        this.originalConfigs.performance = JSON.parse(JSON.stringify(response))
        this.unsavedChanges.performance = false
      } catch (error) {
        this.performanceConfigError = error instanceof Error ? error.message : '加载性能配置失败'
        console.error('Failed to load performance config:', error)
      } finally {
        this.performanceConfigLoading = false
      }
    },

    updatePerformanceConfig(config: PerformanceConfig) {
      this.performanceConfig = config
      this.unsavedChanges.performance = true
      this.performanceConfigError = null
    },

    async savePerformanceConfig() {
      if (!this.performanceConfig) return

      this.performanceConfigLoading = true
      this.performanceConfigError = null

      try {
        const response = await ConfigManagementService.updatePerformanceConfig(this.performanceConfig)
        this.performanceConfig = response
        this.originalConfigs.performance = JSON.parse(JSON.stringify(response))
        this.unsavedChanges.performance = false
        this.lastSaved = new Date().toISOString()
      } catch (error) {
        this.performanceConfigError = error instanceof Error ? error.message : '保存性能配置失败'
        throw error
      } finally {
        this.performanceConfigLoading = false
      }
    },

    async clearCache(type?: string) {
      try {
        await ConfigManagementService.clearCache(type)
      } catch (error) {
        console.error('Clear cache failed:', error)
        throw error
      }
    },

    // ============ 监控配置 Actions ============

    async loadMonitoringConfig() {
      this.monitoringConfigLoading = true
      this.monitoringConfigError = null

      try {
        const response = await ConfigManagementService.getMonitoringConfig()
        this.monitoringConfig = response
        this.originalConfigs.monitoring = JSON.parse(JSON.stringify(response))
        this.unsavedChanges.monitoring = false
      } catch (error) {
        this.monitoringConfigError = error instanceof Error ? error.message : '加载监控配置失败'
        console.error('Failed to load monitoring config:', error)
      } finally {
        this.monitoringConfigLoading = false
      }
    },

    updateMonitoringConfig(config: MonitoringConfig) {
      this.monitoringConfig = config
      this.unsavedChanges.monitoring = true
      this.monitoringConfigError = null
    },

    async saveMonitoringConfig() {
      if (!this.monitoringConfig) return

      this.monitoringConfigLoading = true
      this.monitoringConfigError = null

      try {
        const response = await ConfigManagementService.updateMonitoringConfig(this.monitoringConfig)
        this.monitoringConfig = response
        this.originalConfigs.monitoring = JSON.parse(JSON.stringify(response))
        this.unsavedChanges.monitoring = false
        this.lastSaved = new Date().toISOString()
      } catch (error) {
        this.monitoringConfigError = error instanceof Error ? error.message : '保存监控配置失败'
        throw error
      } finally {
        this.monitoringConfigLoading = false
      }
    },

    async testLogging(): Promise<TestResult[]> {
      this.testing = true

      try {
        const results = await ConfigManagementService.testLogging()
        return results
      } catch (error) {
        console.error('Logging test failed:', error)
        throw error
      } finally {
        this.testing = false
      }
    },

    // ============ 安全配置 Actions ============

    async loadSecurityConfig() {
      this.securityConfigLoading = true
      this.securityConfigError = null

      try {
        const response = await ConfigManagementService.getSecurityConfig()
        this.securityConfig = response
        this.originalConfigs.security = JSON.parse(JSON.stringify(response))
        this.unsavedChanges.security = false
      } catch (error) {
        this.securityConfigError = error instanceof Error ? error.message : '加载安全配置失败'
        console.error('Failed to load security config:', error)
      } finally {
        this.securityConfigLoading = false
      }
    },

    updateSecurityConfig(config: SecurityConfig) {
      this.securityConfig = config
      this.unsavedChanges.security = true
      this.securityConfigError = null
    },

    async saveSecurityConfig() {
      if (!this.securityConfig) return

      this.securityConfigLoading = true
      this.securityConfigError = null

      try {
        const response = await ConfigManagementService.updateSecurityConfig(this.securityConfig)
        this.securityConfig = response
        this.originalConfigs.security = JSON.parse(JSON.stringify(response))
        this.unsavedChanges.security = false
        this.lastSaved = new Date().toISOString()
      } catch (error) {
        this.securityConfigError = error instanceof Error ? error.message : '保存安全配置失败'
        throw error
      } finally {
        this.securityConfigLoading = false
      }
    },

    async regenerateSecurityKeys() {
      try {
        await ConfigManagementService.regenerateAPIKeys()
        // 重新加载安全配置
        await this.loadSecurityConfig()
      } catch (error) {
        console.error('Regenerate security keys failed:', error)
        throw error
      }
    },

    // ============ 备份管理 Actions ============

    async loadBackupList() {
      this.backupLoading = true
      this.backupError = null

      try {
        const response = await ConfigManagementService.getBackupList()
        this.backupList = response
      } catch (error) {
        this.backupError = error instanceof Error ? error.message : '加载备份列表失败'
        console.error('Failed to load backup list:', error)
      } finally {
        this.backupLoading = false
      }
    },

    async createBackup(name: string, description?: string) {
      this.backupLoading = true
      this.backupError = null

      try {
        const response = await ConfigManagementService.createBackup(name, description)
        this.backupList.unshift(response)
      } catch (error) {
        this.backupError = error instanceof Error ? error.message : '创建备份失败'
        throw error
      } finally {
        this.backupLoading = false
      }
    },

    async restoreBackup(backupId: string) {
      this.backupLoading = true
      this.backupError = null

      try {
        await ConfigManagementService.restoreBackup(backupId)
        // 重新加载所有配置
        await this.loadAllConfigs()
      } catch (error) {
        this.backupError = error instanceof Error ? error.message : '恢复备份失败'
        throw error
      } finally {
        this.backupLoading = false
      }
    },

    async deleteBackup(backupId: string) {
      this.backupLoading = true
      this.backupError = null

      try {
        await ConfigManagementService.deleteBackup(backupId)
        this.backupList = this.backupList.filter(backup => backup.id !== backupId)
      } catch (error) {
        this.backupError = error instanceof Error ? error.message : '删除备份失败'
        throw error
      } finally {
        this.backupLoading = false
      }
    },

    async loadBackupSchedule() {
      try {
        const response = await ConfigManagementService.getBackupSchedule()
        this.backupSchedule = response
      } catch (error) {
        console.error('Failed to load backup schedule:', error)
      }
    },

    async scheduleBackup(schedule: BackupSchedule) {
      try {
        const response = await ConfigManagementService.updateBackupSchedule(schedule)
        this.backupSchedule = response
      } catch (error) {
        console.error('Schedule backup failed:', error)
        throw error
      }
    },

    // ============ 批量操作 Actions ============

    async saveAllChanges() {
      this.saving = true

      try {
        const configs: any = {}

        if (this.unsavedChanges.system && this.systemConfig) {
          configs.system = this.systemConfig
        }
        if (this.unsavedChanges.ai && this.aiConfig) {
          configs.ai = this.aiConfig
        }
        if (this.unsavedChanges.performance && this.performanceConfig) {
          configs.performance = this.performanceConfig
        }
        if (this.unsavedChanges.monitoring && this.monitoringConfig) {
          configs.monitoring = this.monitoringConfig
        }
        if (this.unsavedChanges.security && this.securityConfig) {
          configs.security = this.securityConfig
        }

        await ConfigManagementService.saveAllConfigs(configs)

        // 更新原始配置和清除未保存标记
        Object.keys(configs).forEach(type => {
          this.originalConfigs[type as keyof typeof this.originalConfigs] =
            JSON.parse(JSON.stringify(configs[type]))
          this.unsavedChanges[type as keyof typeof this.unsavedChanges] = false
        })

        this.lastSaved = new Date().toISOString()
      } catch (error) {
        console.error('Save all changes failed:', error)
        throw error
      } finally {
        this.saving = false
      }
    },

    discardAllChanges() {
      // 恢复到原始配置
      if (this.originalConfigs.system) {
        this.systemConfig = JSON.parse(JSON.stringify(this.originalConfigs.system))
      }
      if (this.originalConfigs.ai) {
        this.aiConfig = JSON.parse(JSON.stringify(this.originalConfigs.ai))
      }
      if (this.originalConfigs.performance) {
        this.performanceConfig = JSON.parse(JSON.stringify(this.originalConfigs.performance))
      }
      if (this.originalConfigs.monitoring) {
        this.monitoringConfig = JSON.parse(JSON.stringify(this.originalConfigs.monitoring))
      }
      if (this.originalConfigs.security) {
        this.securityConfig = JSON.parse(JSON.stringify(this.originalConfigs.security))
      }

      // 清除未保存标记
      Object.keys(this.unsavedChanges).forEach(key => {
        this.unsavedChanges[key as keyof typeof this.unsavedChanges] = false
      })
    },

    async loadAllConfigs() {
      await Promise.allSettled([
        this.loadSystemConfig(),
        this.loadAIConfig(),
        this.loadPerformanceConfig(),
        this.loadMonitoringConfig(),
        this.loadSecurityConfig()
      ])
    },

    // ============ 导入导出 Actions ============

    async exportConfiguration() {
      try {
        const blob = await ConfigManagementService.exportConfiguration()

        // 创建下载链接
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `system-config-${new Date().toISOString().slice(0, 19)}.json`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
      } catch (error) {
        console.error('Export configuration failed:', error)
        throw error
      }
    },

    async importConfiguration(configData: any) {
      try {
        await ConfigManagementService.importConfiguration(configData)
        // 重新加载所有配置
        await this.loadAllConfigs()
      } catch (error) {
        console.error('Import configuration failed:', error)
        throw error
      }
    },

    // ============ 系统信息 Actions ============

    async loadSystemInfo() {
      try {
        const response = await ConfigManagementService.getSystemInfo()
        this.systemInfo = response
      } catch (error) {
        console.error('Failed to load system info:', error)
      }
    },

    async loadConfigStatus() {
      try {
        const response = await ConfigManagementService.getConfigStatus()
        this.configStatus = response
      } catch (error) {
        console.error('Failed to load config status:', error)
      }
    },

    // ============ 清理和重置 ============

    clearAllErrors() {
      this.systemConfigError = null
      this.aiConfigError = null
      this.performanceConfigError = null
      this.monitoringConfigError = null
      this.securityConfigError = null
      this.backupError = null
    },

    resetConfigManagement() {
      // 重置所有数据到初始状态
      this.systemConfig = null
      this.aiConfig = null
      this.performanceConfig = null
      this.monitoringConfig = null
      this.securityConfig = null
      this.backupList = []
      this.backupSchedule = null

      // 重置原始配置
      Object.keys(this.originalConfigs).forEach(key => {
        this.originalConfigs[key as keyof typeof this.originalConfigs] = null
      })

      // 重置未保存标记
      Object.keys(this.unsavedChanges).forEach(key => {
        this.unsavedChanges[key as keyof typeof this.unsavedChanges] = false
      })

      this.clearAllErrors()
      this.lastSaved = null
      this.systemInfo = null
      this.configStatus = null
    }
  }
})
