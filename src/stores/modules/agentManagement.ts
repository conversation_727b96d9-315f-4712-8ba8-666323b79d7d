import { defineStore } from 'pinia'
import { AgentManagementService } from '@/api/agentManagement'
import type {
  AgentDefinition,
  AgentFlow,
  SystemMetrics,
  ChartData,
  SystemResources,
  LogEntry,
  Alert
} from '@/api/agentManagement'

interface AgentManagementState {
  // Agent 管理
  agents: AgentDefinition[]
  currentAgent: AgentDefinition | null
  agentLoading: boolean
  agentError: string | null

  // 流程管理
  flows: AgentFlow[]
  currentFlow: AgentFlow | null
  flowLoading: boolean
  flowError: string | null

  // 监控数据
  metrics: SystemMetrics
  executionTrendData: ChartData | null
  agentUsageData: ChartData | null
  responseTimeData: ChartData | null
  systemResources: SystemResources | null
  logs: LogEntry[]
  alerts: Alert[]
  monitoringLoading: boolean
  monitoringError: string | null

  // 系统配置
  systemConfig: Record<string, any>
  configLoading: boolean
  configError: string | null

  // 分页和过滤
  pagination: {
    agents: { page: number; limit: number; total: number }
    flows: { page: number; limit: number; total: number }
    logs: { page: number; limit: number; total: number }
  }
  filters: {
    agents: { status?: string; category?: string; search?: string }
    flows: { status?: string; category?: string; search?: string }
    logs: { level?: string; source?: string }
  }
  sorting: {
    agents: { field: string; order: 'asc' | 'desc' }
    flows: { field: string; order: 'asc' | 'desc' }
  }
}

export const useAgentManagementStore = defineStore('agentManagement', {
  state: (): AgentManagementState => ({
    // Agent 管理
    agents: [],
    currentAgent: null,
    agentLoading: false,
    agentError: null,

    // 流程管理
    flows: [],
    currentFlow: null,
    flowLoading: false,
    flowError: null,

    // 监控数据
    metrics: {
      totalExecutions: 0,
      successRate: 0,
      avgResponseTime: 0,
      activeUsers: 0,
      executionsTrend: 0,
      successRateTrend: 0,
      responseTimeTrend: 0,
      activeUsersTrend: 0
    },
    executionTrendData: null,
    agentUsageData: null,
    responseTimeData: null,
    systemResources: null,
    logs: [],
    alerts: [],
    monitoringLoading: false,
    monitoringError: null,

    // 系统配置
    systemConfig: {},
    configLoading: false,
    configError: null,

    // 分页和过滤
    pagination: {
      agents: { page: 1, limit: 20, total: 0 },
      flows: { page: 1, limit: 20, total: 0 },
      logs: { page: 1, limit: 100, total: 0 }
    },
    filters: {
      agents: {},
      flows: {},
      logs: {}
    },
    sorting: {
      agents: { field: 'createdAt', order: 'desc' },
      flows: { field: 'createdAt', order: 'desc' }
    }
  }),

  getters: {
    // Agent 相关
    activeAgents: (state) => Array.isArray(state.agents) ? state.agents.filter(agent => agent.status === 'ACTIVE') : [],
    agentsByCategory: (state) => {
      const categories: Record<string, AgentDefinition[]> = {}
      if (Array.isArray(state.agents)) {
        state.agents.forEach(agent => {
          if (!categories[agent.category]) {
            categories[agent.category] = []
          }
          categories[agent.category].push(agent)
        })
      }
      return categories
    },

    // 流程相关
    activeFlows: (state) => Array.isArray(state.flows) ? state.flows.filter(flow => flow.status === 'ACTIVE') : [],
    flowsByCategory: (state) => {
      const categories: Record<string, AgentFlow[]> = {}
      if (Array.isArray(state.flows)) {
        state.flows.forEach(flow => {
          const category = flow.category || 'default'
          if (!categories[category]) {
            categories[category] = []
          }
          categories[category].push(flow)
        })
      }
      return categories
    },

    // 监控相关
    criticalAlerts: (state) => Array.isArray(state.alerts) ? state.alerts.filter(alert =>
      alert.type === 'error' && !alert.dismissed
    ) : [],
    warningAlerts: (state) => Array.isArray(state.alerts) ? state.alerts.filter(alert =>
      alert.type === 'warning' && !alert.dismissed
    ) : [],
    errorLogs: (state) => Array.isArray(state.logs) ? state.logs.filter(log => log.level === 'ERROR') : [],

    // 统计相关
    agentStatistics: (state) => ({
      total: Array.isArray(state.agents) ? state.agents.length : 0,
      active: Array.isArray(state.agents) ? state.agents.filter(a => a.status === 'ACTIVE').length : 0,
      inactive: Array.isArray(state.agents) ? state.agents.filter(a => a.status === 'INACTIVE').length : 0,
      draft: Array.isArray(state.agents) ? state.agents.filter(a => a.status === 'DRAFT').length : 0
    }),
    flowStatistics: (state) => ({
      total: Array.isArray(state.flows) ? state.flows.length : 0,
      active: Array.isArray(state.flows) ? state.flows.filter(f => f.status === 'ACTIVE').length : 0,
      inactive: Array.isArray(state.flows) ? state.flows.filter(f => f.status === 'INACTIVE').length : 0,
      draft: Array.isArray(state.flows) ? state.flows.filter(f => f.status === 'DRAFT').length : 0
    })
  },

  actions: {
    // ============ Agent 管理 Actions ============

    async loadAgents(refresh = false) {
      if (this.agentLoading && !refresh) return

      this.agentLoading = true
      this.agentError = null

      try {
        const params = {
          page: this.pagination.agents.page,
          limit: this.pagination.agents.limit,
          ...this.filters.agents
        }

        const response = await AgentManagementService.getAgents(params)

        if (response.success && response.data) {
          // 确保 response.data 是数组，如果不是则使用空数组
          this.agents = Array.isArray(response.data) ? response.data : []
          // 假设 API 返回总数信息
          this.pagination.agents.total = response.total || (Array.isArray(response.data) ? response.data.length : 0)
        } else {
          // 失败时确保 agents 仍然是空数组
          this.agents = []
          this.pagination.agents.total = 0
          throw new Error(response.message || '加载 Agent 列表失败')
        }
      } catch (error) {
        // 发生错误时确保 agents 是空数组
        this.agents = []
        this.pagination.agents.total = 0
        this.agentError = error instanceof Error ? error.message : '未知错误'
        console.error('Failed to load agents:', error)
      } finally {
        this.agentLoading = false
      }
    },

    async createAgent(agentData: AgentDefinition) {
      try {
        const response = await AgentManagementService.createAgent(agentData)

        if (response.success && response.data) {
          // 确保 agents 是数组后再添加新项
          if (Array.isArray(this.agents)) {
            this.agents.unshift(response.data)
            this.pagination.agents.total += 1
          } else {
            // 如果 agents 不是数组，重新初始化为包含新项的数组
            this.agents = [response.data]
            this.pagination.agents.total = 1
          }
          return response.data
        } else {
          throw new Error(response.message || '创建 Agent 失败')
        }
      } catch (error) {
        const message = error instanceof Error ? error.message : '创建 Agent 失败'
        this.agentError = message
        throw new Error(message)
      }
    },

    async updateAgent(agentData: AgentDefinition) {
      try {
        const response = await AgentManagementService.updateAgent(agentData)

        if (response.success && response.data) {
          // 确保 agents 是数组后再进行查找和更新
          if (Array.isArray(this.agents)) {
            const index = this.agents.findIndex(agent => agent.id === agentData.id)
            if (index !== -1) {
              this.agents[index] = response.data
            }
          }

          if (this.currentAgent?.id === agentData.id) {
            this.currentAgent = response.data
          }

          return response.data
        } else {
          throw new Error(response.message || '更新 Agent 失败')
        }
      } catch (error) {
        const message = error instanceof Error ? error.message : '更新 Agent 失败'
        this.agentError = message
        throw new Error(message)
      }
    },

    async deleteAgent(agentId: number) {
      try {
        const response = await AgentManagementService.deleteAgent(agentId)

        if (response.success) {
          // 确保 agents 是数组后再进行过滤操作
          if (Array.isArray(this.agents)) {
            this.agents = this.agents.filter(agent => agent.id !== agentId)
            this.pagination.agents.total = Math.max(0, this.pagination.agents.total - 1)
          }

          if (this.currentAgent?.id === agentId) {
            this.currentAgent = null
          }
        } else {
          throw new Error(response.message || '删除 Agent 失败')
        }
      } catch (error) {
        const message = error instanceof Error ? error.message : '删除 Agent 失败'
        this.agentError = message
        throw new Error(message)
      }
    },

    async updateAgentStatus(agentId: number, status: 'ACTIVE' | 'INACTIVE') {
      try {
        const response = await AgentManagementService.updateAgentStatus(agentId, status)

        if (response.success) {
          // 确保 agents 是数组后再查找
          if (Array.isArray(this.agents)) {
            const agent = this.agents.find(a => a.id === agentId)
            if (agent) {
              agent.status = status
            }
          }

          if (this.currentAgent?.id === agentId) {
            this.currentAgent.status = status
          }
        } else {
          throw new Error(response.message || '更新 Agent 状态失败')
        }
      } catch (error) {
        const message = error instanceof Error ? error.message : '更新 Agent 状态失败'
        this.agentError = message
        throw new Error(message)
      }
    },

    async testAgent(agentId: number, testData: any) {
      try {
        const response = await AgentManagementService.testAgent(agentId, testData)

        if (response.success) {
          return response.data
        } else {
          throw new Error(response.message || 'Agent 测试失败')
        }
      } catch (error) {
        const message = error instanceof Error ? error.message : 'Agent 测试失败'
        throw new Error(message)
      }
    },

    async importAgents(agentsData: AgentDefinition[]) {
      try {
        const response = await AgentManagementService.importAgents(agentsData)

        if (response.success && response.data) {
          // 重新加载 Agent 列表
          await this.loadAgents(true)
          return response.data
        } else {
          throw new Error(response.message || '导入 Agent 失败')
        }
      } catch (error) {
        const message = error instanceof Error ? error.message : '导入 Agent 失败'
        this.agentError = message
        throw new Error(message)
      }
    },

    // ============ 流程管理 Actions ============

    async loadFlows(refresh = false) {
      if (this.flowLoading && !refresh) return

      this.flowLoading = true
      this.flowError = null

      try {
        const params = {
          page: this.pagination.flows.page,
          limit: this.pagination.flows.limit,
          ...this.filters.flows
        }

        const response = await AgentManagementService.getFlows(params)

        if (response.success && response.data) {
          this.flows = response.data
          this.pagination.flows.total = response.total || response.data.length
        } else {
          throw new Error(response.message || '加载流程列表失败')
        }
      } catch (error) {
        this.flowError = error instanceof Error ? error.message : '未知错误'
        console.error('Failed to load flows:', error)
      } finally {
        this.flowLoading = false
      }
    },

    async createFlow(flowData: AgentFlow) {
      try {
        const response = await AgentManagementService.createFlow(flowData)

        if (response.success && response.data) {
          this.flows.unshift(response.data)
          this.pagination.flows.total += 1
          return response.data
        } else {
          throw new Error(response.message || '创建流程失败')
        }
      } catch (error) {
        const message = error instanceof Error ? error.message : '创建流程失败'
        this.flowError = message
        throw new Error(message)
      }
    },

    async updateFlow(flowData: AgentFlow) {
      try {
        const response = await AgentManagementService.updateFlow(flowData)

        if (response.success && response.data) {
          const index = this.flows.findIndex(flow => flow.id === flowData.id)
          if (index !== -1) {
            this.flows[index] = response.data
          }

          if (this.currentFlow?.id === flowData.id) {
            this.currentFlow = response.data
          }

          return response.data
        } else {
          throw new Error(response.message || '更新流程失败')
        }
      } catch (error) {
        const message = error instanceof Error ? error.message : '更新流程失败'
        this.flowError = message
        throw new Error(message)
      }
    },

    async deleteFlow(flowId: number) {
      try {
        const response = await AgentManagementService.deleteFlow(flowId)

        if (response.success) {
          this.flows = this.flows.filter(flow => flow.id !== flowId)
          this.pagination.flows.total -= 1

          if (this.currentFlow?.id === flowId) {
            this.currentFlow = null
          }
        } else {
          throw new Error(response.message || '删除流程失败')
        }
      } catch (error) {
        const message = error instanceof Error ? error.message : '删除流程失败'
        this.flowError = message
        throw new Error(message)
      }
    },

    async validateFlow(flowData: AgentFlow) {
      try {
        const response = await AgentManagementService.validateFlow(flowData)

        if (response.success) {
          return response.data
        } else {
          throw new Error(response.message || '流程验证失败')
        }
      } catch (error) {
        const message = error instanceof Error ? error.message : '流程验证失败'
        throw new Error(message)
      }
    },

    async testFlow(flowId: number, testData: any) {
      try {
        const response = await AgentManagementService.testFlow(flowId, testData)

        if (response.success) {
          return response.data
        } else {
          throw new Error(response.message || '流程测试失败')
        }
      } catch (error) {
        const message = error instanceof Error ? error.message : '流程测试失败'
        throw new Error(message)
      }
    },

    // ============ 监控数据 Actions ============

    async loadMetrics(timeRange = '24h') {
      this.monitoringLoading = true
      this.monitoringError = null

      try {
        const response = await AgentManagementService.getMetrics(timeRange)

        if (response.success && response.data) {
          this.metrics = response.data
        } else {
          throw new Error(response.message || '加载系统指标失败')
        }
      } catch (error) {
        this.monitoringError = error instanceof Error ? error.message : '未知错误'
        console.error('Failed to load metrics:', error)
      } finally {
        this.monitoringLoading = false
      }
    },

    async loadChartData(timeRange = '24h') {
      try {
        const [
          executionTrend,
          agentUsage,
          responseTime
        ] = await Promise.all([
          AgentManagementService.getExecutionTrend(timeRange),
          AgentManagementService.getAgentUsage(timeRange),
          AgentManagementService.getResponseTimeDistribution(timeRange)
        ])

        if (executionTrend.success && executionTrend.data) {
          this.executionTrendData = executionTrend.data
        }

        if (agentUsage.success && agentUsage.data) {
          this.agentUsageData = agentUsage.data
        }

        if (responseTime.success && responseTime.data) {
          this.responseTimeData = responseTime.data
        }
      } catch (error) {
        console.error('Failed to load chart data:', error)
      }
    },

    async loadSystemResources() {
      try {
        const response = await AgentManagementService.getSystemResources()

        if (response.success && response.data) {
          this.systemResources = response.data
        }
      } catch (error) {
        console.error('Failed to load system resources:', error)
      }
    },

    async loadRecentLogs() {
      try {
        const params = {
          ...this.filters.logs,
          limit: this.pagination.logs.limit
        }

        const response = await AgentManagementService.getRecentLogs(params)

        if (response.success && response.data) {
          this.logs = response.data
        }
      } catch (error) {
        console.error('Failed to load logs:', error)
      }
    },

    async loadAlerts() {
      try {
        const response = await AgentManagementService.getAlerts()

        if (response.success && response.data) {
          this.alerts = response.data
        }
      } catch (error) {
        console.error('Failed to load alerts:', error)
      }
    },

    async dismissAlert(alertId: string) {
      try {
        const response = await AgentManagementService.dismissAlert(alertId)

        if (response.success) {
          const alert = this.alerts.find(a => a.id === alertId)
          if (alert) {
            alert.dismissed = true
          }
        }
      } catch (error) {
        console.error('Failed to dismiss alert:', error)
      }
    },

    async dismissAllAlerts() {
      try {
        const response = await AgentManagementService.clearAlerts()

        if (response.success) {
          this.alerts.forEach(alert => {
            alert.dismissed = true
          })
        }
      } catch (error) {
        console.error('Failed to dismiss all alerts:', error)
      }
    },

    clearLogs() {
      this.logs = []
    },

    // ============ 系统配置 Actions ============

    async loadSystemConfig() {
      this.configLoading = true
      this.configError = null

      try {
        const response = await AgentManagementService.getSystemConfig()

        if (response.success && response.data) {
          this.systemConfig = response.data
        } else {
          throw new Error(response.message || '加载系统配置失败')
        }
      } catch (error) {
        this.configError = error instanceof Error ? error.message : '未知错误'
        console.error('Failed to load system config:', error)
      } finally {
        this.configLoading = false
      }
    },

    async updateSystemConfig(config: Record<string, any>) {
      try {
        const response = await AgentManagementService.updateSystemConfig(config)

        if (response.success) {
          this.systemConfig = { ...this.systemConfig, ...config }
        } else {
          throw new Error(response.message || '更新系统配置失败')
        }
      } catch (error) {
        const message = error instanceof Error ? error.message : '更新系统配置失败'
        this.configError = message
        throw new Error(message)
      }
    },

    // ============ 过滤和排序 ============

    setAgentFilters(filters: typeof this.filters.agents) {
      this.filters.agents = { ...this.filters.agents, ...filters }
      this.pagination.agents.page = 1
    },

    setFlowFilters(filters: typeof this.filters.flows) {
      this.filters.flows = { ...this.filters.flows, ...filters }
      this.pagination.flows.page = 1
    },

    setLogFilters(filters: typeof this.filters.logs) {
      this.filters.logs = { ...this.filters.logs, ...filters }
    },

    sortAgents(field: string, order: 'asc' | 'desc') {
      this.sorting.agents = { field, order }

      // 确保 agents 是数组后再进行排序
      if (Array.isArray(this.agents)) {
        this.agents.sort((a, b) => {
          const aValue = (a as any)[field]
          const bValue = (b as any)[field]

          if (order === 'asc') {
            return aValue > bValue ? 1 : -1
          } else {
            return aValue < bValue ? 1 : -1
          }
        })
      }
    },

    sortFlows(field: string, order: 'asc' | 'desc') {
      this.sorting.flows = { field, order }

      // 确保 flows 是数组后再进行排序
      if (Array.isArray(this.flows)) {
        this.flows.sort((a, b) => {
          const aValue = (a as any)[field]
          const bValue = (b as any)[field]

          if (order === 'asc') {
            return aValue > bValue ? 1 : -1
          } else {
            return aValue < bValue ? 1 : -1
          }
        })
      }
    },

    // ============ 分页 ============

    setAgentPage(page: number) {
      this.pagination.agents.page = page
    },

    setFlowPage(page: number) {
      this.pagination.flows.page = page
    },

    setPageSize(type: 'agents' | 'flows' | 'logs', size: number) {
      this.pagination[type].limit = size
      this.pagination[type].page = 1
    },

    // ============ 清理和重置 ============

    clearAgentError() {
      this.agentError = null
    },

    clearFlowError() {
      this.flowError = null
    },

    clearMonitoringError() {
      this.monitoringError = null
    },

    clearConfigError() {
      this.configError = null
    },

    resetFilters() {
      this.filters = {
        agents: {},
        flows: {},
        logs: {}
      }
      this.pagination.agents.page = 1
      this.pagination.flows.page = 1
    }
  }
})
