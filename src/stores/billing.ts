import type {
  AppealRecord,
  AppealRecordQueryParams,
  AppealRequest,
  BalanceWarningConfig,
  BillingAccount,
  BillingConfig,
  BillingStatistics,
  PaymentMethod,
  RechargeRecord,
  RechargeRecordQueryParams,
  RechargeRequest,
  TokenCalculationRequest,
  TokenCalculationResponse,
  UsageRecord,
  UsageRecordQueryParams,
} from '@/types/billing'
import type { ErrorConfig } from '@/utils/errorHandler'
import { BillingAPI } from '@/api/billing'
import { tokenCalculationCacheManager } from '@/utils/cacheManager'
import { classifyError, errorHandler, showErrorMessage } from '@/utils/errorHandler'
import { useDebounceFn } from '@vueuse/core'
import { message } from 'ant-design-vue'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

/**
 * 计费系统状态管理
 */
export const useBillingStore = defineStore('billing', () => {
  // 基础状态
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // 账户信息
  const account = ref<BillingAccount | null>(null)
  const config = ref<BillingConfig | null>(null)
  const balanceWarningConfig = ref<BalanceWarningConfig | null>(null)

  // 记录数据
  const usageRecords = ref<UsageRecord[]>([])
  const rechargeRecords = ref<RechargeRecord[]>([])
  const appealRecords = ref<AppealRecord[]>([])

  // 支付方式
  const paymentMethods = ref<PaymentMethod[]>([])

  // 统计数据
  const statistics = ref<BillingStatistics | null>(null)
  const currentTokenCalculation = ref<TokenCalculationResponse | null>(null)

  // 分页信息
  const usageRecordsPagination = ref({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条`,
  })

  const rechargeRecordsPagination = ref({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条`,
  })

  const appealRecordsPagination = ref({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条`,
  })

  // 计算属性
  const balance = computed(() => account.value?.balance || 0)
  const currency = computed(() => account.value?.currency || 'CNY')
  const accountStatus = computed(() => account.value?.status || 'ACTIVE')

  // 余额状态
  const balanceStatus = computed(() => {
    if (!account.value) {
      return 'normal'
    }

    const { balance } = account.value

    // 如果余额警告配置未加载，使用默认阈值
    if (!balanceWarningConfig.value) {
      // 默认阈值：余额 <= 0 为严重不足，余额 <= 10 为不足
      if (balance <= 0) {
        return 'critical'
      }
      if (balance <= 10) {
        return 'warning'
      }
      return 'normal'
    }

    const { lowBalanceThreshold, criticalBalanceThreshold } = balanceWarningConfig.value

    if (balance <= criticalBalanceThreshold) {
      return 'critical'
    }
    if (balance <= lowBalanceThreshold) {
      return 'warning'
    }
    return 'normal'
  })

  const isBalanceSufficient = computed(() => balanceStatus.value !== 'critical')

  // 当前使用的计费配置
  const currentConfig = computed(() => {
    return config.value
  })

  // 赠送余额
  const giftBalance = computed(() => config.value?.giftBalance || 0)

  // 充值余额
  const rechargedBalance = computed(() => config.value?.rechargedBalance || 0)

  // 免费Token
  const freeTokens = computed(() => config.value?.freeTokens || 0)

  // 剩余免费Token（考虑今日使用量）
  const remainingFreeTokens = computed(() => {
    const total = config.value?.freeTokens || 0
    const used = config.value?.todayTokenUsage || 0
    return Math.max(0, total - used)
  })

  // Token费用计算缓存统计
  const cacheStats = ref({
    hits: 0,
    misses: 0,
    get hitRate() {
      const total = this.hits + this.misses
      return total > 0 ? (this.hits / total * 100).toFixed(2) : '0.00'
    },
  })

  /**
   * 防抖计算状态
   */
  const isCalculating = ref(false)

  /**
   * 错误处理状态
   */
  const errorInfo = ref<ErrorConfig | null>(null)
  const retryCount = ref(0)

  /**
   * 细粒度加载状态
   */
  const loadingStates = ref({
    calculating: false,
    retrying: false,
    caching: false,
    batchProcessing: false,
    fetching: false,
  })

  /**
   * 计算是否有任何加载状态
   */
  const isAnyLoading = computed(() => {
    return Object.values(loadingStates.value).some(state => state)
  })

  /**
   * 设置特定的加载状态
   */
  const setLoadingState = (key: keyof typeof loadingStates.value, value: boolean) => {
    loadingStates.value[key] = value
    // 兼容原有的 isLoading
    isLoading.value = isAnyLoading.value
  }

  /**
   * 获取当前加载状态描述
   */
  const getLoadingDescription = computed(() => {
    if (loadingStates.value.calculating) {
      return '正在计算Token费用...'
    }
    if (loadingStates.value.retrying) {
      return '重试中，请稍候...'
    }
    if (loadingStates.value.batchProcessing) {
      return '批量处理中...'
    }
    if (loadingStates.value.fetching) {
      return '获取数据中...'
    }
    if (loadingStates.value.caching) {
      return '缓存处理中...'
    }
    return ''
  })

  // Actions

  /**
   * 设置加载状态
   */
  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  /**
   * 设置错误信息
   */
  const setError = (errorMessage: string | null) => {
    error.value = errorMessage
    if (errorMessage) {
      message.error(errorMessage)
    }
  }

  /**
   * 清除错误
   */
  const clearError = () => {
    error.value = null
  }

  /**
   * 获取账户信息
   */
  const fetchAccount = async () => {
    try {
      setLoading(true)
      clearError()

      const response = await BillingAPI.getAccount()
      if (response.success) {
        account.value = response.data
      }
      else {
        setError(response.message || '获取账户信息失败')
      }
    }
    catch (err: any) {
      setError(err.message || '获取账户信息失败')
    }
    finally {
      setLoading(false)
    }
  }

  /**
   * 刷新余额（静默刷新，不显示成功消息）
   */
  const refreshBalance = async (showMessage = false) => {
    try {
      const response = await BillingAPI.refreshBalance()
      if (response.success) {
        account.value = response.data
        if (showMessage) {
          message.success('余额已刷新')
        }
      }
      else {
        setError(response.message || '刷新余额失败')
      }
    }
    catch (err: any) {
      setError(err.message || '刷新余额失败')
    }
  }

  /**
   * 手动刷新余额（显示成功消息）
   */
  const manualRefreshBalance = async () => {
    await refreshBalance(true)
  }

  /**
   * Token消耗后刷新余额（静默刷新）
   */
  const refreshBalanceAfterTokenUsage = async () => {
    await refreshBalance(false)
  }

  /**
   * 获取计费配置
   */
  const fetchConfiguration = async () => {
    try {
      const response = await BillingAPI.getConfiguration()
      if (response.success) {
        config.value = response.data
      }
      else {
        setError(response.message || '获取计费配置失败')
      }
    }
    catch (err: any) {
      setError(err.message || '获取计费配置失败')
    }
  }

  /**
   * 获取余额警告配置
   */
  const fetchBalanceWarningConfig = async () => {
    try {
      const response = await BillingAPI.getBalanceWarningConfig()
      if (response.success) {
        balanceWarningConfig.value = response.data
      }
    }
    catch (err: any) {
      console.warn('获取余额警告配置失败:', err.message)
    }
  }

  /**
   * 更新余额警告配置
   */
  const updateBalanceWarningConfig = async (config: BalanceWarningConfig) => {
    try {
      const response = await BillingAPI.updateBalanceWarningConfig(config)
      if (response.success) {
        balanceWarningConfig.value = response.data
        message.success('余额警告配置已更新')
      }
      else {
        setError(response.message || '更新余额警告配置失败')
      }
    }
    catch (err: any) {
      setError(err.message || '更新余额警告配置失败')
    }
  }

  /**
   * 设置增强的错误信息
   */
  const setEnhancedError = (config: ErrorConfig) => {
    errorInfo.value = config
    error.value = config.userMessage
    showErrorMessage(config)
  }

  /**
   * 计算Token费用 - 内部实现（带缓存和错误处理）
   */
  const calculateTokenCostInternal = async (data: TokenCalculationRequest) => {
    const errorKey = `calculate-${tokenCalculationCacheManager.generateKey(data)}`

    return await errorHandler.handleError(
      errorKey,
      async () => {
        // 检查缓存
        const cacheKey = tokenCalculationCacheManager.generateKey(data)
        const cachedResult = tokenCalculationCacheManager.get(cacheKey)

        if (cachedResult) {
          console.log('💾 [Token计算] 缓存命中:', cacheKey)
          cacheStats.value.hits++
          currentTokenCalculation.value = cachedResult
          return cachedResult
        }

        console.log('🔄 [Token计算] 缓存未命中，调用API:', cacheKey)
        cacheStats.value.misses++

        const response = await BillingAPI.calculateTokenCost(data)
        if (response.success) {
          currentTokenCalculation.value = response.data

          // 保存到缓存
          tokenCalculationCacheManager.set(cacheKey, response.data)
          console.log('💾 [Token计算] 结果已缓存:', cacheKey)

          return response.data
        }
        else {
          throw new Error(response.message || 'Token费用计算失败')
        }
      },
      (config: ErrorConfig) => {
        setEnhancedError(config)
      },
      (attempt: number) => {
        retryCount.value = attempt
        console.log(`🔄 [Token计算] 第 ${attempt} 次重试`)
      },
    )
  }

  /**
   * 计算Token费用 - 防抖版本
   */
  const calculateTokenCostDebounced = useDebounceFn(async (data: TokenCalculationRequest) => {
    if (isCalculating.value) {
      console.log('⏳ [Token计算] 正在计算中，跳过重复请求')
      return currentTokenCalculation.value
    }

    isCalculating.value = true
    try {
      const result = await calculateTokenCostInternal(data)
      return result
    }
    finally {
      isCalculating.value = false
    }
  }, 500) // 500ms 防抖延迟

  /**
   * 计算Token费用 - 公开接口（防抖版本）
   */
  const calculateTokenCost = async (data: TokenCalculationRequest) => {
    // 对于相同参数的请求，先检查缓存
    const cacheKey = tokenCalculationCacheManager.generateKey(data)
    const cachedResult = tokenCalculationCacheManager.get(cacheKey)

    if (cachedResult) {
      console.log('⚡ [Token计算] 立即返回缓存结果:', cacheKey)
      cacheStats.value.hits++
      currentTokenCalculation.value = cachedResult
      return cachedResult
    }

    // 使用防抖版本
    return await calculateTokenCostDebounced(data)
  }

  /**
   * 清理计算缓存
   */
  const clearCalculationCache = () => {
    tokenCalculationCacheManager.clear()
    cacheStats.value.hits = 0
    cacheStats.value.misses = 0
    console.log('🗑️ [Token计算] 缓存已清理')
  }

  /**
   * 获取缓存统计信息
   */
  const getCacheStats = () => {
    return {
      ...cacheStats.value,
      cacheManager: tokenCalculationCacheManager.getStats(),
    }
  }

  /**
   * 获取使用记录
   */
  const fetchUsageRecords = async (params?: UsageRecordQueryParams) => {
    try {
      setLoading(true)
      clearError()

      const response = await BillingAPI.getUsageRecords(params)
      if (response.success) {
        usageRecords.value = response.data.content
        usageRecordsPagination.value = {
          current: response.data.number + 1,
          pageSize: response.data.size,
          total: response.data.totalElements,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total: number) => `共 ${total} 条`,
        }
      }
      else {
        setError(response.message || '获取使用记录失败')
      }
    }
    catch (err: any) {
      setError(err.message || '获取使用记录失败')
    }
    finally {
      setLoading(false)
    }
  }

  /**
   * 获取充值记录
   */
  const fetchRechargeRecords = async (params?: RechargeRecordQueryParams) => {
    try {
      setLoading(true)
      clearError()

      const response = await BillingAPI.getRechargeRecords(params)
      if (response.success) {
        rechargeRecords.value = response.data.content
        rechargeRecordsPagination.value = {
          current: response.data.number + 1,
          pageSize: response.data.size,
          total: response.data.totalElements,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total: number) => `共 ${total} 条`,
        }
      }
      else {
        setError(response.message || '获取充值记录失败')
      }
    }
    catch (err: any) {
      setError(err.message || '获取充值记录失败')
    }
    finally {
      setLoading(false)
    }
  }

  /**
   * 提交充值申请
   */
  const submitRecharge = async (data: RechargeRequest) => {
    try {
      setLoading(true)
      clearError()

      const response = await BillingAPI.submitRecharge(data)
      if (response.success) {
        message.success('充值申请已提交')
        // 刷新充值记录和账户信息
        await Promise.all([
          fetchRechargeRecords(),
          fetchAccount(),
        ])
        return response.data
      }
      else {
        setError(response.message || '提交充值申请失败')
        return null
      }
    }
    catch (err: any) {
      setError(err.message || '提交充值申请失败')
      return null
    }
    finally {
      setLoading(false)
    }
  }

  /**
   * 获取申诉记录
   */
  const fetchAppealRecords = async (params?: AppealRecordQueryParams) => {
    try {
      setLoading(true)
      clearError()

      const response = await BillingAPI.getAppealRecords(params)
      if (response.success) {
        appealRecords.value = response.data.content
        appealRecordsPagination.value = {
          current: response.data.number + 1,
          pageSize: response.data.size,
          total: response.data.totalElements,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total: number) => `共 ${total} 条`,
        }
      }
      else {
        setError(response.message || '获取申诉记录失败')
      }
    }
    catch (err: any) {
      setError(err.message || '获取申诉记录失败')
    }
    finally {
      setLoading(false)
    }
  }

  /**
   * 提交申诉
   */
  const submitAppeal = async (data: AppealRequest) => {
    try {
      setLoading(true)
      clearError()

      const response = await BillingAPI.submitAppeal(data)
      if (response.success) {
        message.success('申诉已提交')
        // 刷新申诉记录
        await fetchAppealRecords()
        return response.data
      }
      else {
        setError(response.message || '提交申诉失败')
        return null
      }
    }
    catch (err: any) {
      setError(err.message || '提交申诉失败')
      return null
    }
    finally {
      setLoading(false)
    }
  }

  /**
   * 获取统计数据
   */
  const fetchStatistics = async (period = 'month') => {
    try {
      const response = await BillingAPI.getUsageStatistics(period)
      if (response.success) {
        statistics.value = response.data
      }
      else {
        setError(response.message || '获取统计数据失败')
      }
    }
    catch (err: any) {
      setError(err.message || '获取统计数据失败')
    }
  }

  /**
   * 预检查计费
   */
  const precheckBilling = async (inputTokens: number, outputTokens: number) => {
    try {
      const response = await BillingAPI.precheckBilling(inputTokens, outputTokens)
      if (response.success) {
        return response.data
      }
      else {
        setError(response.message || '预检查计费失败')
        return false
      }
    }
    catch (err: any) {
      setError(err.message || '预检查计费失败')
      return false
    }
  }

  /**
   * 检查余额
   */
  const checkBalance = async (amount: number) => {
    try {
      const response = await BillingAPI.checkBalance(amount)
      if (response.success) {
        return response.data
      }
      else {
        setError(response.message || '检查余额失败')
        return null
      }
    }
    catch (err: any) {
      setError(err.message || '检查余额失败')
      return null
    }
  }

  /**
   * 获取支付方式列表
   */
  const fetchPaymentMethods = async () => {
    try {
      const response = await BillingAPI.getPaymentMethods()
      if (response.success) {
        paymentMethods.value = response.data
      }
      else {
        setError(response.message || '获取支付方式失败')
      }
    }
    catch (err: any) {
      setError(err.message || '获取支付方式失败')
    }
  }

  /**
   * 获取预设充值金额
   */
  const fetchRechargePresets = async () => {
    try {
      const response = await BillingAPI.getRechargePresets()
      if (response.success) {
        return response.data
      }
      else {
        setError(response.message || '获取预设充值金额失败')
        return []
      }
    }
    catch (err: any) {
      setError(err.message || '获取预设充值金额失败')
      return []
    }
  }

  /**
   * 取消充值
   */
  const cancelRecharge = async (rechargeId: string) => {
    try {
      setLoading(true)
      clearError()

      const response = await BillingAPI.cancelRecharge(rechargeId)
      if (response.success) {
        message.success('充值已取消')
        // 刷新充值记录
        await fetchRechargeRecords()
        return response.data
      }
      else {
        setError(response.message || '取消充值失败')
        return null
      }
    }
    catch (err: any) {
      setError(err.message || '取消充值失败')
      return null
    }
    finally {
      setLoading(false)
    }
  }

  /**
   * 创建申诉 (别名方法，指向 submitAppeal)
   */
  const createAppeal = async (data: AppealRequest) => {
    return await submitAppeal(data)
  }

  /**
   * 取消申诉
   */
  const cancelAppeal = async (appealId: string) => {
    try {
      setLoading(true)
      clearError()

      const response = await BillingAPI.cancelAppeal(appealId)
      if (response.success) {
        message.success('申诉已取消')
        // 刷新申诉记录
        await fetchAppealRecords()
        return response.data
      }
      else {
        setError(response.message || '取消申诉失败')
        return null
      }
    }
    catch (err: any) {
      setError(err.message || '取消申诉失败')
      return null
    }
    finally {
      setLoading(false)
    }
  }

  /**
   * 下载申诉附件
   */
  const downloadAttachment = async (attachmentId: string) => {
    try {
      const blob = await BillingAPI.downloadAttachment(attachmentId)
      // 处理文件下载
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `attachment_${attachmentId}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      message.success('文件下载成功')
    }
    catch (err: any) {
      setError(err.message || '下载附件失败')
    }
  }

  /**
   * 下载使用记录收据
   */
  const downloadReceipt = async (recordId: string) => {
    try {
      const blob = await BillingAPI.downloadReceipt(recordId)
      // 处理文件下载
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `receipt_${recordId}.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      message.success('收据下载成功')
    }
    catch (err: any) {
      setError(err.message || '下载收据失败')
    }
  }

  /**
   * 手动重试计算
   */
  const retryCalculation = async (data: TokenCalculationRequest) => {
    const errorKey = `calculate-${tokenCalculationCacheManager.generateKey(data)}`
    errorHandler.clearRetryCount(errorKey)
    retryCount.value = 0
    clearError()

    return await calculateTokenCostInternal(data)
  }

  /**
   * 初始化数据
   */
  const initialize = async () => {
    await Promise.all([
      fetchAccount(),
      fetchConfiguration(),
      fetchBalanceWarningConfig(),
    ])
  }

  /**
   * 重置状态
   */
  const reset = () => {
    account.value = null
    config.value = null
    balanceWarningConfig.value = null
    usageRecords.value = []
    rechargeRecords.value = []
    appealRecords.value = []
    statistics.value = null
    currentTokenCalculation.value = null
    error.value = null
    isLoading.value = false

    // 重置分页
    usageRecordsPagination.value = {
      current: 1,
      pageSize: 20,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number) => `共 ${total} 条`,
    }
    rechargeRecordsPagination.value = {
      current: 1,
      pageSize: 20,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number) => `共 ${total} 条`,
    }
    appealRecordsPagination.value = {
      current: 1,
      pageSize: 20,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number) => `共 ${total} 条`,
    }
  }

  return {
    // 状态
    isLoading,
    error,
    account,
    config,
    balanceWarningConfig,
    usageRecords,
    rechargeRecords,
    appealRecords,
    statistics,
    currentTokenCalculation,
    paymentMethods,
    usageRecordsPagination,
    rechargeRecordsPagination,
    appealRecordsPagination,

    // 计算属性
    balance,
    currency,
    accountStatus,
    balanceStatus,
    isBalanceSufficient,
    currentConfig,
    giftBalance,
    rechargedBalance,
    freeTokens,
    remainingFreeTokens,

    // Actions
    setLoading,
    setError,
    clearError,
    fetchAccount,
    refreshBalance,
    manualRefreshBalance,
    refreshBalanceAfterTokenUsage,
    fetchConfiguration,
    fetchBalanceWarningConfig,
    updateBalanceWarningConfig,
    calculateTokenCost,
    fetchUsageRecords,
    fetchRechargeRecords,
    submitRecharge,
    fetchAppealRecords,
    submitAppeal,
    fetchStatistics,
    precheckBilling,
    checkBalance,
    fetchPaymentMethods,
    fetchRechargePresets,
    cancelRecharge,
    createAppeal,
    cancelAppeal,
    downloadAttachment,
    downloadReceipt,
    initialize,
    reset,
    cacheStats,
    getCacheStats,
    clearCalculationCache,
    retryCalculation,

    // 细粒度加载状态
    loadingStates,
    isAnyLoading,
    setLoadingState,
    getLoadingDescription,
  }
})

export default useBillingStore
