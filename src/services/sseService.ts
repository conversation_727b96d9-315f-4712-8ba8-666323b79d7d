import type { SSEEvent, ConnectionStatus } from '@/types/agent'

export interface SSEOptions {
  token?: string
  onEvent?: (event: SSEEvent) => void
  onError?: (error: Error) => void
  onReconnect?: () => void
  onClose?: () => void
  onConnectionStatusChange?: (status: ConnectionStatus) => void
  maxReconnectAttempts?: number
  reconnectInterval?: number
  heartbeatInterval?: number
  exponentialBackoff?: boolean
  maxReconnectInterval?: number
  autoReconnect?: boolean
  reconnectOnVisibilityChange?: boolean
}

export class SseService {
  private eventSource: EventSource | null = null
  private options: SSEOptions = {}
  private connectionStatus: ConnectionStatus = 'disconnected'
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectInterval = 3000
  private maxReconnectInterval = 30000
  private heartbeatInterval = 30000
  private heartbeatTimer: ReturnType<typeof setTimeout> | null = null
  private reconnectTimer: ReturnType<typeof setTimeout> | null = null
  private conversationId: string | null = null
  private baseUrl: string
  private manualDisconnect = false
  private lastConnectTime = 0
  private visibilityChangeHandler: (() => void) | null = null
  private lastEventId: string | null = null

  /**
   * 获取动态baseUrl，与API请求保持一致的逻辑
   */
  private getBaseUrl(): string {
    // 与 src/api/index.ts 中的逻辑保持一致
    const isDev = import.meta.env.DEV
    const openProxy = import.meta.env.VITE_OPEN_PROXY
    const apiBaseUrl = import.meta.env.VITE_APP_API_BASEURL

    if (isDev && openProxy) {
      return '/proxy/api/agent-chat/v1/conversations'
    } else {
      // 生产环境或不使用代理时
      return `${apiBaseUrl}/agent-chat/v1/conversations`
    }
  }

  constructor() {
    // 绑定方法以保持 this 上下文
    this.handleOpen = this.handleOpen.bind(this)
    this.handleMessage = this.handleMessage.bind(this)
    this.handleError = this.handleError.bind(this)
    this.handleClose = this.handleClose.bind(this)

    // 初始化动态baseUrl
    this.baseUrl = this.getBaseUrl()

    // 监听页面可见性变化
    this.setupVisibilityChangeListener()
  }

  /**
   * 连接到 SSE 流
   */
  connect(conversationId: string, options: SSEOptions = {}): void {
    if (this.eventSource?.readyState === EventSource.OPEN) {
      console.warn('SSE connection already exists')
      return
    }

    this.conversationId = conversationId
    this.manualDisconnect = false
    this.options = {
      maxReconnectAttempts: 5,
      reconnectInterval: 3000,
      maxReconnectInterval: 30000,
      heartbeatInterval: 30000,
      exponentialBackoff: true,
      autoReconnect: true,
      reconnectOnVisibilityChange: true,
      ...options
    }

    this.maxReconnectAttempts = this.options.maxReconnectAttempts || 5
    this.reconnectInterval = this.options.reconnectInterval || 3000
    this.maxReconnectInterval = this.options.maxReconnectInterval || 30000
    this.heartbeatInterval = this.options.heartbeatInterval || 30000

    this.createConnection()
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    this.manualDisconnect = true
    this.cleanup()
    this.setConnectionStatus('disconnected')
    this.conversationId = null
    this.reconnectAttempts = 0
    this.lastEventId = null
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus(): ConnectionStatus {
    return this.connectionStatus
  }

  /**
   * 创建 SSE 连接
   */
  private createConnection(): void {
    if (!this.conversationId) {
      console.error('Conversation ID is required for SSE connection')
      return
    }

    try {
      this.setConnectionStatus('connecting')

      // 构建 URL
      const url = new URL(`${this.baseUrl}/${this.conversationId}/stream`, window.location.origin)

      // 添加认证参数
      if (this.options.token) {
        url.searchParams.set('token', this.options.token)
      }

      // 添加lastEventId用于消息恢复
      if (this.lastEventId) {
        url.searchParams.set('lastEventId', this.lastEventId)
      }

      // 添加时间戳防止缓存
      url.searchParams.set('t', Date.now().toString())

      console.log('Creating SSE connection to:', url.toString())

      // 创建 EventSource
      this.eventSource = new EventSource(url.toString())

      // 绑定事件监听器
      this.eventSource.addEventListener('open', this.handleOpen)
      this.eventSource.addEventListener('message', this.handleMessage)
      this.eventSource.addEventListener('error', this.handleError)

      // 监听自定义事件类型
      this.eventSource.addEventListener('execution_started', this.handleCustomEvent)
      this.eventSource.addEventListener('step_started', this.handleCustomEvent)
      this.eventSource.addEventListener('step_progress', this.handleCustomEvent)
      this.eventSource.addEventListener('step_completed', this.handleCustomEvent)
      this.eventSource.addEventListener('step_failed', this.handleCustomEvent)
      this.eventSource.addEventListener('thinking_process', this.handleCustomEvent)
      this.eventSource.addEventListener('execution_completed', this.handleCustomEvent)
      this.eventSource.addEventListener('execution_failed', this.handleCustomEvent)
      this.eventSource.addEventListener('execution_paused', this.handleCustomEvent)
      this.eventSource.addEventListener('execution_resumed', this.handleCustomEvent)
      this.eventSource.addEventListener('connection_status', this.handleCustomEvent)

      // 监听 Agent Chat 事件
      this.eventSource.addEventListener('agent-event', this.handleCustomEvent)

    } catch (error) {
      console.error('Failed to create SSE connection:', error)
      this.handleConnectionError(error as Error)
    }
  }

  /**
   * 处理连接打开事件
   */
  private handleOpen(event: Event): void {
    console.log('SSE connection opened:', event)
    this.lastConnectTime = Date.now()

    const wasReconnecting = this.reconnectAttempts > 0
    this.setConnectionStatus('connected')
    this.reconnectAttempts = 0

    // 启动心跳
    this.startHeartbeat()

    // 通知连接成功
    if (this.options.onReconnect && wasReconnecting) {
      this.options.onReconnect()
    }
  }

  /**
   * 处理消息事件
   */
  private handleMessage(event: MessageEvent): void {
    try {
      // 保存最后接收到的事件ID，用于重连时的消息恢复
      if (event.lastEventId) {
        this.lastEventId = event.lastEventId
      }

      const data = JSON.parse(event.data)
      const sseEvent: SSEEvent = {
        type: 'message' as any,
        data,
        timestamp: new Date().toISOString()
      }

      console.log('SSE message received:', sseEvent)

      if (this.options.onEvent) {
        this.options.onEvent(sseEvent)
      }
    } catch (error) {
      console.error('Failed to parse SSE message:', error)
    }
  }

  /**
   * 处理自定义事件
   */
  private handleCustomEvent = (event: MessageEvent): void => {
    try {
      // 保存最后接收到的事件ID，用于重连时的消息恢复
      if (event.lastEventId) {
        this.lastEventId = event.lastEventId
      }

      const data = event.data ? JSON.parse(event.data) : {}

      // 对于 agent-event，使用 data.eventType 作为事件类型
      const eventType = event.type === 'agent-event' ? data.eventType : event.type

      const sseEvent: SSEEvent = {
        type: eventType as any,
        data: data.data || data, // 如果有嵌套的 data 字段，使用它，否则使用整个 data
        timestamp: data.timestamp || new Date().toISOString(),
        executionId: data.executionId,
        stepId: data.stepId
      }

      console.log('SSE custom event received:', sseEvent)

      if (this.options.onEvent) {
        this.options.onEvent(sseEvent)
      }
    } catch (error) {
      console.error('Failed to parse SSE custom event:', error)
    }
  }

  /**
   * 处理错误事件
   */
  private handleError(event: Event): void {
    console.error('SSE connection error:', event)

    const eventSourceError = event.target as EventSource

    if (eventSourceError.readyState === EventSource.CLOSED) {
      this.handleConnectionError(new Error('SSE connection closed'))
    } else {
      this.handleConnectionError(new Error('SSE connection error'))
    }
  }

  /**
   * 处理连接关闭事件
   */
  private handleClose(event: Event): void {
    console.log('SSE connection closed:', event)
    this.setConnectionStatus('disconnected')

    if (this.options.onClose) {
      this.options.onClose()
    }

    // 尝试重连
    this.attemptReconnect()
  }

  /**
   * 处理连接错误
   */
  private handleConnectionError(error: Error): void {
    console.error('SSE connection error:', error)
    this.setConnectionStatus('error')

    if (this.options.onError) {
      this.options.onError(error)
    }

    // 尝试重连
    this.attemptReconnect()
  }

  /**
   * 尝试重连
   */
  private attemptReconnect(): void {
    if (!this.shouldReconnect()) {
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        console.error('Max reconnect attempts reached')
        this.setConnectionStatus('error')
      }
      return
    }

    this.reconnectAttempts++
    this.setConnectionStatus('reconnecting')

    const delay = this.getReconnectDelay()
    console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms...`)

    // 清理当前连接
    this.cleanup(false)

    // 延迟重连
    this.reconnectTimer = setTimeout(() => {
      if (this.conversationId && this.shouldReconnect()) {
        this.createConnection()
      }
    }, delay)
  }

  /**
   * 启动心跳
   */
  private startHeartbeat(): void {
    this.stopHeartbeat()

    this.heartbeatTimer = setTimeout(() => {
      if (this.connectionStatus === 'connected') {
        // 检查连接状态
        if (this.eventSource?.readyState !== EventSource.OPEN) {
          console.warn('SSE connection lost, attempting reconnect...')
          this.handleConnectionError(new Error('Heartbeat failed'))
        } else {
          // 继续下一次心跳
          this.startHeartbeat()
        }
      }
    }, this.heartbeatInterval)
  }

  /**
   * 停止心跳
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearTimeout(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 清理资源
   */
  private cleanup(clearTimers = true): void {
    // 关闭 EventSource
    if (this.eventSource) {
      this.eventSource.removeEventListener('open', this.handleOpen)
      this.eventSource.removeEventListener('message', this.handleMessage)
      this.eventSource.removeEventListener('error', this.handleError)

      // 移除自定义事件监听器
      this.eventSource.removeEventListener('execution_started', this.handleCustomEvent)
      this.eventSource.removeEventListener('step_started', this.handleCustomEvent)
      this.eventSource.removeEventListener('step_progress', this.handleCustomEvent)
      this.eventSource.removeEventListener('step_completed', this.handleCustomEvent)
      this.eventSource.removeEventListener('step_failed', this.handleCustomEvent)
      this.eventSource.removeEventListener('thinking_process', this.handleCustomEvent)
      this.eventSource.removeEventListener('execution_completed', this.handleCustomEvent)
      this.eventSource.removeEventListener('execution_failed', this.handleCustomEvent)
      this.eventSource.removeEventListener('execution_paused', this.handleCustomEvent)
      this.eventSource.removeEventListener('execution_resumed', this.handleCustomEvent)
      this.eventSource.removeEventListener('connection_status', this.handleCustomEvent)

      // 移除 Agent Chat 事件监听器
      this.eventSource.removeEventListener('agent-event', this.handleCustomEvent)

      this.eventSource.close()
      this.eventSource = null
    }

    if (clearTimers) {
      // 清理定时器
      this.stopHeartbeat()

      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer)
        this.reconnectTimer = null
      }
    }
  }

  /**
   * 发送消息到服务器（如果支持双向通信）
   */
  sendMessage(message: any): void {
    // EventSource 是单向的，如果需要发送消息，需要使用其他方式
    // 比如通过 HTTP POST 请求
    console.warn('SSE is unidirectional, use HTTP API to send messages')
  }

  /**
   * 检查是否已连接
   */
  isConnected(): boolean {
    return this.connectionStatus === 'connected' &&
           this.eventSource?.readyState === EventSource.OPEN
  }

  /**
   * 获取当前对话 ID
   */
  getCurrentConversationId(): string | null {
    return this.conversationId
  }

  /**
   * 获取重连尝试次数
   */
  getReconnectAttempts(): number {
    return this.reconnectAttempts
  }

  /**
   * 获取最后事件ID
   */
  getLastEventId(): string | null {
    return this.lastEventId
  }

  /**
   * 重置重连计数器
   */
  resetReconnectAttempts(): void {
    this.reconnectAttempts = 0
  }

  /**
   * 手动触发重连
   */
  forceReconnect(): void {
    console.log('Force reconnecting...')
    this.reconnectAttempts = 0
    this.cleanup(false)
    if (this.conversationId) {
      this.createConnection()
    }
  }

  /**
   * 设置连接状态并通知监听器
   */
  private setConnectionStatus(status: ConnectionStatus): void {
    if (this.connectionStatus !== status) {
      this.connectionStatus = status
      if (this.options.onConnectionStatusChange) {
        this.options.onConnectionStatusChange(status)
      }
    }
  }

  /**
   * 监听页面可见性变化
   */
  private setupVisibilityChangeListener(): void {
    if (typeof document === 'undefined') return

    this.visibilityChangeHandler = () => {
      if (!document.hidden && this.options.reconnectOnVisibilityChange) {
        // 页面重新可见时，检查连接状态
        if (this.conversationId && !this.isConnected() && this.connectionStatus !== 'connecting') {
          console.log('Page visibility changed, attempting to reconnect...')
          this.forceReconnect()
        }
      }
    }

    document.addEventListener('visibilitychange', this.visibilityChangeHandler)

    // 监听网络状态变化
    if (typeof window !== 'undefined' && 'navigator' in window) {
      window.addEventListener('online', () => {
        if (this.conversationId && !this.isConnected() && this.connectionStatus !== 'connecting') {
          console.log('Network is back online, attempting to reconnect...')
          this.forceReconnect()
        }
      })

      window.addEventListener('offline', () => {
        console.log('Network is offline')
        if (this.eventSource) {
          this.setConnectionStatus('disconnected')
        }
      })
    }
  }

  /**
   * 清理可见性监听器
   */
  private cleanupVisibilityListener(): void {
    if (this.visibilityChangeHandler && typeof document !== 'undefined') {
      document.removeEventListener('visibilitychange', this.visibilityChangeHandler)
      this.visibilityChangeHandler = null
    }
  }

  /**
   * 计算重连延迟（支持指数退避）
   */
  private getReconnectDelay(): number {
    if (!this.options.exponentialBackoff) {
      return this.reconnectInterval
    }

    // 指数退避：基础延迟 * 2^(尝试次数-1)，最大不超过 maxReconnectInterval
    const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1)
    return Math.min(delay, this.maxReconnectInterval)
  }

  /**
   * 检查是否应该重连
   */
  private shouldReconnect(): boolean {
    return !this.manualDisconnect &&
           this.options.autoReconnect !== false &&
           this.reconnectAttempts < this.maxReconnectAttempts &&
           this.conversationId !== null
  }

  /**
   * 销毁SSE服务，清理所有资源
   */
  destroy(): void {
    this.disconnect()
    this.cleanupVisibilityListener()
  }
}

// 创建单例实例
export const sseService = new SseService()

// 默认导出
export default SseService
