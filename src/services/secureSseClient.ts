/**
 * 安全的 SSE 客户端实现
 * 使用 fetch + ReadableStream 替代 EventSource，支持 Authorization 头
 */

export type SecureSseConnectionState = 'DISCONNECTED' | 'CONNECTING' | 'OPEN' | 'CLOSED'

export interface SecureSseEvent {
  type: string
  data: string
  id?: string
  retry?: number
}

export interface SecureSseOptions {
  headers?: Record<string, string>
  timeout?: number
  retryInterval?: number
  maxRetries?: number
}

export interface SecureSseEventHandlers {
  onOpen?: () => void
  onMessage?: (event: SecureSseEvent) => void
  onError?: (error: Error) => void
  onClose?: () => void
}

export class SecureSseClient {
  private url: string
  private options: SecureSseOptions
  private handlers: SecureSseEventHandlers
  private abortController: AbortController | null = null
  private readyState: SecureSseConnectionState = 'DISCONNECTED'
  private retryCount = 0
  private retryTimer: number | null = null
  private isManualClose = false

  constructor(url: string, options: SecureSseOptions = {}, handlers: SecureSseEventHandlers = {}) {
    this.url = url
    this.options = {
      timeout: 30000,
      retryInterval: 3000,
      maxRetries: 5,
      ...options
    }
    this.handlers = handlers
  }

  /**
   * 建立连接
   */
  connect(): void {
    if (this.readyState === 'CONNECTING' || this.readyState === 'OPEN') {
      return
    }

    this.readyState = 'CONNECTING'
    this.isManualClose = false
    this.abortController = new AbortController()

    // 准备请求头
    const headers: Record<string, string> = {
      'Accept': 'text/event-stream',
      'Cache-Control': 'no-cache',
      ...this.options.headers
    }

    //console.log('SecureSseClient: Connecting to', this.url)

    fetch(this.url, {
      method: 'GET',
      headers,
      signal: this.abortController.signal,
      // 不使用缓存
      cache: 'no-cache',
      // 启用流式处理
      keepalive: false
    })
      .then(response => {
        if (!response.ok) {
          throw new Error(`SSE connection failed: ${response.status} ${response.statusText}`)
        }

        if (!response.body) {
          throw new Error('Response body is null')
        }

        this.readyState = 'OPEN'
        this.retryCount = 0
        this.handlers.onOpen?.()

        return this.processStream(response.body)
      })
      .catch(error => {
        this.handleError(error)
      })
  }

  /**
   * 处理 ReadableStream
   */
  private async processStream(stream: ReadableStream<Uint8Array>): Promise<void> {
    const reader = stream.getReader()
    const decoder = new TextDecoder()
    let buffer = ''

    try {
      while (true) {
        const { done, value } = await reader.read()

        if (done) {
          //console.log('SecureSseClient: Stream ended')
          break
        }

        // 解码数据
        buffer += decoder.decode(value, { stream: true })

        // 处理完整的事件
        const events = this.parseEvents(buffer)
        for (const event of events.parsed) {
          this.handleEvent(event)
        }
        buffer = events.remaining
      }
    } catch (error) {
      if (!this.isManualClose) {
        this.handleError(error as Error)
      }
    } finally {
      reader.releaseLock()
    }
  }

  /**
   * 解析 SSE 事件
   */
  private parseEvents(buffer: string): { parsed: SecureSseEvent[], remaining: string } {
    const events: SecureSseEvent[] = []
    const lines = buffer.split('\n')
    let i = 0

    while (i < lines.length) {
      // 找到事件的结束位置（空行）
      let eventEnd = i
      while (eventEnd < lines.length && lines[eventEnd].trim() !== '') {
        eventEnd++
      }

      // 如果没有找到完整的事件，保留剩余内容
      if (eventEnd === lines.length && !buffer.endsWith('\n\n')) {
        break
      }

      // 解析单个事件
      const eventLines = lines.slice(i, eventEnd)
      const event = this.parseEvent(eventLines)
      if (event) {
        events.push(event)
      }

      i = eventEnd + 1 // 跳过空行
    }

    // 返回剩余未解析的内容
    const remaining = lines.slice(i).join('\n')
    return { parsed: events, remaining }
  }

  /**
   * 解析单个 SSE 事件
   */
  private parseEvent(lines: string[]): SecureSseEvent | null {
    let type = 'message'
    let data: string[] = []
    let id: string | undefined
    let retry: number | undefined

    for (const line of lines) {
      const colonIndex = line.indexOf(':')
      if (colonIndex === -1) continue

      const field = line.substring(0, colonIndex).trim()
      const value = line.substring(colonIndex + 1).trim()

      switch (field) {
        case 'event':
          type = value
          break
        case 'data':
          data.push(value)
          break
        case 'id':
          id = value
          break
        case 'retry':
          const retryValue = parseInt(value, 10)
          if (!isNaN(retryValue)) {
            retry = retryValue
          }
          break
      }
    }

    // 只有有数据的事件才返回
    if (data.length === 0) {
      return null
    }

    return {
      type,
      data: data.join('\n'),
      id,
      retry
    }
  }

  /**
   * 处理事件
   */
  private handleEvent(event: SecureSseEvent): void {
    // 更新重试间隔
    if (event.retry && this.options.retryInterval) {
      this.options.retryInterval = event.retry
    }

    this.handlers.onMessage?.(event)
  }

  /**
   * 处理错误
   */
  private handleError(error: Error): void {
    console.error('SecureSseClient: Error', error)

    this.readyState = 'CLOSED'
    this.handlers.onError?.(error)

    // 如果不是手动关闭，尝试重连
    if (!this.isManualClose && this.retryCount < (this.options.maxRetries || 5)) {
      this.scheduleReconnect()
    }
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    this.retryCount++
    const delay = this.options.retryInterval || 3000

    //console.log(`SecureSseClient: Scheduling reconnect in ${delay}ms (attempt ${this.retryCount})`)

    this.retryTimer = window.setTimeout(() => {
      this.retryTimer = null
      this.connect()
    }, delay)
  }

  /**
   * 关闭连接
   */
  close(): void {
    //console.log('SecureSseClient: Closing connection')

    this.isManualClose = true
    this.readyState = 'CLOSED'

    // 取消请求
    if (this.abortController) {
      this.abortController.abort()
      this.abortController = null
    }

    // 清除重连定时器
    if (this.retryTimer) {
      clearTimeout(this.retryTimer)
      this.retryTimer = null
    }

    this.handlers.onClose?.()
  }

  /**
   * 获取当前状态
   */
  get state(): SecureSseConnectionState {
    return this.readyState
  }

  /**
   * 检查是否已连接
   */
  get isConnected(): boolean {
    return this.readyState === 'OPEN'
  }

  /**
   * 获取重试次数
   */
  get retryAttempts(): number {
    return this.retryCount
  }
}
