<route lang="yaml">
meta:
  title: 租户管理
  icon: i-ep:office-building
</route>

<script setup lang="ts">
import type { TagProps } from 'element-plus'
import api from '@/api/modules/sys_tenant'
import eventBus from '@/utils/eventBus'
import { getIndustryTypeText, getTenantTypeText, INDUSTRY_TYPE_OPTIONS, TENANT_TYPE_OPTIONS } from '@/utils/tenant-constants'
import dayjs from 'dayjs'
import { ElMessage, ElMessageBox } from 'element-plus'
import FormMode from './components/FormMode/index.vue'
import TenantConfigModal from './components/TenantConfigModal/index.vue'

defineOptions({
  name: 'SysTenantList',
})

const { pagination, getParams, onSortChange } = usePagination()

// 表格是否自适应高度
const tableAutoHeight = ref(false)

/**
 * 详情展示模式
 * dialog 对话框
 * drawer 抽屉
 */
const formMode = ref<'dialog' | 'drawer'>('dialog')

// 详情
const formModeProps = ref({
  visible: false,
  id: '',
})

// 配置弹窗
const configModalProps = ref({
  visible: false,
  tenantId: 0,
  tenantName: '',
})

// 搜索
const searchDefault = {
  tenantCode: '',
  tenantName: '',
  status: undefined as number | undefined,
  contactName: '',
  contactPhone: '',
  contactEmail: '',
  industryType: '',
  tenantType: '',
}
const search = ref({ ...searchDefault })

function searchReset() {
  Object.assign(search.value, searchDefault)
}

// 批量操作
const batch = ref({
  enable: true,
  selectionDataList: [],
})

// 列表
const loading = ref(false)
const dataList = ref([])

// 状态选项
const statusOptions = [
  { label: '启用', value: 1, type: 'success' },
  { label: '禁用', value: 0, type: 'danger' },
  { label: '过期', value: 2, type: 'warning' },
]

onMounted(() => {
  getDataList()
})

function getDataList() {
  loading.value = true
  const params = {
    ...getParams(),
    ...search.value,
  }
  api.getTenantList(params).then((res: any) => {
    loading.value = false
    dataList.value = res.data.content
    pagination.value.total = res.data.totalElements
  })
}

// 每页数量切换
function sizeChange(_size: number) {
  getDataList()
}

// 当前页码切换（翻页）
function currentChange(_page = 1) {
  getDataList()
}

// 字段排序
function sortChange({ prop, order }: { prop: string, order: string }) {
  onSortChange(prop, order).then(() => getDataList())
}

function onCreate() {
  formModeProps.value.id = ''
  formModeProps.value.visible = true
}

function onViewConfig(row: any) {
  configModalProps.value.tenantId = row.id
  configModalProps.value.tenantName = row.tenantName
  configModalProps.value.visible = true
}

function onEdit(row: any) {
  formModeProps.value.id = row.id
  formModeProps.value.visible = true
}

function onDel(row: any) {
  ElMessageBox.confirm(`确认删除租户「${row.tenantName}」吗？`, '确认信息').then(() => {
    api.deleteTenant(row.id).then(() => {
      getDataList()
      ElMessage.success('删除成功')
    })
  }).catch(() => {})
}

// 批量删除
function onBatchDelete() {
  if (!batch.value.selectionDataList.length) {
    ElMessage.warning('请选择要删除的租户')
    return
  }

  ElMessageBox.confirm(`确认删除选中的 ${batch.value.selectionDataList.length} 个租户吗？`, '确认信息').then(() => {
    const ids = batch.value.selectionDataList.map((item: any) => item.id)
    api.deleteTenants(ids).then(() => {
      getDataList()
      ElMessage.success('批量删除成功')
    })
  }).catch(() => {})
}

// 切换租户状态
function toggleStatus(row: any) {
  const newStatus = row.status === 1 ? 0 : 1
  const statusText = newStatus === 1 ? '启用' : '禁用'

  ElMessageBox.confirm(`确认${statusText}租户「${row.tenantName}」吗？`, '确认信息').then(() => {
    api.updateTenantStatus(row.id, newStatus).then(() => {
      row.status = newStatus
      ElMessage.success(`${statusText}成功`)
    })
  }).catch(() => {})
}

// 获取状态标签类型
function getStatusType(status: number) {
  const option = statusOptions.find(item => item.value === status)
  return (option?.type || 'info') as 'success' | 'danger' | 'warning' | 'primary' | 'info'
}

// 获取状态标签文本
function getStatusText(status: number) {
  const option = statusOptions.find(item => item.value === status)
  return option?.label || '未知'
}

// 检查是否过期
function isExpired(expireTime: string): boolean {
  if (!expireTime) {
    return false
  }
  return dayjs(expireTime).isBefore(dayjs())
}

// 计算剩余天数
function getRemainingDays(expireTime: string): number {
  if (!expireTime) {
    return -1
  }
  const diff = dayjs(expireTime).diff(dayjs(), 'day')
  return diff > 0 ? diff : 0
}

// 获取操作菜单项
function getActionItems(row: any) {
  return [
    [
      {
        label: '编辑',
        icon: 'i-ep:edit',
        handle: () => onEdit(row),
      },
      {
        label: '查看配置',
        icon: 'i-ep:setting',
        handle: () => onViewConfig(row),
      },
      {
        label: row.status === 1 ? '禁用' : '启用',
        icon: row.status === 1 ? 'i-ep:lock' : 'i-ep:unlock',
        handle: () => toggleStatus(row),
      },
    ],
    [
      {
        label: '删除',
        icon: 'i-ep:delete',
        handle: () => onDel(row),
      },
    ],
  ]
}

// 刷新租户缓存
const refreshCacheLoading = ref(false)
async function refreshTenantCache() {
  refreshCacheLoading.value = true
  try {
    const result = await api.refreshTenantCache()
    ElMessage.success(result.data || '租户缓存刷新成功')
    // 刷新缓存后重新加载列表
    getDataList()
  } catch (error: any) {
    console.error('刷新租户缓存失败:', error)
    ElMessage.error(error.response?.data?.message || '刷新租户缓存失败')
  } finally {
    refreshCacheLoading.value = false
  }
}

// 查看缓存状态
async function showCacheStatus() {
  try {
    const result = await api.getTenantCacheStatus()
    const status = result.data
    const statusText = `缓存状态: ${status.initialized ? '已初始化' : '未初始化'}
缓存数量: ${status.cacheSize}
详细信息: ${status.message}`

    ElMessageBox.alert(statusText, '租户缓存状态', {
      confirmButtonText: '确定',
      type: 'info'
    })
  } catch (error: any) {
    console.error('获取缓存状态失败:', error)
    ElMessage.error(error.response?.data?.message || '获取缓存状态失败')
  }
}
</script>

<template>
  <div :class="{ 'absolute-container': tableAutoHeight }">
    <FaPageHeader title="租户管理" class="mb-0" />
    <FaPageMain :class="{ 'flex-1 overflow-auto': tableAutoHeight }" :main-class="{ 'flex-1 flex flex-col overflow-auto': tableAutoHeight }">
      <FaSearchBar :show-toggle="true">
        <template #default="{ fold, toggle }">
          <ElForm :model="search" size="default" label-width="100px" inline-message inline class="search-form">
            <ElFormItem label="租户编码">
              <ElInput v-model="search.tenantCode" placeholder="请输入租户编码" clearable @keydown.enter="currentChange()" @clear="currentChange()" />
            </ElFormItem>
            <ElFormItem label="租户名称">
              <ElInput v-model="search.tenantName" placeholder="请输入租户名称" clearable @keydown.enter="currentChange()" @clear="currentChange()" />
            </ElFormItem>
            <ElFormItem label="状态">
              <ElSelect v-model="search.status" placeholder="请选择状态" clearable>
                <ElOption v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
              </ElSelect>
            </ElFormItem>
            <ElFormItem v-show="!fold" label="联系人">
              <ElInput v-model="search.contactName" placeholder="请输入联系人" clearable @keydown.enter="currentChange()" @clear="currentChange()" />
            </ElFormItem>
            <ElFormItem v-show="!fold" label="联系电话">
              <ElInput v-model="search.contactPhone" placeholder="请输入联系电话" clearable @keydown.enter="currentChange()" @clear="currentChange()" />
            </ElFormItem>
            <ElFormItem v-show="!fold" label="联系邮箱">
              <ElInput v-model="search.contactEmail" placeholder="请输入联系邮箱" clearable @keydown.enter="currentChange()" @clear="currentChange()" />
            </ElFormItem>
            <ElFormItem v-show="!fold" label="行业类型">
              <ElSelect v-model="search.industryType" placeholder="请选择行业类型" clearable>
                <ElOption v-for="item in INDUSTRY_TYPE_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
              </ElSelect>
            </ElFormItem>
            <ElFormItem v-show="!fold" label="租户类型">
              <ElSelect v-model="search.tenantType" placeholder="请选择租户类型" clearable>
                <ElOption v-for="item in TENANT_TYPE_OPTIONS" :key="item.value" :label="item.label" :value="item.value" />
              </ElSelect>
            </ElFormItem>
            <ElFormItem>
              <ElButton @click="searchReset(); currentChange()">
                重置
              </ElButton>
              <ElButton type="primary" @click="currentChange()">
                <template #icon>
                  <FaIcon name="i-ep:search" />
                </template>
                筛选
              </ElButton>
              <ElButton link @click="toggle">
                <template #icon>
                  <FaIcon :name="fold ? 'i-ep:caret-bottom' : 'i-ep:caret-top' " />
                </template>
                {{ fold ? '展开' : '收起' }}
              </ElButton>
            </ElFormItem>
          </ElForm>
        </template>
      </FaSearchBar>
      <ElDivider border-style="dashed" />
      <ElSpace wrap>
        <ElButton type="primary" size="default" @click="onCreate">
          <template #icon>
            <FaIcon name="i-ep:plus" />
          </template>
          新增租户
        </ElButton>
        <ElButton v-if="batch.enable" type="danger" size="default" :disabled="!batch.selectionDataList.length" @click="onBatchDelete">
          <template #icon>
            <FaIcon name="i-ep:delete" />
          </template>
          批量删除
        </ElButton>
        <ElButton size="default" @click="getDataList">
          <template #icon>
            <FaIcon name="i-ep:refresh" />
          </template>
          刷新
        </ElButton>
        <ElButton size="default" :loading="refreshCacheLoading" @click="refreshTenantCache">
          <template #icon>
            <FaIcon name="i-ep:cpu" />
          </template>
          刷新缓存
        </ElButton>
        <ElButton size="default" @click="showCacheStatus">
          <template #icon>
            <FaIcon name="i-ep:monitor" />
          </template>
          缓存状态
        </ElButton>
      </ElSpace>
      <ElTable v-loading="loading" class="my-4" :data="dataList" stripe highlight-current-row border height="100%" @sort-change="sortChange" @selection-change="batch.selectionDataList = $event">
        <ElTableColumn v-if="batch.enable" type="selection" align="center" fixed />
        <ElTableColumn prop="id" label="ID" width="80" sortable />
        <ElTableColumn prop="industryType" label="行业类型" width="100" align="center">
          <template #default="{ row }">
            <ElTag v-if="row.industryType" type="primary" size="small">
              {{ getIndustryTypeText(row.industryType) }}
            </ElTag>
            <span v-else>-</span>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="tenantType" label="租户类型" width="100" align="center">
          <template #default="{ row }">
            <ElTag v-if="row.tenantType" type="warning" size="small">
              {{ getTenantTypeText(row.tenantType) }}
            </ElTag>
            <span v-else>-</span>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="tenantCode" label="租户编码" width="120" sortable />
        <ElTableColumn prop="encrypt" label="唯一码" width="120" align="center">
          <template #default="{ row }">
            <ElTag v-if="row.encrypt" type="info" size="small" style="font-family: monospace;">
              {{ row.encrypt }}
            </ElTag>
            <span v-else class="text-gray-400">-</span>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="tenantName" label="租户名称" min-width="150" sortable />
        <!-- <ElTableColumn prop="domain" label="域名" width="180" /> -->
        <ElTableColumn prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <ElTag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="contactName" label="联系人" width="120" />
        <ElTableColumn prop="contactPhone" label="联系电话" width="130" />
        <ElTableColumn prop="contactEmail" label="联系邮箱" width="180" />
        <ElTableColumn prop="createdTime" label="创建时间" width="180" sortable>
          <template #default="{ row }">
            {{ dayjs(row.createdTime).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </ElTableColumn>
        <ElTableColumn label="操作" width="70" align="center" fixed="right">
          <template #default="scope">
            <FaDropdown
              :items="getActionItems(scope.row)"
              align="center"
              side="bottom"
            >
              <ElButton type="primary" size="small" plain>
                <template #icon>
                  <FaIcon name="i-ep:more" />
                </template>
              </ElButton>
            </FaDropdown>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="expireTime" label="到期时间" width="180" sortable>
          <template #default="{ row }">
            <div v-if="row.expireTime">
              <div>{{ dayjs(row.expireTime).format('YYYY-MM-DD') }}</div>
              <div v-if="!isExpired(row.expireTime)" class="text-xs text-gray-500">
                剩余{{ getRemainingDays(row.expireTime) }}天
              </div>
              <div v-else class="text-xs text-red-500">
                已过期
              </div>
            </div>
            <div v-else>
              -
            </div>
          </template>
        </ElTableColumn>
      </ElTable>
      <ElPagination v-model:current-page="pagination.page" v-model:page-size="pagination.size" :total="pagination.total" :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
    </FaPageMain>
    <FormMode v-if="formMode === 'dialog' || formMode === 'drawer'" :id="formModeProps.id" v-model="formModeProps.visible" :mode="formMode" @success="getDataList" />
    <TenantConfigModal v-model:visible="configModalProps.visible" :tenant-id="configModalProps.tenantId" :tenant-name="configModalProps.tenantName" />
  </div>
</template>

<style scoped>
.absolute-container {
  position: absolute;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}
</style>
