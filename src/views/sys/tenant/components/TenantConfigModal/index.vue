<script setup lang="ts">
import type { TenantConfigItem } from '@/api/modules/sys_tenantConfig'
import api from '@/api/modules/sys_tenantConfig'
import dayjs from 'dayjs'
import { ElButton, ElDialog, ElMessage, ElTag } from 'element-plus'
import { computed, ref, watch } from 'vue'

defineOptions({
  name: 'TenantConfigModal',
})

const props = defineProps<Props>()

const emit = defineEmits<Emits>()

interface Props {
  visible: boolean
  tenantId: number
  tenantName: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

// 数据
const loading = ref(false)
const configList = ref<(TenantConfigItem & { expanded?: boolean })[]>([])

// 计算属性 - 本地visible状态
const localVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value),
})

// 计算属性 - 按配置类型分组
const configGroups = computed(() => {
  const groups = configList.value.reduce((acc, config) => {
    const type = config.configType
    if (!acc[type]) {
      acc[type] = []
    }
    acc[type].push(config)
    return acc
  }, {} as Record<string, typeof configList.value>)

  // 按优先级排序返回
  const typeOrder = ['system', 'business', 'ui']
  return typeOrder
    .filter(type => groups[type] && groups[type].length > 0)
    .map(type => ({
      type,
      configs: groups[type],
    }))
})

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal && props.tenantId) {
    fetchConfigs()
  }
})

// 获取配置数据
async function fetchConfigs() {
  loading.value = true
  try {
    const response = await api.getTenantConfigs(props.tenantId)
    configList.value = response.data.map((config: TenantConfigItem) => ({
      ...config,
      expanded: false,
    }))
  }
  catch (error) {
    console.error('获取租户配置失败:', error)
    ElMessage.error('获取租户配置失败')
    configList.value = []
  }
  finally {
    loading.value = false
  }
}

// 获取配置类型文本
function getTypeText(type: string): string {
  const typeMap: Record<string, string> = {
    system: '系统配置',
    business: '业务配置',
    ui: '界面配置',
  }
  return typeMap[type] || type
}

// 获取配置类型颜色
function getTypeColor(type: string): 'success' | 'info' | 'warning' | 'danger' | 'primary' {
  const colorMap: Record<string, 'success' | 'info' | 'warning' | 'danger' | 'primary'> = {
    system: 'danger',
    business: 'primary',
    ui: 'success',
  }
  return colorMap[type] || 'info'
}

// 切换展开状态
function toggleExpand(config: TenantConfigItem & { expanded?: boolean }) {
  config.expanded = !config.expanded
}

// 格式化时间
function formatTime(time: string): string {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

// 关闭弹窗
function handleClose() {
  emit('update:visible', false)
}
</script>

<template>
  <ElDialog v-model="localVisible" :title="`查看租户配置 - ${tenantName}`" width="800px" :close-on-click-modal="false">
    <div v-loading="loading">
      <div v-if="!loading && configGroups.length === 0" class="py-8 text-center text-gray-500">
        暂无配置数据
      </div>
      <div v-else class="space-y-6">
        <div v-for="group in configGroups" :key="group.type" class="border rounded-lg p-4">
          <h3 class="mb-4 flex items-center text-lg font-semibold">
            <ElTag :type="getTypeColor(group.type)" class="mr-2">
              {{ getTypeText(group.type) }}
            </ElTag>
            <span>共 {{ group.configs.length }} 项配置</span>
          </h3>
          <div class="grid grid-cols-1 gap-4">
            <div v-for="config in group.configs" :key="config.id" class="border border-gray-200 rounded bg-gray-50 p-3">
              <div class="mb-2 flex items-start justify-between">
                <div class="text-gray-800 font-medium">
                  {{ config.configKey }}
                </div>
                <div class="text-xs text-gray-500">
                  ID: {{ config.id }}
                </div>
              </div>
              <div class="mb-2">
                <div class="mb-1 text-sm text-gray-600">
                  配置值：
                </div>
                <div v-if="config.configValue" class="border rounded bg-white p-2 text-sm">
                  <div v-if="config.configValue.length <= 100">
                    {{ config.configValue }}
                  </div>
                  <div v-else>
                    <div v-if="!config.expanded">
                      {{ config.configValue.substring(0, 100) }}...
                      <ElButton type="primary" link size="small" @click="toggleExpand(config)">
                        展开
                      </ElButton>
                    </div>
                    <div v-else>
                      {{ config.configValue }}
                      <ElButton type="primary" link size="small" @click="toggleExpand(config)">
                        收起
                      </ElButton>
                    </div>
                  </div>
                </div>
                <div v-else class="text-sm text-gray-400 italic">
                  -
                </div>
              </div>
              <div v-if="config.description" class="text-xs text-gray-500">
                描述：{{ config.description }}
              </div>
              <div class="mt-2 text-xs text-gray-400">
                创建时间：{{ formatTime(config.createdTime) }} | 更新时间：{{ formatTime(config.updatedTime) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="flex justify-end">
        <ElButton @click="handleClose">
          关闭
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.gap-4 {
  gap: 1rem;
}
</style>
