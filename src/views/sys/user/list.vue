<route lang="yaml">
meta:
  title: 用户管理
  icon: i-ep:user
</route>

<script setup lang="ts">
import type { TagProps } from 'element-plus'
import api from '@/api/modules/sys_user'
import eventBus from '@/utils/eventBus'
import dayjs from 'dayjs'
import { ElMessage, ElMessageBox } from 'element-plus'
import { h } from 'vue'
import FormMode from './components/FormMode/index.vue'

defineOptions({
  name: 'SysUserList',
})

const router = useRouter()
const { pagination, getParams, onSortChange } = usePagination()

// 表格是否自适应高度
const tableAutoHeight = ref(false)

/**
 * 详情展示模式
 * router 路由跳转
 * dialog 对话框
 * drawer 抽屉
 */
const formMode = ref<'router' | 'dialog' | 'drawer'>('dialog')

// 详情
const formModeProps = ref({
  visible: false,
  id: '',
})

// 搜索
const searchDefault = {
  username: '',
  email: '',
  phone: '',
  realName: '',
  status: '',
  userType: '',
  tenantName: '',
  tenantCode: '',
}
const search = ref({ ...searchDefault })

function searchReset() {
  pagination.value.page = 1
  Object.assign(search.value, searchDefault)
}

// 批量操作
const batch = ref({
  enable: true,
  selectionDataList: [],
})

// 列表
const loading = ref(false)
const dataList = ref([])

// 免登录链接状态
const noLoginLinks = ref(new Map())

// 状态选项
const statusOptions = [
  { label: '启用', value: 1, type: 'success' },
  { label: '禁用', value: 0, type: 'danger' },
  { label: '锁定', value: 2, type: 'warning' },
]

// 用户类型选项
const userTypeOptions = [
  { label: '管理员', value: 'admin', type: 'danger' },
  { label: '普通用户', value: 'normal', type: 'primary' },
  { label: '访客', value: 'guest', type: 'info' },
]

// 性别选项
const genderOptions = [
  { label: '未知', value: 0 },
  { label: '男', value: 1 },
  { label: '女', value: 2 },
]

onMounted(() => {
  getDataList()
  if (formMode.value === 'router') {
    eventBus.on('get-data-list', () => {
      getDataList()
    })
  }
})

onBeforeUnmount(() => {
  if (formMode.value === 'router') {
    eventBus.off('get-data-list')
  }
})

function getDataList() {
  loading.value = true
  const params = {
    ...getParams(),
    ...search.value,
  }
  api.list(params).then((res: any) => {
    loading.value = false
    dataList.value = res.data.content // Spring Data JPA 分页结构
    pagination.value.total = res.data.totalElements // Spring Data JPA 总数字段
  })
}

// 每页数量切换
function sizeChange(_size: number) {
  // v-model 会自动更新 pagination.size，这里直接重新加载数据
  getDataList()
}

// 当前页码切换（翻页）
function currentChange(_page = 1) {
  // v-model 会自动更新 pagination.page，这里直接重新加载数据
  getDataList()
}

// 字段排序
function sortChange({ prop, order }: { prop: string, order: string }) {
  onSortChange(prop, order).then(() => getDataList())
}

function onCreate() {
  if (formMode.value === 'router') {
    router.push({
      name: 'SysUserDetail',
    })
  }
  else {
    formModeProps.value.id = ''
    formModeProps.value.visible = true
  }
}

function onEdit(row: any) {
  if (formMode.value === 'router') {
    router.push({
      name: 'SysUserDetail',
      params: {
        id: row.id,
      },
    })
  }
  else {
    formModeProps.value.id = row.id
    formModeProps.value.visible = true
  }
}

function onDel(row: any) {
  ElMessageBox.confirm(`确认删除用户「${row.username}」吗？`, '确认信息').then(() => {
    api.delete(row.id).then(() => {
      getDataList()
      ElMessage.success('删除成功')
    })
  }).catch(() => {})
}

// 批量删除
function onBatchDelete() {
  if (!batch.value.selectionDataList.length) {
    ElMessage.warning('请选择要删除的用户')
    return
  }

  ElMessageBox.confirm(`确认删除选中的 ${batch.value.selectionDataList.length} 个用户吗？`, '确认信息').then(() => {
    const ids = batch.value.selectionDataList.map((item: any) => item.id)
    api.batchDelete(ids).then(() => {
      getDataList()
      ElMessage.success('批量删除成功')
    })
  }).catch(() => {})
}

// 批量生成免登录链接
function onBatchGenerateNoLoginLinks() {
  if (!batch.value.selectionDataList.length) {
    ElMessage.warning('请选择要生成免登录链接的用户')
    return
  }

  ElMessageBox.confirm(`确认为选中的 ${batch.value.selectionDataList.length} 个用户生成免登录链接吗？`, '确认信息').then(() => {
    const promises = batch.value.selectionDataList.map((user: any) => {
            return api.generateNoLoginLink(user.id).then((res: any) => {
        console.log(`🔗 [批量生成] 用户 ${user.username} API响应:`, res)

        // 验证响应数据结构
        let linkPath
        if (res.data && res.data.data) {
          // 标准API响应格式: { status: 1, data: '/auto-login?token=...', message: null }
          linkPath = res.data.data
        } else if (res.data && res.data.message) {
          // 实际后端返回格式: { status: 1, message: '/auto-login?token=...', data: null }
          linkPath = res.data.message
        } else if (res.data && typeof res.data === 'string') {
          // 直接返回字符串格式
          linkPath = res.data
        } else {
          throw new Error(`用户 ${user.username} 的链接数据格式错误`)
        }

        if (!linkPath) {
          throw new Error(`用户 ${user.username} 的链接数据为空`)
        }

        const fullLink = `${window.location.origin}${linkPath}`

        console.log(`🔗 [批量生成] 用户 ${user.username} 完整链接:`, fullLink)

        noLoginLinks.value.set(user.id, {
          link: fullLink,
          createdAt: new Date(),
        })
        return { user: user.username, link: fullLink }
      })
    })

    Promise.all(promises).then((results) => {
      let allLinks = results.map(r => `${r.user}: ${r.link}`).join('\n')
      navigator.clipboard.writeText(allLinks).then(() => {
        ElMessage.success(`成功生成 ${results.length} 个免登录链接并复制到剪贴板`)
      }).catch(() => {
        ElMessageBox.alert(allLinks, '批量免登录链接', {
          confirmButtonText: '关闭',
          type: 'info',
        })
      })
    }).catch((error: any) => {
      ElMessage.error('批量生成免登录链接失败: ' + (error.response?.data?.message || error.message))
    })
  }).catch(() => {})
}

// 切换用户状态
function toggleStatus(row: any) {
  const newStatus = row.status === 1 ? 0 : 1
  const statusText = newStatus === 1 ? '启用' : '禁用'

  ElMessageBox.confirm(`确认${statusText}用户「${row.username}」吗？`, '确认信息').then(() => {
    api.updateStatus(row.id, newStatus).then(() => {
      row.status = newStatus
      ElMessage.success(`${statusText}成功`)
    })
  }).catch(() => {})
}

// 重置密码
function resetPassword(row: any) {
  ElMessageBox.confirm(`确认重置用户「${row.username}」的密码吗？`, '确认信息').then(() => {
    api.resetPassword(row.id).then(() => {
      ElMessage.success('密码重置成功，新密码已发送至用户邮箱')
    })
  }).catch(() => {})
}

// 生成免登录链接
function generateNoLoginLink(row: any) {
    api.generateNoLoginLink(row.id).then((res: any) => {
    console.log('🔗 [免登录链接] API响应:', res)

    // 验证响应数据结构
    let linkPath
    if (res.data && res.data.data) {
      // 标准API响应格式: { status: 1, data: '/auto-login?token=...', message: null }
      linkPath = res.data.data
    } else if (res.data && res.data.message) {
      // 实际后端返回格式: { status: 1, message: '/auto-login?token=...', data: null }
      linkPath = res.data.message
    } else if (res.data && typeof res.data === 'string') {
      // 直接返回字符串格式
      linkPath = res.data
    } else {
      throw new Error('服务器返回的链接数据格式错误')
    }

    if (!linkPath) {
      throw new Error('服务器返回的链接数据为空')
    }

    const fullLink = `${window.location.origin}${linkPath}`

    console.log('🔗 [免登录链接] 生成的完整链接:', fullLink)

    // 保存到本地状态
    noLoginLinks.value.set(row.id, {
      link: fullLink,
      createdAt: new Date(),
    })

    // 复制到剪贴板
    navigator.clipboard.writeText(fullLink).then(() => {
      ElMessage.success('免登录链接已复制到剪贴板')
    }).catch(() => {
      // 降级方案：显示链接让用户手动复制
      ElMessageBox.alert(fullLink, '免登录链接', {
        confirmButtonText: '关闭',
        type: 'info',
      })
    })
  }).catch((error: any) => {
    console.error('❌ [免登录链接] 生成失败:', error)
    ElMessage.error('生成免登录链接失败: ' + (error.response?.data?.message || error.message))
  })
}

// 预览免登录链接
function previewNoLoginLink(row: any) {
  const existingLink = noLoginLinks.value.get(row.id)
  if (existingLink) {
    console.log('🔗 [预览链接] 使用已存在的链接:', existingLink.link)
    // 验证链接有效性
    if (existingLink.link && existingLink.link !== 'undefined' && !existingLink.link.includes('undefined')) {
      window.open(existingLink.link, '_blank')
    } else {
      ElMessage.error('免登录链接无效，请重新生成')
      // 清除无效链接
      noLoginLinks.value.delete(row.id)
    }
  } else {
    console.log('🔗 [预览链接] 生成新链接')
    // 生成新链接
        api.generateNoLoginLink(row.id).then((res: any) => {
      console.log('🔗 [预览链接] API响应:', res)

      // 验证响应数据结构
      let linkPath
      if (res.data && res.data.data) {
        // 标准API响应格式: { status: 1, data: '/auto-login?token=...', message: null }
        linkPath = res.data.data
      } else if (res.data && res.data.message) {
        // 实际后端返回格式: { status: 1, message: '/auto-login?token=...', data: null }
        linkPath = res.data.message
      } else if (res.data && typeof res.data === 'string') {
        // 直接返回字符串格式
        linkPath = res.data
      } else {
        throw new Error('服务器返回的链接数据格式错误')
      }

      if (!linkPath) {
        throw new Error('服务器返回的链接数据为空')
      }

      const fullLink = `${window.location.origin}${linkPath}`

      console.log('🔗 [预览链接] 生成的完整链接:', fullLink)

      // 保存到本地状态
      noLoginLinks.value.set(row.id, {
        link: fullLink,
        createdAt: new Date(),
      })

      // 在新窗口中打开链接
      window.open(fullLink, '_blank')
    }).catch((error: any) => {
      console.error('❌ [预览链接] 生成失败:', error)
      ElMessage.error('生成免登录链接失败: ' + (error.response?.data?.message || error.message))
    })
  }
}

// 复制已存在的免登录链接
function copyExistingLink(row: any) {
  const existingLink = noLoginLinks.value.get(row.id)
  if (existingLink) {
    navigator.clipboard.writeText(existingLink.link).then(() => {
      ElMessage.success('免登录链接已复制到剪贴板')
    }).catch(() => {
      ElMessageBox.alert(existingLink.link, '免登录链接', {
        confirmButtonText: '关闭',
        type: 'info',
      })
    })
  }
}

// 显示免登录链接详情
function showNoLoginLinkDetail(row: any) {
  const existingLink = noLoginLinks.value.get(row.id)
  if (existingLink) {
    const createdTime = dayjs(existingLink.createdAt).format('YYYY-MM-DD HH:mm:ss')
    const expiryTime = dayjs(existingLink.createdAt).add(7, 'day').format('YYYY-MM-DD HH:mm:ss')

    // 计算剩余有效时间
    const now = dayjs()
    const expiry = dayjs(existingLink.createdAt).add(7, 'day')
    const remainingDays = expiry.diff(now, 'day')
    const remainingHours = expiry.diff(now, 'hour') % 24
    const remainingMinutes = expiry.diff(now, 'minute') % 60

    let remainingText = ''
    if (remainingDays > 0) {
      remainingText = `${remainingDays}天${remainingHours}小时${remainingMinutes}分钟`
    } else if (remainingHours > 0) {
      remainingText = `${remainingHours}小时${remainingMinutes}分钟`
    } else if (remainingMinutes > 0) {
      remainingText = `${remainingMinutes}分钟`
    } else {
      remainingText = '已过期'
    }

    // 使用自定义HTML内容显示详情
    ElMessageBox({
      title: '免登录链接详情',
      message: h('div', { style: 'line-height: 1.6; font-size: 14px;' }, [
        h('div', { style: 'margin-bottom: 12px;' }, [
          h('strong', { style: 'color: #606266; display: inline-block; width: 80px;' }, '用户名：'),
          h('span', { style: 'color: #303133;' }, row.username || '-')
        ]),
        h('div', { style: 'margin-bottom: 12px;' }, [
          h('strong', { style: 'color: #606266; display: inline-block; width: 80px;' }, '链接地址：'),
          h('div', { style: 'color: #303133; word-break: break-all; margin-top: 4px; padding: 8px; background: #f5f7fa; border-radius: 4px; font-family: monospace; font-size: 12px;' }, existingLink.link)
        ]),
        h('div', { style: 'margin-bottom: 12px;' }, [
          h('strong', { style: 'color: #606266; display: inline-block; width: 80px;' }, '创建时间：'),
          h('span', { style: 'color: #303133;' }, createdTime)
        ]),
        h('div', { style: 'margin-bottom: 12px;' }, [
          h('strong', { style: 'color: #606266; display: inline-block; width: 80px;' }, '过期时间：'),
          h('span', { style: 'color: #303133;' }, expiryTime)
        ]),
        h('div', { style: 'margin-bottom: 8px;' }, [
          h('strong', { style: 'color: #606266; display: inline-block; width: 80px;' }, '剩余时间：'),
          h('span', {
            style: `color: ${remainingText === '已过期' ? '#f56c6c' : '#67c23a'}; font-weight: 500;`
          }, remainingText)
        ])
      ]),
      confirmButtonText: '关闭',
      type: 'info',
      customClass: 'no-login-link-detail-dialog'
    })
  }
}

// 获取状态标签类型
function getStatusType(status: number) {
  const option = statusOptions.find(item => item.value === status)
  return (option?.type || 'info') as 'success' | 'danger' | 'warning' | 'primary' | 'info'
}

// 获取状态标签文本
function getStatusText(status: number) {
  const option = statusOptions.find(item => item.value === status)
  return option?.label || '未知'
}

// 获取用户类型标签类型
function getUserTypeType(userType: string) {
  const option = userTypeOptions.find(item => item.value === userType)
  return (option?.type || 'info') as 'success' | 'danger' | 'warning' | 'primary' | 'info'
}

// 获取用户类型标签文本
function getUserTypeText(userType: string) {
  const option = userTypeOptions.find(item => item.value === userType)
  return option?.label || '未知'
}

// 获取性别文本
function getGenderText(gender: number) {
  const option = genderOptions.find(item => item.value === gender)
  return option?.label || '未知'
}

// 获取操作菜单项
function getActionItems(row: any) {
  return [
    [
      {
        label: '编辑',
        icon: 'i-ep:edit',
        handle: () => onEdit(row),
      },
      {
        label: row.status === 1 ? '禁用' : '启用',
        icon: row.status === 1 ? 'i-ep:lock' : 'i-ep:unlock',
        handle: () => toggleStatus(row),
      },
      {
        label: '重置密码',
        icon: 'i-ep:key',
        handle: () => resetPassword(row),
      },
    ],
    [
      {
        label: '复制免登录链接',
        icon: 'i-ep:link',
        handle: () => generateNoLoginLink(row),
      },
      {
        label: '预览免登录链接',
        icon: 'i-ep:view',
        handle: () => previewNoLoginLink(row),
      },
    ],
    [
      {
        label: '删除',
        icon: 'i-ep:delete',
        handle: () => onDel(row),
      },
    ],
  ]
}
</script>

<style scoped>
/* 免登录链接详情对话框样式 */
:deep(.no-login-link-detail-dialog) {
  .el-message-box__message {
    padding: 0;
  }

  .el-message-box__content {
    padding: 20px 24px;
  }

  .el-message-box__title {
    font-weight: 600;
    color: #303133;
  }
}

/* 免登录链接操作按钮样式 */
.no-login-link-cell {
  display: flex;
  flex-direction: column;
  gap: 6px;
  align-items: center;
}

.link-status,
.no-link-status {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
  width: 100%;
}

.link-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
  justify-content: center;
}

.link-actions .el-button {
  height: auto;
  min-height: 24px;
  padding: 2px 6px;
  font-size: 12px;
}
</style>

<template>
  <div :class="{ 'absolute-container': tableAutoHeight }">
    <FaPageHeader title="用户管理" class="mb-0" />
    <FaPageMain :class="{ 'flex-1 overflow-auto': tableAutoHeight }" :main-class="{ 'flex-1 flex flex-col overflow-auto': tableAutoHeight }">
      <FaSearchBar :show-toggle="true">
        <template #default="{ fold, toggle }">
          <ElForm :model="search" size="default" label-width="100px" inline-message inline class="search-form">
            <ElFormItem label="用户名">
              <ElInput v-model="search.username" placeholder="请输入用户名" clearable @keydown.enter="currentChange()" @clear="currentChange()" />
            </ElFormItem>
            <ElFormItem label="邮箱">
              <ElInput v-model="search.email" placeholder="请输入邮箱" clearable @keydown.enter="currentChange()" @clear="currentChange()" />
            </ElFormItem>
            <ElFormItem label="手机号">
              <ElInput v-model="search.phone" placeholder="请输入手机号" clearable @keydown.enter="currentChange()" @clear="currentChange()" />
            </ElFormItem>
            <ElFormItem v-show="!fold" label="真实姓名">
              <ElInput v-model="search.realName" placeholder="请输入真实姓名" clearable @keydown.enter="currentChange()" @clear="currentChange()" />
            </ElFormItem>
            <ElFormItem v-show="!fold" label="状态">
              <ElSelect v-model="search.status" placeholder="请选择状态" clearable>
                <ElOption v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
              </ElSelect>
            </ElFormItem>
<!--            <ElFormItem v-show="!fold" label="用户类型">-->
<!--              <ElSelect v-model="search.userType" placeholder="请选择用户类型" clearable>-->
<!--                <ElOption v-for="item in userTypeOptions" :key="item.value" :label="item.label" :value="item.value" />-->
<!--              </ElSelect>-->
<!--            </ElFormItem>-->
<!--            <ElFormItem v-show="!fold" label="租户名称">-->
<!--              <ElInput v-model="search.tenantName" placeholder="请输入租户名称" clearable @keydown.enter="currentChange()" @clear="currentChange()" />-->
<!--            </ElFormItem>-->
<!--            <ElFormItem v-show="!fold" label="租户编码">-->
<!--              <ElInput v-model="search.tenantCode" placeholder="请输入租户编码" clearable @keydown.enter="currentChange()" @clear="currentChange()" />-->
<!--            </ElFormItem>-->
            <ElFormItem>
              <ElButton @click="searchReset(); currentChange()">
                重置
              </ElButton>
              <ElButton type="primary" @click="pagination.page = 1;currentChange()">
                <template #icon>
                  <FaIcon name="i-ep:search" />
                </template>
                筛选
              </ElButton>
              <ElButton link @click="toggle">
                <template #icon>
                  <FaIcon :name="fold ? 'i-ep:caret-bottom' : 'i-ep:caret-top' " />
                </template>
                {{ fold ? '展开' : '收起' }}
              </ElButton>
            </ElFormItem>
          </ElForm>
        </template>
      </FaSearchBar>
      <ElDivider border-style="dashed" />
      <ElSpace wrap>
        <ElButton type="primary" size="default" @click="onCreate">
          <template #icon>
            <FaIcon name="i-ep:plus" />
          </template>
          新增用户
        </ElButton>
        <ElButton v-if="batch.enable" type="danger" size="default" :disabled="!batch.selectionDataList.length" @click="onBatchDelete">
          <template #icon>
            <FaIcon name="i-ep:delete" />
          </template>
          批量删除
        </ElButton>
        <ElButton v-if="batch.enable" type="success" size="default" :disabled="!batch.selectionDataList.length" @click="onBatchGenerateNoLoginLinks">
          <template #icon>
            <FaIcon name="i-ep:link" />
          </template>
          批量生成免登录链接
        </ElButton>
        <ElButton size="default" @click="getDataList">
          <template #icon>
            <FaIcon name="i-ep:refresh" />
          </template>
          刷新
        </ElButton>
      </ElSpace>
      <ElTable v-loading="loading" class="my-4" :data="dataList" stripe highlight-current-row border height="100%" @sort-change="sortChange" @selection-change="batch.selectionDataList = $event">
        <ElTableColumn v-if="batch.enable" type="selection" align="center" fixed />
        <ElTableColumn prop="id" label="ID" width="80" sortable />
        <ElTableColumn prop="username" label="用户名" min-width="120" sortable />
        <ElTableColumn prop="tenantName" label="租户名称" min-width="140">
          <template #default="{ row }">
            <span v-if="row.tenantName" :title="`租户编码: ${row.tenantCode || '-'}`">
              {{ row.tenantName }}
            </span>
            <span v-else class="text-gray-400">-</span>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="tenantCode" label="租户编码" min-width="130">
          <template #default="{ row }">
            <ElTag v-if="row.tenantCode" size="small" type="info">
              {{ row.tenantCode }}
            </ElTag>
            <span v-else class="text-gray-400">-</span>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="realName" label="真实姓名" min-width="120" />
        <ElTableColumn prop="nickname" label="昵称" min-width="120" />
        <ElTableColumn prop="email" label="邮箱" min-width="180" />
        <ElTableColumn prop="phone" label="手机号" min-width="130" />
        <ElTableColumn prop="gender" label="性别" width="80" align="center">
          <template #default="{ row }">
            {{ getGenderText(row.gender) }}
          </template>
        </ElTableColumn>
        <ElTableColumn prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <ElTag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </ElTag>
          </template>
        </ElTableColumn>
<!--        <ElTableColumn prop="userType" label="用户类型" width="120" align="center">-->
<!--          <template #default="{ row }">-->
<!--            <ElTag :type="getUserTypeType(row.userType)" size="small">-->
<!--              {{ getUserTypeText(row.userType) }}-->
<!--            </ElTag>-->
<!--          </template>-->
<!--        </ElTableColumn>-->
        <ElTableColumn prop="lastLoginTime" label="最后登录" width="180" sortable>
          <template #default="{ row }">
            {{ row.lastLoginTime ? dayjs(row.lastLoginTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
          </template>
        </ElTableColumn>
        <ElTableColumn prop="createdTime" label="创建时间" width="180" sortable>
          <template #default="{ row }">
            {{ dayjs(row.createdTime).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </ElTableColumn>
        <ElTableColumn label="免登录链接" width="200" align="center">
          <template #default="{ row }">
            <div class="no-login-link-cell">
              <div v-if="noLoginLinks.has(row.id)" class="link-status">
                <ElTag size="small" type="success" class="mb-1">
                  已生成
                </ElTag>
                <div class="link-actions">
                  <ElButton
                    size="small"
                    type="primary"
                    plain
                    @click="copyExistingLink(row)"
                    title="复制链接"
                  >
                    <FaIcon name="i-ep:copy-document" />
                  </ElButton>
                  <ElButton
                    size="small"
                    type="success"
                    plain
                    @click="previewNoLoginLink(row)"
                    title="预览链接"
                  >
                    <FaIcon name="i-ep:view" />
                  </ElButton>
                  <ElButton
                    size="small"
                    type="info"
                    plain
                    @click="showNoLoginLinkDetail(row)"
                    title="查看详情"
                  >
                    <FaIcon name="i-ep:info-filled" />
                  </ElButton>
                </div>
              </div>
              <div v-else class="no-link-status">
                <ElTag size="small" type="info" class="mb-1">
                  未生成
                </ElTag>
                <div class="link-actions">
                  <ElButton
                    size="small"
                    type="primary"
                    @click="generateNoLoginLink(row)"
                    title="生成并复制链接"
                  >
                    <FaIcon name="i-ep:link" />
                    生成
                  </ElButton>
                </div>
              </div>
            </div>
          </template>
        </ElTableColumn>
        <ElTableColumn label="操作" width="70" align="center" fixed="right">
          <template #default="scope">
            <FaDropdown
              :items="getActionItems(scope.row)"
              align="center"
              side="bottom"
            >
              <ElButton type="primary" size="small" plain>
                <template #icon>
                  <FaIcon name="i-ep:more" />
                </template>
              </ElButton>
            </FaDropdown>
          </template>
        </ElTableColumn>
      </ElTable>
      <ElPagination v-model:current-page="pagination.page" v-model:page-size="pagination.size" :total="pagination.total" :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
    </FaPageMain>
    <FormMode v-if="formMode === 'dialog' || formMode === 'drawer'" :id="formModeProps.id" v-model="formModeProps.visible" :mode="formMode" @success="getDataList" />
  </div>
</template>

<style scoped>
.absolute-container {
  position: absolute;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.search-form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(330px, 1fr));
  margin-bottom: -18px;

  :deep(.el-form-item) {
    grid-column: auto / span 1;

    &:last-child {
      grid-column-end: -1;

      .el-form-item__content {
        justify-content: flex-end;
      }
    }
  }
}

.el-divider {
  width: calc(100% + 40px);
  margin-inline: -20px;
}

/* 免登录链接列样式 */
.no-login-link-cell {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.link-status,
.no-link-status {
  display: flex;
  flex-direction: column;
  gap: 6px;
  align-items: center;
}

.link-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
}

.link-actions .el-button {
  min-width: unset;
  padding: 4px 8px;
}

.link-actions .el-button .fa-icon {
  margin-right: 2px;
}
</style>
