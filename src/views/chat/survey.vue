<route lang="yaml">
meta:
  title: 问卷调研
  icon: heroicons-outline:clipboard-document-list
</route>

<template>
  <div class="survey-container">
        <!-- 页面头部 -->
    <div class="page-header">
      <div class="flex items-start justify-between gap-8">
        <!-- 左侧：页面描述信息 -->
        <div class="flex-1">
          <h1 class="text-xl font-bold text-gray-900 dark:text-white">
            问卷调研
          </h1>
          <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
            调研问卷展示页面 - 共 {{ questions.length }} 道题目
          </p>
        </div>

        <!-- 右侧：二维码区域 -->
        <div class="flex-shrink-0">
          <div class="flex items-center space-x-4">
            <div class="qr-code-container">
              <img
                ref="qrCodeImage"
                :src="salesQrCode"
                alt="问卷二维码"
                class="qr-code-image"
                @error="handleImageError"
              />
            </div>
            <div class="qr-code-info">
              <h3 class="text-sm font-medium text-gray-900 dark:text-white">
                扫码进入问卷
              </h3>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                使用手机扫码即可开始作答问卷
              </p>
              <button
                @click="downloadQRCode"
                class="download-btn-small mt-2"
                title="下载问卷二维码"
              >
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                下载
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 问卷内容 -->
    <div class="survey-content">
      <div v-if="loading" class="loading-state">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">正在加载问卷数据...</p>
      </div>

      <div v-else-if="error" class="error-state">
        <div class="text-red-500 text-center">
          <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.876c1.16 0 2.075-1.114 1.548-2.217L11.327 3.291c-.394-.826-1.543-.826-1.937 0L.231 20.783C-.296 21.886.619 23 1.779 23z"></path>
          </svg>
          <h3 class="text-lg font-medium mb-2">数据加载失败</h3>
          <p class="text-sm">{{ error }}</p>
          <button @click="refreshData" class="mt-4 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
            重新加载
          </button>
        </div>
      </div>

      <div v-else class="questions-list">
        <div
          v-for="(question, index) in questions"
          :key="question.编号"
          class="question-card"
        >
          <!-- 题目标题 - 简化布局 -->
          <div class="question-header">
            <div class="question-number">
              {{ index + 1 }}
            </div>
            <div class="question-info">
              <h3 class="question-title">{{ question.描述 }}</h3>
              <div class="question-meta">
                <span class="question-type" :class="getQuestionTypeClass(question.类型)">
                  {{ question.类型 }}
                </span>
                <span class="question-id">编号: {{ question.编号 }}</span>
              </div>
            </div>
          </div>

          <!-- 选项列表 - 紧凑布局 -->
          <div class="options-container">
            <div
              v-for="(option, optionIndex) in getDisplayedOptions(question)"
              :key="optionIndex"
              class="option-item"
              :class="{ 'option-selected': isOptionSelected(question.编号, question.选项.indexOf(option)) }"
              @click="handleOptionClick(question.编号, question.类型, question.选项.indexOf(option))"
            >
              <!-- 单选框/多选框 -->
              <div class="option-control">
                <input
                  v-if="question.类型 === '单选题'"
                  type="radio"
                  :name="`question_${question.编号}`"
                  :id="`q_${question.编号}_${question.选项.indexOf(option)}`"
                  :checked="isOptionSelected(question.编号, question.选项.indexOf(option))"
                  class="control-input"
                  @change="handleOptionClick(question.编号, question.类型, question.选项.indexOf(option))"
                />
                <input
                  v-else
                  type="checkbox"
                  :id="`q_${question.编号}_${question.选项.indexOf(option)}`"
                  :checked="isOptionSelected(question.编号, question.选项.indexOf(option))"
                  class="control-input"
                  @change="handleOptionClick(question.编号, question.类型, question.选项.indexOf(option))"
                />
                <label :for="`q_${question.编号}_${question.选项.indexOf(option)}`" class="control-label">
                  <span class="control-indicator"></span>
                </label>
              </div>

              <!-- 选项文本 -->
              <div class="option-text">
                <span class="option-content">{{ option.描述 }}</span>
              </div>
            </div>

            <!-- 展开/收起按钮 -->
            <div v-if="shouldShowExpandButton(question)" class="expand-button-container">
              <button
                @click="toggleExpanded(question.编号)"
                class="expand-button"
              >
                <span class="expand-text">
                  {{ expandedQuestions[question.编号] ? '收起选项' : `展开全部选项 (${question.选项.length - 5} 个更多)` }}
                </span>
                <svg
                  class="expand-icon"
                  :class="{ 'expand-icon-rotated': expandedQuestions[question.编号] }"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部统计信息 -->
    <div v-if="!loading && !error" class="survey-footer">
      <div class="stats-container">
        <div class="stat-item">
          <span class="stat-label">总题目数</span>
          <span class="stat-value">{{ questions.length }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">单选题</span>
          <span class="stat-value">{{ getSingleChoiceCount() }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">多选题</span>
          <span class="stat-value">{{ getMultipleChoiceCount() }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import salesQrCode from '@/assets/images/sales-qr-code.png'
import { useUserStore } from '@/store/modules/user'
import tenantApi from '@/api/modules/sys_tenant'

defineOptions({
  name: 'SurveyPage',
})

// 数据结构定义
interface QuestionOption {
  描述: string
  分值: number
}

interface Question {
  编号: string
  描述: string
  类型: '单选题' | '多选题'
  顺序号: number
  选项: QuestionOption[]
}

interface RawQuestionData {
  题目编号: string
  题目描述: string
  题目类型: '单选题' | '多选题'
  题目顺序号: number
  题目选项描述: string
  题目分值: number
}

interface VariableData {
  [key: string]: string
}

// 响应式数据
const questions = ref<Question[]>([])
const loading = ref(false)
const error = ref<string | null>(null)
const answers = reactive<Record<string, number[]>>({})
const expandedQuestions = reactive<Record<string, boolean>>({})
const qrCodeImage = ref<HTMLImageElement | null>(null)
const variables = ref<VariableData>({})

// 用户状态管理
const userStore = useUserStore()

// 租户详细信息
const tenantDetail = ref<any>(null)

// 加载租户详细信息
const loadTenantDetail = async (tenantId: number) => {
  try {
    if (!tenantId) {
      console.log('🏢 [租户信息] 租户ID为空，跳过获取租户详情')
      return null
    }

    console.log('🏢 [租户信息] 开始获取租户详细信息，租户ID:', tenantId)
    const response = await tenantApi.getTenantDetail(tenantId)
    const tenantData = response.data

    console.log('🏢 [租户信息] 租户详细信息:')
    console.log('  - 租户ID:', tenantData.id)
    console.log('  - 租户编码:', tenantData.tenantCode)
    console.log('  - 租户名称:', tenantData.tenantName)
    console.log('  - 域名:', tenantData.domain || '未设置')
    console.log('  - 状态:', tenantData.status, '(' + tenantData.statusText + ')')
    console.log('  - 行业类型:', tenantData.industryType || '未设置', '(' + (tenantData.industryTypeText || '') + ')')
    console.log('  - 租户类型:', tenantData.tenantType || '未设置', '(' + (tenantData.tenantTypeText || '') + ')')
    console.log('  - 联系人姓名:', tenantData.contactName || '未设置')
    console.log('  - 联系人电话:', tenantData.contactPhone || '未设置')
    console.log('  - 联系人邮箱:', tenantData.contactEmail || '未设置')
    console.log('  - Logo地址:', tenantData.logoUrl || '未设置')
    console.log('  - 描述:', tenantData.description || '未设置')
    console.log('  - 创建时间:', tenantData.createdTime)
    console.log('  - 更新时间:', tenantData.updatedTime)
    console.log('  - 过期时间:', tenantData.expireTime || '未设置')
    console.log('  - 是否过期:', tenantData.expired ? '是' : '否')

    if (tenantData.remainingDays !== undefined) {
      console.log('  - 剩余天数:', tenantData.remainingDays)
    }

    tenantDetail.value = tenantData
    return tenantData
  } catch (err) {
    console.error('❌ [租户信息] 获取租户详细信息失败:', err)
    tenantDetail.value = null
    return null
  }
}

// 加载变量数据
const loadVariableData = async () => {
  try {
    // 打印当前登录用户信息
    console.log('👤 [问卷变量] 当前登录用户信息:')
    console.log('  - 用户ID:', userStore.userId)
    console.log('  - 用户名:', userStore.username)
    console.log('  - 真实姓名:', userStore.realName)
    console.log('  - 租户ID:', userStore.tenantId)
    console.log('  - 租户名称:', userStore.tenantName)
    console.log('  - 行业类型:', userStore.industryType)
    console.log('  - 租户类型:', userStore.tenantType)
    console.log('  - 登录状态:', userStore.isLogin)
    console.log('  - 首次登录已完成:', userStore.firstLoginCompleted)

    // 打印用户权限信息
    if (userStore.permissions && userStore.permissions.length > 0) {
      console.log('  - 用户权限:', userStore.permissions.join(', '))
    } else {
      console.log('  - 用户权限: 无权限数据')
    }

    // 打印用户头像信息
    if (userStore.avatar) {
      console.log('  - 用户头像:', userStore.avatar)
    } else {
      console.log('  - 用户头像: 未设置')
    }

    // 根据用户租户ID获取租户详细信息
    if (userStore.tenantId) {
      await loadTenantDetail(userStore.tenantId)
    } else {
      console.log('🏢 [租户信息] 用户未关联租户')
    }

        const moduleData = await import('./survey/var.json')
    variables.value = (moduleData.default || moduleData) as VariableData

    console.log('📋 [问卷变量] 加载的变量数据:', variables.value)

    // 测试变量替换功能
    console.log('🔄 [变量替换] 测试变量替换功能:')
    const testStrings = [
      '请问您此次到访{I3tips}的目的是：',
      '除了{I2tips}，请问您是否去过其他的医学美容机构？',
      '联系电话：{I5tips}',
      '联系人：{ym_contact_name}',
      '用户姓名：{ym_user_name}',
      '行业类型：{ym_industry_type}'
    ]

    testStrings.forEach((testString, index) => {
      const replaced = replaceVariables(testString)
      console.log(`  ${index + 1}. 原文: "${testString}"`)
      console.log(`     替换后: "${replaced}"`)
    })
  } catch (err) {
    console.error('❌ [问卷变量] 加载变量数据失败:', err)
    // 如果加载失败，使用空对象，不影响主要功能
    variables.value = {}
  }
}

// 替换题目描述中的占位符
const replaceVariables = (description: string): string => {
  let result = description

  // 1. 首先使用配置文件中的变量进行替换
  Object.keys(variables.value).forEach(key => {
    const placeholder = `{${key}}`
    const value = variables.value[key]

    // 使用全局替换
    result = result.replace(new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g'), value)
  })

  // 2. 然后使用租户详细信息进行动态替换
  if (tenantDetail.value) {
    const tenantReplacements = {
      'd_tenant_id': tenantDetail.value.id?.toString() || '',
      'd_tenant_code': tenantDetail.value.tenantCode || '',
      'd_tenant_name': tenantDetail.value.tenantName || '',
      'd_tenant_domain': tenantDetail.value.domain || '',
      'd_tenant_contact_name': tenantDetail.value.contactName || '',
      'd_tenant_contact_phone': tenantDetail.value.contactPhone || '',
      'd_tenant_contact_email': tenantDetail.value.contactEmail || '',
      'd_tenant_industry_type': tenantDetail.value.industryTypeText || tenantDetail.value.industryType || '',
      'd_tenant_type': tenantDetail.value.tenantTypeText || tenantDetail.value.tenantType || '',
      'd_tenant_description': tenantDetail.value.description || '',
    }

    // 应用租户信息替换
    Object.keys(tenantReplacements).forEach(key => {
      const placeholder = `{${key}}`
      const value = tenantReplacements[key as keyof typeof tenantReplacements]

      // 使用全局替换
      result = result.replace(new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g'), value)
    })
  }

  // 3. 最后使用用户信息进行动态替换
  if (userStore.isLogin) {
    const userReplacements = {
      'd_user_id': userStore.userId?.toString() || '',
      'd_username': userStore.username || '',
      'd_real_name': userStore.realName || '',
      'd_user_industry_type': userStore.industryType || '',
      'd_user_tenant_type': userStore.tenantType || '',
    }

    // 应用用户信息替换
    Object.keys(userReplacements).forEach(key => {
      const placeholder = `{${key}}`
      const value = userReplacements[key as keyof typeof userReplacements]

      // 使用全局替换
      result = result.replace(new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g'), value)
    })
  }

  return result
}

// 读取并解析JSON数据
const loadQuestionData = async () => {
  try {
    loading.value = true
    error.value = null

    // 先加载变量数据
    await loadVariableData()

    // 动态导入JSON文件
    const moduleData = await import('./survey/exam_quesiton.json')
    const rawData = moduleData.default || moduleData as RawQuestionData[]

    // 按题目编号分组并构建问题结构
    const questionMap = new Map<string, Question>()

    rawData.forEach(item => {
      const questionId = item.题目编号

      if (!questionMap.has(questionId)) {
        questionMap.set(questionId, {
          编号: questionId,
          描述: replaceVariables(item.题目描述), // 使用变量替换后的描述
          类型: item.题目类型 as '单选题' | '多选题',
          顺序号: item.题目顺序号,
          选项: []
        })
      }

      const question = questionMap.get(questionId)!
      question.选项.push({
        描述: replaceVariables(item.题目选项描述), // 选项描述也支持变量替换
        分值: item.题目分值
      })
    })

    // 转换为数组并按顺序号排序
    questions.value = Array.from(questionMap.values())
      .sort((a, b) => a.顺序号 - b.顺序号)

    // 初始化答案存储和展开状态
    questions.value.forEach(question => {
      answers[question.编号] = []
      expandedQuestions[question.编号] = false
    })

  } catch (err) {
    console.error('加载问卷数据失败:', err)
    error.value = err instanceof Error ? err.message : '未知错误'
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  loadQuestionData()
}

// 获取题目类型样式类
const getQuestionTypeClass = (type: string) => {
  return {
    'type-single': type === '单选题',
    'type-multiple': type === '多选题'
  }
}

// 检查选项是否被选中
const isOptionSelected = (questionId: string, optionIndex: number) => {
  return answers[questionId]?.includes(optionIndex) || false
}

// 处理选项点击
const handleOptionClick = (questionId: string, questionType: string, optionIndex: number) => {
  if (!answers[questionId]) {
    answers[questionId] = []
  }

  if (questionType === '单选题') {
    // 单选题：清空之前的选择，只保留当前选择
    answers[questionId] = [optionIndex]
  } else if (questionType === '多选题') {
    // 多选题：切换选择状态
    const currentAnswers = answers[questionId]
    const existingIndex = currentAnswers.indexOf(optionIndex)

    if (existingIndex > -1) {
      // 已选中，取消选择
      currentAnswers.splice(existingIndex, 1)
    } else {
      // 未选中，添加选择
      currentAnswers.push(optionIndex)
    }
  }
}

// 统计函数
const getSingleChoiceCount = () => {
  return questions.value.filter(q => q.类型 === '单选题').length
}

const getMultipleChoiceCount = () => {
  return questions.value.filter(q => q.类型 === '多选题').length
}



// 获取显示的选项（支持折叠）
const getDisplayedOptions = (question: Question) => {
  const maxDisplay = 5
  const isExpanded = expandedQuestions[question.编号]

  if (question.选项.length <= maxDisplay || isExpanded) {
    return question.选项
  }

  return question.选项.slice(0, maxDisplay)
}

// 检查是否需要显示展开按钮
const shouldShowExpandButton = (question: Question) => {
  return question.选项.length > 5
}

// 切换选项展开状态
const toggleExpanded = (questionId: string) => {
  expandedQuestions[questionId] = !expandedQuestions[questionId]
}

// 处理图片加载错误
const handleImageError = () => {
  console.error('二维码图片加载失败')
}

// 下载二维码图片
const downloadQRCode = () => {
  try {
    // 创建一个临时的 a 标签来触发下载
    const link = document.createElement('a')
    link.href = salesQrCode
    link.download = 'survey-qr-code.png'
    link.target = '_blank'

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } catch (err) {
    console.error('下载二维码失败:', err)
    // 如果直接下载失败，尝试在新窗口打开
    window.open(salesQrCode, '_blank')
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadQuestionData()
})
</script>

<style scoped>
.survey-container {
  @apply p-4 bg-gray-50 dark:bg-gray-900 min-h-screen;
}

.page-header {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 mb-4;
}

.refresh-btn {
  @apply inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors;
}

/* 二维码相关样式 */
.qr-code-container {
  @apply flex-shrink-0;
}

.qr-code-image {
  @apply w-48 h-48 rounded-lg border border-gray-200 dark:border-gray-600 object-cover shadow-sm;
}

.qr-code-info {
  @apply flex-1;
}

.download-btn-small {
  @apply inline-flex items-center px-2 py-1 border border-gray-300 dark:border-gray-600 text-xs font-medium rounded text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors;
}

.survey-content {
  @apply space-y-4;
}

.loading-state,
.error-state {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 text-center;
}

.questions-list {
  @apply space-y-4;
}

.question-card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow;
}

.question-header {
  @apply flex items-start p-4 border-b border-gray-200 dark:border-gray-700;
}

.question-number {
  @apply flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center font-semibold text-sm mr-3;
}

.question-info {
  @apply flex-1;
}

.question-title {
  @apply text-base font-semibold text-gray-900 dark:text-white mb-1 leading-relaxed;
}

.question-meta {
  @apply flex items-center space-x-3;
}

.question-type {
  @apply inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium;
}

.question-type.type-single {
  @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
}

.question-type.type-multiple {
  @apply bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200;
}

.question-id {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

.options-container {
  @apply p-4 space-y-2;
}

.option-item {
  @apply flex items-center p-2.5 rounded-md border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-all;
}

.option-item.option-selected {
  @apply bg-blue-50 dark:bg-blue-900/20 border-blue-300 dark:border-blue-600;
}

.option-control {
  @apply flex-shrink-0 mr-2.5;
}

.control-input {
  @apply sr-only;
}

.control-label {
  @apply cursor-pointer;
}

.control-indicator {
  @apply w-4 h-4 border-2 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 flex items-center justify-center transition-all;
}

/* 单选框样式 */
.option-item input[type="radio"] + .control-label .control-indicator {
  @apply rounded-full;
}

.option-item input[type="radio"]:checked + .control-label .control-indicator {
  @apply border-blue-600 dark:border-blue-400;
}

.option-item input[type="radio"]:checked + .control-label .control-indicator::after {
  content: "";

  @apply w-2 h-2 rounded-full bg-blue-600 dark:bg-blue-400;
}

/* 多选框样式 */
.option-item input[type="checkbox"] + .control-label .control-indicator {
  @apply rounded;
}

.option-item input[type="checkbox"]:checked + .control-label .control-indicator {
  @apply border-blue-600 dark:border-blue-400 bg-blue-600 dark:bg-blue-400;
}

.option-item input[type="checkbox"]:checked + .control-label .control-indicator::after {
  content: "✓";

  @apply text-white text-xs font-bold;
}

.option-text {
  @apply flex-1 flex items-center text-sm;
}

.option-content {
  @apply text-gray-900 dark:text-white;
}

.survey-footer {
  @apply mt-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4;
}

.stats-container {
  @apply grid grid-cols-3 gap-4;
}

.stat-item {
  @apply text-center;
}

.stat-label {
  @apply block text-xs font-medium text-gray-500 dark:text-gray-400;
}

.stat-value {
  @apply block text-lg font-bold text-gray-900 dark:text-white mt-1;
}

/* 展开按钮样式 */
.expand-button-container {
  @apply px-4 pb-2;
}

.expand-button {
  @apply w-full flex items-center justify-center px-3 py-2 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1;
}

.expand-text {
  @apply mr-2;
}

.expand-icon {
  @apply w-4 h-4 transition-transform duration-200;
}

.expand-icon-rotated {
  @apply rotate-180;
}
</style>
