问卷数据通过 sql 语句从 832 数据库查询，查询结果导入 JSON 格式，替换 exam_question.json 文件

```sql
SELECT
--     q.id as '题目ID',
    q.question_no as '题目编号',
    q.description as '题目描述',
    CASE q.type
        WHEN 1 THEN '单选题'
        WHEN 2 THEN '多选题'
        WHEN 3 THEN '开放题'
        WHEN 7 THEN '说明题'
        ELSE '未知类型'
    END as '题目类型',
    q.oindex as '题目顺序号',
--     o.id as '题目选项ID',
    o.description as '题目选项描述',
    o.value as '题目分值'
FROM
    ex_exam_question AS q
    JOIN ex_exam_question_option AS o ON q.id = o.exam_question_id
WHERE
    q.exam_id = '5435616E-9544-4CD3-8968-497E22BC1895'
ORDER BY
    q.oindex, o.value;
```

占位符替换

```sql
SELECT
	text_expression,
	text_replacement
FROM
	ex_exam_text_replacement
WHERE
	exam_id = '5435616E-9544-4CD3-8968-497E22BC1895';
```

查询结果需要手工处理一下，组成以下结构的信息

> {I5tips|||1=1}	{d_ym_organization_phone_number}
> {A2tips|||I10=1}	暂不考虑产品品质，综合考虑服务和环境体验，您的满意程度如何？
> {I3tips|||1=1}	{d_ym_store_name}
> {A2tips|||I10!=1}	综合考虑服务和环境体验，您的满意程度如何？
> {I2tips|||1=1}	{d_ym_organization_name}

```json
{
  "A2tips": "综合考虑服务和环境体验，您的满意程度如何？",
  "I2tips": "{d_ym_organization_name}",
  "I3tips": "{d_ym_store_name}",
  "I5tips": "{d_ym_organization_phone_number}",
}

```

