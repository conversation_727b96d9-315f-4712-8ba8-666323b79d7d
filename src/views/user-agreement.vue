<template>
  <div class="user-agreement-container">
    <div class="agreement-card">
      <div class="header">
        <h1 class="title">医学美容行业AI用户服务协议</h1>
        <p class="subtitle">请仔细阅读以下条款，点击同意后方可继续使用服务</p>
      </div>

      <div class="agreement-content">
        <div class="content-scroll">
          <div class="agreement-text" v-html="formattedAgreementText"></div>
        </div>
      </div>

      <div class="footer">
        <div class="checkbox-container">
          <input
            id="agree-checkbox"
            v-model="hasRead"
            type="checkbox"
            class="agreement-checkbox"
          >
          <label for="agree-checkbox" class="checkbox-label">
            我已仔细阅读并同意上述用户服务协议
          </label>
        </div>

        <div class="button-group">
          <button
            class="btn btn-secondary"
            @click="handleReject"
          >
            拒绝
          </button>
          <button
            class="btn btn-primary"
            :disabled="!hasRead || loading"
            @click="handleAccept"
          >
            <span v-if="loading" class="loading-spinner"></span>
            {{ loading ? '处理中...' : '同意并继续' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import useUserStore from '@/store/modules/user'
import userAgreementText from '@/data/user-agreement.txt?raw'

const router = useRouter()
const userStore = useUserStore()

const hasRead = ref(false)
const loading = ref(false)

// 格式化协议文本
const formattedAgreementText = computed(() => {
  return userAgreementText
    .split('\n')
    .map(line => {
      const trimmedLine = line.trim()
      if (!trimmedLine) return '<br>'

      // 处理标题（以数字开头的行）
      // if (/^[一二三四五六七八九十]+、/.test(trimmedLine)) {
      //   return `<h2 class="section-title">${trimmedLine}</h2>`
      // }

      // 处理子标题（以数字开头的行）
      // if (/^\d+、/.test(trimmedLine)) {
      //   return `<h3 class="subsection-title">${trimmedLine}</h3>`
      // }

      // 处理带括号的条目
      // if (/^（\d+）/.test(trimmedLine) || /^\([a-z]\)/.test(trimmedLine)) {
      //   return `<p class="">${trimmedLine}</p>`
      // }

      // 处理重要提示
      if (trimmedLine.includes('重要提示') || trimmedLine.includes('医学美容行业AI用户服务协议')) {
        return `<h1 class="main-title">${trimmedLine}</h1>`
      }

      // 普通段落
      return `<p class="paragraph">${trimmedLine}</p>`
    })
    .join('')
})

// 同意协议
async function handleAccept() {
  if (!hasRead.value) {
    ElMessage.warning('请先阅读并勾选同意协议')
    return
  }

  loading.value = true
  try {
    await userStore.acceptUserAgreement()
    ElMessage.success('协议已确认，欢迎使用我们的服务！')

    // 跳转到首页或之前的页面
    const redirect = router.currentRoute.value.query.redirect as string
    if (redirect) {
      router.push(redirect)
    } else {
      router.push('/')
    }
  } catch (error) {
    console.error('接受用户协议失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 拒绝协议
function handleReject() {
  ElMessage.info('您已拒绝用户协议，将退出登录')
  userStore.logout()
  router.push('/login')
}
</script>

<style scoped>
.user-agreement-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.agreement-card {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgb(0 0 0 / 10%);
}

.header {
  padding: 30px 30px 20px;
  text-align: center;
  border-bottom: 1px solid #eee;
}

.title {
  margin: 0 0 10px;
  font-size: 28px;
  font-weight: 600;
  color: #333;
}

.subtitle {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.agreement-content {
  flex: 1;
  overflow: hidden;
}

.content-scroll {
  height: 400px;
  padding: 30px;
  overflow-y: auto;
}

.agreement-text {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.agreement-text .main-title {
  padding-bottom: 10px;
  margin: 0 0 20px;
  font-size: 20px;
  font-weight: 700;
  color: #333;
  text-align: center;
  border-bottom: 2px solid #667eea;
}

.agreement-text .section-title {
  padding-left: 10px;
  margin: 25px 0 15px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  border-left: 4px solid #667eea;
}

.agreement-text .subsection-title {
  padding-left: 15px;
  margin: 20px 0 10px;
  font-size: 16px;
  font-weight: 600;
  color: #444;
}

.agreement-text .list-item {
  padding-left: 15px;
  margin: 8px 0;
  font-size: 14px;
  line-height: 1.6;
  color: #555;
}

.agreement-text .paragraph {
  margin: 12px 0;
  font-size: 14px;
  line-height: 1.7;
  color: #555;
  text-align: justify;
}

.agreement-text br {
  line-height: 0.5;
}

.footer {
  padding: 20px 30px 30px;
  border-top: 1px solid #eee;
}

.checkbox-container {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.agreement-checkbox {
  width: 18px;
  height: 18px;
  margin-right: 10px;
  cursor: pointer;
}

.checkbox-label {
  font-size: 14px;
  color: #333;
  cursor: pointer;
  user-select: none;
}

.button-group {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
}

.btn {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.btn-secondary {
  color: #666;
  background: #f5f5f5;
}

.btn-secondary:hover {
  background: #e8e8e8;
}

.btn-primary {
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.btn-primary:hover:not(:disabled) {
  box-shadow: 0 4px 12px rgb(102 126 234 / 40%);
  transform: translateY(-1px);
}

.btn-primary:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentcolor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 滚动条样式 */
.content-scroll::-webkit-scrollbar {
  width: 6px;
}

.content-scroll::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.content-scroll::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.content-scroll::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (width <= 768px) {
  .user-agreement-container {
    padding: 10px;
  }

  .agreement-card {
    max-height: 95vh;
  }

  .header {
    padding: 20px 20px 15px;
  }

  .title {
    font-size: 24px;
  }

  .content-scroll {
    height: 300px;
    padding: 20px;
  }

  .footer {
    padding: 15px 20px 20px;
  }

  .button-group {
    flex-direction: column;
  }

  .btn {
    justify-content: center;
    width: 100%;
  }
}
</style>
