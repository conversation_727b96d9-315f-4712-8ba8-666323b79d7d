<template>
  <div class="auto-login-container">
    <div class="auto-login-content">
      <div v-if="loading" class="loading-section">
        <el-icon class="loading-icon">
          <Loading />
        </el-icon>
        <h2>正在验证免登录链接...</h2>
        <p>请稍候，系统正在为您自动登录</p>
      </div>

      <div v-else-if="error" class="error-section">
        <el-icon class="error-icon">
          <CircleClose />
        </el-icon>
        <h2>免登录验证失败</h2>
        <p class="error-message">{{ errorMessage }}</p>
        <div class="error-tips" v-if="isTokenExpired">
          <p class="tips-text">
            <el-icon><InfoFilled /></el-icon>
            温馨提示：免登录链接有效期为7天，如需重新获取，请联系管理员。
          </p>
        </div>
        <div class="button-group">
          <el-button type="primary" @click="goToLogin">
            前往登录页面
          </el-button>
          <el-button v-if="isTokenExpired" type="default" @click="contactAdmin">
            联系管理员
          </el-button>
        </div>
      </div>

      <div v-else class="success-section">
        <el-icon class="success-icon">
          <CircleCheck />
        </el-icon>
        <h2>免登录验证成功</h2>
        <p>正在跳转到系统首页...</p>
      </div>
    </div>

    <!-- 联系管理员对话框 -->
    <el-dialog
      v-model="showContactDialog"
      title="联系管理员"
      width="400px"
      align-center
    >
      <div class="contact-dialog-content">
        <p class="contact-title">扫描二维码联系管理员</p>
        <div class="qr-code-container">
                    <img
            src="@/assets/images/dips-agent.png"
            alt="联系管理员二维码"
            class="qr-code-image"
            @error="handleImageError"
          />
        </div>
        <p class="contact-tips">
          请扫描上方二维码联系管理员<br/>
          重新生成免登录链接
        </p>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showContactDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElDialog } from 'element-plus'
import { Loading, CircleClose, CircleCheck, InfoFilled } from '@element-plus/icons-vue'
import apiAuth from '@/api/modules/auth'
import useUserStore from '@/store/modules/user'

defineOptions({
  name: 'AutoLogin',
})

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const loading = ref(true)
const error = ref(false)
const errorMessage = ref('')
const isTokenExpired = ref(false)
const showContactDialog = ref(false)

onMounted(() => {
  handleAutoLogin()
})

async function handleAutoLogin() {
  try {
    const token = route.query.token as string

    if (!token) {
      throw new Error('缺少免登录Token')
    }

    //console.log('🔐 [免登录] 开始免登录认证')

    // 调用免登录认证接口
    const response = await apiAuth.autoLogin(token)

    //console.log('🔐 [免登录] API响应:', response)

    // 检查响应格式，兼容axios拦截器解包的情况
    // axios拦截器在成功时返回带有success字段的解包数据
    let loginData: any

    // 简化逻辑：直接使用前端日志显示的响应格式
    // 从日志可以看出，前端接收到的是解包后的用户数据对象
    if (response && (response as any).accessToken) {
      // 响应直接是用户数据
      loginData = response
    } else if (response && response.data && (response.data as any).accessToken) {
      // 响应在data字段中包含用户数据
      loginData = response.data
    } else {
      // 其他情况抛出错误
      console.error('🔐 [免登录] 响应格式不匹配，完整响应:', response)
      throw new Error('免登录认证失败：响应格式不正确')
    }

    //console.log('✅ [免登录] 认证成功，解析到的用户数据:', loginData)

    // 直接设置用户信息到localStorage和store
    const storage = await import('@/utils/storage')

    storage.default.local.set('account', loginData.username)
    storage.default.local.set('token', loginData.accessToken)
    storage.default.local.set('refreshToken', loginData.refreshToken)
    storage.default.local.set('avatar', loginData.avatar || '')
    storage.default.local.set('userId', String(loginData.userId))
    storage.default.local.set('username', loginData.username)
    storage.default.local.set('realName', loginData.realName || '')
    storage.default.local.set('tenantId', String(loginData.tenantId))
    storage.default.local.set('tenantName', loginData.tenantName || '')

    // 确保正确处理 firstLoginCompleted 布尔值
    const isFirstLoginCompleted = Boolean(loginData.firstLoginCompleted)
    storage.default.local.set('firstLoginCompleted', String(isFirstLoginCompleted))

    // 添加租户类型和行业类型到localStorage
    storage.default.local.set('tenantIndustryType', loginData.industryType || '')
    storage.default.local.set('tenantType', loginData.tenantType || '')

    //console.log('🔐 [免登录] 用户信息保存完成，准备跳转')
    //console.log('🔐 [免登录] 后端返回的 firstLoginCompleted:', loginData.firstLoginCompleted)
    //console.log('🔐 [免登录] 处理后的 isFirstLoginCompleted:', isFirstLoginCompleted)
    //console.log('🔐 [免登录] 行业类型:', loginData.industryType)
    //console.log('🔐 [免登录] 租户类型:', loginData.tenantType)

    // 导入设置store获取默认首页路径
    const settingsStore = (await import('@/store/modules/settings')).default()
    const defaultHomePath = settingsStore.settings.home.fullPath

    //console.log('🔐 [免登录] 配置的默认首页路径:', defaultHomePath)

    // 跳转到配置的默认首页路径，而不是硬编码的根路径
    //console.log('🔐 [免登录] 即将跳转到:', defaultHomePath)
    window.location.href = defaultHomePath

  } catch (err: any) {
    console.error('❌ [免登录] 认证失败:', err)
    loading.value = false
    error.value = true
    const message = err.response?.data?.message || err.message || '免登录认证失败'
    errorMessage.value = message

    // 检查是否是Token过期错误
    isTokenExpired.value = message.includes('已过期') || message.includes('过期')

    ElMessage.error(errorMessage.value)
  }
}

function goToLogin() {
  router.replace('/login')
}

function contactAdmin() {
  showContactDialog.value = true
}

function handleImageError() {
  ElMessage.error('二维码图片加载失败，请稍后重试')
}
</script>

<style scoped>
.auto-login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.auto-login-content {
  width: 100%;
  max-width: 500px;
  padding: 60px 40px;
  text-align: center;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgb(0 0 0 / 10%);
}

.loading-section h2,
.error-section h2,
.success-section h2 {
  margin: 20px 0 10px;
  font-size: 24px;
  font-weight: 600;
}

.loading-section p,
.success-section p {
  margin-bottom: 0;
  font-size: 16px;
  color: #666;
}

.error-message {
  margin-bottom: 20px;
  font-size: 16px;
  color: #f56c6c;
  word-break: break-word;
}

.error-tips {
  padding: 12px 16px;
  margin-bottom: 30px;
  background-color: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
}

.tips-text {
  display: flex;
  gap: 8px;
  align-items: center;
  margin: 0;
  font-size: 14px;
  color: #1e40af;
}

.button-group {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.loading-icon {
  font-size: 48px;
  color: #409eff;
  animation: spin 1s linear infinite;
}

.error-icon {
  font-size: 48px;
  color: #f56c6c;
}

.success-icon {
  font-size: 48px;
  color: #67c23a;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 联系管理员对话框样式 */
.contact-dialog-content {
  padding: 20px 0;
  text-align: center;
}

.contact-title {
  margin: 0 0 20px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.qr-code-container {
  display: flex;
  justify-content: center;
  padding: 20px;
  margin: 20px 0;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.qr-code-image {
  width: 200px;
  height: 200px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
}

.contact-tips {
  margin: 20px 0 0;
  font-size: 14px;
  line-height: 1.6;
  color: #666;
}

.dialog-footer {
  text-align: center;
}
</style>
