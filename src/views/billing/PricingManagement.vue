<script setup lang="ts">
import { computed, onMounted, ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { BillingAPI } from '@/api/billing'
import type { IndustryTenantPricing, PackageFeature } from '@/types/billing'

// 响应式数据
const loading = ref(false)
const tableData = ref<IndustryTenantPricing[]>([])
const dialogVisible = ref(false)
const editMode = ref(false)
const currentRecord = ref<Partial<IndustryTenantPricing>>({})

// 搜索表单
const searchForm = reactive({
  industryType: '',
  tenantType: '',
  packageName: '',
  isAvailable: ''
})

// 行业类型选项
const industryTypeOptions = [
  { label: '医美', value: 'MEDICAL_BEAUTY' },
  { label: '地产', value: 'REAL_ESTATE' }
]

// 租户类型选项
const tenantTypeOptions = [
  { label: '机构', value: 'ORGANIZATION' },
  { label: '机构集团', value: 'ORGANIZATION_GROUP' },
  { label: '厂家', value: 'MANUFACTURER' },
  { label: '消费者', value: 'CONSUMER' }
]

// 表格列定义
const columns = [
  {
    prop: 'id',
    label: 'ID',
    width: 80,
    sortable: true
  },
  {
    prop: 'industryType',
    label: '行业类型',
    width: 100,
    formatter: (row: IndustryTenantPricing) => {
      const option = industryTypeOptions.find(opt => opt.value === row.industryType)
      return option?.label || row.industryType
    }
  },
  {
    prop: 'tenantType',
    label: '租户类型',
    width: 120,
    formatter: (row: IndustryTenantPricing) => {
      const option = tenantTypeOptions.find(opt => opt.value === row.tenantType)
      return option?.label || row.tenantType
    }
  },
  {
    prop: 'packageName',
    label: '套餐名称',
    width: 120
  },
  {
    prop: 'price',
    label: '价格',
    width: 100,
    formatter: (row: IndustryTenantPricing) => {
      return row.price ? `¥${row.price}` : '即将推出'
    }
  },
  {
    prop: 'originalPrice',
    label: '原价',
    width: 100,
    formatter: (row: IndustryTenantPricing) => {
      return row.originalPrice ? `¥${row.originalPrice}` : '-'
    }
  },
  {
    prop: 'discountText',
    label: '折扣',
    width: 80
  },
  {
    prop: 'period',
    label: '周期',
    width: 80
  },
  {
    prop: 'isAvailable',
    label: '状态',
    width: 80
  },
  {
    prop: 'isPopular',
    label: '推荐',
    width: 80
  },
  {
    prop: 'sortOrder',
    label: '排序',
    width: 80,
    sortable: true
  },
  {
    prop: 'createdAt',
    label: '创建时间',
    width: 160,
    sortable: true
  }
]

// 计算属性
const filteredData = computed(() => {
  return tableData.value.filter(item => {
    return (!searchForm.industryType || item.industryType === searchForm.industryType) &&
           (!searchForm.tenantType || item.tenantType === searchForm.tenantType) &&
           (!searchForm.packageName || item.packageName.includes(searchForm.packageName)) &&
           (searchForm.isAvailable === '' || item.isAvailable === (searchForm.isAvailable === 'true'))
  })
})

// 方法
const fetchData = async () => {
  try {
    loading.value = true
    const response = await BillingAPI.getAllPricing()
    if (response.success) {
      tableData.value = response.data || []
    } else {
      ElMessage.error('获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const resetSearch = () => {
  Object.assign(searchForm, {
    industryType: '',
    tenantType: '',
    packageName: '',
    isAvailable: ''
  })
}

const openCreateDialog = () => {
  editMode.value = false
  currentRecord.value = {
    isAvailable: true,
    isPopular: false,
    sortOrder: 0,
    features: []
  }
  dialogVisible.value = true
}

const openEditDialog = (row: IndustryTenantPricing) => {
  editMode.value = true
  currentRecord.value = { ...row }
  dialogVisible.value = true
}

const closeDialog = () => {
  dialogVisible.value = false
  currentRecord.value = {}
}

const handleSave = async () => {
  try {
    loading.value = true
    let response

    if (editMode.value) {
      response = await BillingAPI.updatePricing(currentRecord.value.id!, currentRecord.value)
    } else {
      response = await BillingAPI.createPricing(currentRecord.value)
    }

    if (response.success) {
      ElMessage.success(editMode.value ? '更新成功' : '创建成功')
      closeDialog()
      await fetchData()
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

const handleDelete = async (row: IndustryTenantPricing) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除套餐 "${row.packageName}" 吗？`,
      '确认删除',
      {
        type: 'warning'
      }
    )

    loading.value = true
    const response = await BillingAPI.deletePricing(row.id)

    if (response.success) {
      ElMessage.success('删除成功')
      await fetchData()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  } finally {
    loading.value = false
  }
}

const addFeature = () => {
  if (!currentRecord.value.features) {
    currentRecord.value.features = []
  }
  currentRecord.value.features.push({
    name: '',
    description: '',
    included: true
  })
}

const removeFeature = (index: number) => {
  currentRecord.value.features?.splice(index, 1)
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<template>
  <div class="pricing-management">
    <fa-page-main title="行业租户定价管理" class="pricing-management__container">
      <template #title>
        <div class="page-title">
          <h2>行业租户定价管理</h2>
          <p>管理不同行业和租户类型的套餐定价配置</p>
        </div>
      </template>

      <!-- 搜索区域 -->
      <el-card class="search-card" shadow="never">
        <el-form :model="searchForm" inline>
          <el-form-item label="行业类型">
            <el-select v-model="searchForm.industryType" placeholder="请选择" clearable>
              <el-option
                v-for="option in industryTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="租户类型">
            <el-select v-model="searchForm.tenantType" placeholder="请选择" clearable>
              <el-option
                v-for="option in tenantTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="套餐名称">
            <el-input
              v-model="searchForm.packageName"
              placeholder="请输入套餐名称"
              clearable
            />
          </el-form-item>

          <el-form-item label="状态">
            <el-select v-model="searchForm.isAvailable" placeholder="请选择" clearable>
              <el-option label="可用" value="true" />
              <el-option label="不可用" value="false" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="fetchData">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
            <el-button type="success" @click="openCreateDialog">新增套餐</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 表格区域 -->
      <el-card class="table-card" shadow="never">
        <el-table
          :data="filteredData"
          :loading="loading"
          stripe
          border
          height="calc(100vh - 400px)"
        >
          <el-table-column
            v-for="column in columns"
            :key="column.prop"
            v-bind="column"
          >
            <template v-if="column.prop === 'isAvailable'" #default="{ row }">
              <el-tag :type="row.isAvailable ? 'success' : 'danger'">
                {{ row.isAvailable ? '可用' : '不可用' }}
              </el-tag>
            </template>

            <template v-if="column.prop === 'isPopular'" #default="{ row }">
              <el-tag v-if="row.isPopular" type="warning">推荐</el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="openEditDialog(row)"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </fa-page-main>

    <!-- 编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="editMode ? '编辑套餐定价' : '新增套餐定价'"
      width="800px"
      @close="closeDialog"
    >
      <el-form :model="currentRecord" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="行业类型" required>
              <el-select v-model="currentRecord.industryType" placeholder="请选择">
                <el-option
                  v-for="option in industryTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="租户类型" required>
              <el-select v-model="currentRecord.tenantType" placeholder="请选择">
                <el-option
                  v-for="option in tenantTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="套餐名称" required>
              <el-input v-model="currentRecord.packageName" placeholder="请输入套餐名称" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="当前价格">
              <el-input-number
                v-model="currentRecord.price"
                :precision="2"
                :min="0"
                controls-position="right"
                placeholder="请输入价格"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="原价">
              <el-input-number
                v-model="currentRecord.originalPrice"
                :precision="2"
                :min="0"
                controls-position="right"
                placeholder="请输入原价"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="折扣文本">
              <el-input v-model="currentRecord.discountText" placeholder="如：5折" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="折扣结束日期">
              <el-date-picker
                v-model="currentRecord.discountEndDate"
                type="date"
                placeholder="选择日期"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="计费周期">
              <el-input v-model="currentRecord.period" placeholder="如：/ 年" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="是否可用">
              <el-switch v-model="currentRecord.isAvailable" />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="是否推荐">
              <el-switch v-model="currentRecord.isPopular" />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="排序">
              <el-input-number
                v-model="currentRecord.sortOrder"
                :min="0"
                controls-position="right"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="套餐描述">
          <el-input
            v-model="currentRecord.packageDescription"
            type="textarea"
            :rows="3"
            placeholder="请输入套餐描述"
          />
        </el-form-item>

        <!-- 功能特性 -->
        <el-form-item label="功能特性">
          <div class="features-container">
            <div
              v-for="(feature, index) in currentRecord.features"
              :key="index"
              class="feature-item"
            >
              <el-row :gutter="10">
                <el-col :span="6">
                  <el-input v-model="feature.name" placeholder="功能名称" />
                </el-col>
                <el-col :span="12">
                  <el-input v-model="feature.description" placeholder="功能描述" />
                </el-col>
                <el-col :span="3">
                  <el-switch v-model="feature.included" />
                </el-col>
                <el-col :span="3">
                  <el-button
                    type="danger"
                    size="small"
                    @click="removeFeature(index)"
                  >
                    删除
                  </el-button>
                </el-col>
              </el-row>
            </div>

            <el-button type="primary" plain @click="addFeature">
              <el-icon><fa-icon name="i-ep:plus" /></el-icon>
              添加功能特性
            </el-button>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSave">
          {{ editMode ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.pricing-management {
  @apply h-full;

  &__container {
    @apply h-full;
  }

  .page-title {
    h2 {
      @apply text-xl font-semibold text-gray-800 m-0;
    }

    p {
      @apply text-sm text-gray-600 mt-1 m-0;
    }
  }

  .search-card {
    @apply mb-4;
  }

  .table-card {
    @apply flex-1;
  }

  .features-container {
    @apply w-full;

    .feature-item {
      @apply mb-2;
    }
  }
}
</style>
