<template>
  <div class="admin-invoice">
    <fa-page-main title="发票管理" class="admin-invoice__container">
      <template #title>
        <div class="page-header">
          <h2>发票管理</h2>
          <p>管理系统发票申请和开票流程</p>
        </div>
      </template>

      <div class="admin-invoice__content">
        <!-- 顶部操作栏 -->
        <div class="admin-header">
          <div class="header-left">
            <a-space>
              <a-select
                v-model:value="statusFilter"
                placeholder="筛选状态"
                style="width: 150px;"
                allow-clear
                @change="handleFilterChange">
                <a-select-option value="">全部</a-select-option>
                <a-select-option value="PENDING">待处理</a-select-option>
                <a-select-option value="COMPLETED">已完成</a-select-option>
                <a-select-option value="CANCELLED">已取消</a-select-option>
              </a-select>
              <a-button @click="refreshList">
                <template #icon>
                  <ReloadOutlined />
                </template>
                刷新
              </a-button>
            </a-space>
          </div>
          <div class="header-right">
            <span class="pending-count">待处理 {{ pendingCount }} 张</span>
          </div>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-section">
          <a-row :gutter="24">
            <a-col :xs="24" :sm="6">
              <a-card class="stat-card">
                <a-statistic
                  title="总发票数"
                  :value="systemStats.totalCount || 0"
                  :loading="false"
                />
              </a-card>
            </a-col>
            <a-col :xs="24" :sm="6">
              <a-card class="stat-card">
                <a-statistic
                  title="待处理"
                  :value="systemStats.pendingCount || 0"
                  :loading="false"
                />
              </a-card>
            </a-col>
            <a-col :xs="24" :sm="6">
              <a-card class="stat-card">
                <a-statistic
                  title="已完成"
                  :value="systemStats.completedCount || 0"
                  :loading="false"
                />
              </a-card>
            </a-col>
            <a-col :xs="24" :sm="6">
              <a-card class="stat-card">
                <a-statistic
                  title="总开票金额"
                  :value="systemStats.totalAmount || 0"
                  :precision="2"
                  :loading="false"
                >
                  <template #prefix>¥</template>
                </a-statistic>
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 搜索表单 -->
        <div class="search-section">
          <a-card title="搜索条件" class="search-card">
            <a-form layout="inline" :model="searchForm" @submit.prevent="handleSearch">
              <a-form-item label="发票单号">
                <a-input
                  v-model:value="searchForm.invoiceNo"
                  placeholder="请输入发票单号"
                  style="width: 200px;"
                  allow-clear
                  @press-enter="handleSearch"
                />
              </a-form-item>
              <a-form-item label="发票类型">
                <a-select
                  v-model:value="searchForm.invoiceType"
                  placeholder="请选择发票类型"
                  style="width: 150px;"
                  allow-clear
                >
                  <a-select-option value="NORMAL">普通发票</a-select-option>
                  <a-select-option value="SPECIAL">专用发票</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="发票抬头">
                <a-input
                  v-model:value="searchForm.invoiceTitle"
                  placeholder="请输入发票抬头"
                  style="width: 200px;"
                  allow-clear
                  @press-enter="handleSearch"
                />
              </a-form-item>
              <a-form-item>
                <a-space>
                  <a-button type="primary" @click="handleSearch">
                    <template #icon>
                      <SearchOutlined />
                    </template>
                    搜索
                  </a-button>
                  <a-button @click="handleReset">
                    重置
                  </a-button>
                </a-space>
              </a-form-item>
            </a-form>
          </a-card>
        </div>

        <!-- 发票列表 -->
        <div class="invoice-list">
          <a-card title="发票列表" class="invoice-card">
            <a-table
              :columns="columns"
              :data-source="invoiceList"
              :loading="loading"
              :pagination="paginationConfig"
              :scroll="{ x: 1200 }"
              row-key="id"
              @change="handleTableChange"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'invoiceNo'">
                  <a-button type="link" @click="viewDetail(record)">
                    {{ record.invoiceNo }}
                  </a-button>
                </template>

                <template v-if="column.key === 'invoiceType'">
                  <a-tag :color="record.invoiceType === 'NORMAL' ? 'blue' : 'green'">
                    {{ record.invoiceTypeDescription }}
                  </a-tag>
                </template>

                <template v-if="column.key === 'totalAmount'">
                  <span class="amount">¥{{ record.totalAmount.toFixed(2) }}</span>
                </template>

                <template v-if="column.key === 'status'">
                  <a-tag :color="getStatusColor(record.status)">
                    {{ getStatusText(record.status) }}
                  </a-tag>
                </template>

                <template v-if="column.key === 'applicationTime'">
                  <span>{{ formatDateTime(record.applicationTime) }}</span>
                </template>

                <template v-if="column.key === 'processedTime'">
                  <span v-if="record.status === 'COMPLETED' && record.processedTime">
                    {{ formatDateTime(record.processedTime) }}
                  </span>
                  <span v-else class="text-gray-400">-</span>
                </template>

                <template v-if="column.key === 'actions'">
                  <a-space>
                    <a-button type="link" size="small" @click="viewDetail(record)">
                      查看详情
                    </a-button>
                    <a-button
                      v-if="record.status === 'PENDING'"
                      type="link"
                      size="small"
                      @click="processInvoice(record)"
                    >
                      处理开票
                    </a-button>
                    <!-- <a-button
                      v-if="record.status === 'PENDING'"
                      type="link"
                      size="small"
                      danger
                      @click="cancelInvoice(record)"
                    >
                      取消
                    </a-button> -->
                  </a-space>
                </template>
              </template>
            </a-table>


          </a-card>
        </div>
      </div>

      <!-- 处理开票对话框 -->
      <InvoiceProcessDialog
        v-model="showProcessDialog"
        :invoice="selectedInvoice"
        @success="handleProcessSuccess"
      />

      <!-- 发票详情对话框 -->
      <InvoiceDetailDialog
        v-model="showDetailDialog"
        :invoice-id="selectedInvoiceId"
        @download="downloadFile"
      />

      <!-- 取消发票对话框 -->
      <a-modal
        v-model:open="showCancelDialog"
        title="取消发票申请"
        :width="500"
        :confirm-loading="cancelling"
        @ok="confirmCancel"
        @cancel="showCancelDialog = false"
      >
        <a-form :model="cancelForm" layout="vertical">
          <a-form-item label="取消原因" required>
            <a-textarea
              v-model:value="cancelForm.reason"
              :rows="4"
              placeholder="请输入取消原因"
              :maxlength="500"
              show-count
            />
          </a-form-item>
        </a-form>
      </a-modal>
    </fa-page-main>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons-vue'
import { formatDateTime } from '@/utils/format'
import { invoiceApi, type InvoiceListItem, type InvoiceDetail } from '@/api/invoice'
import InvoiceDetailDialog from '@/components/invoice/InvoiceDetailDialog.vue'
import InvoiceProcessDialog from '@/components/invoice/InvoiceProcessDialog.vue'

// 响应式数据
const loading = ref(false)
const showProcessDialog = ref(false)
const showDetailDialog = ref(false)
const showCancelDialog = ref(false)
const cancelling = ref(false)
const selectedInvoice = ref<InvoiceListItem | null>(null)
const selectedInvoiceId = ref<number | undefined>(undefined)

const invoiceList = ref<InvoiceListItem[]>([])
const systemStats = ref<any>({})
const statusFilter = ref('')

// 搜索表单
const searchForm = reactive({
  invoiceNo: '',
  invoiceType: '',
  invoiceTitle: ''
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const sortInfo = ref({
  prop: 'applicationTime',
  order: 'asc'
})

const cancelForm = reactive({
  reason: ''
})

// 表格列配置
const columns = [
  {
    title: '发票单号',
    dataIndex: 'invoiceNo',
    key: 'invoiceNo',
    width: 250,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '申请人',
    dataIndex: 'applicantName',
    key: 'applicantName',
    width: 80,
    align: 'center',
  },
  {
    title: '发票抬头',
    dataIndex: 'invoiceTitle',
    key: 'invoiceTitle',
    width: 200,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '发票类型',
    dataIndex: 'invoiceType',
    key: 'invoiceType',
    width: 100,
    align: 'center',
  },
  {
    title: '发票金额',
    dataIndex: 'totalAmount',
    key: 'totalAmount',
    width: 120,
    align: 'center',
    sorter: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    align: 'center',
  },
  {
    title: '申请时间',
    dataIndex: 'applicationTime',
    key: 'applicationTime',
    width: 180,
    align: 'center',
    sorter: true,
  },
  {
    title: '开票时间',
    dataIndex: 'processedTime',
    key: 'processedTime',
    width: 180,
    align: 'center',
    sorter: true,
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    align: 'center',
    fixed: 'right',
  },
]

// 分页配置
const paginationConfig = computed(() => ({
  current: pagination.current,
  pageSize: pagination.size,
  total: pagination.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
}))



// 计算属性
const pendingCount = computed(() => {
  return invoiceList.value.filter(invoice => invoice.status === 'PENDING').length
})



// 获取发票列表
const getInvoiceList = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.current - 1,
      size: pagination.size,
      sort: sortInfo.value.prop,
      direction: sortInfo.value.order,
      status: statusFilter.value || undefined, // 空字符串转为undefined，让后端处理
      invoiceNo: searchForm.invoiceNo || undefined,
      invoiceType: searchForm.invoiceType || undefined,
      invoiceTitle: searchForm.invoiceTitle || undefined
    }

    console.log('📡 发送API请求，参数:', params)

    // 统一使用getAllInvoices API处理所有状态过滤
    const response = await invoiceApi.getAllInvoices(params)

    invoiceList.value = response.data.content
    pagination.total = response.data.totalElements
  } catch (error) {
    console.error('获取发票列表失败:', error)
    message.error('获取发票列表失败')
  } finally {
    loading.value = false
  }
}

// 获取系统统计
const getSystemStats = async () => {
  try {
    const response = await invoiceApi.getSystemInvoiceStats()
    systemStats.value = response.data || {}
  } catch (error) {
    console.error('获取系统统计失败:', error)
  }
}

// 状态相关方法
const getStatusColor = (status: string): string => {
  const statusMap: Record<string, string> = {
    'PENDING': 'orange',
    'COMPLETED': 'green',
    'CANCELLED': 'red'
  }
  return statusMap[status] || 'default'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'PENDING': '开票中',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消'
  }
  return statusMap[status] || status
}

// 查看详情
const viewDetail = (invoice: InvoiceListItem) => {
  selectedInvoice.value = invoice
  selectedInvoiceId.value = invoice.id
  showDetailDialog.value = true
}

// 处理开票
const processInvoice = (invoice: any) => {
  selectedInvoice.value = invoice
  showProcessDialog.value = true
}

// 处理发票成功回调
const handleProcessSuccess = () => {
  showProcessDialog.value = false
  selectedInvoice.value = null
  refreshList()
}

// 取消发票
const cancelInvoice = (invoice: any) => {
  selectedInvoice.value = invoice
  cancelForm.reason = ''
  showCancelDialog.value = true
}

// 确认取消
const confirmCancel = async () => {
  if (!selectedInvoice.value || !cancelForm.reason.trim()) {
    message.warning('请输入取消原因')
    return
  }

  try {
    cancelling.value = true
    await invoiceApi.cancelInvoice(selectedInvoice.value.id, cancelForm.reason)

    message.success('发票已取消')
    showCancelDialog.value = false
    refreshList()
  } catch (error) {
    console.error('取消发票失败:', error)
    message.error('取消发票失败')
  } finally {
    cancelling.value = false
  }
}



// 搜索处理
const handleSearch = () => {
  console.log('🔍 执行搜索，搜索条件:', searchForm)
  pagination.current = 1
  getInvoiceList()
}

// 重置搜索
const handleReset = () => {
  searchForm.invoiceNo = ''
  searchForm.invoiceType = ''
  searchForm.invoiceTitle = ''
  statusFilter.value = ''
  pagination.current = 1
  getInvoiceList()
}

// 表格事件处理
const handleFilterChange = () => {
  pagination.current = 1
  getInvoiceList()
}

const handleTableChange = ({ current, pageSize, sorter }: any) => {
  pagination.current = current
  pagination.size = pageSize

  if (sorter?.field) {
    const fieldMap: Record<string, string> = {
      'applicationTime': 'applicationTime',
      'processedTime': 'processedTime',
      'totalAmount': 'totalAmount'
    }
    sortInfo.value.prop = fieldMap[sorter.field] || 'applicationTime'
    sortInfo.value.order = sorter.order === 'ascend' ? 'asc' : 'desc'
  }

  getInvoiceList()
}

// 下载发票文件
const downloadFile = async (invoice: InvoiceDetail) => {
  try {
    const response = await invoiceApi.downloadInvoiceFile(invoice.id)

        // 兼容处理：下载链接可能在 data 或 message 字段中
    const downloadUrl = response.data || response.message

    if (response.success && downloadUrl) {
      // 使用获取的下载链接打开新窗口下载
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = invoice.invoiceFileName || `发票_${invoice.invoiceNo}.pdf`
      link.target = '_blank'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      message.success('发票下载成功')
    } else {
      message.error('获取下载链接失败')
    }
  } catch (error) {
    console.error('🚀 AdminInvoice.vue·511🔦', error)
    message.error('下载发票失败')
  }
}



// 刷新数据
const refreshList = () => {
  getInvoiceList()
  getSystemStats()
}

// 初始化
onMounted(() => {
  refreshList()
})
</script>

<style scoped lang="scss">
.admin-invoice {
  @apply h-full;

  &__container {
    @apply h-full;
  }

  &__content {
    @apply flex flex-col gap-6;
  }

  .page-header {
    @apply text-center mb-8;

    h2 {
      @apply text-2xl font-bold text-gray-800 mb-2;
    }

    p {
      @apply text-gray-600;
    }
  }

  .admin-header {
    @apply flex items-center justify-between p-6 bg-white rounded-lg;

    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);

    .header-left {
      @apply flex items-center;
    }

    .header-right {
      @apply flex items-center;

      .pending-count {
        @apply text-sm font-medium text-orange-600;
      }
    }
  }

  .search-section {
    .search-card {
      box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    }
  }

  .stats-section {
    .stat-card {
      @apply text-center;

      box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    }
  }

  .invoice-card {
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);

    .amount {
      @apply font-semibold text-red-600;
    }

    // 表头居中样式
    :deep(.ant-table-thead > tr > th) {
      text-align: center;
    }

    // 表格单元格内容居中样式
    :deep(.ant-table-tbody > tr > td) {
      text-align: center;
    }
  }

  .process-dialog-content,
  .detail-dialog-content {
    @apply text-center py-10;

    .dialog-actions {
      @apply mt-6;
    }
  }
}

// 响应式设计
@media (width <= 768px) {
  .admin-invoice {
    .admin-header {
      @apply flex-col gap-4 items-stretch;
    }

    .stats-section {
      .ant-col {
        @apply mb-4;
      }
    }
  }
}
</style>
