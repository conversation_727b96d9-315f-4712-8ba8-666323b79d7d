<script setup lang="ts">
import type { RechargeRecord, RechargeRequest, InvoiceRequest } from '@/types/billing'
import type { TableColumnsType } from 'ant-design-vue'
import BalanceDisplay from '@/components/billing/BalanceDisplay.vue'
import CurrentPackageInfo from '@/components/billing/CurrentPackageInfo.vue'
import QuickRechargePanel from '@/components/billing/QuickRechargePanel.vue'
import InvoiceForm from '@/components/billing/InvoiceForm.vue'
import { useBillingStore } from '@/stores/billing'
import { useUserStore } from '@/store/modules/user'
import useSettingsStore from '@/store/modules/settings'
import { formatCurrency, getCurrencySymbol } from '@/utils/format'
import { InvoiceAPI } from '@/api/billing'
import {
  AlipayOutlined,
  CreditCardOutlined,
  DownOutlined,
  ReloadOutlined,
  UpOutlined,
  WechatOutlined,
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { computed, nextTick, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import storage from '@/utils/storage'

// Store
const billingStore = useBillingStore()
const userStore = useUserStore()
const settingsStore = useSettingsStore()

// 路由
const route = useRoute()

// 响应式引用
const rechargeFormRef = ref<HTMLElement>()
const selectedAmount = ref<number>(0)
const customAmount = ref<number | null>(null)
const selectedPaymentMethod = ref<string>('')
const isProcessing = ref(false)
const historyLoading = ref(false)
const detailDrawerVisible = ref(false)
const selectedRecord = ref<RechargeRecord | null>(null)

// 发票申请相关状态
const invoiceDialogVisible = ref(false)
const invoiceFormLoading = ref(false)
const currentInvoiceRecord = ref<RechargeRecord | null>(null)

// 浮动面板相关状态
const quickPanelVisible = ref(false)
const quickPanelMode = ref<'quick' | 'full'>('quick')
const historyCollapsed = ref(true)
const packageCollapsed = ref(true)
const initialAmount = ref<number>(0)

// 充值记录相关
const rechargeHistory = ref<RechargeRecord[]>([])
const historyFilters = ref({
  status: '',
  dateRange: null as any,
})

const historyPagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
})

// 充值记录显示数量控制
const historyDisplayCount = computed(() => historyCollapsed.value ? 3 : rechargeHistory.value.length)
const displayedHistory = computed(() => rechargeHistory.value.slice(0, historyDisplayCount.value))

// 预设充值金额
// 注释：暂时禁用赠额功能 - bonus字段已注释
const presetAmounts = ref<Array<{ value: number /* bonus: number */ }>>([])

// 计算属性
const currency = computed(() => billingStore.currency)
const paymentMethods = computed(() => billingStore.paymentMethods || [])

const minRechargeAmount = computed(() => 1)
const maxRechargeAmount = computed(() => 10000)

const finalAmount = computed(() => {
  return selectedAmount.value > 0 ? selectedAmount.value : (customAmount.value || 0)
})

// 注释：暂时禁用赠额功能 - bonusAmount计算属性已注释
// const bonusAmount = computed(() => {
//   const preset = presetAmounts.value.find(p => p.value === selectedAmount.value)
//   return preset?.bonus || 0
// })

const canSubmitRecharge = computed(() => {
  return finalAmount.value >= minRechargeAmount.value
    && selectedPaymentMethod.value
    && !isProcessing.value
})

// 表格列定义
const historyColumns: TableColumnsType = [
  {
    title: '充值单号',
    dataIndex: 'paymentNo',
    key: 'paymentNo',
    width: 120,
    ellipsis: true,
    customRender: ({ record }: { record: RechargeRecord }) => {
      // 优先显示paymentNo，如果没有则显示id
      return record.paymentNo || record.id
    }
  },
  {
    title: '充值金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 120,
    align: 'right',
  },
  {
    title: '支付方式',
    dataIndex: 'paymentMethod',
    key: 'paymentMethod',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 160,
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
  },
]

// 方法
function toggleQuickPanel() {
  quickPanelVisible.value = !quickPanelVisible.value
  if (quickPanelVisible.value) {
    quickPanelMode.value = 'quick'
  }
}

function openQuickPanel(mode: 'quick' | 'full' = 'quick', amount?: number) {
  quickPanelMode.value = mode
  if (amount) {
    initialAmount.value = amount
  }
  quickPanelVisible.value = true
}

function switchPanelMode(mode: 'quick' | 'full') {
  quickPanelMode.value = mode
}

function toggleHistoryCollapse() {
  historyCollapsed.value = !historyCollapsed.value
}

function togglePackageCollapse() {
  packageCollapsed.value = !packageCollapsed.value
}

function selectAmount(amount: number) {
  selectedAmount.value = amount
  customAmount.value = null

  // 自动选择默认支付方式
  if (!selectedPaymentMethod.value && paymentMethods.value.length > 0) {
    const enabledMethod = paymentMethods.value.find(m => m.enabled)
    if (enabledMethod) {
      selectedPaymentMethod.value = enabledMethod.code
    }
  }
}

function onCustomAmountChange(value: number | null) {
  if (value && value > 0) {
    selectedAmount.value = 0

    // 自动选择默认支付方式
    if (!selectedPaymentMethod.value && paymentMethods.value.length > 0) {
      const enabledMethod = paymentMethods.value.find((m: any) => m.enabled)
      if (enabledMethod) {
        selectedPaymentMethod.value = enabledMethod.code
      }
    }
  }
}

function scrollToRechargeForm() {
  if (rechargeFormRef.value) {
    rechargeFormRef.value.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    })
  }
}

async function handleQuickRecharge(rechargeData: RechargeRequest) {
  isProcessing.value = true
  try {
    await billingStore.submitRecharge(rechargeData)

    // 立即关闭面板并重置初始金额
    quickPanelVisible.value = false
    initialAmount.value = 0

    message.success('充值订单创建成功，请完成支付')

    // 在后台刷新余额和记录
    Promise.all([
      billingStore.manualRefreshBalance(),
      loadRechargeHistory()
    ])

    // 检查是否是从 onboarding 页面来的首次登录用户
    const sourceParam = route.query.source
    if (sourceParam === 'onboarding') {
      console.log('🎯 [充值完成] 检测到来自引导页面的首次充值，更新引导状态')

      // 直接更新前端状态，标记引导已完成
      userStore.firstLoginCompleted = true
      storage.local.set('firstLoginCompleted', 'true')

      console.log('✅ [充值完成] 引导状态已更新为完成')

      // 显示完成提示
      message.success('充值成功！引导流程已完成，欢迎使用！', 3)
    }
  }
  catch (error: any) {
    console.error('充值失败:', error)
    message.error(error.message || '充值失败，请稍后重试')
  }
  finally {
    isProcessing.value = false
  }
}

async function handlePaymentSuccess(outTradeNo: string) {
  try {
    console.log('🎯 [支付成功] 检测到支付成功，订单号:', outTradeNo)

    // 刷新余额和记录
    await Promise.all([
      billingStore.manualRefreshBalance(),
      loadRechargeHistory()
    ])

    console.log('✅ [支付成功] 余额和充值记录已刷新')

    // 检查是否是从 onboarding 页面来的首次登录用户
    const sourceParam = route.query.source
    if (sourceParam === 'onboarding') {
      console.log('🎯 [支付成功] 检测到来自引导页面的首次充值，更新引导状态')

      // 直接更新前端状态，标记引导已完成
      userStore.firstLoginCompleted = true
      storage.local.set('firstLoginCompleted', 'true')

      console.log('✅ [支付成功] 引导状态已更新为完成')

      // 显示完成提示
      message.success('充值成功！引导流程已完成，欢迎使用！', 3)
    }
  }
  catch (error: any) {
    console.error('处理支付成功事件失败:', error)
    // 不显示错误消息，因为支付已经成功，只是刷新数据失败
  }
}

async function submitRecharge() {
  if (!canSubmitRecharge.value) {
    return
  }

  isProcessing.value = true
  try {
    const rechargeData: RechargeRequest = {
      amount: finalAmount.value,
      paymentMethod: selectedPaymentMethod.value as 'ALIPAY' | 'WECHAT' | 'BANK_CARD' | 'OTHER',
      description: `充值 ${formatCurrency(finalAmount.value)}`,
    }

    await billingStore.submitRecharge(rechargeData)

    message.success('充值订单创建成功，请完成支付')

    // 重置表单
    selectedAmount.value = 0
    customAmount.value = null
    selectedPaymentMethod.value = ''

    // 刷新余额和记录
    await billingStore.manualRefreshBalance()
    await loadRechargeHistory()

    // 检查是否是从 onboarding 页面来的首次登录用户
    const sourceParam = route.query.source
    if (sourceParam === 'onboarding') {
      console.log('🎯 [充值完成] 检测到来自引导页面的首次充值，更新引导状态')

      // 直接更新前端状态，标记引导已完成
      userStore.firstLoginCompleted = true
      storage.local.set('firstLoginCompleted', 'true')

      console.log('✅ [充值完成] 引导状态已更新为完成')

      // 显示完成提示
      message.success('充值成功！引导流程已完成，欢迎使用！', 3)
    }
  }
  catch (error: any) {
    console.error('充值失败:', error)
    message.error(error.message || '充值失败，请稍后重试')
  }
  finally {
    isProcessing.value = false
  }
}

async function loadRechargeHistory() {
  historyLoading.value = true
  try {
    const params = {
      page: historyPagination.value.current - 1,
      size: historyPagination.value.pageSize,
      status: (historyFilters.value.status as 'PENDING' | 'SUCCESS' | 'FAILED' | 'CANCELLED' | 'REFUNDED' | undefined) || undefined,
      startDate: historyFilters.value.dateRange?.[0]?.format('YYYY-MM-DD 00:00:00'),
      endDate: historyFilters.value.dateRange?.[1]?.format('YYYY-MM-DD 23:59:59'),
    }

    await billingStore.fetchRechargeRecords(params)
    rechargeHistory.value = billingStore.rechargeRecords
    historyPagination.value = { ...billingStore.rechargeRecordsPagination }
  }
  catch (error: any) {
    console.error('加载充值记录失败:', error)
    message.error('加载充值记录失败')
  }
  finally {
    historyLoading.value = false
  }
}

async function loadRechargePresets() {
  try {
    const response = await billingStore.fetchRechargePresets()
    if (response && Array.isArray(response)) {
      // 将API返回的RechargePresetDto对象转换为前端所需的格式
      // 注释：暂时禁用赠额功能 - 移除bonus字段处理
      presetAmounts.value = response.map((preset: any) => {
        return {
          value: typeof preset.amount === 'number' ? preset.amount : Number.parseFloat(String(preset.amount || 0)),
          // bonus: typeof preset.bonusAmount === 'number' ? preset.bonusAmount : Number.parseFloat(String(preset.bonusAmount || 0)),
        }
      }).filter(item => !Number.isNaN(item.value)) // 过滤掉无效数据
    }
  }
  catch (error: any) {
    console.error('加载预设充值金额失败:', error)
    // 如果API失败，使用默认值
    // 注释：暂时禁用赠额功能 - 移除bonus字段
    presetAmounts.value = [
      { value: 10 /* bonus: 0 */ },
      { value: 50 /* bonus: 5 */ },
      { value: 100 /* bonus: 15 */ },
      { value: 500 /* bonus: 100 */ },
      { value: 1000 /* bonus: 250 */ },
      { value: 2000 /* bonus: 600 */ },
    ]
  }
}

function refreshHistory() {
  loadRechargeHistory()
}

function onHistoryFilterChange() {
  historyPagination.value.current = 1
  loadRechargeHistory()
}

function resetHistoryFilters() {
  historyFilters.value.status = ''
  historyFilters.value.dateRange = null
  historyPagination.value.current = 1
  loadRechargeHistory()
}

function onHistoryTableChange({ current, pageSize }: any) {
  historyPagination.value.current = current
  historyPagination.value.pageSize = pageSize
  loadRechargeHistory()
}

function viewRechargeDetail(record: RechargeRecord) {
  selectedRecord.value = record
  detailDrawerVisible.value = true
}

function closeDetailDrawer() {
  detailDrawerVisible.value = false
  selectedRecord.value = null
}

function continuePayment(_record: RechargeRecord) {
  message.info('继续支付功能开发中...')
  // TODO: 实现继续支付逻辑
}

async function cancelRecharge(record: RechargeRecord) {
  try {
    await billingStore.cancelRecharge(record.id)
    message.success('充值订单已取消')
    await loadRechargeHistory()
  }
  catch (error: any) {
    console.error('取消充值失败:', error)
    message.error('取消充值失败')
  }
}

function requestInvoice(record: RechargeRecord) {
  console.log('开发票请求:', record)
  currentInvoiceRecord.value = record
  invoiceDialogVisible.value = true
}

async function handleInvoiceSubmit(invoiceData: InvoiceRequest) {
  if (!currentInvoiceRecord.value) {
    message.error('充值记录信息丢失')
    return
  }

  invoiceFormLoading.value = true
  try {
    // 使用支付单号(paymentNo)而不是记录ID，后端会从数据库查询真实金额
    const requestData: InvoiceRequest = {
      ...invoiceData,
      // 使用支付单号作为outTradeNo，如果没有paymentNo则使用id
      outTradeNo: currentInvoiceRecord.value.paymentNo || currentInvoiceRecord.value.id,
      // 不传递金额，后端会根据支付记录查询
      amount: currentInvoiceRecord.value.amount
    }

    // 清理空字符串字段，避免后端验证错误
    const cleanedData = { ...requestData }
    Object.keys(cleanedData).forEach(key => {
      if (typeof cleanedData[key as keyof InvoiceRequest] === 'string' &&
          cleanedData[key as keyof InvoiceRequest] === '') {
        delete cleanedData[key as keyof InvoiceRequest]
      }
    })

    console.log('发票申请数据:', cleanedData)

    const response = await InvoiceAPI.applyInvoice(cleanedData)

    if (response.success) {
      message.success('发票申请提交成功！')
      invoiceDialogVisible.value = false
      currentInvoiceRecord.value = null

      // 可以跳转到发票管理页面或显示申请结果
      console.log('发票申请结果:', response.data)
    } else {
      message.error(response.message || '发票申请失败')
    }
  } catch (error: any) {
    console.error('发票申请失败:', error)
    message.error(error.message || '发票申请失败，请稍后重试')
  } finally {
    invoiceFormLoading.value = false
  }
}

function handleInvoiceCancel() {
  invoiceDialogVisible.value = false
  currentInvoiceRecord.value = null
}

// 工具方法
function getPaymentIcon(code: string) {
  const iconMap: Record<string, any> = {
    alipay: AlipayOutlined,
    wechat: WechatOutlined,
  }
  return iconMap[code] || CreditCardOutlined
}

function getPaymentMethodName(code: string) {
  const method = paymentMethods.value.find(m => m.code === code)
  return method?.name || code
}

function getStatusColor(status: string) {
  const colorMap: Record<string, string> = {
    PENDING: 'orange',
    SUCCESS: 'green',
    FAILED: 'red',
    CANCELLED: 'default',
    REFUNDED: 'blue',
  }
  return colorMap[status] || 'default'
}

function getStatusText(status: string) {
  const textMap: Record<string, string> = {
    // PENDING: '待支付',
    PENDING: '未支付',
    SUCCESS: '已支付',
    FAILED: '支付失败',
    CANCELLED: '已取消',
    REFUNDED: '已退款',
  }
  return textMap[status] || status
}

function formatDateTime(dateTime: string) {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 处理URL参数设置充值金额
function handleUrlParams() {
  const amountParam = route.query.amount
  const sourceParam = route.query.source
  const planParam = route.query.plan

  if (amountParam && typeof amountParam === 'string') {
    const amount = Number.parseFloat(amountParam)
    if (!Number.isNaN(amount) && amount > 0) {
      // 如果是从 onboarding 页面来的，显示提示信息
      if (sourceParam === 'onboarding' && planParam) {
        message.success(`已为您预设充值金额：¥${amount}（来自套餐：${planParam}）`)
      }

      // 直接打开充值面板，并设置金额
      openQuickPanel('full', amount)
    }
  }
}

// 生命周期
onMounted(async () => {
  // 确保使用中文
  if (settingsStore.lang !== 'zh-cn') {
    settingsStore.setDefaultLang('zh-cn')
  }

  // 初始化数据
  await Promise.all([
    billingStore.fetchAccount(),
    billingStore.fetchPaymentMethods(),
    loadRechargeHistory(),
    loadRechargePresets(),
  ])

  // 处理URL参数（在预设金额加载完成后）
  handleUrlParams()
})
</script>

<template>
  <div class="recharge-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">
        账户充值
      </h1>
      <p class="page-description">
        为您的账户充值，享受更多AI服务
      </p>
    </div>

    <div class="recharge-container">
      <!-- 当前余额展示 + 快捷充值按钮 -->
      <div class="balance-section">
        <BalanceDisplay
          size="large"
          :show-package-info="false"
          @recharge-click="toggleQuickPanel"
        />
        <div class="quick-recharge-actions">
          <a-button
            type="primary"
            size="large"
            class="quick-recharge-btn"
            @click="openQuickPanel('quick')"
          >
            <template #icon>
              <CreditCardOutlined />
            </template>
            快速充值
          </a-button>
          <a-button
            size="large"
            class="detail-recharge-btn"
            @click="openQuickPanel('full')"
          >
            详细充值
          </a-button>
        </div>
      </div>

      <!-- 套餐信息（可折叠） -->
      <!-- <div class="package-section">
        <a-card class="package-card">
          <template #title>
            <div class="card-header">
              <span>当前套餐</span>
              <a-button
                type="text"
                size="small"
                @click="togglePackageCollapse"
              >
                <template #icon>
                  <component :is="packageCollapsed ? DownOutlined : UpOutlined" />
                </template>
                {{ packageCollapsed ? '展开' : '收起' }}
              </a-button>
            </div>
          </template>
          <div v-show="!packageCollapsed">
            <CurrentPackageInfo size="default" />
          </div>
        </a-card>
      </div> -->

      <!-- 充值记录（可折叠） -->
      <div class="recharge-history-section">
        <a-card class="history-card">
          <template #title>
            <div class="card-header">
              <span>充值记录</span>
              <a-button
                type="text"
                size="small"
                @click="toggleHistoryCollapse"
              >
                <template #icon>
                  <component :is="historyCollapsed ? DownOutlined : UpOutlined" />
                </template>
                {{ historyCollapsed ? '展开全部' : '收起' }}
                <span v-if="historyCollapsed && historyPagination.total > 3">
                  (共{{ historyPagination.total }}条)
                </span>
              </a-button>
            </div>
          </template>
          <template #extra>
            <a-button :loading="historyLoading" @click="refreshHistory">
              <template #icon>
                <ReloadOutlined />
              </template>
              刷新
            </a-button>
          </template>

          <!-- 筛选条件（只在展开时显示） -->
          <div v-show="!historyCollapsed" class="history-filters">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-select
                  v-model:value="historyFilters.status"
                  placeholder="选择状态"
                  allow-clear
                  @change="onHistoryFilterChange"
                >
                  <a-select-option value="">
                    全部
                  </a-select-option>
                  <a-select-option value="PENDING">
                    待支付
                  </a-select-option>
                  <a-select-option value="SUCCESS">
                    已支付
                  </a-select-option>
                  <a-select-option value="FAILED">
                    支付失败
                  </a-select-option>
                  <a-select-option value="CANCELLED">
                    已取消
                  </a-select-option>
                  <a-select-option value="REFUNDED">
                    已退款
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :span="10">
                <a-range-picker
                  v-model:value="historyFilters.dateRange"
                  class="date-range"
                  format="YYYY-MM-DD"
                  @change="onHistoryFilterChange"
                />
              </a-col>
              <a-col :span="6">
                <a-button @click="resetHistoryFilters">
                  重置
                </a-button>
              </a-col>
            </a-row>
          </div>

          <!-- 充值记录表格 -->
          <a-table
            :columns="historyColumns"
            :data-source="historyCollapsed ? displayedHistory : rechargeHistory"
            :loading="historyLoading"
            :pagination="historyCollapsed ? false : historyPagination"
            row-key="id"
            class="history-table"
            @change="onHistoryTableChange"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'amount'">
                <span class="amount-cell">
                  {{ formatCurrency(record.amount, record.currency) }}
                </span>
              </template>

              <template v-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ getStatusText(record.status) }}
                </a-tag>
              </template>

              <template v-if="column.key === 'createdAt'">
                <span>{{ formatDateTime(record.createdAt) }}</span>
              </template>

              <template v-if="column.key === 'actions'">
                <a-space>
                  <a-button
                    type="link"
                    size="small"
                    @click="viewRechargeDetail(record)"
                  >
                    查看详情
                  </a-button>
                  <a-button
                    v-if="record.status === 'SUCCESS'"
                    type="link"
                    size="small"
                    @click="requestInvoice(record)"
                  >
                    开发票
                  </a-button>
                  <!-- <a-button
                    v-if="record.status === 'PENDING'"
                    type="link"
                    size="small"
                    @click="continuePayment(record)"
                  >
                    继续支付
                  </a-button> -->
                  <!-- <a-button
                    v-if="record.status === 'PENDING'"
                    type="link"
                    size="small"
                    danger
                    @click="cancelRecharge(record)"
                  >
                    取消
                  </a-button> -->
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>
      </div>
    </div>

    <!-- 充值详情抽屉 -->
    <a-drawer
      v-model:open="detailDrawerVisible"
      title="充值详情"
      placement="right"
      :width="500"
      @close="closeDetailDrawer"
    >
      <div v-if="selectedRecord" class="recharge-detail">
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="充值单号">
            {{ selectedRecord.id }}
          </a-descriptions-item>
          <a-descriptions-item v-if="selectedRecord.paymentNo" label="支付单号">
            <span class="payment-no">{{ selectedRecord.paymentNo }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="充值金额">
            <span class="amount-value">
              {{ formatCurrency(selectedRecord.amount, selectedRecord.currency) }}
            </span>
          </a-descriptions-item>
          <a-descriptions-item label="支付方式">
            {{ getPaymentMethodName(selectedRecord.paymentMethod) }}
          </a-descriptions-item>
          <a-descriptions-item v-if="selectedRecord.platformTransactionNo" label="第三方交易号">
            <span class="platform-transaction-no">{{ selectedRecord.platformTransactionNo }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="充值状态">
            <a-tag :color="getStatusColor(selectedRecord.status)">
              {{ getStatusText(selectedRecord.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ formatDateTime(selectedRecord.createdAt) }}
          </a-descriptions-item>
          <a-descriptions-item v-if="selectedRecord.completedAt" label="完成时间">
            {{ formatDateTime(selectedRecord.completedAt) }}
          </a-descriptions-item>
          <a-descriptions-item v-if="selectedRecord.remark" label="备注">
            {{ selectedRecord.remark }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-drawer>

    <!-- 浮动快捷充值面板 -->
    <QuickRechargePanel
      v-model:visible="quickPanelVisible"
      v-model:mode="quickPanelMode"
      :currency="currency"
      :payment-methods="paymentMethods"
      :preset-amounts="presetAmounts"
      :min-recharge-amount="minRechargeAmount"
      :max-recharge-amount="maxRechargeAmount"
      :is-processing="isProcessing"
      :initial-amount="initialAmount"
      @submit-recharge="handleQuickRecharge"
      @payment-success="handlePaymentSuccess"
    />

    <!-- 发票申请对话框 -->
    <a-modal
      v-model:open="invoiceDialogVisible"
      title="申请发票"
      :width="800"
      :footer="null"
      :mask-closable="false"
      @cancel="handleInvoiceCancel"
    >
      <div v-if="currentInvoiceRecord" class="invoice-dialog-content">
        <div class="invoice-info">
          <a-descriptions :column="2" bordered size="small">
            <a-descriptions-item label="充值单号">
              {{ currentInvoiceRecord.paymentNo || currentInvoiceRecord.id }}
            </a-descriptions-item>
            <a-descriptions-item label="充值金额">
              <span class="amount-highlight">
                {{ formatCurrency(currentInvoiceRecord.amount, currentInvoiceRecord.currency) }}
              </span>
            </a-descriptions-item>
            <a-descriptions-item label="支付方式">
              {{ getPaymentMethodName(currentInvoiceRecord.paymentMethod) }}
            </a-descriptions-item>
            <a-descriptions-item label="充值时间">
              {{ formatDateTime(currentInvoiceRecord.createdAt) }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <div class="invoice-form-wrapper">
          <InvoiceForm
            :out-trade-no="currentInvoiceRecord.id"
            :amount="currentInvoiceRecord.amount"
            :loading="invoiceFormLoading"
            @submit="handleInvoiceSubmit"
            @cancel="handleInvoiceCancel"
          />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<style scoped lang="scss">
.recharge-page {
  max-width: 1200px;
  padding: 24px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 32px;
  text-align: center;

  .page-title {
    margin-bottom: 8px;
    font-size: 32px;
    font-weight: 600;
    color: var(--text-color, #262626);
  }

  .page-description {
    margin: 0;
    font-size: 16px;
    color: var(--text-color-secondary, #8c8c8c);
  }
}

.recharge-container {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.balance-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: center;

  .quick-recharge-actions {
    display: flex;
    gap: 16px;
    justify-content: center;

    .quick-recharge-btn {
      height: 48px;
      padding: 0 24px;
      font-size: 16px;
      font-weight: 600;
    }

    .detail-recharge-btn {
      height: 48px;
      padding: 0 24px;
      font-size: 16px;
    }
  }
}

// 卡片头部样式
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.package-section {
  margin-bottom: 24px;

  .package-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  }
}

.history-card {
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
}

.history-filters {
  margin-bottom: 24px;

  .date-range {
    width: 100%;
  }
}

.history-table {
  .amount-cell {
    font-weight: 600;
    color: var(--primary-color, #1890ff);
  }
}

.recharge-detail {
  .amount-value {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-color, #1890ff);
  }

  .payment-no,
  .platform-transaction-no {
    padding: 4px 8px;
    font-family: Monaco, Menlo, "Ubuntu Mono", monospace;
    font-size: 12px;
    word-break: break-all;
    cursor: text;
    user-select: all;
    background-color: var(--bg-color-secondary, #f5f5f5);
    border-radius: 4px;
  }
}

// 响应式设计
@media (width <= 768px) {
  .recharge-page {
    padding: 16px;
  }

  .page-header {
    .page-title {
      font-size: 24px;
    }
  }

  .recharge-container {
    gap: 24px;
  }

  .balance-section {
    .quick-recharge-actions {
      flex-direction: column;
      gap: 12px;
      width: 100%;

      .quick-recharge-btn,
      .detail-recharge-btn {
        width: 100%;
      }
    }
  }

  .history-filters {
    .ant-row {
      flex-direction: column;
      gap: 16px;
    }
  }
}

// 发票对话框样式
.invoice-dialog-content {
  .invoice-info {
    padding: 16px;
    margin-bottom: 24px;
    background-color: var(--bg-color-secondary, #fafafa);
    border-radius: 8px;

    .amount-highlight {
      font-size: 16px;
      font-weight: 600;
      color: var(--primary-color, #1890ff);
    }
  }

  .invoice-form-wrapper {
    max-height: 60vh;
    padding-right: 8px;
    overflow-y: auto;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: var(--bg-color-secondary, #f1f1f1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--border-color, #d9d9d9);
      border-radius: 3px;

      &:hover {
        background: var(--text-color-secondary, #8c8c8c);
      }
    }
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  .recharge-page {
    --text-color: #e8e8e8;
    --text-color-secondary: #a6a6a6;
    --border-color: #434343;
    --primary-color: #1890ff;
    --success-color: #52c41a;
    --bg-color-secondary: #262626;
  }
}
</style>
