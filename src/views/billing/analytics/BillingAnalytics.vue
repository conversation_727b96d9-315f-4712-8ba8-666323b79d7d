<script setup lang="ts">
import type { PaginatedUserData, UserDetailData } from '@/api/modules/billing'
import { billingAnalyticsApi } from '@/api/modules/billing'
import {
  ArrowDown,
  ArrowUp,
  Box,
  CircleCheck,
  Coin,
  DataLine,
  Document,
  Download,
  Money,
  QuestionFilled,
  Refresh,
  Search,
  TrophyBase,
  User,
  UserFilled,
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, nextTick, onMounted, reactive, ref } from 'vue'

// 响应式数据
const selectedTimeRange = ref('30d')
const revenueChartType = ref('daily')
const showProfitMargin = ref(false)
const qualificationViewType = ref('rate')
const searchKeyword = ref('')
const selectedPackageFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)
const tableLoading = ref(false)

// 概览数据
const overviewData = reactive({
  totalRevenue: 0,
  activeUsers: 0,
  packageSubscriptions: 0,
  qualificationRate: 0,
  revenueTrend: { type: 'up', percentage: 0 },
  usersTrend: { type: 'up', percentage: 0 },
  packagesTrend: { type: 'up', percentage: 0 },
  qualificationTrend: { type: 'up', percentage: 0 },
  // 消费统计字段
  totalUsageCount: 0,
  successUsageCount: 0,
  averageUsageCost: 0,
  averageUsageTokens: 0,
  consumingUsers: 0,
  usageCountTrend: { type: 'stable', percentage: 0 },
  averageCostTrend: { type: 'stable', percentage: 0 },
})

// 实时数据
const realTimeData = reactive({
  todayRevenue: 0,
  yesterdayRevenue: 0,
  onlineUsers: 0,
  peakUsers: 0,
  systemLoad: 0,
  alertCount: 0,
})

// 表格数据
const tableData = ref<UserDetailData[]>([])
const lastUpdateTime = ref('')

// Chart引用
const revenueChartRef = ref()
const userLayerChartRef = ref()
const roiChartRef = ref()
const qualificationChartRef = ref()

// Chart实例
let revenueChart: echarts.ECharts | null = null
let userLayerChart: echarts.ECharts | null = null
let roiChart: echarts.ECharts | null = null
let qualificationChart: echarts.ECharts | null = null

// 计算属性
const filteredTableData = computed(() => {
  let data = tableData.value

  if (searchKeyword.value) {
    data = data.filter(item =>
      item.userName.includes(searchKeyword.value)
      || item.userId.toString().includes(searchKeyword.value),
    )
  }

  if (selectedPackageFilter.value) {
    data = data.filter(item => item.packageType === selectedPackageFilter.value)
  }

  return data
})

// 生命周期
onMounted(async () => {
  await initializeData()
  await nextTick()
  initializeCharts()
  startRealTimeUpdates()
})

// 方法
async function initializeData() {
  try {
    await Promise.all([
      loadOverviewData(),
      loadRealTimeData(),
      loadTableData(),
    ])
  }
  catch (error) {
    ElMessage.error('数据加载失败')
    console.error('Initialize data error:', error)
  }
}

async function loadOverviewData() {
  try {
    const response = await billingAnalyticsApi.getOverviewData(selectedTimeRange.value)
    Object.assign(overviewData, response.data)
  }
  catch (error) {
    console.error('Load overview data error:', error)
  }
}

async function loadRealTimeData() {
  try {
    const response = await billingAnalyticsApi.getRealTimeMetrics()
    Object.assign(realTimeData, response.data)
    lastUpdateTime.value = new Date().toLocaleTimeString()
  }
  catch (error) {
    console.error('Load real-time data error:', error)
  }
}

async function loadTableData() {
  tableLoading.value = true
  try {
    const response = await billingAnalyticsApi.getUserList({
      page: currentPage.value,
      size: pageSize.value,
      keyword: searchKeyword.value,
      packageType: selectedPackageFilter.value,
      timeRange: selectedTimeRange.value,
    })
    tableData.value = response.data.content
    totalCount.value = response.data.totalElements
  }
  catch (error) {
    console.error('Load table data error:', error)
  }
  finally {
    tableLoading.value = false
  }
}

function initializeCharts() {
  initRevenueChart()
  initUserLayerChart()
  initROIChart()
  initQualificationChart()
}

async function initRevenueChart() {
  if (!revenueChartRef.value) {
    return
  }

  revenueChart = echarts.init(revenueChartRef.value)

  try {
    const response = await billingAnalyticsApi.getRevenueTrend(
      revenueChartType.value as 'daily' | 'weekly' | 'monthly',
      selectedTimeRange.value,
    )

    const option = {
      title: {
        text: '收入趋势',
        left: 'center',
        textStyle: { fontSize: 14 },
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          return `${params[0].name}<br/>收入: ¥${formatNumber(params[0].value)}`
        },
      },
      xAxis: {
        type: 'category',
        data: response.data.dates,
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: (value: number) => `¥${formatNumber(value)}`,
        },
      },
      series: [{
        name: '收入',
        type: 'line',
        smooth: true,
        data: response.data.values,
        lineStyle: { color: '#409EFF' },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
            { offset: 1, color: 'rgba(64, 158, 255, 0.1)' },
          ]),
        },
      }],
    }

    revenueChart.setOption(option)
  }
  catch (error) {
    console.error('Init revenue chart error:', error)
  }
}

async function initUserLayerChart() {
  if (!userLayerChartRef.value) {
    return
  }

  userLayerChart = echarts.init(userLayerChartRef.value)

  try {
    const response = await billingAnalyticsApi.getUserLayerData(selectedTimeRange.value)

    const option = {
      title: {
        text: '用户分层',
        left: 'center',
        textStyle: { fontSize: 14 },
      },
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)',
      },
      series: [{
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '60%'],
        data: response.data.layers,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      }],
    }

    userLayerChart.setOption(option)
  }
  catch (error) {
    console.error('Init user layer chart error:', error)
  }
}

async function initROIChart() {
  if (!roiChartRef.value) {
    return
  }

  roiChart = echarts.init(roiChartRef.value)

  try {
    const response = await billingAnalyticsApi.getPackageROIAnalysis(selectedTimeRange.value)

    const option = {
      title: {
        text: '套餐ROI分析',
        left: 'center',
        textStyle: { fontSize: 14 },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
      },
      xAxis: {
        type: 'category',
        data: response.data.packages.map(pkg => pkg.name),
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: showProfitMargin.value ? '{value}%' : '¥{value}',
        },
      },
      series: [{
        name: showProfitMargin.value ? '利润率' : '收入',
        type: 'bar',
        data: showProfitMargin.value
          ? response.data.packages.map(pkg => pkg.profitMargin)
          : response.data.packages.map(pkg => pkg.revenue),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#67C23A' },
            { offset: 1, color: '#85CE61' },
          ]),
        },
      }],
    }

    roiChart.setOption(option)
  }
  catch (error) {
    console.error('Init ROI chart error:', error)
  }
}

async function initQualificationChart() {
  if (!qualificationChartRef.value) {
    return
  }

  qualificationChart = echarts.init(qualificationChartRef.value)

  try {
    const response = await billingAnalyticsApi.getQualificationAnalysis(selectedTimeRange.value)

    const option = qualificationViewType.value === 'rate'
      ? {
          title: {
            text: '达标率趋势',
            left: 'center',
            textStyle: { fontSize: 14 },
          },
          tooltip: {
            trigger: 'axis',
            formatter: (params: any) => `${params[0].name}<br/>达标率: ${params[0].value}%`,
          },
          xAxis: {
            type: 'category',
            data: response.data.qualificationRate.dates,
          },
          yAxis: {
            type: 'value',
            min: 0,
            max: 100,
            axisLabel: { formatter: '{value}%' },
          },
          series: [{
            name: '达标率',
            type: 'line',
            data: response.data.qualificationRate.rates,
            lineStyle: { color: '#F56C6C' },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(245, 108, 108, 0.3)' },
                { offset: 1, color: 'rgba(245, 108, 108, 0.1)' },
              ]),
            },
          }],
        }
      : {
          title: {
            text: '消费分布',
            left: 'center',
            textStyle: { fontSize: 14 },
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: { type: 'shadow' },
          },
          xAxis: {
            type: 'category',
            data: response.data.consumptionDistribution.ranges,
          },
          yAxis: {
            type: 'value',
          },
          series: [{
            name: '用户数',
            type: 'bar',
            data: response.data.consumptionDistribution.counts,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#E6A23C' },
                { offset: 1, color: '#F0B90B' },
              ]),
            },
          }],
        }

    qualificationChart.setOption(option)
  }
  catch (error) {
    console.error('Init qualification chart error:', error)
  }
}

function startRealTimeUpdates() {
  setInterval(async () => {
    await loadRealTimeData()
  }, 30000) // 每30秒更新一次
}

// 事件处理
async function handleTimeRangeChange() {
  await loadOverviewData()
  await loadTableData()
  initializeCharts()
}

async function refreshData() {
  await initializeData()
  initializeCharts()
  ElMessage.success('数据刷新成功')
}

async function exportData() {
  try {
    const response = await billingAnalyticsApi.exportAnalyticsReport(
      selectedTimeRange.value,
      'excel',
    )

    // 下载文件
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.download = `billing-analytics-${selectedTimeRange.value}.xlsx`
    link.click()

    ElMessage.success('导出成功')
  }
  catch (error) {
    ElMessage.error('导出失败')
    console.error('Export error:', error)
  }
}

function handleSizeChange(size: number) {
  pageSize.value = size
  loadTableData()
}

function handleCurrentChange(page: number) {
  currentPage.value = page
  loadTableData()
}

function viewUserDetail(user: UserDetailData) {
  // 跳转到用户详情页
  window.open(`/billing/user/${user.userId}`, '_blank')
}

function showAlerts() {
  ElMessageBox.alert('系统检测到异常情况，请及时处理', '系统告警', {
    confirmButtonText: '查看详情',
    callback: () => {
      window.open('/system/alerts', '_blank')
    },
  })
}

// 工具函数
function formatNumber(num: number): string {
  return new Intl.NumberFormat('zh-CN').format(num)
}

function formatDateTime(dateStr: string): string {
  return new Date(dateStr).toLocaleString('zh-CN')
}

function formatSuccessRate(success: number, total: number): string {
  if (total === 0) {
    return '0'
  }
  return ((success / total) * 100).toFixed(1)
}

function formatPercentage(part: number, total: number): string {
  if (total === 0) {
    return '0'
  }
  return ((part / total) * 100).toFixed(1)
}

function formatTokens(tokens: number): string {
  if (tokens >= 1000000) {
    return `${(tokens / 1000000).toFixed(1)}M`
  }
  else if (tokens >= 1000) {
    return `${(tokens / 1000).toFixed(1)}K`
  }
  return tokens.toString()
}

function getPackageTagType(packageName: string): 'success' | 'info' | 'warning' | 'danger' | 'primary' | undefined {
  const typeMap: Record<string, 'success' | 'info' | 'warning' | 'danger' | 'primary' | undefined> = {
    基础套餐: undefined,
    高级套餐: 'success',
    企业套餐: 'warning',
  }
  return typeMap[packageName] || undefined
}

function getLoadLevel(load: number): string {
  if (load < 50) {
    return 'success'
  }
  if (load < 80) {
    return 'warning'
  }
  return 'danger'
}
</script>

<template>
  <div class="billing-analytics">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        计费数据分析
      </h1>
      <div class="header-actions">
        <el-select
          v-model="selectedTimeRange"
          placeholder="选择时间范围"
          style="width: 150px; margin-right: 12px;"
          @change="handleTimeRangeChange"
        >
          <el-option label="最近7天" value="7d" />
          <el-option label="最近30天" value="30d" />
          <el-option label="最近90天" value="90d" />
          <el-option label="最近一年" value="1y" />
        </el-select>
        <el-button type="primary" :icon="Refresh" @click="refreshData">
          刷新数据
        </el-button>
        <el-button :icon="Download" @click="exportData">
          导出报告
        </el-button>
      </div>
    </div>

    <!-- 数据概览卡片 -->
    <div class="overview-cards">
      <div class="card-row">
        <div class="overview-card">
          <div class="card-content">
            <div class="card-icon revenue">
              <el-icon><Money /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">
                总收入
              </div>
              <div class="card-value">
                ¥{{ formatNumber(overviewData.totalRevenue) }}
              </div>
              <div class="card-trend" :class="overviewData.revenueTrend.type">
                <el-icon v-if="overviewData.revenueTrend.type === 'up'">
                  <ArrowUp />
                </el-icon>
                <el-icon v-else-if="overviewData.revenueTrend.type === 'down'">
                  <ArrowDown />
                </el-icon>
                <span>{{ overviewData.revenueTrend.percentage }}%</span>
              </div>
            </div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-content">
            <div class="card-icon users">
              <el-icon><User /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">
                活跃用户
              </div>
              <div class="card-value">
                {{ formatNumber(overviewData.activeUsers) }}
              </div>
              <div class="card-trend" :class="overviewData.usersTrend.type">
                <el-icon v-if="overviewData.usersTrend.type === 'up'">
                  <ArrowUp />
                </el-icon>
                <el-icon v-else-if="overviewData.usersTrend.type === 'down'">
                  <ArrowDown />
                </el-icon>
                <span>{{ overviewData.usersTrend.percentage }}%</span>
              </div>
            </div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-content">
            <div class="card-icon packages">
              <el-icon><Box /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">
                套餐订阅
              </div>
              <div class="card-value">
                {{ formatNumber(overviewData.packageSubscriptions) }}
              </div>
              <div class="card-trend" :class="overviewData.packagesTrend.type">
                <el-icon v-if="overviewData.packagesTrend.type === 'up'">
                  <ArrowUp />
                </el-icon>
                <el-icon v-else-if="overviewData.packagesTrend.type === 'down'">
                  <ArrowDown />
                </el-icon>
                <span>{{ overviewData.packagesTrend.percentage }}%</span>
              </div>
            </div>
          </div>
        </div>

        <div class="overview-card">
          <div class="card-content">
            <div class="card-icon qualification">
              <el-icon><TrophyBase /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">
                达标率
              </div>
              <div class="card-value">
                {{ overviewData.qualificationRate }}%
              </div>
              <div class="card-trend" :class="overviewData.qualificationTrend.type">
                <el-icon v-if="overviewData.qualificationTrend.type === 'up'">
                  <ArrowUp />
                </el-icon>
                <el-icon v-else-if="overviewData.qualificationTrend.type === 'down'">
                  <ArrowDown />
                </el-icon>
                <span>{{ overviewData.qualificationTrend.percentage }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 消费行为分析区域 -->
    <div class="consumption-stats-section">
      <div class="section-header">
        <h2>消费行为分析</h2>
        <div class="section-description">
          基于用户实际消费行为的统计分析
        </div>
      </div>

      <div class="consumption-cards">
        <div class="consumption-card">
          <div class="card-content">
            <div class="card-icon consumption">
              <el-icon><Document /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">
                总消费次数
              </div>
              <div class="card-value">
                {{ formatNumber(overviewData.totalUsageCount) }}
              </div>
              <div class="card-trend" :class="overviewData.usageCountTrend.type">
                <el-icon v-if="overviewData.usageCountTrend.type === 'up'">
                  <ArrowUp />
                </el-icon>
                <el-icon v-else-if="overviewData.usageCountTrend.type === 'down'">
                  <ArrowDown />
                </el-icon>
                <span>{{ overviewData.usageCountTrend.percentage }}%</span>
              </div>
            </div>
          </div>
        </div>

        <div class="consumption-card">
          <div class="card-content">
            <div class="card-icon success">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">
                成功消费次数
              </div>
              <div class="card-value">
                {{ formatNumber(overviewData.successUsageCount) }}
              </div>
              <div class="card-sub">
                成功率: {{ formatSuccessRate(overviewData.successUsageCount, overviewData.totalUsageCount) }}%
              </div>
            </div>
          </div>
        </div>

        <div class="consumption-card">
          <div class="card-content">
            <div class="card-icon average">
              <el-icon><Coin /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">
                平均消费金额
              </div>
              <div class="card-value">
                ¥{{ formatNumber(overviewData.averageUsageCost) }}
              </div>
              <div class="card-trend" :class="overviewData.averageCostTrend.type">
                <el-icon v-if="overviewData.averageCostTrend.type === 'up'">
                  <ArrowUp />
                </el-icon>
                <el-icon v-else-if="overviewData.averageCostTrend.type === 'down'">
                  <ArrowDown />
                </el-icon>
                <span>{{ overviewData.averageCostTrend.percentage }}%</span>
              </div>
            </div>
          </div>
        </div>

        <div class="consumption-card">
          <div class="card-content">
            <div class="card-icon users">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">
                消费用户数量
              </div>
              <div class="card-value">
                {{ formatNumber(overviewData.consumingUsers) }}
              </div>
              <div class="card-sub">
                占活跃用户: {{ formatPercentage(overviewData.consumingUsers, overviewData.activeUsers) }}%
              </div>
            </div>
          </div>
        </div>

        <div class="consumption-card">
          <div class="card-content">
            <div class="card-icon tokens">
              <el-icon><DataLine /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">
                平均Token消耗
              </div>
              <div class="card-value">
                {{ formatTokens(overviewData.averageUsageTokens) }}
              </div>
              <div class="card-sub">
                每次消费平均
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="charts-row">
        <!-- 收入趋势图 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>收入趋势分析</h3>
            <el-select v-model="revenueChartType" size="small" style="width: 120px;">
              <el-option label="日收入" value="daily" />
              <el-option label="周收入" value="weekly" />
              <el-option label="月收入" value="monthly" />
            </el-select>
          </div>
          <div class="chart-content">
            <div ref="revenueChartRef" class="chart-container" />
          </div>
        </div>

        <!-- 用户分层分析 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>用户分层分析</h3>
            <el-tooltip content="按消费金额分层的用户分布" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
          <div class="chart-content">
            <div ref="userLayerChartRef" class="chart-container" />
          </div>
        </div>
      </div>

      <div class="charts-row">
        <!-- 套餐ROI分析 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>套餐ROI分析</h3>
            <el-switch
              v-model="showProfitMargin"
              active-text="显示利润率"
              inactive-text="显示收入"
            />
          </div>
          <div class="chart-content">
            <div ref="roiChartRef" class="chart-container" />
          </div>
        </div>

        <!-- 资格达标分析 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>资格达标分析</h3>
            <el-radio-group v-model="qualificationViewType" size="small">
              <el-radio-button value="rate">
                达标率
              </el-radio-button>
              <el-radio-button value="distribution">
                消费分布
              </el-radio-button>
            </el-radio-group>
          </div>
          <div class="chart-content">
            <div ref="qualificationChartRef" class="chart-container" />
          </div>
        </div>
      </div>
    </div>

    <!-- 实时监控面板 -->
    <div class="real-time-section">
      <div class="section-header">
        <h2>实时监控</h2>
        <div class="update-info">
          <el-icon><Refresh /></el-icon>
          <span>最后更新: {{ lastUpdateTime }}</span>
        </div>
      </div>

      <div class="real-time-metrics">
        <div class="metric-card">
          <div class="metric-label">
            今日收入
          </div>
          <div class="metric-value primary">
            ¥{{ formatNumber(realTimeData.todayRevenue) }}
          </div>
          <div class="metric-sub">
            昨日: ¥{{ formatNumber(realTimeData.yesterdayRevenue) }}
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-label">
            在线用户
          </div>
          <div class="metric-value success">
            {{ realTimeData.onlineUsers }}
          </div>
          <div class="metric-sub">
            峰值: {{ realTimeData.peakUsers }}
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-label">
            系统负载
          </div>
          <div class="metric-value" :class="getLoadLevel(realTimeData.systemLoad)">
            {{ realTimeData.systemLoad }}%
          </div>
          <div class="metric-sub">
            CPU & 内存
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-label">
            异常告警
          </div>
          <div class="metric-value warning">
            {{ realTimeData.alertCount }}
          </div>
          <div class="metric-sub">
            <el-button
              v-if="realTimeData.alertCount > 0"
              link
              size="small"
              @click="showAlerts"
            >
              查看详情
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <div class="section-header">
        <h2>详细数据</h2>
        <div class="table-actions">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索用户、套餐..."
            style="width: 200px; margin-right: 12px;"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-select v-model="selectedPackageFilter" placeholder="筛选套餐" style="width: 120px;">
            <el-option label="全部套餐" value="" />
            <el-option label="基础套餐" value="basic" />
            <el-option label="高级套餐" value="premium" />
            <el-option label="企业套餐" value="enterprise" />
          </el-select>
        </div>
      </div>

      <el-table
        v-loading="tableLoading"
        :data="filteredTableData"
        height="400"
        stripe
        border
      >
        <el-table-column prop="userId" label="用户ID" width="80" />
        <el-table-column prop="userName" label="用户名" width="120" />
        <el-table-column prop="packageName" label="套餐" width="100">
          <template #default="{ row }">
            <el-tag :type="getPackageTagType(row.packageName)">
              {{ row.packageName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="monthlySpending" label="月消费" width="100" sortable>
          <template #default="{ row }">
            ¥{{ formatNumber(row.monthlySpending) }}
          </template>
        </el-table-column>
        <el-table-column prop="qualificationStatus" label="达标状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.qualificationStatus === 'QUALIFIED' ? 'success' : 'warning'">
              {{ row.qualificationStatus === 'QUALIFIED' ? '已达标' : '未达标' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lifetimeValue" label="生命周期价值" width="120" sortable>
          <template #default="{ row }">
            ¥{{ formatNumber(row.lifetimeValue) }}
          </template>
        </el-table-column>
        <el-table-column prop="lastActiveTime" label="最后活跃" width="150">
          <template #default="{ row }">
            {{ formatDateTime(row.lastActiveTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button link size="small" @click="viewUserDetail(row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="table-pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[20, 50, 100, 200]"
          :total="totalCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.billing-analytics {
  padding: 20px;

  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;

    .page-title {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }

    .header-actions {
      display: flex;
      align-items: center;
    }
  }

  .overview-cards {
    margin-bottom: 24px;

    .card-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 16px;
    }

    .overview-card {
      overflow: hidden;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgb(0 0 0 / 10%);
      transition: box-shadow 0.3s ease;

      &:hover {
        box-shadow: 0 4px 20px rgb(0 0 0 / 15%);
      }

      .card-content {
        display: flex;
        align-items: center;
        padding: 24px;

        .card-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 48px;
          height: 48px;
          margin-right: 16px;
          border-radius: 50%;

          .el-icon {
            font-size: 24px;
            color: white;
          }

          &.revenue { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
          &.users { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
          &.packages { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
          &.qualification { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        }

        .card-info {
          flex: 1;

          .card-title {
            margin-bottom: 8px;
            font-size: 14px;
            color: #909399;
          }

          .card-value {
            margin-bottom: 4px;
            font-size: 28px;
            font-weight: 600;
            color: #303133;
          }

          .card-trend {
            display: flex;
            align-items: center;
            font-size: 12px;

            .el-icon {
              margin-right: 4px;
            }

            &.up { color: #67c23a; }
            &.down { color: #f56c6c; }
            &.stable { color: #909399; }
          }
        }
      }
    }
  }

  .consumption-stats-section {
    margin-bottom: 24px;

    .section-header {
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      margin-bottom: 16px;

      h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }

      .section-description {
        font-size: 14px;
        color: #909399;
      }
    }

    .consumption-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 16px;
    }

    .consumption-card {
      overflow: hidden;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgb(0 0 0 / 10%);
      transition: box-shadow 0.3s ease;

      &:hover {
        box-shadow: 0 4px 20px rgb(0 0 0 / 15%);
      }

      .card-content {
        display: flex;
        align-items: center;
        padding: 24px;

        .card-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 48px;
          height: 48px;
          margin-right: 16px;
          border-radius: 50%;

          .el-icon {
            font-size: 24px;
            color: white;
          }

          &.consumption { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
          &.success { background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); }
          &.average { background: linear-gradient(135deg, #f2994a 0%, #f2c94c 100%); }
          &.users { background: linear-gradient(135deg, #c471ed 0%, #f64f59 100%); }
          &.tokens { background: linear-gradient(135deg, #12c2e9 0%, #c471ed 100%); }
        }

        .card-info {
          flex: 1;

          .card-title {
            margin-bottom: 8px;
            font-size: 14px;
            color: #909399;
          }

          .card-value {
            margin-bottom: 4px;
            font-size: 28px;
            font-weight: 600;
            color: #303133;
          }

          .card-trend {
            display: flex;
            align-items: center;
            font-size: 12px;

            .el-icon {
              margin-right: 4px;
            }

            &.up { color: #67c23a; }
            &.down { color: #f56c6c; }
            &.stable { color: #909399; }
          }

          .card-sub {
            font-size: 12px;
            color: #c0c4cc;
          }
        }
      }
    }
  }

  .charts-section {
    margin-bottom: 24px;

    .charts-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .chart-card {
      overflow: hidden;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgb(0 0 0 / 10%);

      .chart-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 20px;
        border-bottom: 1px solid #ebeef5;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }
      }

      .chart-content {
        padding: 20px;

        .chart-container {
          width: 100%;
          height: 300px;
        }
      }
    }
  }

  .real-time-section {
    margin-bottom: 24px;

    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }

      .update-info {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #909399;

        .el-icon {
          margin-right: 4px;
        }
      }
    }

    .real-time-metrics {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;

      .metric-card {
        padding: 20px;
        text-align: center;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgb(0 0 0 / 10%);

        .metric-label {
          margin-bottom: 8px;
          font-size: 14px;
          color: #909399;
        }

        .metric-value {
          margin-bottom: 4px;
          font-size: 24px;
          font-weight: 600;

          &.primary { color: #409eff; }
          &.success { color: #67c23a; }
          &.warning { color: #e6a23c; }
          &.danger { color: #f56c6c; }
        }

        .metric-sub {
          font-size: 12px;
          color: #c0c4cc;
        }
      }
    }
  }

  .table-section {
    overflow: hidden;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgb(0 0 0 / 10%);

    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px;
      border-bottom: 1px solid #ebeef5;

      h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }

      .table-actions {
        display: flex;
        align-items: center;
      }
    }

    .table-pagination {
      display: flex;
      justify-content: center;
      padding: 16px 20px;
    }
  }
}

@media (width <= 768px) {
  .billing-analytics {
    padding: 12px;

    .consumption-stats-section {
      .section-header {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
      }

      .consumption-cards {
        grid-template-columns: 1fr;
      }
    }

    .charts-section .charts-row {
      grid-template-columns: 1fr;
    }

    .real-time-metrics {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
  }
}
</style>
