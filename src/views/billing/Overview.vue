<script setup lang="ts">
import type { UsageRecord } from '@/types/billing'
import BalanceDisplay from '@/components/billing/BalanceDisplay.vue'
import { useBillingStore } from '@/stores/billing'
import { formatCurrency, formatNumber, getCurrencySymbol } from '@/utils/format'
import {
  DollarOutlined,
  FireOutlined,
  GiftOutlined,
  HistoryOutlined,
  TrophyOutlined,
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'

// 路由实例
const router = useRouter()

// Store
const billingStore = useBillingStore()

// 响应式引用
const loading = ref(false)
const recentRecords = ref<UsageRecord[]>([])

// 统计数据
const stats = ref({
  todayUsage: {
    cost: 0,
    tokens: 0,
    sessions: 0,
  },
  monthUsage: {
    cost: 0,
    tokens: 0,
    sessions: 0,
  },
  totalUsage: {
    cost: 0,
    tokens: 0,
    sessions: 0,
  },
  recentDays: [] as Array<{
    date: string
    cost: number
    tokens: number
  }>,
})

// 计算属性
const currency = computed(() => billingStore.currency)
const giftBalance = computed(() => billingStore.giftBalance)
const _rechargedBalance = computed(() => billingStore.rechargedBalance)
const freeTokens = computed(() => billingStore.freeTokens)
const remainingFreeTokens = computed(() => billingStore.remainingFreeTokens)

const quickActions = [
  {
    title: '账户充值',
    description: '为账户充值，继续使用AI服务',
    icon: DollarOutlined,
    color: '#52c41a',
    route: '/billing/recharge',
  },
  {
    title: '消费记录',
    description: '查看详细的消费记录和统计',
    icon: HistoryOutlined,
    color: '#1890ff',
    route: '/billing/usage-history',
  },
  {
    title: '费用申诉',
    description: '对消费记录有疑问？提交申诉',
    icon: TrophyOutlined,
    color: '#fa8c16',
    route: '/billing/appeal',
  },
]

// 方法
async function loadOverviewData() {
  loading.value = true
  try {
    // 加载基础数据
    await Promise.all([
      billingStore.fetchAccount(),
      billingStore.fetchConfiguration(),
      billingStore.fetchStatistics(),
    ])

    // 加载最近使用记录
    await billingStore.fetchUsageRecords({ page: 0, size: 5 })
    recentRecords.value = billingStore.usageRecords

    // 简化统计数据显示，基于已有的数据
    if (billingStore.statistics) {
      stats.value.totalUsage.cost = billingStore.statistics.totalCost
      stats.value.totalUsage.tokens = billingStore.statistics.totalTokens
      stats.value.totalUsage.sessions = billingStore.statistics.conversationCount
    }
  }
  catch (error: any) {
    console.error('加载概览数据失败:', error)
    message.error('加载数据失败，请稍后重试')
  }
  finally {
    loading.value = false
  }
}

function navigateToAction(route: string) {
  // 使用 Vue Router 进行导航
  router.push(route)
}

function formatDateTime(dateTime: string) {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(async () => {
  await loadOverviewData()
})
</script>

<template>
  <div class="billing-overview">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">
        计费概览
      </h1>
      <p class="page-description">
        查看您的账户余额和消费统计
      </p>
    </div>

    <div class="overview-content">
      <!-- 余额展示区域 -->
      <div class="balance-section">
        <BalanceDisplay
          size="large"
          @recharge-click="navigateToAction('/billing/recharge')"
        />
      </div>

      <!-- 统计卡片 -->
      <div class="stats-section">
        <a-row :gutter="24">
          <a-col :xs="24" :sm="6">
            <a-card class="stat-card">
              <a-statistic
                title="今日消费"
                :value="stats.todayUsage.cost"
                :precision="2"
                :loading="loading"
              >
                <template #prefix>
                  {{ getCurrencySymbol(currency) }}
                </template>
                <template #suffix>
                  <div class="stat-suffix">
                    <FireOutlined style="color: #f50;" />
                    <span class="stat-tokens">{{ formatNumber(stats.todayUsage.tokens) }} Tokens</span>
                  </div>
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="6">
            <a-card class="stat-card">
              <a-statistic
                title="本月消费"
                :value="stats.monthUsage.cost"
                :precision="2"
                :loading="loading"
              >
                <template #prefix>
                  {{ getCurrencySymbol(currency) }}
                </template>
                <template #suffix>
                  <div class="stat-suffix">
                    <HistoryOutlined style="color: #1890ff;" />
                    <span class="stat-tokens">{{ formatNumber(stats.monthUsage.tokens) }} Tokens</span>
                  </div>
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="6">
            <a-card class="stat-card">
              <a-statistic
                title="累计消费"
                :value="stats.totalUsage.cost"
                :precision="2"
                :loading="loading"
              >
                <template #prefix>
                  {{ getCurrencySymbol(currency) }}
                </template>
                <template #suffix>
                  <div class="stat-suffix">
                    <TrophyOutlined style="color: #52c41a;" />
                    <span class="stat-tokens">{{ formatNumber(stats.totalUsage.tokens) }} Tokens</span>
                  </div>
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="6">
            <a-card class="stat-card">
              <a-statistic
                title="赠送余额"
                :value="giftBalance"
                :precision="2"
                :loading="loading"
              >
                <template #prefix>
                  {{ getCurrencySymbol(currency) }}
                </template>
                <template #suffix>
                  <div class="stat-suffix">
                    <GiftOutlined style="color: #52c41a;" />
                  </div>
                </template>
              </a-statistic>
            </a-card>
          </a-col>
        </a-row>

        <!-- 第二行统计卡片 -->
        <a-row :gutter="24" style="margin-top: 24px;">
          <a-col :xs="24" :sm="6">
            <a-card class="stat-card">
              <a-statistic
                title="免费Token"
                :value="remainingFreeTokens"
                :loading="loading"
              >
                <template #suffix>
                  <div class="stat-suffix">
                    <FireOutlined style="color: #52c41a;" />
                    <span class="stat-tokens">剩余/{{ freeTokens }} 总量</span>
                  </div>
                </template>
              </a-statistic>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <a-row :gutter="24">
        <!-- 快捷操作 -->
        <a-col :xs="24" :lg="12">
          <a-card title="快捷操作" class="actions-card">
            <div class="quick-actions">
              <div
                v-for="action in quickActions"
                :key="action.title"
                class="action-item"
                @click="navigateToAction(action.route)"
              >
                <div class="action-icon" :style="{ color: action.color }">
                  <component :is="action.icon" />
                </div>
                <div class="action-content">
                  <h4 class="action-title">
                    {{ action.title }}
                  </h4>
                  <p class="action-desc">
                    {{ action.description }}
                  </p>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 最近消费记录 -->
        <a-col :xs="24" :lg="12">
          <a-card title="最近消费" class="recent-card">
            <template #extra>
              <a-button type="link" @click="navigateToAction('/billing/usage-history')">
                查看更多
              </a-button>
            </template>

            <div v-if="loading" class="loading-placeholder">
              <a-skeleton :loading="true" active :paragraph="{ rows: 3 }" />
            </div>

            <div v-else-if="recentRecords.length === 0" class="empty-placeholder">
              <a-empty description="暂无消费记录" />
            </div>

            <div v-else class="recent-records">
              <div
                v-for="record in recentRecords"
                :key="record.id"
                class="record-item"
              >
                <div class="record-info">
                  <div class="record-type">
                    计费记录 #{{ record.id }}
                  </div>
                  <div class="record-conversation">
                    会话: {{ record.conversationId ? `${record.conversationId.substring(0, 8)}...` : '未知会话' }}
                  </div>
                  <div class="record-time">
                    {{ formatDateTime(record.createdAt) }}
                  </div>
                </div>
                <div class="record-cost">
                  <div class="cost-amount">
                    {{ formatCurrency(record.totalCost, currency) }}
                  </div>
                  <div class="cost-tokens">
                    {{ formatNumber(record.totalTokens) }} Tokens
                  </div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 消费趋势图表 -->
      <div class="trends-section">
        <a-card title="近7天消费趋势" class="trends-card">
          <div class="trend-chart">
            <!-- 这里应该集成图表组件，如 ECharts -->
            <div class="chart-placeholder">
              <a-empty description="图表功能开发中..." />
            </div>
          </div>
        </a-card>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.billing-overview {
  max-width: 1400px;
  padding: 24px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 32px;
  text-align: center;

  .page-title {
    margin-bottom: 8px;
    font-size: 32px;
    font-weight: 600;
    color: var(--text-color, #262626);
  }

  .page-description {
    margin: 0;
    font-size: 16px;
    color: var(--text-color-secondary, #8c8c8c);
  }
}

.overview-content {
  .balance-section {
    margin-bottom: 32px;
  }

  .stats-section {
    margin-bottom: 32px;

    .stat-card {
      text-align: center;
      box-shadow: 0 2px 8px rgb(0 0 0 / 10%);

      .stat-suffix {
        display: flex;
        flex-direction: column;
        gap: 4px;
        align-items: center;

        .stat-tokens {
          font-size: 12px;
          color: var(--text-color-secondary, #8c8c8c);
        }
      }
    }
  }

  .actions-card,
  .recent-card,
  .trends-card {
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  }
}

.quick-actions {
  .action-item {
    display: flex;
    gap: 16px;
    align-items: center;
    padding: 16px;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
      background: var(--background-color-light, #fafafa);
    }

    .action-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48px;
      height: 48px;
      font-size: 24px;
      background: var(--background-color-light, #fafafa);
      border-radius: 50%;
    }

    .action-content {
      flex: 1;

      .action-title {
        margin: 0 0 4px;
        font-size: 16px;
        font-weight: 600;
        color: var(--text-color, #262626);
      }

      .action-desc {
        margin: 0;
        font-size: 14px;
        color: var(--text-color-secondary, #8c8c8c);
      }
    }
  }
}

.recent-records {
  .record-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-color, #f0f0f0);

    &:last-child {
      border-bottom: none;
    }

    .record-info {
      flex: 1;

      .record-type {
        font-size: 14px;
        font-weight: 500;
        color: var(--text-color, #262626);
      }

      .record-model {
        margin-top: 2px;
        font-size: 12px;
        color: var(--primary-color, #1890ff);
      }

      .record-time {
        margin-top: 2px;
        font-size: 12px;
        color: var(--text-color-secondary, #8c8c8c);
      }
    }

    .record-cost {
      text-align: right;

      .cost-amount {
        font-size: 14px;
        font-weight: 600;
        color: var(--text-color, #262626);
      }

      .cost-tokens {
        margin-top: 2px;
        font-size: 12px;
        color: var(--text-color-secondary, #8c8c8c);
      }
    }
  }
}

.trend-chart {
  .chart-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;
    background: var(--background-color-light, #fafafa);
    border-radius: 8px;
  }
}

.loading-placeholder,
.empty-placeholder {
  padding: 24px 0;
}

/* 响应式设计 */
@media (width <= 768px) {
  .billing-overview {
    padding: 16px;
  }

  .page-header {
    margin-bottom: 24px;

    .page-title {
      font-size: 24px;
    }
  }

  .overview-content {
    .stats-section {
      .ant-col {
        flex: none !important;
        width: 100% !important;
        margin-bottom: 16px;
      }
    }

    .actions-card,
    .recent-card {
      margin-bottom: 24px;
    }
  }

  .quick-actions {
    .action-item {
      padding: 12px;

      .action-icon {
        width: 40px;
        height: 40px;
        font-size: 20px;
      }

      .action-content {
        .action-title {
          font-size: 14px;
        }

        .action-desc {
          font-size: 12px;
        }
      }
    }
  }
}
</style>
