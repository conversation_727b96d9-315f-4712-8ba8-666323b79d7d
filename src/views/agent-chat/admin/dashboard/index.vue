<template>
  <div class="dashboard-page">
    <!-- 页面头部 -->
    <div class="dashboard-header">
      <div class="header-left">
        <h1 class="page-title">
          <i class="i-carbon-dashboard"></i>
          系统监控
        </h1>
        <div class="page-description">
          实时监控 AI Agent 系统运行状态和性能指标
        </div>
      </div>

      <div class="header-controls">
        <div class="time-range-selector">
          <label>时间范围:</label>
          <select v-model="timeRange" @change="handleTimeRangeChange" class="time-select">
            <option value="1h">最近1小时</option>
            <option value="24h">最近24小时</option>
            <option value="7d">最近7天</option>
            <option value="30d">最近30天</option>
          </select>
        </div>

        <button
          @click="toggleRealTime"
          class="real-time-btn"
          :class="{ active: realTimeEnabled }"
        >
          <i class="i-carbon-radio-button-checked" v-if="realTimeEnabled"></i>
          <i class="i-carbon-radio-button" v-else></i>
          实时更新
        </button>

        <button
          @click="refreshAllData"
          class="refresh-btn"
          :disabled="loading"
        >
          <i class="i-carbon-renew" :class="{ 'animate-spin': loading }"></i>
          刷新
        </button>

        <button @click="exportData" class="export-btn">
          <i class="i-carbon-download"></i>
          导出
        </button>
      </div>
    </div>

    <!-- 系统健康状态 -->
    <div class="health-banner" :class="healthStatusClass">
      <div class="health-icon">
        <i :class="healthIcon"></i>
      </div>
      <div class="health-content">
        <div class="health-title">{{ healthTitle }}</div>
        <div class="health-description">{{ healthDescription }}</div>
      </div>
      <div class="health-score">
        <div class="score-circle" :style="{ '--score': systemHealthScore }">
          <span class="score-value">{{ systemHealthScore }}</span>
          <span class="score-label">健康分</span>
        </div>
      </div>
    </div>

    <!-- 概览指标 -->
    <div class="metrics-overview">
      <MetricCard
        title="总执行次数"
        :value="metrics.totalExecutions"
        :trend="metrics.executionsTrend"
        trend-label="较上期"
        icon="i-carbon-play-filled"
        color="primary"
        :loading="metricsLoading"
        :chart-data="executionChartData"
        show-refresh
        @refresh="loadMetrics"
      />
      <MetricCard
        title="成功率"
        :value="`${metrics.successRate}%`"
        :trend="metrics.successRateTrend"
        trend-label="较上期"
        icon="i-carbon-checkmark-filled"
        color="success"
        :loading="metricsLoading"
        :chart-data="successRateChartData"
        show-refresh
        @refresh="loadMetrics"
      />
      <MetricCard
        title="平均响应时间"
        :value="`${metrics.avgResponseTime}ms`"
        :trend="metrics.responseTimeTrend"
        trend-label="较上期"
        icon="i-carbon-time"
        color="warning"
        :loading="metricsLoading"
        :chart-data="responseTimeChartData"
        show-refresh
        @refresh="loadMetrics"
      />
      <MetricCard
        title="活跃用户"
        :value="metrics.activeUsers"
        :trend="metrics.activeUsersTrend"
        trend-label="较上期"
        icon="i-carbon-user-multiple"
        color="info"
        :loading="metricsLoading"
        :chart-data="activeUsersChartData"
        show-refresh
        @refresh="loadMetrics"
      />
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="chart-row">
        <!-- 执行趋势图 -->
        <div class="chart-container">
          <div class="chart-header">
            <h3 class="chart-title">执行趋势</h3>
            <div class="chart-legend">
              <span class="legend-item success">
                <span class="legend-dot"></span>成功
              </span>
              <span class="legend-item error">
                <span class="legend-dot"></span>失败
              </span>
            </div>
          </div>
          <div class="chart-content">
            <LineChart
              :data="executionTrendData"
              :options="executionTrendOptions"
              :loading="chartDataLoading"
              :error="chartDataError"
              height="300"
              smooth
              show-points
              @retry="loadChartData"
            />
          </div>
        </div>

        <!-- Agent 使用统计 -->
        <div class="chart-container">
          <div class="chart-header">
            <h3 class="chart-title">Agent 使用情况</h3>
            <div class="chart-actions">
              <button @click="toggleChartType" class="chart-toggle">
                <i :class="agentChartType === 'pie' ? 'i-carbon-chart-column' : 'i-carbon-chart-pie'"></i>
              </button>
            </div>
          </div>
          <div class="chart-content">
            <PieChart
              v-if="agentChartType === 'pie'"
              :data="agentUsageData"
              :options="agentUsageOptions"
              :loading="chartDataLoading"
              :error="chartDataError"
              show-legend
              legend-position="right"
              @retry="loadChartData"
              @segment-click="handleAgentClick"
            />
            <BarChart
              v-else
              :data="agentUsageData"
              :options="agentUsageBarOptions"
              :loading="chartDataLoading"
              :error="chartDataError"
              height="300"
              @retry="loadChartData"
              @bar-click="handleAgentClick"
            />
          </div>
        </div>
      </div>

      <div class="chart-row">
        <!-- 响应时间分布 -->
        <div class="chart-container">
          <div class="chart-header">
            <h3 class="chart-title">响应时间分布</h3>
            <div class="chart-stats">
              <span class="stat-item">
                P95: <strong>{{ responseTimeP95 }}ms</strong>
              </span>
              <span class="stat-item">
                P99: <strong>{{ responseTimeP99 }}ms</strong>
              </span>
            </div>
          </div>
          <div class="chart-content">
            <BarChart
              :data="responseTimeData"
              :options="responseTimeOptions"
              :loading="chartDataLoading"
              :error="chartDataError"
              height="300"
              type="column"
              show-values
              @retry="loadChartData"
            />
          </div>
        </div>

        <!-- 系统资源监控 -->
        <div class="chart-container">
          <div class="chart-header">
            <h3 class="chart-title">系统资源</h3>
            <div class="resource-status" :class="resourceStatusClass">
              <i :class="resourceStatusIcon"></i>
              {{ resourceStatusText }}
            </div>
          </div>
          <div class="chart-content">
            <ResourceMonitor
              :cpu-usage="systemResources?.cpuUsage || 0"
              :memory-usage="systemResources?.memoryUsage || 0"
              :disk-usage="systemResources?.diskUsage || 0"
              :cpu-info="cpuInfo"
              :memory-info="memoryInfo"
              :disk-info="diskInfo"
              :network-info="networkInfo"
              :system-info="systemInfo"
              auto-refresh
              @refresh="loadSystemResources"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 实时日志 -->
    <div class="logs-section">
      <div class="logs-header">
        <h3 class="logs-title">
          <i class="i-carbon-log-file"></i>
          实时日志
        </h3>
        <div class="logs-controls">
          <div class="log-level-filter">
            <select v-model="logLevel" @change="handleLogFilterChange" class="level-select">
              <option value="">所有级别</option>
              <option value="ERROR">ERROR</option>
              <option value="WARN">WARN</option>
              <option value="INFO">INFO</option>
              <option value="DEBUG">DEBUG</option>
            </select>
          </div>

          <div class="log-actions">
            <button @click="toggleLogAutoScroll" class="log-action-btn" :class="{ active: logAutoScroll }">
              <i class="i-carbon-arrow-down"></i>
              自动滚动
            </button>
            <button @click="clearLogs" class="log-action-btn">
              <i class="i-carbon-clean"></i>
              清空
            </button>
            <button @click="downloadLogs" class="log-action-btn">
              <i class="i-carbon-download"></i>
              下载
            </button>
          </div>
        </div>
      </div>

      <div class="logs-content">
        <LogViewer
          :logs="filteredLogs"
          :auto-scroll="logAutoScroll"
          :loading="logsLoading"
          height="350px"
          @clear="clearLogs"
          @load-more="loadMoreLogs"
          @search-similar="handleLogSearch"
        />
      </div>
    </div>

    <!-- 告警信息 -->
    <div v-if="visibleAlerts.length > 0" class="alerts-section">
      <div class="alerts-header">
        <h3 class="alerts-title">
          <i class="i-carbon-warning-filled"></i>
          系统告警
          <span class="alerts-count">({{ unreadAlertsCount }})</span>
        </h3>
        <div class="alerts-actions">
          <button @click="filterAlerts('all')" class="filter-btn" :class="{ active: alertFilter === 'all' }">
            全部
          </button>
          <button @click="filterAlerts('error')" class="filter-btn" :class="{ active: alertFilter === 'error' }">
            错误
          </button>
          <button @click="filterAlerts('warning')" class="filter-btn" :class="{ active: alertFilter === 'warning' }">
            警告
          </button>
          <button @click="dismissAllAlerts" class="dismiss-all-btn">
            全部忽略
          </button>
        </div>
      </div>

      <div class="alerts-list">
        <AlertItem
          v-for="alert in visibleAlerts"
          :key="alert.id"
          :alert="alert"
          @dismiss="handleAlertDismiss"
          @copy="handleAlertCopy"
          @view-details="handleAlertDetails"
          @create-issue="handleCreateIssue"
          @mark-resolved="handleMarkResolved"
          @delete="handleAlertDelete"
        />
      </div>
    </div>

    <!-- 性能概览 -->
    <div class="performance-overview">
      <div class="performance-header">
        <h3>性能概览</h3>
        <div class="performance-trend" :class="performanceTrendClass">
          <i :class="performanceTrendIcon"></i>
          {{ performanceTrendText }}
        </div>
      </div>

      <div class="performance-grid">
        <div class="performance-item">
          <div class="performance-label">今日执行</div>
          <div class="performance-value">{{ todayExecutions }}</div>
          <div class="performance-change" :class="todayChangeClass">
            {{ todayChangeText }}
          </div>
        </div>

        <div class="performance-item">
          <div class="performance-label">错误率</div>
          <div class="performance-value">{{ errorRate }}%</div>
          <div class="performance-change" :class="errorRateChangeClass">
            {{ errorRateChangeText }}
          </div>
        </div>

        <div class="performance-item">
          <div class="performance-label">平均队列时间</div>
          <div class="performance-value">{{ avgQueueTime }}ms</div>
          <div class="performance-change" :class="queueTimeChangeClass">
            {{ queueTimeChangeText }}
          </div>
        </div>

        <div class="performance-item">
          <div class="performance-label">并发连接</div>
          <div class="performance-value">{{ concurrentConnections }}</div>
          <div class="performance-change" :class="connectionsChangeClass">
            {{ connectionsChangeText }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useMonitoringStore } from '@/stores/modules/monitoring'
import MetricCard from '@/components/agent-chat/admin/MetricCard.vue'
import LineChart from '@/components/agent-chat/charts/LineChart.vue'
import PieChart from '@/components/agent-chat/charts/PieChart.vue'
import BarChart from '@/components/agent-chat/charts/BarChart.vue'
import ResourceMonitor from '@/components/agent-chat/admin/ResourceMonitor.vue'
import LogViewer from '@/components/agent-chat/admin/LogViewer.vue'
import AlertItem from '@/components/agent-chat/admin/AlertItem.vue'
import type { Alert, LogEntry } from '@/api/agentManagement'

// Store
const monitoringStore = useMonitoringStore()

// 响应式数据
const timeRange = ref('24h')
const logLevel = ref('')
const logAutoScroll = ref(true)
const agentChartType = ref<'pie' | 'bar'>('pie')
const alertFilter = ref<'all' | 'error' | 'warning'>('all')
const loading = ref(false)

// 计算属性 - Store 数据
const metrics = computed(() => monitoringStore.metrics)
const executionTrendData = computed(() => monitoringStore.executionTrendData)
const agentUsageData = computed(() => monitoringStore.agentUsageData)
const responseTimeData = computed(() => monitoringStore.responseTimeData)
const systemResources = computed(() => monitoringStore.systemResources)
const logs = computed(() => monitoringStore.logs)
const alerts = computed(() => monitoringStore.alerts)

const metricsLoading = computed(() => monitoringStore.metricsLoading)
const chartDataLoading = computed(() => monitoringStore.chartDataLoading)
const logsLoading = computed(() => monitoringStore.logsLoading)
const chartDataError = computed(() => monitoringStore.chartDataError)

const systemHealthScore = computed(() => monitoringStore.systemHealthScore)
const performanceTrend = computed(() => monitoringStore.performanceTrend)
const unreadAlertsCount = computed(() => monitoringStore.unreadAlertsCount)
const realTimeEnabled = computed(() => monitoringStore.realTimeEnabled)

// 计算属性 - 衍生数据
const filteredLogs = computed(() => {
  if (!logLevel.value) return logs.value
  return logs.value.filter(log => log.level === logLevel.value)
})

const visibleAlerts = computed(() => {
  if (alertFilter.value === 'all') return alerts.value.filter(alert => !alert.dismissed)
  return alerts.value.filter(alert =>
    alert.type === alertFilter.value && !alert.dismissed
  )
})

// 健康状态
const healthStatusClass = computed(() => {
  const score = systemHealthScore.value
  if (score >= 80) return 'health-good'
  if (score >= 60) return 'health-warning'
  return 'health-critical'
})

const healthIcon = computed(() => {
  const score = systemHealthScore.value
  if (score >= 80) return 'i-carbon-checkmark-filled'
  if (score >= 60) return 'i-carbon-warning-filled'
  return 'i-carbon-error-filled'
})

const healthTitle = computed(() => {
  const score = systemHealthScore.value
  if (score >= 80) return '系统运行正常'
  if (score >= 60) return '系统存在警告'
  return '系统需要关注'
})

const healthDescription = computed(() => {
  const score = systemHealthScore.value
  if (score >= 80) return '所有指标都在正常范围内，系统运行稳定'
  if (score >= 60) return '部分指标超出正常范围，建议检查相关组件'
  return '多个指标异常，需要立即检查和处理'
})

// 资源状态
const resourceStatusClass = computed(() => {
  if (!systemResources.value) return 'status-unknown'

  const { cpuUsage, memoryUsage, diskUsage } = systemResources.value
  const maxUsage = Math.max(cpuUsage, memoryUsage, diskUsage)

  if (maxUsage >= 90) return 'status-critical'
  if (maxUsage >= 75) return 'status-warning'
  return 'status-normal'
})

const resourceStatusIcon = computed(() => {
  const className = resourceStatusClass.value
  if (className === 'status-critical') return 'i-carbon-error-filled'
  if (className === 'status-warning') return 'i-carbon-warning-filled'
  return 'i-carbon-checkmark-filled'
})

const resourceStatusText = computed(() => {
  const className = resourceStatusClass.value
  if (className === 'status-critical') return '资源紧张'
  if (className === 'status-warning') return '资源预警'
  return '资源正常'
})

// 性能趋势
const performanceTrendClass = computed(() => `trend-${performanceTrend.value}`)

const performanceTrendIcon = computed(() => {
  switch (performanceTrend.value) {
    case 'improving': return 'i-carbon-trending-up'
    case 'declining': return 'i-carbon-trending-down'
    default: return 'i-carbon-trending-flat'
  }
})

const performanceTrendText = computed(() => {
  switch (performanceTrend.value) {
    case 'improving': return '性能提升'
    case 'declining': return '性能下降'
    default: return '性能稳定'
  }
})

// 模拟数据
const executionChartData = computed(() => [45, 52, 48, 61, 58, 67, 73])
const successRateChartData = computed(() => [96, 97, 95, 98, 97, 96, 98])
const responseTimeChartData = computed(() => [850, 920, 780, 760, 820, 790, 850])
const activeUsersChartData = computed(() => [38, 42, 35, 48, 52, 45, 42])

const responseTimeP95 = computed(() => '1,240')
const responseTimeP99 = computed(() => '2,850')

const todayExecutions = computed(() => '1,847')
const todayChangeText = computed(() => '+12.5%')
const todayChangeClass = computed(() => 'change-positive')

const errorRate = computed(() => '2.1')
const errorRateChangeText = computed(() => '-0.3%')
const errorRateChangeClass = computed(() => 'change-positive')

const avgQueueTime = computed(() => '45')
const queueTimeChangeText = computed(() => '-8ms')
const queueTimeChangeClass = computed(() => 'change-positive')

const concurrentConnections = computed(() => '156')
const connectionsChangeText = computed(() => '+23')
const connectionsChangeClass = computed(() => 'change-positive')

// 模拟系统信息
const cpuInfo = computed(() => ({
  cores: 8,
  load: '1.2',
  temperature: '45'
}))

const memoryInfo = computed(() => ({
  total: 32 * 1024 * 1024 * 1024,
  used: 0,
  available: 0
}))

const diskInfo = computed(() => ({
  total: 2 * 1024 * 1024 * 1024 * 1024,
  used: 0,
  available: 0
}))

const networkInfo = computed(() => ({
  upload: 2.4 * 1024 * 1024,
  download: 15.6 * 1024 * 1024
}))

const systemInfo = computed(() => ({
  os: 'Linux Ubuntu 22.04 LTS',
  uptime: 7 * 24 * 60 * 60 + 12 * 60 * 60,
  processes: 168,
  users: 3
}))

// 图表选项
const executionTrendOptions = {
  responsive: true,
  plugins: {
    legend: {
      display: false
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      grid: {
        color: 'rgba(0, 0, 0, 0.1)'
      }
    },
    x: {
      grid: {
        display: false
      }
    }
  }
}

const agentUsageOptions = {
  responsive: true,
  plugins: {
    legend: {
      position: 'right' as const
    }
  }
}

const agentUsageBarOptions = {
  responsive: true,
  plugins: {
    legend: {
      display: false
    }
  },
  scales: {
    y: {
      beginAtZero: true
    }
  }
}

const responseTimeOptions = {
  responsive: true,
  plugins: {
    legend: {
      display: false
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      title: {
        display: true,
        text: '请求数量'
      }
    },
    x: {
      title: {
        display: true,
        text: '响应时间'
      }
    }
  }
}

// 生命周期
onMounted(async () => {
  await refreshAllData()

  // 监听错误变化
  watch([
    () => monitoringStore.metricsError,
    () => monitoringStore.chartDataError,
    () => monitoringStore.resourcesError,
    () => monitoringStore.logsError,
    () => monitoringStore.alertsError
  ], (errors) => {
    const hasError = errors.some(error => error)
    if (hasError) {
      // 这里可以显示错误通知
      console.warn('监控数据加载出现错误')
    }
  })
})

onUnmounted(() => {
  monitoringStore.stopRealTimeUpdates()
})

// 方法
async function refreshAllData() {
  loading.value = true

  try {
    await monitoringStore.refreshAllData(timeRange.value)
  } catch (error) {
    console.error('Failed to refresh dashboard data:', error)
  } finally {
    loading.value = false
  }
}

async function handleTimeRangeChange() {
  await refreshAllData()
}

function toggleRealTime() {
  if (realTimeEnabled.value) {
    monitoringStore.stopRealTimeUpdates()
  } else {
    monitoringStore.startRealTimeUpdates()
  }
}

async function loadMetrics() {
  await monitoringStore.loadMetrics(timeRange.value)
}

async function loadChartData() {
  await monitoringStore.loadChartData(timeRange.value)
}

async function loadSystemResources() {
  await monitoringStore.loadSystemResources()
}

function handleLogFilterChange() {
  monitoringStore.setLogFilters({ level: logLevel.value })
  monitoringStore.loadRecentLogs()
}

function toggleLogAutoScroll() {
  logAutoScroll.value = !logAutoScroll.value
}

function clearLogs() {
  monitoringStore.clearLogs()
}

async function loadMoreLogs() {
  // 加载更多日志的逻辑
  await monitoringStore.loadRecentLogs()
}

function downloadLogs() {
  // 下载日志的逻辑
  const logText = logs.value
    .map(log => `${log.timestamp} [${log.level}] ${log.source || ''}: ${log.message}`)
    .join('\n')

  const blob = new Blob([logText], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)

  const a = document.createElement('a')
  a.href = url
  a.download = `dashboard-logs-${new Date().toISOString().slice(0, 19)}.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)

  URL.revokeObjectURL(url)
}

function handleLogSearch(log: LogEntry) {
  // 搜索相似日志的逻辑
  //console.log('Search similar logs:', log)
}

function toggleChartType() {
  agentChartType.value = agentChartType.value === 'pie' ? 'bar' : 'pie'
}

function handleAgentClick(data: any) {
  //console.log('Agent clicked:', data)
  // 这里可以跳转到具体的 Agent 详情页
}

function filterAlerts(type: 'all' | 'error' | 'warning') {
  alertFilter.value = type
}

function handleAlertDismiss(alertId: string) {
  monitoringStore.dismissAlert(alertId)
}

function handleAlertCopy(alert: Alert) {
  //console.log('Copy alert:', alert)
}

function handleAlertDetails(alert: Alert) {
  //console.log('View alert details:', alert)
}

function handleCreateIssue(alert: Alert) {
  //console.log('Create issue for alert:', alert)
}

function handleMarkResolved(alert: Alert) {
  //console.log('Mark alert resolved:', alert)
}

function handleAlertDelete(alert: Alert) {
  //console.log('Delete alert:', alert)
}

function dismissAllAlerts() {
  monitoringStore.dismissAllAlerts()
}

async function exportData() {
  try {
    await monitoringStore.exportMetrics(timeRange.value, 'json')
  } catch (error) {
    console.error('Export failed:', error)
  }
}
</script>

<style scoped>
.dashboard-page {
  @apply p-6 bg-gray-50 dark:bg-gray-900 min-h-screen space-y-6;
}

.dashboard-header {
  @apply flex items-start justify-between;
}

.header-left {
  @apply space-y-2;
}

.page-title {
  @apply text-2xl font-bold text-gray-900 dark:text-white m-0 flex items-center gap-3;
}

.page-description {
  @apply text-gray-600 dark:text-gray-400;
}

.header-controls {
  @apply flex items-center gap-3;
}

.time-range-selector {
  @apply flex items-center gap-2;
}

.time-range-selector label {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.time-select {
  @apply px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm;
}

.real-time-btn {
  @apply flex items-center gap-2 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors;
}

.real-time-btn.active {
  @apply bg-green-500 text-white border-green-500;
}

.refresh-btn,
.export-btn {
  @apply flex items-center gap-2 px-3 py-2 text-sm bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white rounded-lg transition-colors;
}

.export-btn {
  @apply bg-gray-500 hover:bg-gray-600;
}

.health-banner {
  @apply flex items-center gap-4 p-4 rounded-lg border-l-4;
}

.health-good {
  @apply bg-green-50 dark:bg-green-900/20 border-green-500;
}

.health-warning {
  @apply bg-yellow-50 dark:bg-yellow-900/20 border-yellow-500;
}

.health-critical {
  @apply bg-red-50 dark:bg-red-900/20 border-red-500;
}

.health-icon {
  @apply w-12 h-12 rounded-full flex items-center justify-center text-xl;
}

.health-good .health-icon {
  @apply bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400;
}

.health-warning .health-icon {
  @apply bg-yellow-100 dark:bg-yellow-900 text-yellow-600 dark:text-yellow-400;
}

.health-critical .health-icon {
  @apply bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400;
}

.health-content {
  @apply flex-1;
}

.health-title {
  @apply text-lg font-semibold;
}

.health-good .health-title {
  @apply text-green-800 dark:text-green-200;
}

.health-warning .health-title {
  @apply text-yellow-800 dark:text-yellow-200;
}

.health-critical .health-title {
  @apply text-red-800 dark:text-red-200;
}

.health-description {
  @apply text-sm text-gray-600 dark:text-gray-400 mt-1;
}

.health-score {
  @apply flex-shrink-0;
}

.score-circle {
  @apply relative w-16 h-16 rounded-full flex flex-col items-center justify-center text-center;

  background: conic-gradient(#3b82f6 0deg, #3b82f6 calc(var(--score) * 3.6deg), #e5e7eb calc(var(--score) * 3.6deg));
}

.score-circle::before {
  @apply content-[""] absolute inset-2 bg-white dark:bg-gray-800 rounded-full;
}

.score-value,
.score-label {
  @apply relative z-10;
}

.score-value {
  @apply text-lg font-bold text-gray-900 dark:text-white;
}

.score-label {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

.metrics-overview {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}

.charts-section {
  @apply space-y-6;
}

.chart-row {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-6;
}

.chart-container {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden;
}

.chart-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900;
}

.chart-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white m-0;
}

.chart-legend {
  @apply flex gap-4;
}

.legend-item {
  @apply flex items-center gap-1 text-sm;
}

.legend-item.success {
  @apply text-green-600 dark:text-green-400;
}

.legend-item.error {
  @apply text-red-600 dark:text-red-400;
}

.legend-dot {
  @apply w-2 h-2 rounded-full;
}

.legend-item.success .legend-dot {
  @apply bg-green-500;
}

.legend-item.error .legend-dot {
  @apply bg-red-500;
}

.chart-actions {
  @apply flex gap-2;
}

.chart-toggle {
  @apply w-8 h-8 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 flex items-center justify-center text-gray-500 dark:text-gray-400 transition-colors;
}

.chart-stats {
  @apply flex gap-4 text-sm text-gray-600 dark:text-gray-400;
}

.stat-item strong {
  @apply text-gray-900 dark:text-white;
}

.resource-status {
  @apply flex items-center gap-1 text-sm font-medium;
}

.status-normal {
  @apply text-green-600 dark:text-green-400;
}

.status-warning {
  @apply text-yellow-600 dark:text-yellow-400;
}

.status-critical {
  @apply text-red-600 dark:text-red-400;
}

.status-unknown {
  @apply text-gray-600 dark:text-gray-400;
}

.chart-content {
  @apply p-4;
}

.logs-section {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden;
}

.logs-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900;
}

.logs-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white m-0 flex items-center gap-2;
}

.logs-controls {
  @apply flex items-center gap-3;
}

.log-level-filter {
  @apply flex items-center gap-2;
}

.level-select {
  @apply px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white;
}

.log-actions {
  @apply flex gap-2;
}

.log-action-btn {
  @apply flex items-center gap-1 px-3 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors;
}

.log-action-btn.active {
  @apply bg-blue-500 text-white border-blue-500;
}

.logs-content {
  @apply p-0;
}

.alerts-section {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden;
}

.alerts-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-red-50 dark:bg-red-900/20;
}

.alerts-title {
  @apply text-lg font-semibold text-red-800 dark:text-red-200 m-0 flex items-center gap-2;
}

.alerts-count {
  @apply text-sm font-normal text-red-600 dark:text-red-400;
}

.alerts-actions {
  @apply flex items-center gap-2;
}

.filter-btn {
  @apply px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors;
}

.filter-btn.active {
  @apply bg-blue-500 text-white border-blue-500;
}

.dismiss-all-btn {
  @apply px-3 py-1 text-sm bg-red-500 hover:bg-red-600 text-white rounded transition-colors;
}

.alerts-list {
  @apply p-4 space-y-3;
}

.performance-overview {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6;
}

.performance-header {
  @apply flex items-center justify-between mb-6;
}

.performance-header h3 {
  @apply text-lg font-semibold text-gray-900 dark:text-white m-0;
}

.performance-trend {
  @apply flex items-center gap-1 text-sm font-medium;
}

.trend-improving {
  @apply text-green-600 dark:text-green-400;
}

.trend-declining {
  @apply text-red-600 dark:text-red-400;
}

.trend-stable {
  @apply text-gray-600 dark:text-gray-400;
}

.performance-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}

.performance-item {
  @apply text-center space-y-2;
}

.performance-label {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.performance-value {
  @apply text-2xl font-bold text-gray-900 dark:text-white;
}

.performance-change {
  @apply text-sm font-medium;
}

.change-positive {
  @apply text-green-600 dark:text-green-400;
}

.change-negative {
  @apply text-red-600 dark:text-red-400;
}

.change-neutral {
  @apply text-gray-600 dark:text-gray-400;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (width <= 1024px) {
  .chart-row {
    @apply grid-cols-1;
  }

  .metrics-overview {
    @apply grid-cols-2;
  }
}

@media (width <= 768px) {
  .dashboard-page {
    @apply p-4;
  }

  .dashboard-header {
    @apply flex-col items-stretch gap-4;
  }

  .header-controls {
    @apply flex-wrap gap-2;
  }

  .health-banner {
    @apply flex-col text-center;
  }

  .metrics-overview {
    @apply grid-cols-1;
  }

  .logs-header,
  .alerts-header {
    @apply flex-col items-stretch gap-3;
  }

  .logs-controls,
  .alerts-actions {
    @apply flex-wrap justify-between;
  }

  .performance-grid {
    @apply grid-cols-2;
  }
}
</style>
