<template>
  <div class="agent-chat-page">
    <!-- 头部导航 -->
    <div class="agent-chat-header">
      <div class="header-left">
        <h1 class="page-title">
          <i class="i-carbon-machine-learning"></i>
          AI Agent 对话
        </h1>
        <div class="connection-indicator" :class="connectionStatusClass">
          <div class="connection-dot"></div>
          <span>{{ connectionStatusText }}</span>
          <button
            v-if="connectionStatus === 'error' || connectionStatus === 'disconnected'"
            @click="forceReconnect"
            class="reconnect-btn"
            :disabled="loading"
            title="手动重连"
          >
            <i class="i-carbon-renew"></i>
          </button>
        </div>
      </div>
      <div class="header-actions">
        <button
          @click="createNewConversation"
          class="btn-primary"
          :disabled="loading"
        >
          <i class="i-carbon-add"></i>
          新建对话
        </button>
        <button
          @click="showSettings"
          class="btn-secondary"
        >
          <i class="i-carbon-settings"></i>
          设置
        </button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="agent-chat-content">
      <!-- 对话列表侧边栏 -->
      <div class="conversation-sidebar" :class="{ collapsed: sidebarCollapsed }">
        <div class="sidebar-header">
          <button
            @click="toggleSidebar"
            class="sidebar-toggle"
            :title="sidebarCollapsed ? '展开侧边栏' : '收起侧边栏'"
          >
            <i :class="sidebarCollapsed ? 'i-carbon-chevron-right' : 'i-carbon-chevron-left'"></i>
          </button>
        </div>

        <div v-if="!sidebarCollapsed" class="sidebar-content">
          <ConversationList
            :conversations="conversations"
            :current-conversation="currentConversation"
            :loading="loading"
            :loading-conversation-id="loadingConversationId"
            @select="selectConversation"
            @delete="deleteConversation"
            @archive="archiveConversation"
            @unarchive="unarchiveConversation"
            @refresh="loadConversations"
            @rename="renameConversation"
          />
        </div>
      </div>

      <!-- 对话主区域 -->
      <div class="conversation-main" :class="{ 'has-loading': !!loadingConversationId }">
        <!-- 对话加载遮罩 -->
        <div v-if="loadingConversationId" class="conversation-loading-overlay">
          <div class="conversation-loading-content">
            <i class="i-carbon-in-progress animate-spin"></i>
            <p>正在加载对话...</p>
          </div>
        </div>

        <div v-if="currentConversation" class="conversation-container">
          <!-- 流程概览 (可折叠) -->
          <FlowOverview
            v-if="currentFlow || hasActiveExecution"
            :flow="currentFlow"
            :current-step="currentStep"
            :execution-history="executionHistory"
            :default-collapsed="flowCollapsed"
            class="flow-overview"
            @step-select="handleStepSelect"
            @flow-pause="pauseExecution"
            @flow-resume="resumeExecution"
          />

          <!-- 消息列表 -->
          <div class="message-list" ref="messageListRef">
            <div class="message-container">
              <!-- 消息加载状态 -->
              <div v-if="loadingConversationId && currentConversation?.id === loadingConversationId" class="message-loading">
                <div class="message-loading-content">
                  <div class="loading-spinner">
                    <i class="i-carbon-in-progress animate-spin"></i>
                  </div>
                  <p>正在加载消息...</p>
                  <div class="loading-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>

              <!-- 欢迎消息 -->
              <div v-else-if="messages.length === 0" class="welcome-message">
                <div class="welcome-content">
                  <div class="welcome-icon">
                    <i class="i-carbon-machine-learning"></i>
                  </div>
                  <h3>欢迎使用 AI Agent 协作系统</h3>
                  <p>我是您的智能助手，可以帮您处理各种复杂任务。请描述您的需求，我会调用合适的 AI Agents 来协作完成。</p>
                  <div class="welcome-suggestions">
                    <h4>您可以尝试问我：</h4>
                    <div class="suggestion-list">
                      <button
                        v-for="suggestion in quickSuggestions"
                        :key="suggestion.id"
                        @click="sendSuggestion(suggestion)"
                        class="suggestion-btn"
                      >
                        <i :class="suggestion.icon"></i>
                        <span>{{ suggestion.text }}</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 历史消息 -->
              <MessageItem
                v-else
                v-for="message in messages"
                :key="message.id"
                :message="message"
                :show-chain-of-thought="settings.showChainOfThought"
                :enable-markdown="true"
                @copy="handleMessageCopy"
                @regenerate="regenerateMessage"
                @feedback="handleMessageFeedback"
                @retry="retryMessage"
              />

              <!-- Agent 状态卡片 -->
              <AgentStatusCard
                v-if="activeExecution || hasActiveExecution"
                :execution="activeExecution"
                :agents="activeAgents"
                :connection-status="connectionStatus"
                class="agent-status"
                @pause="pauseExecution"
                @resume="resumeExecution"
                @cancel="cancelExecution"
              />

              <!-- 加载更多历史消息 -->
              <div v-if="hasMoreMessages" class="load-more">
                <button
                  @click="loadMoreMessages"
                  class="load-more-btn"
                  :disabled="loadingMore"
                >
                  <i v-if="loadingMore" class="i-carbon-in-progress animate-spin"></i>
                  <i v-else class="i-carbon-chevron-up"></i>
                  {{ loadingMore ? '加载中...' : '加载更多消息' }}
                </button>
              </div>
            </div>
          </div>

          <!-- 输入区域 -->
          <SmartInput
            v-model="inputMessage"
            :disabled="isProcessing"
            :loading="isProcessing"
            :suggestions="inputSuggestions"
            :enable-voice-input="settings.enableVoiceInput"
            :enable-file-upload="true"
            :show-char-count="true"
            :max-length="10000"
            placeholder="描述您的需求，我会调用 AI Agents 来协作完成..."
            class="message-input"
            @send="sendMessage"
            @voice-input="handleVoiceInput"
            @file-upload="handleFileUpload"
          />
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-state">
          <div class="empty-content">
            <div class="empty-icon">
              <i class="i-carbon-machine-learning"></i>
            </div>
            <h3>开始 AI Agent 协作</h3>
            <p>选择一个对话或创建新对话来体验 AI Agent 智能协作系统</p>
            <div class="empty-actions">
              <button @click="createNewConversation" class="btn-primary">
                <i class="i-carbon-add"></i>
                创建新对话
              </button>
              <button @click="showHelp" class="btn-secondary">
                <i class="i-carbon-help"></i>
                使用帮助
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 设置面板 -->
    <div v-if="settingsVisible" class="settings-panel">
      <div class="settings-overlay" @click="hideSettings"></div>
      <div class="settings-content">
        <div class="settings-header">
          <h3>对话设置</h3>
          <button @click="hideSettings" class="close-btn">
            <i class="i-carbon-close"></i>
          </button>
        </div>

        <div class="settings-body">
          <div class="setting-group">
            <h4>显示选项</h4>
            <label class="setting-item">
              <input
                v-model="settings.showChainOfThought"
                type="checkbox"
                class="setting-checkbox"
              >
              <span>显示 Agent 思维过程</span>
            </label>
            <label class="setting-item">
              <input
                v-model="settings.autoScroll"
                type="checkbox"
                class="setting-checkbox"
              >
              <span>自动滚动到最新消息</span>
            </label>
          </div>

          <div class="setting-group">
            <h4>交互选项</h4>
            <label class="setting-item">
              <input
                v-model="settings.enableVoiceInput"
                type="checkbox"
                class="setting-checkbox"
              >
              <span>启用语音输入</span>
            </label>
            <label class="setting-item">
              <input
                v-model="settings.enableSound"
                type="checkbox"
                class="setting-checkbox"
              >
              <span>启用声音提示</span>
            </label>
          </div>

          <div class="setting-group">
            <h4>主题设置</h4>
            <div class="theme-options">
              <label class="theme-option">
                <input
                  v-model="settings.theme"
                  type="radio"
                  value="light"
                  class="theme-radio"
                >
                <span>浅色主题</span>
              </label>
              <label class="theme-option">
                <input
                  v-model="settings.theme"
                  type="radio"
                  value="dark"
                  class="theme-radio"
                >
                <span>深色主题</span>
              </label>
              <label class="theme-option">
                <input
                  v-model="settings.theme"
                  type="radio"
                  value="auto"
                  class="theme-radio"
                >
                <span>跟随系统</span>
              </label>
            </div>
          </div>
        </div>

        <div class="settings-footer">
          <button @click="resetSettings" class="btn-secondary">
            重置设置
          </button>
          <button @click="hideSettings" class="btn-primary">
            确定
          </button>
        </div>
      </div>
    </div>

    <!-- 帮助面板 -->
    <div v-if="helpVisible" class="help-panel">
      <div class="help-overlay" @click="hideHelp"></div>
      <div class="help-content">
        <div class="help-header">
          <h3>使用帮助</h3>
          <button @click="hideHelp" class="close-btn">
            <i class="i-carbon-close"></i>
          </button>
        </div>
        <div class="help-body">
          <div class="help-section">
            <h4>什么是 AI Agent 协作？</h4>
            <p>AI Agent 协作系统通过多个专业化的 AI Agent 协同工作，为您提供更智能、更精准的服务。</p>
          </div>

          <div class="help-section">
            <h4>如何开始对话？</h4>
            <ol>
              <li>点击"新建对话"创建新的会话</li>
              <li>在输入框中描述您的需求</li>
              <li>系统会自动选择合适的 Agent 来处理</li>
              <li>观察 Agent 的执行过程和思维链</li>
            </ol>
          </div>

          <div class="help-section">
            <h4>支持的功能</h4>
            <ul>
              <li>🎤 语音输入</li>
              <li>📎 文件上传</li>
              <li>🧠 思维过程可视化</li>
              <li>📊 实时执行状态</li>
              <li>💾 对话历史管理</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- 全局提示 -->
    <div v-if="notification.visible" class="notification" :class="notification.type">
      <i :class="getNotificationIcon()"></i>
      <span>{{ notification.message }}</span>
      <button @click="hideNotification" class="notification-close">
        <i class="i-carbon-close"></i>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { useAgentChatStore } from '@/stores/modules/agentChat'
import { useUserStore } from '@/store/modules/user'
import { SseService } from '@/services/sseService'
import ConversationList from '@/components/agent-chat/agent/ConversationList.vue'
import FlowOverview from '@/components/agent-chat/agent/FlowOverview.vue'
import MessageItem from '@/components/agent-chat/agent/MessageItem.vue'
import AgentStatusCard from '@/components/agent-chat/agent/AgentStatusCard.vue'
import SmartInput from '@/components/agent-chat/agent/SmartInput.vue'
import type { Conversation, InputSuggestion } from '@/types/agent'

// Store
const agentChatStore = useAgentChatStore() as any // 添加类型断言解决 TypeScript 问题
const userStore = useUserStore()

// 响应式数据
const inputMessage = ref('')
const messageListRef = ref<HTMLElement>()
const sseService = ref<SseService>()
const sidebarCollapsed = ref(false)
const flowCollapsed = ref(false)
const settingsVisible = ref(false)
const helpVisible = ref(false)
const loadingMore = ref(false)
const hasMoreMessages = ref(false)
const loadingConversationId = ref<string | null>(null)

const notification = ref({
  visible: false,
  message: '',
  type: 'info' as 'success' | 'error' | 'warning' | 'info'
})

// 快速建议
const quickSuggestions = ref<InputSuggestion[]>([
  {
    id: 'analyze-data',
    text: '分析我的销售数据',
    description: '数据分析和报告生成',
    icon: 'i-carbon-analytics',
    category: '数据分析'
  },
  {
    id: 'create-report',
    text: '生成月度总结报告',
    description: '自动生成业务报告',
    icon: 'i-carbon-document',
    category: '报告生成'
  },
  {
    id: 'optimize-process',
    text: '优化我的工作流程',
    description: '流程分析和优化建议',
    icon: 'i-carbon-rocket',
    category: '流程优化'
  },
  {
    id: 'data-visualization',
    text: '创建数据可视化图表',
    description: '数据可视化和图表制作',
    icon: 'i-carbon-chart-line',
    category: '可视化'
  }
])

// 计算属性
const conversations = computed(() => agentChatStore.conversations)
const currentConversation = computed(() => agentChatStore.currentConversation)
const messages = computed(() => agentChatStore.messages)
const currentFlow = computed(() => agentChatStore.currentFlow)
const currentStep = computed(() => agentChatStore.currentStep)
const executionHistory = computed(() => agentChatStore.executionHistory)
const activeExecution = computed(() => agentChatStore.activeExecution)
const activeAgents = computed(() => agentChatStore.activeAgents)
const isProcessing = computed(() => agentChatStore.isProcessing)
const connectionStatus = computed(() => agentChatStore.connectionStatus)
const inputSuggestions = computed(() => agentChatStore.inputSuggestions)
const loading = computed(() => agentChatStore.loading)
const error = computed(() => agentChatStore.error)
const settings = computed(() => agentChatStore.settings)

const hasActiveExecution = computed(() => agentChatStore.hasActiveExecution)

const connectionStatusClass = computed(() => {
  switch (connectionStatus.value) {
    case 'connected': return 'connected'
    case 'connecting': return 'connecting'
    case 'reconnecting': return 'reconnecting'
    case 'error': return 'error'
    default: return 'disconnected'
  }
})

const connectionStatusText = computed(() => {
  switch (connectionStatus.value) {
    case 'connected': return '已连接'
    case 'connecting': return '连接中'
    case 'reconnecting': return '重连中'
    case 'error': return '连接错误'
    default: return '未连接'
  }
})

// 生命周期
onMounted(async () => {
  await loadConversations()

  // 如果清理后仍有有效的当前会话，加载其消息
  if (currentConversation.value) {
    try {
      await loadMessages(currentConversation.value.id)
      setupSSEForConversation(currentConversation.value.id)
    } catch (error) {
      console.error('加载当前会话消息失败:', error)
      showNotification('加载当前会话失败', 'error')
    }
  }

  setupSSE()

  // 监听错误
  watch(error, (newError) => {
    if (newError) {
      showNotification(newError, 'error')
      agentChatStore.clearError()
    }
  })
})

onUnmounted(() => {
  cleanupSSE()
})

// 监听当前对话变化
watch(currentConversation, async (newConv, oldConv) => {
  if (newConv && newConv.id !== oldConv?.id) {
    // 验证会话是否在对话列表中存在
    const conversationExists = conversations.value.some((conv: Conversation) => conv.id === newConv.id)

    if (!conversationExists) {
      console.warn('当前会话不在对话列表中，可能是无效的会话ID:', newConv.id)
      // 清除无效的当前会话
      agentChatStore.currentConversation = null
      showNotification('检测到无效的会话，已自动清理', 'warning')
      return
    }

    try {
      await loadMessages(newConv.id)
      setupSSEForConversation(newConv.id)
    } catch (error) {
      console.error('加载消息失败:', error)
      showNotification('加载消息失败', 'error')
    } finally {
      // 消息加载完成后清除loading状态
      if (loadingConversationId.value === newConv.id) {
        loadingConversationId.value = null
      }
    }
  }
}, { immediate: false }) // 移除 immediate，等对话列表加载完成后再处理

// 监听设置变化
watch(() => settings.value.autoScroll, (autoScroll) => {
  if (autoScroll) {
    nextTick(() => scrollToBottom())
  }
}, { immediate: true })

// 方法
async function loadConversations() {
  try {
    await agentChatStore.loadConversations()
  } catch (error) {
    console.error('Failed to load conversations:', error)
  }
}

async function createNewConversation() {
  try {
    const title = `AI Agent 对话 ${new Date().toLocaleString()}`
    await agentChatStore.createConversation(title)
    scrollToBottom()
  } catch (error) {
    showNotification('创建对话失败', 'error')
  }
}

async function selectConversation(conversation: Conversation) {
  // 如果已经是当前对话或正在加载，则忽略
  if (currentConversation.value?.id === conversation.id || loadingConversationId.value) {
    return
  }

  try {
    // 设置加载状态
    loadingConversationId.value = conversation.id

    // 设置当前对话，加载消息将在watch中处理
    agentChatStore.setCurrentConversation(conversation)

    // loading状态将在消息加载完成后由watch监听器清除
  } catch (error) {
    showNotification('加载对话失败', 'error')
    // 出错时清除加载状态
    loadingConversationId.value = null
  }
}

async function deleteConversation(conversationId: string) {
  try {
    await agentChatStore.deleteConversation(conversationId)
    showNotification('对话已删除', 'success')
  } catch (error) {
    showNotification('删除对话失败', 'error')
  }
}

async function archiveConversation(conversationId: string) {
  // TODO: 实现归档功能
  showNotification('对话已归档', 'success')
}

async function unarchiveConversation(conversationId: string) {
  // TODO: 实现取消归档功能
  showNotification('对话已恢复', 'success')
}

async function renameConversation(conversationId: string, newTitle: string) {
  // TODO: 实现重命名功能
  showNotification('对话已重命名', 'success')
}

async function loadMessages(conversationId: string) {
  try {
    await agentChatStore.loadMessages(conversationId)
    await nextTick()
    scrollToBottom()
  } catch (error) {
    showNotification('加载消息失败', 'error')
  }
}

async function loadMoreMessages() {
  if (loadingMore.value || !currentConversation.value) return

  try {
    loadingMore.value = true
    // TODO: 实现加载更多消息的逻辑
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟延迟
    hasMoreMessages.value = false // 假设没有更多消息了
  } catch (error) {
    showNotification('加载更多消息失败', 'error')
  } finally {
    loadingMore.value = false
  }
}

async function sendMessage(content: string, files: any[]) {
  if (!content.trim() && files.length === 0) return
  if (!currentConversation.value) {
    await createNewConversation()
  }

  try {
    await agentChatStore.sendMessage(currentConversation.value!.id, content)

    if (settings.value.autoScroll) {
      await nextTick()
      scrollToBottom()
    }
  } catch (error) {
    showNotification('发送消息失败', 'error')
  }
}

function sendSuggestion(suggestion: InputSuggestion) {
  inputMessage.value = suggestion.text
  if (currentConversation.value) {
    sendMessage(suggestion.text, [])
  }
}

function handleVoiceInput(transcription: string) {
  inputMessage.value = transcription
  showNotification('语音识别完成', 'success')
}

function handleFileUpload(files: File[]) {
  showNotification(`已上传 ${files.length} 个文件`, 'success')
}

function handleMessageCopy(content: string) {
  showNotification('消息已复制到剪贴板', 'success')
}

function regenerateMessage(messageId: string) {
  showNotification('正在重新生成回复...', 'info')
  // TODO: 实现重新生成逻辑
}

function handleMessageFeedback(messageId: string, type: string, text?: string) {
  showNotification('感谢您的反馈', 'success')
  // TODO: 实现反馈提交逻辑
}

async function retryMessage(messageId: string) {
  if (!currentConversation.value) return

  try {
    showNotification('正在重试发送...', 'info')

    // 找到要重试的消息
    const messageToRetry = messages.value.find(m => m.id === messageId)
    if (!messageToRetry || messageToRetry.role !== 'user') {
      showNotification('无法重试此消息', 'error')
      return
    }

    // 更新消息状态为发送中
    const messageIndex = messages.value.findIndex(m => m.id === messageId)
    if (messageIndex !== -1) {
      messages.value[messageIndex] = {
        ...messages.value[messageIndex],
        isTemporary: true,
        metadata: {
          ...messages.value[messageIndex].metadata,
          status: 'sending',
          error: undefined  // 清除之前的错误
        }
      }
    }

    // 重新发送消息
    await agentChatStore.sendMessage(currentConversation.value.id, messageToRetry.content)

    // 删除原来的失败消息（因为 sendMessage 会添加新的消息）
    messages.value = messages.value.filter(m => m.id !== messageId)

    showNotification('消息重发成功', 'success')
  } catch (error) {
    console.error('重试消息失败:', error)
    showNotification('重试失败，请稍后再试', 'error')

    // 恢复错误状态
    const messageIndex = messages.value.findIndex(m => m.id === messageId)
    if (messageIndex !== -1) {
      messages.value[messageIndex] = {
        ...messages.value[messageIndex],
        isTemporary: false,
        metadata: {
          ...messages.value[messageIndex].metadata,
          status: 'error',
          error: error instanceof Error ? error.message : '重试失败'
        }
      }
    }
  }
}

function handleStepSelect(step: any) {
  //console.log('Selected step:', step)
}

function pauseExecution() {
  showNotification('执行已暂停', 'info')
  // TODO: 实现暂停逻辑
}

function resumeExecution() {
  showNotification('执行已恢复', 'info')
  // TODO: 实现恢复逻辑
}

function cancelExecution() {
  showNotification('执行已取消', 'warning')
  // TODO: 实现取消逻辑
}

function toggleSidebar() {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

function showSettings() {
  settingsVisible.value = true
}

function hideSettings() {
  settingsVisible.value = false
}

function showHelp() {
  helpVisible.value = true
}

function hideHelp() {
  helpVisible.value = false
}

function resetSettings() {
  agentChatStore.updateSettings({
    autoScroll: true,
    showChainOfThought: true,
    enableSound: false,
    enableVoiceInput: true,
    theme: 'auto'
  })
  showNotification('设置已重置', 'success')
}

function setupSSE() {
  sseService.value = new SseService()
}

function setupSSEForConversation(conversationId: string) {
  if (!sseService.value) return

  agentChatStore.setConnectionStatus('connecting')

  sseService.value.connect(conversationId, {
    token: userStore.token,  // 传递JWT token用于SSE认证
    onEvent: handleSSEEvent,
    onError: handleSSEError,
    onReconnect: handleSSEReconnect,
    onConnectionStatusChange: handleSSEStatusChange,
    maxReconnectAttempts: 10,
    exponentialBackoff: true,
    maxReconnectInterval: 30000,
    reconnectOnVisibilityChange: true
  })
}

function cleanupSSE() {
  if (sseService.value) {
    sseService.value.disconnect()
  }
}

function handleSSEEvent(event: any) {
  agentChatStore.handleSseEvent(event)

  // 自动滚动到底部
  if (settings.value.autoScroll) {
    nextTick(() => {
      scrollToBottom()
    })
  }
}

function handleSSEError(error: any) {
  console.error('SSE error:', error)
  agentChatStore.setConnectionStatus('error')
  showNotification('连接出现问题，正在尝试重连...', 'warning')
}

function handleSSEReconnect() {
  //console.log('SSE reconnected')
  agentChatStore.setConnectionStatus('connected')
  showNotification('连接已恢复', 'success')
}

function handleSSEStatusChange(status: ConnectionStatus) {
  agentChatStore.setConnectionStatus(status)

  switch (status) {
    case 'connecting':
      // 首次连接时不显示通知
      break
    case 'reconnecting':
      showNotification('连接断开，正在重连...', 'warning')
      break
    case 'connected':
      // 重连成功的通知在 handleSSEReconnect 中处理
      break
    case 'error':
      showNotification('连接失败，请检查网络', 'error')
      break
    case 'disconnected':
      // 手动断开时不显示通知
      break
  }
}

function forceReconnect() {
  if (!sseService.value || !currentConversation.value) {
    showNotification('无法重连，请刷新页面', 'error')
    return
  }

  showNotification('正在尝试重连...', 'info')
  sseService.value.forceReconnect()
}

function scrollToBottom() {
  if (messageListRef.value) {
    messageListRef.value.scrollTop = messageListRef.value.scrollHeight
  }
}

function showNotification(message: string, type: typeof notification.value.type = 'info') {
  notification.value = {
    visible: true,
    message,
    type
  }

  // 3秒后自动隐藏
  setTimeout(() => {
    hideNotification()
  }, 3000)
}

function hideNotification() {
  notification.value.visible = false
}

function getNotificationIcon(): string {
  switch (notification.value.type) {
    case 'success': return 'i-carbon-checkmark-filled'
    case 'error': return 'i-carbon-error-filled'
    case 'warning': return 'i-carbon-warning-filled'
    default: return 'i-carbon-information-filled'
  }
}
</script>

<style scoped>
.agent-chat-page {
  @apply min-h-screen flex flex-col bg-gray-50 dark:bg-gray-900;

  height: 100vh; /* 使用视口高度作为初始高度 */
  overflow: auto; /* 允许页面滚动，以便sticky定位正常工作 */
}

.agent-chat-header {
  @apply flex items-center justify-between px-6 py-4 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm;
}

.header-left {
  @apply flex items-center gap-4;
}

.page-title {
  @apply text-xl font-semibold text-gray-900 dark:text-white m-0 flex items-center gap-2;
}

.connection-indicator {
  @apply flex items-center gap-2 text-sm;
}

.connection-dot {
  @apply w-2 h-2 rounded-full;
}

.connection-indicator.connected .connection-dot {
  @apply bg-green-500 animate-pulse;
}

.connection-indicator.connecting .connection-dot,
.connection-indicator.reconnecting .connection-dot {
  @apply bg-yellow-500 animate-pulse;
}

.connection-indicator.error .connection-dot,
.connection-indicator.disconnected .connection-dot {
  @apply bg-red-500;
}

.connection-indicator.connected {
  @apply text-green-600 dark:text-green-400;
}

.connection-indicator.connecting,
.connection-indicator.reconnecting {
  @apply text-yellow-600 dark:text-yellow-400;
}

.connection-indicator.error,
.connection-indicator.disconnected {
  @apply text-red-600 dark:text-red-400;
}

.reconnect-btn {
  @apply ml-2 p-1 bg-transparent hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors;
}

.reconnect-btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.reconnect-btn i {
  @apply text-sm;
}

.header-actions {
  @apply flex gap-3;
}

.btn-primary {
  @apply flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white rounded-lg transition-colors;
}

.btn-secondary {
  @apply flex items-center gap-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors;
}

.agent-chat-content {
  @apply flex flex-1 overflow-hidden;
}

.conversation-sidebar {
  @apply w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-all duration-300;
}

.conversation-sidebar.collapsed {
  @apply w-12;
}

.sidebar-header {
  @apply p-3 border-b border-gray-200 dark:border-gray-700;
}

.sidebar-toggle {
  @apply w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 flex items-center justify-center text-gray-600 dark:text-gray-400 transition-colors;
}

.sidebar-content {
  @apply h-full overflow-hidden;
}

.conversation-main {
  @apply flex-1 flex flex-col relative;

  min-height: 0; /* 确保flex子元素能够正确收缩 */
}

.conversation-container {
  @apply flex flex-col h-full;

  min-height: 0; /* 确保flex子元素能够正确收缩 */
}

.flow-overview {
  @apply flex-shrink-0;
}

.message-list {
  @apply flex-1 overflow-y-auto;

  min-height: 0; /* 确保flex子元素能够正确收缩 */

  /* 为底部的SmartInput预留空间 */
  padding-bottom: 0;
}

.message-container {
  @apply p-6 space-y-6 max-w-4xl mx-auto;

  /* 确保底部有足够的空间，避免被SmartInput遮挡 */
  padding-bottom: 12rem; /* 增加更多底部间距，确保输入框不会遮挡内容 */
}

.welcome-message {
  @apply text-center py-6;
}

.welcome-content {
  @apply space-y-4;
}

.welcome-icon {
  @apply text-4xl text-blue-500 dark:text-blue-400;
}

.welcome-content h3 {
  @apply text-2xl font-semibold text-gray-900 dark:text-white;
}

.welcome-content p {
  @apply text-gray-600 dark:text-gray-400 max-w-2xl mx-auto;
}

.welcome-suggestions {
  @apply space-y-4;
}

.welcome-suggestions h4 {
  @apply text-lg font-medium text-gray-900 dark:text-white;
}

.suggestion-list {
  @apply grid grid-cols-1 md:grid-cols-2 gap-3 max-w-2xl mx-auto;
}

.suggestion-btn {
  @apply flex items-center gap-3 p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-500 dark:hover:border-blue-400 hover:shadow-md transition-all text-left;
}

.agent-status {
  @apply mx-6;
}

.load-more {
  @apply text-center py-4;
}

.load-more-btn {
  @apply flex items-center gap-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors;
}

.message-input {
  @apply flex-shrink-0 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800;

  /* 使用sticky定位，确保输入框始终在视口底部可见 */
  position: sticky;
  bottom: 0;
  z-index: 10;

  /* 添加阴影效果，让输入框更明显地分离于内容 */
  box-shadow: 0 -4px 6px -1px rgb(0 0 0 / 10%), 0 -2px 4px -2px rgb(0 0 0 / 10%);
}

.empty-state {
  @apply flex items-center justify-center h-full;
}

.empty-content {
  @apply text-center space-y-6;
}

.empty-icon {
  @apply text-6xl text-gray-400 dark:text-gray-500;
}

.empty-content h3 {
  @apply text-xl font-semibold text-gray-900 dark:text-white;
}

.empty-content p {
  @apply text-gray-600 dark:text-gray-400 max-w-md;
}

.empty-actions {
  @apply flex gap-3 justify-center;
}

.settings-panel,
.help-panel {
  @apply fixed inset-0 z-50 flex items-center justify-center;
}

.settings-overlay,
.help-overlay {
  @apply absolute inset-0 bg-black bg-opacity-50;
}

.settings-content,
.help-content {
  @apply relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-lg w-full mx-4 max-h-[80vh] overflow-hidden;
}

.settings-header,
.help-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700;
}

.settings-header h3,
.help-header h3 {
  @apply text-lg font-semibold text-gray-900 dark:text-white m-0;
}

.close-btn {
  @apply w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 flex items-center justify-center text-gray-500 dark:text-gray-400 transition-colors;
}

.settings-body,
.help-body {
  @apply p-6 space-y-6 overflow-y-auto;
}

.setting-group {
  @apply space-y-3;
}

.setting-group h4 {
  @apply text-sm font-medium text-gray-900 dark:text-white;
}

.setting-item {
  @apply flex items-center gap-3 cursor-pointer;
}

.setting-checkbox {
  @apply w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600;
}

.theme-options {
  @apply space-y-2;
}

.theme-option {
  @apply flex items-center gap-3 cursor-pointer;
}

.theme-radio {
  @apply w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600;
}

.settings-footer {
  @apply flex gap-3 p-6 border-t border-gray-200 dark:border-gray-700;
}

.help-section {
  @apply space-y-3;
}

.help-section h4 {
  @apply text-base font-medium text-gray-900 dark:text-white;
}

.help-section p {
  @apply text-gray-600 dark:text-gray-400;
}

.help-section ol,
.help-section ul {
  @apply space-y-2 pl-4;
}

.help-section li {
  @apply text-gray-600 dark:text-gray-400;
}

.notification {
  @apply fixed top-4 right-4 flex items-center gap-3 px-4 py-3 rounded-lg shadow-lg z-50 transition-all duration-300;
}

.notification.success {
  @apply bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 border border-green-200 dark:border-green-800;
}

.notification.error {
  @apply bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 border border-red-200 dark:border-red-800;
}

.notification.warning {
  @apply bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 border border-yellow-200 dark:border-yellow-800;
}

.notification.info {
  @apply bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 border border-blue-200 dark:border-blue-800;
}

.notification-close {
  @apply w-5 h-5 rounded-full bg-current bg-opacity-20 hover:bg-opacity-30 flex items-center justify-center transition-colors;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 对话主区域loading样式 */
.conversation-main.has-loading {
  @apply pointer-events-none select-none;
}

.conversation-loading-overlay {
  @apply absolute inset-0 bg-white/70 dark:bg-gray-900/70 backdrop-blur-sm z-50 flex items-center justify-center;

  backdrop-filter: blur(8px);
}

.conversation-loading-content {
  @apply flex flex-col items-center gap-4 p-8 bg-white dark:bg-gray-800 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700;
}

.conversation-loading-content i {
  @apply text-4xl text-blue-500 dark:text-blue-400;
}

.conversation-loading-content p {
  @apply text-lg font-semibold text-gray-800 dark:text-gray-200 m-0;
}

/* 消息列表loading样式 */
.message-loading {
  @apply flex items-center justify-center h-64;
}

.message-loading-content {
  @apply flex flex-col items-center gap-4 text-center;
}

.message-loading .loading-spinner {
  @apply text-4xl text-blue-500 dark:text-blue-400 mb-2;
}

.message-loading p {
  @apply text-lg font-medium text-gray-700 dark:text-gray-300 m-0;
}

.loading-dots {
  @apply flex gap-1;
}

.loading-dots span {
  @apply w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full;

  animation: loading-dots 1.4s infinite ease-in-out;
}

.loading-dots span:nth-child(1) {
  animation-delay: 0s;
}

.loading-dots span:nth-child(2) {
  animation-delay: 0.16s;
}

.loading-dots span:nth-child(3) {
  animation-delay: 0.32s;
}

@keyframes loading-dots {
  0%,
  80%,
  100% {
    opacity: 0.5;
    transform: scale(0.8);
  }

  40% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%,
  100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 响应式布局优化 */
@media (height <= 600px) {
  .agent-chat-page {
    height: 100vh;
    min-height: 600px;
  }

  .message-container {
    padding: 1rem;
    padding-bottom: 1rem;
  }

  .welcome-message {
    @apply py-3;
  }
}

@media (width <= 768px) {
  .conversation-sidebar {
    @apply w-64;
  }

  .agent-chat-header {
    @apply px-4 py-3;
  }

  .message-container {
    @apply px-4;
  }
}

@media (width <= 640px) {
  .conversation-sidebar {
    @apply w-full absolute inset-y-0 left-0 z-20;

    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .conversation-sidebar:not(.collapsed) {
    transform: translateX(0);
  }

  .suggestion-list {
    @apply grid-cols-1;
  }
}

/* 确保在任何设备上都有合适的最小高度 */
@media (height >= 400px) {
  .agent-chat-page {
    height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom));
    min-height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom));
  }
}
</style>
