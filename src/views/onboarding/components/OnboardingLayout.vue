<template>
  <div class="onboarding-layout">
    <div class="main-content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
// This is a layout component, so it might not need complex logic.
// Logic for handling layout variations could be added here in the future.
</script>

<style scoped>
.onboarding-layout {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  min-height: 100vh;
  padding: 64px 24px;
  background-color: #fff;
}

.main-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 1200px; /* Adjust max-width as needed */
}
</style>
