<template>
  <div
    class="pricing-card"
    :class="{
      'is-recommended': plan.isRecommended && !selected,
      'is-selected': selected,
      'is-selectable': selectable
    }"
    @click="handleCardClick"
    :tabindex="selectable ? 0 : -1"
    @keydown.enter="handleKeyboardSelect"
  >
    <div v-if="plan.isRecommended && !selected" class="recommended-badge">Recommended</div>
    <div v-if="selected" class="selected-badge">
      <svg class="selected-icon" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M13.3334 4L6.00008 11.3333L2.66675 8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      Selected
    </div>
    <div class="plan-name">{{ plan.name }}</div>
    <div class="price-container">
      <div v-if="!isNumberPrice(plan.price)" class="price-string">
        {{ plan.price }}
      </div>
      <template v-else>
        <span class="price-currency">¥</span>
        <span class="price-amount">{{ plan.price }}</span>
        <span class="price-period"> / 年</span>
      </template>
    </div>
    <div class="plan-description">{{ plan.description }}</div>
    <button
      class="cta-button"
      :class="{ 'is-recommended': plan.isRecommended && !selected, 'is-selected': selected }"
      @click.stop="handleButtonClick"
    >
      {{ plan.ctaText }}
    </button>
    <div class="features-list">
      <div v-for="(feature, index) in plan.features" :key="index" class="feature-item">
        <svg class="feature-checkmark" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M13.3334 4L6.00008 11.3333L2.66675 8" stroke="#34D399" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>
        <span>{{ feature }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';
import { useRouter } from 'vue-router';

interface Plan {
  name: string;
  price: string | number;
  description: string;
  features: string[];
  isRecommended: boolean;
  ctaText: string;
}

interface Props {
  plan: Plan;
  selected?: boolean;
  selectable?: boolean;
  moduleData?: any; // 添加可选的原始模块数据
}

interface Emits {
  (e: 'select', plan: Plan): void;
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  selectable: true
});

const emit = defineEmits<Emits>();
const router = useRouter();

const isNumberPrice = (price: string | number): price is number => {
  return typeof price === 'number';
};

// 提取数字价格用于跳转
const getNumericPrice = (): number | null => {
  // 优先使用 moduleData 中的 priceValue
  if (props.moduleData && typeof props.moduleData.priceValue === 'number' && props.moduleData.priceValue > 0) {
    return props.moduleData.priceValue;
  }

  // 如果 plan.price 是数字类型，直接使用
  if (isNumberPrice(props.plan.price) && props.plan.price > 0) {
    return props.plan.price;
  }

  // 如果 plan.price 是字符串，尝试从中提取数字
  if (typeof props.plan.price === 'string') {
    const match = props.plan.price.match(/(\d+)/);
    if (match) {
      const price = parseInt(match[1], 10);
      if (price > 0) {
        return price;
      }
    }
  }

  return null;
};

// 处理卡片点击 - 只选择，不跳转
const handleCardClick = () => {
  if (props.selectable && !props.selected) {
    // 只发射 select 事件给父组件
    emit('select', props.plan);
  }
};

// 处理按钮点击 - 执行跳转逻辑
const handleButtonClick = (event: Event) => {
  // 阻止事件冒泡到卡片
  event.stopPropagation();

  if (props.selectable) {
    // 如果尚未选择，先选择
    if (!props.selected) {
      emit('select', props.plan);
    }

    // 获取数字价格
    const numericPrice = getNumericPrice();

    // 构建路由查询参数
    const query: Record<string, string> = {
      source: 'onboarding',
      plan: props.plan.name
    };

    // 如果有数值价格，添加到查询参数中
    if (numericPrice) {
      query.amount = numericPrice.toString();
    }

    // 直接跳转到充值页面，用户在引导页面时肯定已经登录
    router.push({
      path: '/billing/recharge',
      query
    }).catch((error) => {
      console.error('Navigation failed:', error);
    });
  }
};

// 处理键盘选择 - 保持原有逻辑用于无障碍访问
const handleKeyboardSelect = () => {
  if (props.selectable && !props.selected) {
    // 发射原有的 select 事件以保持兼容性
    emit('select', props.plan);

    // 获取数字价格
    const numericPrice = getNumericPrice();

    // 构建路由查询参数
    const query: Record<string, string> = {
      source: 'onboarding',
      plan: props.plan.name
    };

    // 如果有数值价格，添加到查询参数中
    if (numericPrice) {
      query.amount = numericPrice.toString();
    }

    // 直接跳转到充值页面，用户在引导页面时肯定已经登录
    router.push({
      path: '/billing/recharge',
      query
    }).catch((error) => {
      console.error('Navigation failed:', error);
    });
  }
};
</script>

<style scoped>
.pricing-card {
  @apply border border-gray-200 rounded-lg p-6 flex flex-col items-start w-full max-w-sm bg-white transition-all duration-300;

  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 5%), 0 2px 4px -2px rgb(0 0 0 / 5%);
}

.pricing-card.is-selectable {
  @apply cursor-pointer;
}

.pricing-card.is-selectable:hover {
  @apply transform scale-102;

  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 15%), 0 4px 6px -4px rgb(0 0 0 / 15%);
}

.pricing-card.is-selectable:hover:not(.is-selected, .is-recommended) {
  @apply border-blue-300;
}

.pricing-card.is-recommended {
  @apply border-2 border-blue-500 relative;

  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 10%), 0 4px 6px -4px rgb(0 0 0 / 10%);
}

.pricing-card.is-selected {
  @apply border-2 border-blue-600 relative;

  background: linear-gradient(135deg, rgb(239 246 255) 0%, rgb(219 234 254) 100%);
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 15%), 0 10px 10px -5px rgb(0 0 0 / 10%);
}

.recommended-badge {
  @apply absolute -top-3.5 right-6 bg-blue-500 text-white text-xs font-semibold px-3 py-1 rounded-full;
}

.selected-badge {
  @apply absolute -top-3.5 right-6 bg-blue-600 text-white text-xs font-semibold px-3 py-1 rounded-full flex items-center gap-1;
}

.selected-icon {
  @apply w-3 h-3;
}

.plan-name {
  @apply text-lg font-semibold text-gray-800;
}

.pricing-card.is-selected .plan-name {
  @apply text-blue-900;
}

.price-container {
  @apply flex items-baseline mt-4;
}

.price-string {
  @apply text-2xl font-bold text-gray-900;
}

.pricing-card.is-selected .price-string {
  @apply text-blue-900;
}

.price-currency {
  @apply text-2xl font-semibold text-gray-800 mr-1;
}

.price-amount {
  @apply text-5xl font-bold text-gray-900;
}

.pricing-card.is-selected .price-currency,
.pricing-card.is-selected .price-amount {
  @apply text-blue-900;
}

.price-period {
  @apply text-sm text-gray-500 ml-2;
}

.pricing-card.is-selected .price-period {
  @apply text-blue-700;
}

.plan-description {
  @apply text-sm text-gray-600 mt-2 h-10;
}

.pricing-card.is-selected .plan-description {
  @apply text-blue-800;
}

.cta-button {
  @apply w-full text-center py-2.5 rounded-md mt-6 font-semibold text-sm transition-colors duration-300;
  @apply bg-white text-gray-800 border border-gray-300 hover:bg-gray-50 cursor-pointer;
}

.cta-button.is-recommended {
  @apply bg-blue-500 text-white hover:bg-blue-600 border-none;
}

.cta-button.is-selected {
  @apply bg-blue-600 text-white hover:bg-blue-700 border-none;
}

.features-list {
  @apply mt-6 space-y-3;
}

.feature-item {
  @apply flex items-center text-sm text-gray-700;
}

.pricing-card.is-selected .feature-item {
  @apply text-blue-800;
}

.feature-checkmark {
  @apply mr-2;
}

/* Focus styles for accessibility */
.pricing-card:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}
</style>
