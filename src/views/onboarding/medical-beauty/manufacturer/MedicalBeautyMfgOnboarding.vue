<template>
  <OnboardingLayout>
    <div class="header-section">
      <h1 class="title">欢迎，医美厂商！</h1>
      <h2 class="subtitle">探索为您的业务量身打造的强大功能。</h2>
    </div>

    <div class="pricing-container">
      <PricingSection
        v-for="module in modules"
        :key="module.id"
        :plan="mapModuleToPlan(module)"
        :module-data="module"
        :selected="selectedModule?.id === module.id"
        @select="handleModuleSelect"
      />
    </div>

    <div class="completion-section">
      <button @click="completeOnboarding" class="complete-button">
        开始使用
      </button>
    </div>
  </OnboardingLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import OnboardingLayout from '../../components/OnboardingLayout.vue';
import PricingSection from '../../components/PricingSection.vue';
import { useUserStore } from '@/store/modules/user';
import { ONBOARDING_CONFIG } from '../../config/onboardingConfig';
import { MODULE_CONFIGS } from '../../config/moduleConfigs';

type ModuleId = keyof typeof MODULE_CONFIGS;

interface Module {
  id: string;
  title: string;
  description: string;
  features: string[];
  pricing: string;
  priceValue: number;
  targetRoute: string;
  isRecommended?: boolean;
}

interface Plan {
  name: string;
  price: number;
  description: string;
  features: string[];
  isRecommended: boolean;
  ctaText: string;
}

const router = useRouter();
const userStore = useUserStore();

const modules = ref<Module[]>([]);
const selectedModule = ref<Module | null>(null);

const mapModuleToPlan = (module: Module): Plan => {
  return {
    name: module.title,
    price: module.priceValue,
    description: module.description,
    features: module.features,
    isRecommended: module.isRecommended || false,
    ctaText: '选择',
  };
};

const handleModuleSelect = (plan: Plan) => {
  // Find the corresponding module by matching the title
  const module = modules.value.find(m => m.title === plan.name);
  if (module) {
    selectedModule.value = module;
  }
};

onMounted(() => {
  const industryType = 'MEDICAL_BEAUTY';
  const tenantType = 'MANUFACTURER';

  const config = ONBOARDING_CONFIG[industryType]?.[tenantType];
  if (config && config.modules) {
    modules.value = config.modules
      .map(moduleId => MODULE_CONFIGS[moduleId as ModuleId])
      .filter(Boolean) as Module[];
  }
});

async function completeOnboarding() {
  try {
    await userStore.completeOnboarding();
    console.log('Onboarding completed for Medical Beauty Manufacturer!');
    router.push('/');
  } catch (error) {
    console.error('Failed to complete onboarding:', error);
  }
}
</script>

<style scoped>
.header-section {
  @apply text-center mb-12;
}

.title {
  @apply text-5xl font-bold text-gray-900 tracking-tight;
}

.subtitle {
  @apply text-3xl font-semibold text-gray-700 mt-4;
}

.pricing-container {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 w-full;
}

.completion-section {
  @apply mt-16 text-center;
}

.complete-button {
  @apply bg-blue-600 text-white font-semibold py-3 px-8 rounded-lg text-lg hover:bg-blue-700 transition-colors;
}
</style>
