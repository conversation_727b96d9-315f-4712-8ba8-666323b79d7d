<template>
  <div class="ym-onboarding-container">
    <!-- 退出登录按钮 -->
    <button class="logout-button" @click="handleLogout" title="退出登录">
      <svg class="logout-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
      </svg>
    </button>

    <!-- 背景装饰 -->
    <div class="background-decoration" />

    <div class="container">
      <!-- 标题区域 -->
      <div class="header-section">
        <div class="header-top">
          <h2 class="main-title">
            <span class="gradient-text">欢迎，{{ currentUserName }}！</span>
          </h2>
        </div>
        <p class="subtitle">
          为您精选了以下核心功能，助您快速起步
        </p>
      </div>

      <!-- 套餐卡片 -->
      <div class="pricing-cards-container">
        <div
          v-for="plan in pricingPlans"
          :key="plan.id"
          :class="getPlanCardClasses(plan)"
          @click="handlePlanSelect(plan)"
        >
          <!-- 推荐标签 -->
          <div v-if="plan.isPopular" class="popular-badge">
            推荐
          </div>

          <div class="card-content">
            <!-- 套餐标题区域 -->
            <div class="plan-header">
              <h3 class="plan-title">{{ plan.name }}</h3>

              <div class="price-section">
                <!-- 价格显示 -->
                <div class="price-display">
                  <span class="price-value">
                    {{ plan.price === '即将推出' ? plan.price : `¥${plan.price}` }}
                  </span>
                  <span v-if="plan.period" class="price-period">
                    {{ plan.period }}
                  </span>
                </div>

                <!-- 原价显示 -->
                <div v-if="plan.originalPrice" class="original-price-section">
                  <span class="original-price">
                    原价 ¥{{ plan.originalPrice }}
                  </span>
                  <span class="savings">
                    节省 ¥{{ calculateSavings(plan) }}
                  </span>
                </div>
              </div>

              <p class="plan-description">{{ plan.description }}</p>
            </div>

            <!-- 折扣提示 -->
            <div v-if="plan.discount" class="discount-notice">
              <div class="discount-content">
                <span class="discount-emoji">🔥 限时优惠</span>
                <span class="discount-text">
                  {{ plan.discountEndDate }}前享受{{ plan.discount }}优惠
                </span>
              </div>
            </div>

            <!-- 选择按钮 -->
            <div class="action-section">
              <button
                v-if="plan.isAvailable && plan.price !== '即将推出'"
                :class="getActionButtonClasses(plan)"
                @click.stop="handlePlanSelect(plan)"
              >
                {{ getButtonText(plan) }}
              </button>
              <div v-else-if="plan.price === '即将推出'" class="coming-soon-notice">
                即将推出，敬请期待
              </div>
            </div>

            <!-- 功能列表 -->
            <div class="features-list">
              <div
                v-for="(feature, index) in plan.features"
                :key="index"
                class="feature-item"
              >
                <div :class="getFeatureIconClasses(feature)">
                  <svg
                    v-if="feature.included"
                    class="check-icon"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  <span v-else class="cross-icon">×</span>
                </div>
                <div class="feature-content">
                  <h4 :class="getFeatureTitleClasses(feature)">
                    {{ feature.name }}
                  </h4>
                  <p :class="getFeatureDescClasses(feature)">
                    {{ feature.description }}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- 装饰性渐变 -->
          <div class="card-decoration-1" />
          <div class="card-decoration-2" />
        </div>
      </div>

      <!-- 完成按钮区域 - 已移除，用户直接通过购买跳转 -->

      <!-- 底部说明 -->
      <div class="bottom-section">
        <p class="bottom-text">
          需要定制化方案？
          <button class="contact-link" @click="openContactDialog">
            联系我们
          </button>
        </p>
      </div>
    </div>

    <!-- 联系销售顾问弹窗 -->
    <div v-if="isContactDialogOpen" class="dialog-overlay" @click="closeContactDialog">
      <div class="dialog-content" @click.stop>
        <div class="dialog-inner">
          <h3 class="dialog-title">联系销售顾问</h3>
          <p class="dialog-subtitle">微信扫码，我们即刻为您服务</p>
          <div class="qr-code-container">
            <img
              src="@/assets/images/dips-agent.png"
              alt="销售顾问微信二维码"
              class="qr-code-image"
            />
          </div>
          <p class="working-hours">工作时间：周一至周五 9:00-18:00</p>
          <button @click="closeContactDialog" class="close-button">
            关闭
          </button>
        </div>
      </div>
    </div>

    <!-- 微信支付二维码对话框 -->
    <WechatPaymentModal
      v-model:visible="wechatPaymentVisible"
      :recharge-data="currentRechargeData"
      @payment-success="handlePaymentSuccess"
      @payment-failed="handlePaymentFailed"
      @payment-cancelled="handlePaymentCancelled"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/store/modules/user';
import { useBillingStore } from '@/stores/billing';
import WechatPaymentModal from '@/components/billing/WechatPaymentModal.vue';
import { BillingAPI } from '@/api/billing';
import type { RechargeRequest, IndustryTenantPricing, PackageFeature, ApiResponse } from '@/types/billing';
import { request } from '@/api';
import { formatCurrency } from '@/utils/format';
import storage from '@/utils/storage';
import { message } from 'ant-design-vue';

interface PricingPlan {
  id: string;
  name: string;
  price: string;
  originalPrice?: string;
  discount?: string;
  discountEndDate?: string;
  period?: string;
  description: string;
  isAvailable: boolean;
  isPopular: boolean;
  features: Array<PackageFeature>;
}

const router = useRouter();
const userStore = useUserStore();
const billingStore = useBillingStore();

// 手动扩展 BillingAPI - 解决导入缓存问题
const ExtendedBillingAPI = {
  ...BillingAPI,
  // 获取当前用户对应的套餐定价列表
  async getCurrentUserPricing(): Promise<ApiResponse<IndustryTenantPricing[]>> {
    console.log('🔍 调用手动定义的 getCurrentUserPricing 方法');
    return request.get('/api/billing/pricing/current');
  },
  // 根据行业类型和租户类型获取套餐定价列表
  async getPricingByType(industryType: string, tenantType: string): Promise<ApiResponse<IndustryTenantPricing[]>> {
    return request.get('/api/billing/pricing/by-type', {
      params: { industryType, tenantType }
    });
  },
  // 获取所有可用的套餐定价列表
  async getAllAvailablePricing(): Promise<ApiResponse<IndustryTenantPricing[]>> {
    return request.get('/api/billing/pricing/available');
  }
};

const selectedPlan = ref<PricingPlan | null>(null);
const isContactDialogOpen = ref(false);
const loading = ref(false);

// 微信支付相关状态
const wechatPaymentVisible = ref(false);
const currentRechargeData = ref<RechargeRequest | null>(null);

// 获取当前用户名称
const currentUserName = computed(() => {
  // 优先使用真实姓名，其次是用户名，如果都没有则使用默认文本
  return userStore.realName || userStore.username || userStore.account || '医美机构用户';
});

// 从后端获取的套餐数据
const pricingPlans = ref<PricingPlan[]>([]);

// 将后端数据转换为前端格式
const convertToPricingPlan = (pricing: IndustryTenantPricing): PricingPlan => {
  return {
    id: pricing.id.toString(),
    name: pricing.packageName,
    price: pricing.price ? pricing.price.toString() : '即将推出',
    originalPrice: pricing.originalPrice ? pricing.originalPrice.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') : undefined,
    discount: pricing.discountText || undefined,
    discountEndDate: pricing.discountEndDate || undefined,
    period: pricing.period || undefined,
    description: pricing.packageDescription || '',
    isAvailable: pricing.isAvailable,
    isPopular: pricing.isPopular,
    features: pricing.features || []
  };
};

// 加载套餐数据
const loadPricingPlans = async () => {
  try {
    loading.value = true;
    const response = await ExtendedBillingAPI.getCurrentUserPricing();

    if (response.success && response.data) {
      pricingPlans.value = response.data.map(convertToPricingPlan);
    } else {
      message.error('获取套餐信息失败');
      // 如果获取失败，使用默认数据作为备选
      // loadDefaultPricingPlans();
    }
  } catch (error) {
    console.error('加载套餐信息失败:', error);
    message.error('加载套餐信息失败，使用默认配置');
    // 如果获取失败，使用默认数据作为备选
    // loadDefaultPricingPlans();
  } finally {
    loading.value = false;
  }
};

// 备选的默认套餐数据（如果后端获取失败时使用）
const loadDefaultPricingPlans = () => {
  pricingPlans.value = [
    {
      id: 'customer-experience',
      name: '基础版',
      price: '1',
      originalPrice: '59,999',
      discount: '5折',
      discountEndDate: '2025年8月31日',
      period: '/ 年',
      description: '智能版标准化消费者体验研究',
      isAvailable: true,
      isPopular: true,
      features: [
        {
          name: '消费者体验研究问卷',
          description: '专业定制问卷服务 - 专业定制问卷服务，基于李克特5分量表精准评估产品与服务核心维度，真实反馈客户需求',
          included: true
        },
        {
          name: '数据采集',
          description: '多渠道智能数据收集 - 支持一店一码、短信链接等多种线上填写方式，数据实时同步云端，杜绝手工录入与信息遗漏',
          included: true
        },
        {
          name: '数据质量控制',
          description: '数据清洗与验证服务 - 专业数据分析团队对原始数据进行逻辑校验、完整性检查及异常值处理，确保最终分析结果精准可靠',
          included: true
        },
        {
          name: '数据分析',
          description: '深度洞察分析服务 - 自研数据流计算引擎，实现高效低成本数据处理，支持多维度交叉分析及NPS/忠诚度等专业模型评估，快速识别服务短板',
          included: true
        },
        {
          name: '数据动态看板',
          description: '实时可视化平台 - 交互看板实时展示调研进度、回访得分、客户VOC数据，多维度筛选分析，助力机构动态掌握消费者评价',
          included: true
        },
        {
          name: '回访数据导出',
          description: '灵活数据导出功能 - 支持按需导出Excel格式的原始数据，便于二次分析或纳入其他管理系统',
          included: true
        },
        {
          name: '标准分析报告',
          description: '标准分析报告服务 - 调研周期内月度更新标准报告，支持多维度分析及可视化图表，配置灵活高效',
          included: true
        },
        {
          name: '使用手册',
          description: '手册服务-本手册详细说明系统的操作流程与功能模块，帮助用户快速掌握系统使用方法，实现高效数据管理与分析',
          included: true
        },
        {
          name: '客服支持',
          description: '专业客服团队服务-解答使用问题，协助您高效开展消费者体验研究工作',
          included: true
        }
      ]
    },
    {
      id: 'premium-ai',
      name: '升级专业版',
      price: '即将推出',
      period: '',
      description: '定制化全流程行业AI',
      isAvailable: true,
      isPopular: false,
      features: [
        {
          name: '基础版所有功能',
          description: '包含基础版本中的所有功能',
          included: true
        },
        {
          name: '关键指标异常自动预警',
          description: '每当有不满意的客户完成答卷，通过邮件接收通知，及时采取补救措施，挽回客户关系，降低客户流失风险',
          included: true
        },
        {
          name: '分析结果智能输出',
          description: '基于调研数据智能输出调研结果月度统计报表',
          included: true
        },
        {
          name: '行业数据查询',
          description: '快捷查询行业数据库，精准定位本品在竞争市场中的坐标位次和竞争力水平，了解行业基准数据，找到提升方向',
          included: true
        },
        {
          name: '高级分析',
          description: '增加文本情感分析、消费者行为和偏好、消费者满意度等深度分析，基于消费行为和满意度数据，将客户划分类型',
          included: true
        },
        {
          name: '定制报告服务',
          description: '根据机构特定需求制作个性化分析报告，满足不同层级管理者的决策需求，提供更有针对性的数据支持',
          included: true
        },
        {
          name: '报告解读',
          description: '专业咨询顾问进行数据解读与数据应用指导，帮助您深入理解数据背后的含义，制定优化策略',
          included: true
        }
      ]
    }
  ];
};

// 计算节省金额
const calculateSavings = (plan: PricingPlan) => {
  if (!plan.originalPrice || plan.price === '即将推出') return '0';
  const original = parseFloat(plan.originalPrice.replace(',', ''));
  const current = parseFloat(plan.price.replace(',', ''));
  return (original - current).toLocaleString();
};

// 卡片样式类
const getPlanCardClasses = (plan: PricingPlan) => {
  const baseClasses = 'pricing-card';
  const popularClasses = plan.isPopular ? 'popular' : '';
  const selectedClasses = selectedPlan.value?.id === plan.id ? 'selected' : '';
  const unavailableClasses = !plan.isAvailable ? 'unavailable' : '';

  return `${baseClasses} ${popularClasses} ${selectedClasses} ${unavailableClasses}`.trim();
};

// 按钮样式类
const getActionButtonClasses = (plan: PricingPlan) => {
  const baseClasses = 'action-button';
  const selectedClasses = selectedPlan.value?.id === plan.id ? 'selected' : '';

  return `${baseClasses} ${selectedClasses}`.trim();
};

// 功能图标样式类
const getFeatureIconClasses = (feature: { included: boolean }) => {
  const baseClasses = 'feature-icon';
  const includedClasses = feature.included ? 'included' : 'excluded';

  return `${baseClasses} ${includedClasses}`.trim();
};

// 功能标题样式类
const getFeatureTitleClasses = (feature: { included: boolean }) => {
  const baseClasses = 'feature-title';
  const includedClasses = feature.included ? 'included' : 'excluded';

  return `${baseClasses} ${includedClasses}`.trim();
};

// 功能描述样式类
const getFeatureDescClasses = (feature: { included: boolean }) => {
  const baseClasses = 'feature-description';
  const includedClasses = feature.included ? 'included' : 'excluded';

  return `${baseClasses} ${includedClasses}`.trim();
};

const handlePlanSelect = (plan: PricingPlan) => {
  // 如果是基础版，直接弹出微信支付二维码
  if (plan.id === 'customer-experience' && plan.price !== '即将推出') {
    // 获取数字价格
    const numericPrice = parseFloat(plan.price.replace(',', ''));

    // 构建充值请求数据
    const rechargeData: RechargeRequest = {
      amount: numericPrice,
      paymentMethod: 'WECHAT',
      description: `购买套餐：${plan.name} - ${formatCurrency(numericPrice)}`,
    };

    // 设置当前充值数据并显示微信支付对话框
    currentRechargeData.value = rechargeData;
    wechatPaymentVisible.value = true;

    return;
  }

  // 其他套餐保持原有选择逻辑
  selectedPlan.value = selectedPlan.value?.id === plan.id ? null : plan;
};

const openContactDialog = () => {
  isContactDialogOpen.value = true;
};

const closeContactDialog = () => {
  isContactDialogOpen.value = false;
};

const handleLogout = async () => {
  try {
    // 假设 userStore 中有 logout 方法，用于清理状态和 token
    await userStore.logout();
    message.success('您已成功退出登录');
    // 跳转到登录页
    router.push('/login');
  } catch (error) {
    console.error('退出登录失败:', error);
    message.error('退出登录失败，请稍后重试');
  }
};

// 页面加载时获取套餐数据
onMounted(() => {
  loadPricingPlans();
});

// completeOnboarding 函数已移除 - 用户现在通过购买流程完成引导

const getButtonText = (plan: PricingPlan) => {
  if (plan.id === 'customer-experience') {
    return '立即购买';
  }
  return '立即咨询';
};

// 微信支付相关方法
const handlePaymentSuccess = async (outTradeNo: string) => {
  try {
    console.log('🎯 [支付成功] 检测到支付成功，订单号:', outTradeNo);

    // 刷新余额
    await billingStore.manualRefreshBalance();

    console.log('✅ [支付成功] 余额已刷新');

    // 标记引导已完成
    userStore.firstLoginCompleted = true;
    storage.local.set('firstLoginCompleted', 'true');

    console.log('✅ [支付成功] 引导状态已更新为完成');

    // 显示成功提示
    message.success('购买成功！引导流程已完成，欢迎使用 DIPS Pro！', 3);

    // 可选：延时后跳转到主页面
    setTimeout(() => {
      router.push('/login').catch((error) => {
        console.error('Navigation failed:', error);
      });
    }, 2000);

  } catch (error: any) {
    console.error('处理支付成功事件失败:', error);
    // 支付已经成功，只是刷新数据失败，显示成功消息
    message.success('购买成功！', 2);
  }
};

const handlePaymentFailed = (error: string) => {
  console.error('支付失败:', error);
  message.error(`支付失败：${error}`);
};

const handlePaymentCancelled = () => {
  message.info('支付已取消');
};
</script>

<style scoped>
.ym-onboarding-container {
  position: relative;
  min-height: 100vh;
  padding: 6rem 0;
  overflow: hidden;
  background: linear-gradient(135deg, rgb(248 250 252) 0%, rgb(243 232 255) 100%);
}

.background-decoration {
  position: absolute;
  inset: 0;
  background-image: radial-gradient(circle at 1px 1px, rgb(147 51 234 / 10%) 1px, transparent 0);
  background-size: 20px 20px;
  mask-image: linear-gradient(0deg, white, rgb(255 255 255 / 60%));
}

.container {
  position: relative;
  z-index: 10;
  max-width: 1200px;
  padding: 0 1.5rem;
  margin: 0 auto;
}

/* 标题区域 */
.header-section {
  margin-bottom: 4rem;
  text-align: center;
}

.header-top {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.logout-button {
  position: absolute;
  top: 2rem;
  right: 2rem;
  z-index: 20;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  padding: 0;
  color: rgb(107 114 128);
  cursor: pointer;
  background-color: transparent;
  border: 1px solid rgb(209 213 219);
  border-radius: 50%;
  transition: all 0.2s ease-in-out;
}

.logout-button:hover {
  color: rgb(17 24 39);
  background-color: rgb(243 244 246);
  border-color: rgb(156 163 175);
}

.logout-icon {
  width: 1.25rem;
  height: 1.25rem;
}

@media (width < 768px) {
  .logout-button {
    top: 1rem;
    right: 1rem;
  }

  .main-title {
    margin-bottom: 0;
  }
}

.main-title {
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: 700;
  color: rgb(17 24 39);
}

@media (width >= 768px) {
  .main-title {
    font-size: 3rem;
  }
}

@media (width >= 1024px) {
  .main-title {
    font-size: 4rem;
  }
}

.gradient-text {
  background: linear-gradient(135deg, rgb(147 51 234) 0%, rgb(168 85 247) 50%, rgb(192 132 252) 100%);
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.subtitle {
  max-width: 48rem;
  margin: 0 auto;
  font-size: 1.25rem;
  line-height: 1.6;
  color: rgb(75 85 99);
}

@media (width >= 768px) {
  .subtitle {
    font-size: 1.5rem;
  }
}

/* 套餐卡片容器 */
.pricing-cards-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 3rem;
  max-width: 72rem;
  margin: 0 auto;
}

@media (width >= 768px) {
  .pricing-cards-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 套餐卡片 */
.pricing-card {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  background: white;
  border: 2px solid transparent;
  border-radius: 1.5rem;
  box-shadow: 0 25px 50px -12px rgb(0 0 0 / 25%);
  transition: all 0.3s ease;
}

.pricing-card:hover {
  box-shadow: 0 32px 64px -12px rgb(0 0 0 / 35%);
  transform: translateY(-4px);
}

.pricing-card.popular {
  border-color: rgb(147 51 234);
}

.pricing-card.selected {
  border-color: rgb(34 197 94);
  box-shadow: 0 0 0 2px rgb(34 197 94 / 20%);
}

.pricing-card.unavailable {
  opacity: 0.75;
}

.popular-badge {
  position: absolute;
  top: 0;
  left: 50%;
  padding: 0.25rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: white;
  background: rgb(147 51 234);
  border-radius: 0 0 0.5rem 0.5rem;
  transform: translateX(-50%);
}

.card-content {
  padding: 2.5rem;
}

/* 套餐标题区域 */
.plan-header {
  margin-bottom: 2rem;
  text-align: center;
}

.plan-title {
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
  font-weight: 700;
  color: rgb(17 24 39);
}

.price-section {
  margin-bottom: 0.5rem;
}

.price-display {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 0.25rem;
}

.price-value {
  font-size: 3rem;
  font-weight: 700;
  color: rgb(147 51 234);
}

.price-period {
  margin-left: 0.25rem;
  font-size: 1.125rem;
  color: rgb(168 85 247);
}

.original-price-section {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  justify-content: center;
  margin-top: 0.25rem;
}

.original-price {
  font-size: 1.125rem;
  color: rgb(156 163 175);
  text-decoration: line-through;
}

.savings {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgb(34 197 94);
}

.plan-description {
  line-height: 1.5;
  color: rgb(75 85 99);
}

/* 折扣提示 */
.discount-notice {
  padding: 0.75rem;
  margin-bottom: 1rem;
  background: rgb(255 251 235);
  border: 1px solid rgb(254 215 170);
  border-radius: 0.5rem;
}

.discount-content {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  justify-content: center;
}

.discount-emoji {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgb(234 88 12);
}

.discount-text {
  font-size: 0.875rem;
  color: rgb(180 83 9);
}

/* 按钮区域 */
.action-section {
  margin-bottom: 2rem;
}

.action-button {
  width: 100%;
  padding: 1rem 1.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  cursor: pointer;
  background: rgb(147 51 234);
  border: none;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
}

.action-button:hover {
  background: rgb(126 34 206);
  box-shadow: 0 10px 25px rgb(147 51 234 / 30%);
  transform: scale(1.02);
}

.action-button.selected {
  background: rgb(34 197 94);
}

.action-button.selected:hover {
  background: rgb(22 163 74);
}

.coming-soon-notice {
  width: 100%;
  padding: 1rem 1.5rem;
  margin-top: 1rem;
  font-size: 1.125rem;
  font-weight: 600;
  color: rgb(156 163 175);
  text-align: center;
  background: rgb(243 244 246);
  border: 1px solid rgb(229 231 235);
  border-radius: 0.75rem;
}

/* 功能列表 */
.features-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.feature-item {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.feature-icon {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  margin-top: 0.25rem;
  border-radius: 50%;
}

.feature-icon.included {
  background: rgb(243 232 255);
}

.feature-icon.excluded {
  background: rgb(243 244 246);
}

.check-icon {
  width: 1.5rem;
  height: 1.5rem;
  color: rgb(147 51 234);
}

.cross-icon {
  font-size: 0.75rem;
  font-weight: 700;
  color: rgb(156 163 175);
}

.feature-content {
  flex: 1;
}

.feature-title {
  margin-bottom: 0.25rem;
  font-weight: 600;
  line-height: 1.5;
}

.feature-title.included {
  color: rgb(17 24 39);
}

.feature-title.excluded {
  color: rgb(156 163 175);
}

.feature-description {
  font-size: 0.875rem;
  line-height: 1.6;
}

.feature-description.included {
  color: rgb(75 85 99);
}

.feature-description.excluded {
  color: rgb(156 163 175);
}

/* 完成区域样式已移除 - 用户现在通过购买流程完成引导 */

/* 底部说明 */
.bottom-section {
  margin-top: 4rem;
  text-align: center;
}

.bottom-text {
  font-size: 1.125rem;
  color: rgb(75 85 99);
}

.contact-link {
  margin-left: 0.5rem;
  font-weight: 500;
  color: rgb(147 51 234);
  text-decoration: underline;
  cursor: pointer;
  background: transparent;
  border: none;
}

.contact-link:hover {
  color: rgb(126 34 206);
}

/* 弹窗样式 */
.dialog-overlay {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgb(0 0 0 / 50%);
}

.dialog-content {
  width: 90%;
  max-width: 28rem;
  background: white;
  border-radius: 1rem;
}

.dialog-inner {
  padding: 1.5rem;
  text-align: center;
}

.dialog-title {
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 700;
  color: rgb(31 41 55);
}

.dialog-subtitle {
  margin-bottom: 1.5rem;
  color: rgb(75 85 99);
}

.qr-code-container {
  display: inline-block;
  padding: 1rem;
  margin-bottom: 1.5rem;
  background: white;
  border: 1px solid rgb(229 231 235);
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 10%);
}

.qr-code-image {
  width: 14rem;
  height: 14rem;
  object-fit: contain;
  border-radius: 0.25rem;
}

.working-hours {
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
  color: rgb(107 114 128);
}

.close-button {
  padding: 0.5rem 1.5rem;
  font-size: 1rem;
  color: rgb(55 65 81);
  cursor: pointer;
  background: rgb(243 244 246);
  border: none;
  border-radius: 1.5rem;
  transition: background-color 0.2s;
}

.close-button:hover {
  background: rgb(229 231 235);
}

/* 装饰性渐变 */
.card-decoration-1 {
  position: absolute;
  top: 0;
  right: 0;
  width: 8rem;
  height: 8rem;
  background: linear-gradient(135deg, rgb(147 51 234 / 20%) 0%, rgb(168 85 247 / 20%) 100%);
  border-radius: 50%;
  opacity: 0.2;
  filter: blur(3rem);
}

.card-decoration-2 {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 6rem;
  height: 6rem;
  background: linear-gradient(135deg, rgb(147 51 234 / 20%) 0%, rgb(168 85 247 / 20%) 100%);
  border-radius: 50%;
  opacity: 0.15;
  filter: blur(2rem);
}

/* 响应式调整 */
@media (width <= 768px) {
  .ym-onboarding-container {
    padding: 3rem 0;
  }

  .card-content {
    padding: 2rem 1.5rem;
  }

  .price-value {
    font-size: 2.5rem;
  }

  .main-title {
    font-size: 2.5rem;
  }

  .pricing-cards-container {
    gap: 2rem;
  }
}
</style>
