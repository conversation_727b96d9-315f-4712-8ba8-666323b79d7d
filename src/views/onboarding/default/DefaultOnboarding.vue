<template>
  <OnboardingLayout>
    <div class="header-section">
      <h1 class="title">One tool for your whole company.</h1>
      <h2 class="subtitle">Free for teams to try.</h2>
    </div>

    <div class="pricing-container">
      <PricingSection
        v-for="plan in defaultPlans"
        :key="plan.name"
        :plan="plan"
        :selected="selectedPlan?.name === plan.name"
        @select="handlePlanSelect"
      />
    </div>

    <div class="completion-section">
      <button @click="completeOnboarding" class="complete-button">
        Get Started
      </button>
    </div>
  </OnboardingLayout>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import OnboardingLayout from '../components/OnboardingLayout.vue';
import PricingSection from '../components/PricingSection.vue';
import { useUserStore } from '@/store/modules/user';

interface Plan {
  name: string;
  price: number;
  description: string;
  features: string[];
  isRecommended: boolean;
  ctaText: string;
}

const router = useRouter();
const userStore = useUserStore();

const selectedPlan = ref<Plan | null>(null);

const defaultPlans = ref([
  {
    name: 'Free',
    price: 0,
    description: 'For individuals to organize personal projects and life.',
    features: ['Free for individual usage', 'Basic forms', 'Basic sites', 'Notion Calendar'],
    isRecommended: false,
    ctaText: 'Sign up',
  },
  {
    name: 'Plus',
    price: 10,
    description: 'For small teams and professionals to work together.',
    features: ['Everything in Free', 'Unlimited collaborative blocks', 'Unlimited file uploads', 'Unlimited charts'],
    isRecommended: false,
    ctaText: 'Get started',
  },
  {
    name: 'Business',
    price: 20,
    description: 'For growing businesses to streamline teamwork.',
    features: ['Everything in Plus', 'SAML SSO', 'Verify any page', 'Private teamspaces'],
    isRecommended: true,
    ctaText: 'Get started',
  },
]);

const handlePlanSelect = (plan: Plan) => {
  selectedPlan.value = plan;
};

async function completeOnboarding() {
  try {
    await userStore.completeOnboarding();
    console.log('Onboarding completed!');
    router.push('/'); // Redirect to the main dashboard
  } catch (error) {
    console.error('Failed to complete onboarding process:', error);
    // Optionally, show an error message to the user
  }
}
</script>

<style scoped>
.header-section {
  @apply text-center mb-12;
}

.title {
  @apply text-5xl font-bold text-gray-900 tracking-tight;
}

.subtitle {
  @apply text-3xl font-semibold text-gray-700 mt-4;
}

.pricing-container {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 w-full;
}

.completion-section {
  @apply mt-16 text-center;
}

.complete-button {
  @apply bg-blue-600 text-white font-semibold py-3 px-8 rounded-lg text-lg hover:bg-blue-700 transition-colors;
}
</style>
