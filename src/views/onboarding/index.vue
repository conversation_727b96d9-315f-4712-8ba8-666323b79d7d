<template>
  <div class="loading-container">
    <p>Loading your personalized onboarding experience...</p>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/store/modules/user';
import { ONBOARDING_CONFIG } from './config/onboardingConfig';

type IndustryType = keyof typeof ONBOARDING_CONFIG;
type TenantType<T extends IndustryType> = keyof typeof ONBOARDING_CONFIG[T];

const router = useRouter();
const userStore = useUserStore();

onMounted(() => {
  const industryType = userStore.industryType?.toUpperCase() as IndustryType;
  const tenantType = userStore.tenantType?.toUpperCase() as TenantType<typeof industryType>;

  let targetPath = '/onboarding/default'; // Default path

  if (industryType && tenantType && ONBOARDING_CONFIG[industryType] && ONBOARDING_CONFIG[industryType][tenantType]) {
    targetPath = ONBOARDING_CONFIG[industryType][tenantType].path;
  }

  router.replace(targetPath);
});
</script>

<style scoped>
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  font-size: 1.2rem;
  color: #666;
}
</style>
