// onboardingConfig.ts
export const ONBOARDING_CONFIG = {
  'MEDICAL_BEAUTY': {
    'ORGANIZATION': {
      component: 'MedicalBeautyOrgOnboarding',
      path: '/onboarding/medical-beauty/organization',
      modules: ['satisfaction-survey', 'ai-qa', 'ai-analysis']
    },
    'MANUFACTURER': {
      component: 'MedicalBeautyMfgOnboarding',
      path: '/onboarding/medical-beauty/manufacturer',
      modules: ['product-showcase', 'market-analysis', 'distribution-network']
    },
    'CONSUMER': {
      component: 'MedicalBeautyConsumerOnboarding',
      path: '/onboarding/medical-beauty/consumer',
      modules: ['service-finder', 'consultation', 'review-system']
    }
  },
  'REAL_ESTATE': {
    'ORGANIZATION': {
      component: 'RealEstateOrgOnboarding',
      path: '/onboarding/real-estate/organization',
      modules: ['property-management', 'client-analysis', 'market-reports']
    },
    'MANUFACTURER': {
      component: 'RealEstateMfgOnboarding',
      path: '/onboarding/real-estate/manufacturer',
      modules: ['project-showcase', 'sales-analytics', 'market-positioning']
    },
    'CONSUMER': {
      component: 'RealEstateConsumerOnboarding',
      path: '/onboarding/real-estate/consumer',
      modules: ['property-search', 'price-analysis', 'investment-advisor']
    }
  }
}
