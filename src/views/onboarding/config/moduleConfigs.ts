export const MODULE_CONFIGS = {
  // 医美行业
  'satisfaction-survey': {
    id: 'satisfaction-survey',
    title: '客户研究-基础版',
    description: '111',
    features: ['客户满意度调研问卷', '调研动态数据仪表盘', '数据清洗', '作答数据导出', '客服支持', '标准报告（2？份）+ 标准解读'],
    pricing: '¥8999/年',
    priceValue: 999,
    preferredPriceField: 'priceValue',
    targetRoute: '/chat'
  },
  'ai-qa': {
    id: 'ai-qa',
    title: '客户研究-高级版',
    description: '222',
    features: ['基础版所有功能', '客户追踪', '低分客户通知', '医美满意度研究报告', '调研得分表'],
    pricing: '¥49999/年',
    priceValue: 0,
    preferredPriceField: 'pricing',
    targetRoute: '/chat'
  },
  // 'ai-analysis': {
  //   id: 'ai-analysis',
  //   title: '医美AI分析报告/服务',
  //   description: '多样化的AI分析报告和定制服务',
  //   features: ['多类型分析报告', '定制化服务', '数据深度挖掘'],
  //   pricing: '按报告收费',
  //   priceValue: 3999,
  //   preferredPriceField: 'priceValue',
  //   targetRoute: '/billing/analytics'
  // },
  'product-showcase': {
    id: 'product-showcase',
    title: '产品展示平台',
    description: '专业的医美产品展示和推广平台',
    features: ['产品3D展示', '技术资料管理', '客户案例展示'],
    pricing: '¥1999/年',
    priceValue: 1999,
    preferredPriceField: 'priceValue',
    targetRoute: '/products'
  },
  'market-analysis': {
    id: 'market-analysis',
    title: '市场分析工具',
    description: '深度市场数据分析和趋势预测',
    features: ['市场趋势分析', '竞品对比', '消费者洞察'],
    pricing: '¥299/月',
    priceValue: 299,
    preferredPriceField: 'pricing',
    targetRoute: '/analytics'
  },
  'distribution-network': {
    id: 'distribution-network',
    title: '渠道网络管理',
    description: '经销商和合作伙伴关系管理系统',
    features: ['渠道商管理', '订单跟踪', '佣金结算'],
    pricing: '¥2999/年',
    priceValue: 2999,
    preferredPriceField: 'priceValue',
    targetRoute: '/partners'
  },
  'service-finder': {
    id: 'service-finder',
    title: '服务查找',
    description: '快速查找并对比医美服务',
    features: ['按地理位置搜索', '服务项目筛选', '价格透明'],
    pricing: '免费',
    priceValue: 0,
    preferredPriceField: 'pricing',
    targetRoute: '/services'
  },
  consultation: {
    id: 'consultation',
    title: '在线咨询',
    description: '与专业医美顾问进行在线沟通',
    features: ['即时消息', '视频咨询', '方案定制'],
    pricing: '按次咨询',
    priceValue: 0,
    preferredPriceField: 'pricing',
    targetRoute: '/chat'
  },
  'review-system': {
    id: 'review-system',
    title: '评价系统',
    description: '查看真实的用户评价和案例分享',
    features: ['真实用户评价', '术前术后对比', '医生评价'],
    pricing: '免费',
    priceValue: 0,
    preferredPriceField: 'pricing',
    targetRoute: '/reviews'
  },

  // 地产行业 - 机构
  'property-management': {
    id: 'property-management',
    title: '房产管理系统',
    description: '全面的房产信息管理和客户关系管理系统',
    features: ['房源信息管理', '客户关系管理', '交易流程跟踪'],
    pricing: '¥1599/年',
    priceValue: 1599,
    preferredPriceField: 'priceValue',
    targetRoute: '/properties',
    isRecommended: true
  },
  'client-analysis': {
    id: 'client-analysis',
    title: '客户分析工具',
    description: '深度分析客户需求和购买行为',
    features: ['客户画像分析', '购买意向预测', '个性化推荐'],
    pricing: '¥899/年',
    priceValue: 899,
    preferredPriceField: 'priceValue',
    targetRoute: '/analytics'
  },
  'market-reports': {
    id: 'market-reports',
    title: '市场报告生成',
    description: '自动生成专业的房地产市场分析报告',
    features: ['市场趋势分析', '价格走势预测', '区域对比报告'],
    pricing: '¥599/年',
    priceValue: 599,
    preferredPriceField: 'priceValue',
    targetRoute: '/reports'
  },

  // 地产行业 - 开发商
  'project-showcase': {
    id: 'project-showcase',
    title: '项目展示平台',
    description: '专业的房地产项目展示和营销平台',
    features: ['3D项目展示', '虚拟样板间', '营销素材管理'],
    pricing: '¥3999/年',
    priceValue: 3999,
    preferredPriceField: 'priceValue',
    targetRoute: '/projects',
    isRecommended: true
  },
  'sales-analytics': {
    id: 'sales-analytics',
    title: '销售数据分析',
    description: '全面的销售数据分析和业绩追踪',
    features: ['销售漏斗分析', '业绩统计', '团队管理'],
    pricing: '¥1999/年',
    priceValue: 1999,
    preferredPriceField: 'priceValue',
    targetRoute: '/sales'
  },
  'market-positioning': {
    id: 'market-positioning',
    title: '市场定位分析',
    description: '精准的市场定位和竞品分析工具',
    features: ['竞品对比分析', '市场定位策略', '价格策略建议'],
    pricing: '¥1299/年',
    priceValue: 1299,
    preferredPriceField: 'priceValue',
    targetRoute: '/positioning'
  },

  // 地产行业 - 消费者
  'property-search': {
    id: 'property-search',
    title: '智能找房',
    description: '基于AI的智能房源搜索和推荐系统',
    features: ['智能搜索', '个性化推荐', '地图找房'],
    pricing: '免费',
    priceValue: 0,
    preferredPriceField: 'pricing',
    targetRoute: '/search'
  },
  'price-analysis': {
    id: 'price-analysis',
    title: '房价分析',
    description: '专业的房价分析和投资价值评估',
    features: ['房价走势分析', '投资回报计算', '区域价值评估'],
    pricing: '¥299/年',
    priceValue: 299,
    preferredPriceField: 'priceValue',
    targetRoute: '/analysis'
  },
  'investment-advisor': {
    id: 'investment-advisor',
    title: '投资顾问',
    description: 'AI驱动的房产投资建议和风险评估',
    features: ['投资建议', '风险评估', '收益预测'],
    pricing: '¥599/年',
    priceValue: 599,
    preferredPriceField: 'priceValue',
    targetRoute: '/advisor',
    isRecommended: true
  },
};

// 模块配置接口
export interface ModuleConfig {
  id: string;
  title: string;
  description: string;
  features: string[];
  pricing: string;
  priceValue: number;
  preferredPriceField: 'pricing' | 'priceValue';
  targetRoute: string;
  isRecommended?: boolean;
}

// 计划配置接口
export interface PlanConfig {
  name: string;
  price: string | number;
  description: string;
  features: string[];
  isRecommended: boolean;
  ctaText: string;
}
