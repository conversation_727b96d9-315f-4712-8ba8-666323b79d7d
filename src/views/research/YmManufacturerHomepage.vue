<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import QrCode from '@/assets/images/dips-agent.png'

defineOptions({
  name: 'YmManufacturerHomepage',
})

const userStore = useUserStore()
const username = ref(userStore.username)
const isModalVisible = ref(false)
const router = useRouter()

// 搜索关键字
const searchKeyword = ref('')

/**
 * 跳转到分析报告页面
 */
const navigateToAnalysisReport = () => {
  router.push('/analysis/report')
}

const showModal = () => {
  isModalVisible.value = true
}

const handleCancel = () => {
  isModalVisible.value = false
}

const analysisCards = ref([
  {
    icon: 'i-ph:chart-line-up-light',
    title: '热门品类销售市场趋势',
    tag: '3月',
    description: '帮助厂家迅速了解品牌总体和各产品的实际市场需求, 热门医美产品销售趋势预测与市场机会识别',
    tags: ['玻尿酸环比增长22%', '国械注准20203130295销量高'],
    updated: '2025-04-05',
    link: '查看完整内容',
    tagColor: '#e8dffc',
  },
  {
    icon: 'i-ph:chart-bar-light',
    title: '区域市场潜力分析',
    tag: '3月',
    description: '通过地理位置(城市)流向动态调整核心区域和销售渠道; 也可通过不同产品和行业趋势的对比优化产能规划',
    tags: ['上海、杭州、深圳销量优势'],
    updated: '2025-04-05',
    link: '查看完整内容',
    tagColor: '#e8dffc',
  },
  {
    icon: 'i-ph:chart-pie-slice-light',
    title: '产品竞争力对比',
    tag: '3月',
    description: '基于国产和进口玻尿酸市场占有率, 帮助厂家诊断市场地位',
    tags: ['国产玻尿酸销量占比75%', '我司市场占有率靠前'],
    updated: '2025-04-05',
    link: '查看完整内容',
    tagColor: '#e8dffc',
  },
  {
    icon: 'i-ph:users-three-light',
    title: '求美者偏好与行为分析',
    tag: '即将上线',
    description: '挖掘的求美者消费行为、偏好变化与决策因素分析',
    tags: ['85后、90后95前为主力', '安全性是首要考虑'],
    updated: '2025-04-05',
    link: '敬请期待',
    comingSoon: true,
  },
])

const isExpanded = ref(false)
const showCount = ref(1)

const latestReports = ref([
  {
    title: '2025年Q2医美行业市场分析报告',
    description: '全面分析2025年Q2医美行业市场情况, 包括产品销售数据、竞争格局、消费者行为变化等关键指标',
    date: '2025-07-05',
  },
  {
    title: '2025年Q1医美行业市场分析报告',
    description: '全面分析2025年Q1医美行业市场情况, 包括产品销售数据、竞争格局、消费者行为变化等关键指标',
    date: '2025-04-05',
  },
  {
    title: '2024年医美行业市场分析报告',
    description: '全面分析2024年医美行业市场情况, 包括产品销售数据、竞争格局、消费者行为变化等关键指标',
    date: '2025-01-05',
  }
])

// 过滤后的报告列表
const filteredReports = computed(() => {
  if (!searchKeyword.value.trim()) {
    return latestReports.value
  }
  const keyword = searchKeyword.value.toLowerCase().trim()
  return latestReports.value.filter(report =>
    report.title.toLowerCase().includes(keyword) ||
    report.description.toLowerCase().includes(keyword)
  )
})

// 当前显示的报告列表
const displayedReports = computed(() => {
  const filtered = filteredReports.value
  if (isExpanded.value || searchKeyword.value.trim()) {
    return filtered
  } else {
    return filtered.slice(0, showCount.value)
  }
})

const changeExpand = () => {
  isExpanded.value = !isExpanded.value
  if (isExpanded.value) {
    showCount.value = filteredReports.value.length
  } else {
    showCount.value = 1
  }
}

</script>

<template>
  <div class="manufacturer-homepage">
    <!-- 页面头部 -->
    <header class="page-banner">
      <div class="banner-content">
        <h1>医学美容行业 AI</h1>
        <p>20年求美者评价研究积淀, AI赋能商业分析, 驱动医学美容行业产品优化与服务升级</p>
        <p>聚焦AI新技术在行业的创新落地</p>
      </div>
    </header>

    <main class="page-content">
      <!-- 欢迎与定制 -->
      <section class="welcome-section">
        <h2>欢迎回来, <span style="color: #7b4dff;">{{ username }}</span>！</h2>
        <h3>AI分析深度定制</h3>
        <p>基于商业推理模型, 解构区域市场格局, 洞察品类增长逻辑, 助力机构建立差异化竞争优势, 实现可持续经营发展。</p>
      </section>

      <!-- AI分析预览 -->
      <section class="analysis-preview">
        <h2 class="section-title">
          AI分析预览
        </h2>
        <div class="preview-cards-grid">
          <div v-for="(card, index) in analysisCards" :key="index" class="card">
            <div class="card-header">
              <div class="card-icon">
                <FaIcon :name="card.icon" />
              </div>
              <h3 class="card-title">
                {{ card.title }}
              </h3>
              <span class="card-tag" :class="{ 'coming-soon': card.comingSoon }" :style="{ backgroundColor: card.tagColor }">{{ card.tag }}</span>
            </div>
            <p class="card-description">
              {{ card.description }}
            </p>
            <div class="card-tags">
              <span v-for="(tag, i) in card.tags" :key="i" class="tag" :class="`tag-${index}-${i}`">{{ tag }}</span>
            </div>
            <div class="card-footer">
              <span class="update-date">更新于 {{ card.updated }}</span>
              <!-- <a href="#" class="card-link" :class="{ disabled: card.comingSoon }">{{ card.link }}</a> -->
            </div>
          </div>
        </div>
        <div class="section-footer">
          <a href="#" class="view-all-link" @click.prevent="navigateToAnalysisReport">查看完整内容</a>
        </div>
      </section>

      <!-- 最新报告列表 -->
      <section class="latest-reports">
        <div class="section-header">
          <h2 class="section-title">
            最新报告列表
          </h2>
          <div class="actions">
            <div class="search-bar">
              <FaIcon name="i-ph:magnifying-glass-light" />
              <input
                v-model="searchKeyword"
                type="text"
                placeholder="搜索报告标题或内容"
              >
            </div>
            <el-button
              v-if="!searchKeyword.trim() && !isExpanded && filteredReports.length > 1"
              class="view-more"
              type="primary"
              size="small"
              link
              @click="changeExpand"
            >
              查看更多
            </el-button>
            <el-button
              v-if="!searchKeyword.trim() && isExpanded"
              class="view-more"
              type="primary"
              size="small"
              link
              @click="changeExpand"
            >
              收起
            </el-button>
          </div>
        </div>
        <div v-if="displayedReports.length === 0 && searchKeyword.trim()" class="no-results">
          <FaIcon name="i-ph:magnifying-glass-light" class="no-results-icon" />
          <p>没有找到相关报告</p>
          <p class="no-results-tip">请尝试其他关键字</p>
        </div>
        <div v-for="(report, index) in displayedReports" :key="index" class="report-item">
          <div class="report-info">
            <h4>{{ report.title }}</h4>
            <p>{{ report.description }}</p>
          </div>
          <div class="report-date">
            {{ report.date }}
          </div>
        </div>
      </section>

      <!-- 联系我们 -->
      <div class="cta-banner">
        <span>联系我们, 深度定制</span>
        <button class="cta-button" @click="showModal">
          立即咨询
        </button>
      </div>
      <a-modal
        v-model:open="isModalVisible"
        :footer="null"
        centered
        :closable="false"
        wrap-class-name="sales-qr-modal"
        @cancel="handleCancel"
      >
        <div class="modal-content-inner">
          <button class="close-button" @click="handleCancel">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
          <div class="qr-code-content">
            <h3>联系销售</h3>
            <img :src="QrCode" alt="QR Code" style="display: block; width: 256px; height: 256px; margin: 0 auto;">
            <p>微信扫一扫，联系我们</p>
          </div>
        </div>
      </a-modal>

      <!-- AI分析模块 -->
      <section class="analysis-modules">
        <h2 class="section-title">
          AI分析模块
        </h2>
        <div class="modules-container">
          <div class="module-card">
            <h4>消费市场研究</h4>
            <p class="module-description">
              为医美企业提供全方位的市场研究服务, 深入洞察消费者需求和市场趋势
            </p>
            <div class="module-features">
              <div class="feature-item">
                <FaIcon name="i-ph:user-focus-light" class="feature-icon" />
                <div>
                  <h5>消费者画像深度分析</h5>
                  <p>多维度分析消费者年龄、消费习惯等特征, 构建精准用户画像</p>
                </div>
              </div>
              <div class="feature-item">
                <FaIcon name="i-ph:package-light" class="feature-icon" />
                <div>
                  <h5>产品市场潜力评估</h5>
                  <p>通过分析新兴机会点, 解构热点品类增长动因, 帮助企业捕获爆款背后的消费驱动力</p>
                </div>
              </div>
            </div>
          </div>
          <div class="module-card">
            <h4>企业、产品竞争力</h4>
            <p class="module-description">
              全面评估企业及产品的市场竞争力, 提供战略优化建议
            </p>
            <div class="module-features">
              <div class="feature-item">
                <FaIcon name="i-ph:binoculars-light" class="feature-icon" />
                <div>
                  <h5>竞争对手分析</h5>
                  <p>将竞争区域细分至城市商圈, 不仅能够评估当地的竞争强度, 还可智能识别商圈竞对, 帮助了解竞争者的数量、规模、产品情况</p>
                </div>
              </div>
              <div class="feature-item">
                <FaIcon name="i-ph:crosshair-light" class="feature-icon" />
                <div>
                  <h5>产品差异化定位</h5>
                  <p>更好地认识自己和竞争者, 了解彼此的优劣势才能采取有效的市场策略</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 自研AI分析引擎 -->
      <section class="ai-engine">
        <h2 class="section-title">
          自研AI分析引擎
        </h2>
        <div class="engine-features">
          <div class="engine-feature">
            <FaIcon name="i-ph:brain-light" class="engine-icon" />
            <p>推理神经网络</p>
          </div>
          <div class="engine-feature">
            <FaIcon name="i-ph:calculator-light" class="engine-icon" />
            <p>逻辑计算</p>
          </div>
          <div class="engine-feature">
            <FaIcon name="i-ph:graph-light" class="engine-icon" />
            <p>知识图谱</p>
          </div>
          <div class="engine-feature">
            <FaIcon name="i-ph:chart-bar-horizontal-light" class="engine-icon" />
            <p>API/可视化引擎</p>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<style lang="scss" scoped>
.manufacturer-homepage {
  color: #333;
  background-color: #f6f7fb;
}

.page-banner {
  padding: 60px 20px 30px;
  color: white;
  text-align: center;
  background-color: #7b4dff;

  .banner-content {
    max-width: 800px;
    margin: 0 auto;
  }

  h1 {
    margin-bottom: 32px;
    font-size: 40px;
    font-weight: 600;
  }

  p {
    font-size: 14px;
    line-height: 1.2;
    opacity: 0.9;
  }
}

.page-content {
  max-width: 1200px;
  padding: 40px;
  margin: 0 auto;
}

.section-title {
  margin-bottom: 30px;
  font-size: 20px;
  font-weight: 600;
  text-align: center;
}

.welcome-section {
  padding: 30px;
  margin-bottom: 40px;
  text-align: center;
  // background-color: #fff;
  border-radius: 12px;

  h2 {
    margin-bottom: 16px;
    font-size: 22px;
    font-weight: 600;
  }

  h3 {
    margin-bottom: 12px;
    font-size: 20px;
    font-weight: 600;
    color: #7b4dff;
  }

  p {
    max-width: 800px;
    margin: 0 auto;
    font-size: 14px;
    line-height: 1.7;
    color: #666;
  }
}

.analysis-preview {
  margin-bottom: 40px;

  .section-footer {
    margin-top: 30px;
    text-align: center;
  }

  .view-all-link {
    display: inline-block;
    padding: 10px 30px;
    font-weight: 500;
    color: #7b4dff;
    text-decoration: none;
    background-color: #fff;
    border: 1px solid #7b4dff;
    border-radius: 8px;
    transition: all 0.3s;

    &:hover {
      color: #fff;
      background-color: #7b4dff;
    }
  }

  .preview-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;
  }

  .card {
    padding: 24px;
    background: #fff;
    border: 1px solid #eef;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgb(0 0 0 / 5%);
    transition: transform 0.3s, box-shadow 0.3s;

    &:hover {
      box-shadow: 0 8px 24px rgb(0 0 0 / 10%);
      transform: translateY(-5px);
    }
  }

  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }

  .card-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    margin-right: 12px;
    font-size: 20px;
    color: #7b4dff;
    background-color: #f0ebff;
    border-radius: 8px;
  }

  .card-title {
    flex-grow: 1;
    margin: 0 0 0 10px;
    font-size: 16px;
    font-weight: 600;
  }

  .card-tag {
    padding: 4px 10px;
    font-size: 12px;
    font-weight: 500;
    color: #7b4dff;
    border-radius: 12px;

    &.coming-soon {
      color: #888;
      background-color: #f5f5f5 !important;
      border: 1px solid #ddd;
    }
  }

  .card-description {
    margin-bottom: 20px;
    font-size: 14px;
    line-height: 1.7;
    color: #666;
  }

  .card-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 20px;

    .tag {
      padding: 4px 10px;
      font-size: 12px;
      border-radius: 6px;
    }

    .tag-0-0 {
      color: #7b4dff;
      background-color: #e8dffc;
    }

    .tag-0-1 {
      color: #7b4dff;
      background-color: #e8dffc;
    }

    .tag-1-0 {
      color: #28a745;
      background-color: #dffce8;
    }

    .tag-2-0 {
      color: #ffc107;
      background-color: #fff0d4;
    }

    .tag-2-1 {
      color: #ffc107;
      background-color: #fff0d4;
    }

    .tag-3-0 {
      color: #e91e63;
      background-color: #fce4ec;
    }

    .tag-3-1 {
      color: #e91e63;
      background-color: #fce4ec;
    }
  }

  .card-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;

    .update-date {
      font-size: 12px;
      color: #999;
    }
  }
}

.latest-reports {
  padding: 30px;
  margin-bottom: 40px;
  background-color: #fff;
  border-radius: 12px;

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
  }

  .actions {
    display: flex;
    gap: 16px;
    align-items: center;
  }

  .search-bar {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background-color: #f6f7fb;
    border: 1px solid #eef;
    border-radius: 8px;

    .fa-icon {
      margin-right: 8px;
      color: #999;
    }

    input {
      outline: none;
      background: transparent;
      border: none;

      &::placeholder {
        color: #999;
      }
    }
  }

  .view-more {
    font-size: 14px;
    color: #7b4dff;
    text-decoration: none;
  }

  .report-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    margin-bottom: 5px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
  }

  .report-info h4 {
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: 600;
  }

  .report-info p {
    font-size: 14px;
    color: #666;
  }

  .report-date {
    font-size: 14px;
    color: #999;
  }

  .no-results {
    padding: 40px 20px;
    color: #999;
    text-align: center;

    .no-results-icon {
      margin-bottom: 16px;
      font-size: 48px;
      color: #ddd;
    }

    p {
      margin-bottom: 8px;
      font-size: 16px;

      &.no-results-tip {
        font-size: 14px;
        color: #bbb;
      }
    }
  }
}

.cta-banner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 40px;
  margin-bottom: 40px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgb(0 0 0 / 5%);

  span {
    font-size: 18px;
    font-weight: 600;
    color: #7b4dff;
  }

  .cta-button {
    padding: 10px 24px;
    font-size: 14px;
    color: white;
    cursor: pointer;
    background-color: #7b4dff;
    border: none;
    border-radius: 8px;
    transition: background-color 0.3s;

    &:hover {
      background-color: #6233ff;
    }
  }
}

.analysis-modules {
  margin-bottom: 40px;

  .modules-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
  }

  .module-card {
    padding: 24px;
    background: #fafbff;
    border: 1px solid #eef;
    border-radius: 12px;
  }

  h4 {
    margin-bottom: 14px;
    font-size: 16px;
    font-weight: 600;
  }

  .module-description {
    margin-bottom: 32px;
    font-size: 12px;
    color: #666;
  }

  .module-features {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .feature-item {
    display: flex;
    gap: 16px;
    align-items: flex-start;
    margin: 10px 0 0;

    .feature-icon {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      font-size: 20px;
      color: #7b4dff;
      background-color: #f0ebff;
      border-radius: 8px;
    }

    h5 {
      margin-bottom: 6px;
      font-size: 14px;
      font-weight: 600;
    }

    p {
      font-size: 12px;
      line-height: 1.6;
      color: #666;
    }
  }
}

.ai-engine {
  margin-bottom: 40px;
  text-align: center;

  .engine-features {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
    padding: 30px;
    background-color: #fff;
    border-radius: 12px;
  }

  .engine-feature {
    .engine-icon {
      margin-bottom: 12px;
      font-size: 3rem;
      color: #7b4dff;
    }

    p {
      font-size: 16px;
      font-weight: 500;
    }
  }
}

@media (width <= 992px) {
  .analysis-preview .preview-cards-grid {
    grid-template-columns: 1fr;
  }

  .analysis-modules .modules-container {
    grid-template-columns: 1fr;
  }
}

@media (width <= 768px) {
  .page-banner {
    padding: 40px 20px;
    h1 { font-size: 32px; }
  }

  .page-content {
    padding: 20px 15px;
  }

  .latest-reports .section-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .cta-banner {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .ai-engine .engine-features {
    grid-template-columns: 1fr 1fr;
  }
}

@media (width <= 480px) {
  .analysis-preview .preview-cards-grid {
    grid-template-columns: 1fr;
  }

  .analysis-preview .card-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}
</style>
<style lang="scss">
.sales-qr-modal {
  .ant-modal-mask {
    background-color: rgb(0 0 0 / 60%) !important;
    backdrop-filter: blur(4px);
  }

  .ant-modal-content {
    padding: 0;
    background-color: transparent;
    box-shadow: none;
  }

  .ant-modal-body {
    padding: 0;
  }

  .modal-content-inner {
    position: relative;
    padding: 32px 24px 24px;
    background-color: white;
    border: 1px solid #f0f0f0;
    border-radius: 1rem;
    box-shadow: 0 25px 50px -12px rgb(0 0 0 / 25%);

    .qr-code-content {
      text-align: center;

      h3 {
        margin-bottom: 20px;
        font-size: 20px;
        font-weight: 600;
      }

      p {
        margin-top: 16px;
        font-size: 14px;
        color: #666;
      }
    }

    .close-button {
      position: absolute;
      top: 1rem;
      right: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 2rem;
      height: 2rem;
      color: #4b5563;
      cursor: pointer;
      background-color: #f3f4f6;
      border: none;
      border-radius: 9999px;
      transition: background-color 0.2s;

      &:hover {
        color: #1f2937;
        background-color: #e5e7eb;
      }

      svg {
        width: 1.25rem;
        height: 1.25rem;
      }
    }
  }
}
</style>
