<template>
  <!-- 问卷详细说明模块 -->
  <div class="survey-details-section">
    <div class="container">
      <h2 class="section-title">
        {{ surveyDetails.title }}
      </h2>
      <p class="section-overview">
        {{ surveyDetails.overview }}
      </p>

      <div class="details-content">
        <!-- 目标客户群体 -->
        <div class="detail-module">
          <h3 class="module-title">
            <el-icon class="text-blue-600">
              <User />
            </el-icon>
            {{ surveyDetails.targetAudience.title }}
          </h3>
          <p v-if="surveyDetails.targetAudience.description" class="module-description" v-html="surveyDetails.targetAudience.description"></p>

          <div v-for="group in surveyDetails.targetAudience.groups" :key="group.category" class="audience-group">
            <h4 class="group-category">{{ group.category }}</h4>
            <p v-if="group.description" class="group-description" v-html="group.description"></p>
            <div class="audience-grid">
              <div v-for="card in group.cards" :key="card.name" :class="['audience-card', card.bgColor]">
                <h5 :class="['card-title', card.textColor]">{{ card.name }}</h5>
                <ul class="feature-list">
                  <li v-for="feature in card.features" :key="feature" :class="card.textColor">
                    • {{ feature }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- 问卷题型设计 -->
        <div class="detail-module">
          <h3 class="module-title">
            <el-icon class="text-green-600">
              <List />
            </el-icon>
            {{ surveyDetails.questionTypes.title }}
          </h3>
          <p v-if="surveyDetails.questionTypes.description" class="module-description" v-html="surveyDetails.questionTypes.description"></p>

          <div class="question-types">
            <div v-for="category in surveyDetails.questionTypes.categories" :key="category.name" class="type-category">
              <h4 class="category-title">{{ category.name }}</h4>
              <div class="category-items">
                <div v-for="item in category.items" :key="item" class="type-item">
                  <p class="type-desc">{{ item }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { User, List } from '@element-plus/icons-vue'

// 问卷详细说明
const surveyDetails = ref({
  title: '问卷和研究指标说明',
  overview: '本次调研搭建了科学完善的评价体系，通过多维度的问题设计，精准评估产品与服务核心维度，真实反馈求美者需求',
  targetAudience: {
    title: '目标群体与指标设计',
    description: '',
    groups: [
      {
        category: '服务端',
        description: '研究的客户群体为<span class="text-highlight">有过医美服务咨询体验的客户</span>，根据不同求美者在医疗美容服务流程中的关键接触点（咨询、到访、治疗），针对性地设计差异化调研指标体系',
        cards: [
          {
            name: '咨询客户',
            bgColor: 'bg-blue-50',
            borderColor: '',
            textColor: 'text-blue-900',
            features: [
              '机构满意度',
              '忠诚度',
              '人员表现',
              '服务和环境',
              '求美者行为和偏好'
            ]
          },
          {
            name: '到访客户',
            bgColor: 'bg-blue-50',
            borderColor: '',
            textColor: 'text-blue-900',
            features: [
              '机构满意度',
              '忠诚度',
              '人员表现',
              '服务和环境',
              '日常关怀',
              '求美者行为和偏好'
            ]
          },
          {
            name: '治疗客户',
            bgColor: 'bg-blue-50',
            borderColor: '',
            textColor: 'text-blue-900',
            features: [
              '机构满意度',
              '忠诚度',
              '人员表现',
              '服务和环境',
              '治疗感受',
              '日常关怀',
              '求美者行为和偏好'
            ]
          }
        ]
      },
      {
        category: '产品端',
        description: '根据<span class="text-highlight">不同医美项目修复周期，在不同时间节点</span>收集求美者的即时效果满意度与最终效果体验评价，为优化治疗方案、提升用户体验提供数据支持',
        cards: [
          {
            name: '短期修复',
            bgColor: 'bg-blue-50',
            borderColor: '',
            textColor: 'text-blue-900',
            features: [
              '产品满意度',
              '忠诚度',
              '即时效果',
              '产品舒适度',
              '术后护理'
            ]
          },
          {
            name: '中等修复',
            bgColor: 'bg-blue-50',
            borderColor: '',
            textColor: 'text-blue-900',
            features: [
              '产品满意度',
              '忠诚度',
              '产品持续效果',
              '产品舒适度',
              '术后护理'
            ]
          },
          {
            name: '长期修复',
            bgColor: 'bg-blue-50',
            borderColor: '',
            textColor: 'text-blue-900',
            features: [
              '产品满意度',
              '忠诚度',
              '产品最终效果',
              '产品舒适度',
              '术后护理'
            ]
          }
        ]
      }
    ]
  },
  questionTypes: {
    title: '问卷题型设计',
    description: '问卷设计支持多样化题型，确保数据收集的<span class="text-highlight">全面性与科学性</span>',
    categories: [
      {
        name: '题型概览',
        items: [
          '本问卷包含​​是否题、多选题、评分题（李克特5级量表）、开放题等，题型多样以全面收集求美者的反馈',
        ]
      },
      {
        name: '题型说明和示例',
        items: [
          '是否题​​：二项分布的是否题，如“您是否曾就服务或产品问题主动联系过[机构名称]并进行售后反馈？”（1=是，2=否）',
          '多选题​​：可勾选多项答案，识别求美者最关注/最需求选项，如“请问您主要通过以下哪些渠道获取医美项目或机构的信息？（最多选3项）”',
          '​​评分题​​：采用5级对称量表形式作答的问题，让回访者对陈述的满意/不满意程度进行分级评分，量化主观态度（如满意度、重要性等），如“综合考虑此次到访的服务和产品体验，您对 [机构名称]的总体满意程度如何？”（5分=非常满意，4分=满意，3分=一般，2分=不满意，1分=非常不满意）',
          '开放题​​：若有补充意见，请在文本框内简要描述，如“关于机构提供所服务或治疗方面，您还有其他的问题反馈吗？”（1=是，2=否）',
        ]
      },
      {
        name: '隐私与数据使用',
        items: [
          '答案仅用于统计分析，被访者可根据“便于处理反馈的问题需求”选择是否匿名提交',
        ]
      }
    ]
  }
})
</script>

<style scoped>
/* ===== 通用容器样式 ===== */
.container {
  max-width: 1152px;
  padding: 0 24px;
  margin: 0 auto;
}

/* ===== 详细说明模块样式 ===== */
.survey-details-section {
  padding: 64px 0;
  background: #f9fafb; /* 浅灰背景 */
}

.section-title {
  margin-bottom: 16px;
  font-size: 1.875rem;
  font-weight: 700;
  color: #111827;
  text-align: center;
}

.section-overview {
  max-width: 896px;
  margin: 0 auto 48px;
  font-size: 1.125rem;
  line-height: 1.625;
  color: #4b5563;
  text-align: center;
}

.details-content {
  display: flex;
  flex-direction: column;
  gap: 48px;
}

.detail-module {
  padding: 32px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 10%); /* 轻微阴影 */
}

.module-title {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 24px;
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
}

.module-title i {
  font-size: 1.5rem;
}

/* ===== 模块描述样式 ===== */
.module-description {
  margin-bottom: 24px;
  font-size: 1rem;
  line-height: 1.625;
  color: #6b7280;
}

/* ===== 目标客户群体样式 ===== */
.audience-group {
  margin-bottom: 32px;
}

.audience-group:last-child {
  margin-bottom: 0;
}

.group-category {
  margin-bottom: 8px;
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
}

.group-description {
  margin-bottom: 20px;
  font-size: 0.875rem;
  line-height: 1.6;
  color: #6b7280;
}

:deep(.text-highlight) {
  font-weight: 500 !important;
  color: #a855f7 !important;
}

.group-description :deep(.text-highlight) {
  font-weight: 500 !important;
  color: #a855f7 !important;
}

.module-description :deep(.text-highlight) {
  font-weight: 500 !important;
  color: #a855f7 !important;
}

.audience-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 80px;
  padding: 0 70px;
}

@media (width >= 640px) {
  .audience-grid {
    grid-template-columns: repeat(3, 1fr); /* 中等宽度及以上使用三列布局 */
  }
}

.audience-card {
  padding: 20px;
  border: none !important;
  border-radius: 8px;
}

.card-title {
  margin-bottom: 32px;
  font-size: 1.5rem;
  font-weight: 600;
}

.feature-list {
  padding: 0;
  margin: 0;
  list-style: none;
}

.feature-list li {
  margin-bottom: 6px;
  font-size: 0.875rem;
  line-height: 1.4;
}

.feature-list li:last-child {
  margin-bottom: 0;
}

/* ===== 问卷题型样式 ===== */
.question-types {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.type-category {
  padding: 20px;
  background: linear-gradient(90deg, #f0fdf4 0%, #ecfdf5 100%); /* 绿色渐变背景 */
  border: 1px solid #bbf7d0;
  border-radius: 12px;
}

.category-title {
  margin-bottom: 16px;
  font-size: 1.125rem;
  font-weight: 600;
  color: #14532d;
}

.category-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.type-item {
  padding: 0;
  background: transparent;
  border: none;
  border-radius: 0;
}

.type-desc {
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.6;
  color: #15803d;
}

/* ===== 核心调研指标样式 ===== */
.metrics-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
}

@media (width >= 768px) {
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.metric-card {
  display: flex;
  gap: 16px;
  align-items: flex-start;
  padding: 24px;
  background: #f9fafb;
  border-radius: 12px;
}

.metric-icon {
  flex-shrink: 0;
  padding: 12px;
  font-size: 1.5rem;
  background: white;
  border-radius: 8px;
}

.metric-info {
  flex: 1;
}

.metric-title {
  margin-bottom: 8px;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
}

.metric-desc {
  font-size: 0.875rem;
  line-height: 1.6;
  color: #6b7280;
}

/* 背景色和文本色 */
.bg-blue-50 {
  background-color: #eff6ff;
}

.text-blue-900 {
  color: #1e3a8a;
}

.bg-green-50 {
  background-color: #f0fdf4;
}

.text-green-900 {
  color: #14532d;
}

.bg-purple-50 {
  background-color: #faf5ff;
}

.text-purple-900 {
  color: #5b21b6;
}

.bg-yellow-50 {
  background-color: #fefce8;
}

.text-yellow-900 {
  color: #713f12;
}
</style>

<style scoped>
.survey-details-section {
  padding: 60px 0;
  background-color: #f8f9fa;
}

.container {
  max-width: 1200px;
  padding: 0 15px;
  margin: 0 auto;
}

.section-title {
  margin-bottom: 20px;
  font-size: 32px;
  font-weight: 700;
  color: #333;
  text-align: center;
}

.section-overview {
  max-width: 800px;
  margin: 0 auto 40px;
  font-size: 16px;
  line-height: 1.6;
  color: #666;
  text-align: center;
}

.details-content {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.detail-module {
  padding: 30px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgb(0 0 0 / 5%);
}

.module-title {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 20px;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.module-description {
  margin-bottom: 20px;
  font-size: 14px;
  line-height: 1.8;
  color: #555;
}

.audience-group {
  margin-bottom: 30px;
}

.group-category {
  padding-bottom: 10px;
  margin-bottom: 15px;
  font-size: 20px;
  font-weight: 600;
  color: #444;
  border-bottom: 2px solid #eee;
}

.group-description {
  margin-bottom: 20px;
  font-size: 14px;
  line-height: 1.8;
  color: #555;
}

/*
.audience-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.audience-card {
  padding: 20px;
  background-color: #f1f5f9;
  border-radius: 8px;
  transition: transform 0.3s ease;
}

.audience-card:hover {
  transform: translateY(-5px);
}

.card-title {
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: 600;
}

.feature-list {
  padding-left: 0;
  margin: 0;
  list-style: none;
}

.feature-list li {
  margin-bottom: 8px;
  font-size: 14px;
} */

.text-highlight {
  font-weight: bold;
  color: #007bff;
}

.bg-blue-50 {
  background-color: #f0f8ff;
}

.text-blue-900 {
  color: #0056b3;
}

.text-blue-600 {
  color: #007bff;
}

.text-green-600 {
  color: #28a745;
}
</style>
