<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import SurveyDesignDetail from './SurveyDesignDetail.vue'
import { ElRow, ElCol, ElCard, ElButton } from 'element-plus'
import Chart from '@/components/visual/Chart/index.vue'
import { useUserStore } from '@/store/modules/user'
import satisfactionChartOptions from './data/satisfactionChart.json'
import marketChartOptions from './data/marketChart.json'
import Report from "@/views/report/Report.vue";

defineOptions({
  name: 'ResearchGroupHomepage',
})

const router = useRouter()
const userStore = useUserStore()
const userName = computed(() => userStore.username || '获取用户名失败')

const stats = ref({
  satisfaction: 89,
  satisfactionYoY: 1.2,
  instNps: 53,
  prodNps: 65,
})

const researchReports = ref([
  { title: '2025年7月满意度分析报告', date: '2025-08-01' },
  { title: '2024年6月满意度分析报告', date: '2025-07-03' },
  { title: '2024年5月满意度分析报告', date: '2025-06-02' },
])

const industryUpdates = ref([
  { title: '2025年Q2医美行业市场分析报告', date: '2025-07-15' },
  { title: '2025年6月医美广州市场竞争格局分析', date: '2025-07-15' },
  { title: '2025年半年度求美者需求和行为分析报告', date: '2025-06-01' },
])

const showSurveyDetailDialog = ref(false)

const recentActivities = ref([
  { time: '2025-05-10 14:30', event: '您的Q1满意度分析报告已生成完成', type: '满意度报告已生成' },
  { time: '2025-05-05 16:45', event: '您下载了"2025年医美行业趋势分析"', type: '行业报告已下载' },
])

const showReport = ref(false);
const reportTitle = ref('')
const params = ref<any>({});

const viewSatisReport = () => {
  showReport.value = true
  reportTitle.value = '满意度分析报告'
  params.value.gptStencilId = '37UT85QM5E8FGK4URQVR'
  params.value.gptShotId = 'original'
}

const closeReport = () => {
  showReport.value = false
}

/**
 * 导航到图表页面
 */
const navigateToGraph = () => {
  router.push('/research/consumer/graph')
}

/**
 * 导航到分析报告页面
 */
const navigateToAnalysisReport = () => {
  router.push('/analysis/report')
}
</script>

<template>
  <div class="group-homepage">
    <!-- Header -->
    <header class="header-main">
      <div class="header-content">
        <div class="logo-section">
          <h1 class="main-title">
            医学美容行业 AI
          </h1>
          <p class="subtitle">
            20年求美者评价研究积淀，AI赋能商业分析，驱动医学美容行业产品优化与服务升级，聚焦AI新技术在行业的创新落地
          </p>
        </div>
        <div class="user-section">
          欢迎回来, <span class="username">{{ userName }}</span>
        </div>
      </div>
    </header>

    <!-- Main Section -->
    <main class="main-container">
      <!-- Action Cards -->
      <ElRow :gutter="24" class="action-cards">
        <ElCol :xs="24" :sm="12" :md="8">
          <ElCard shadow="hover" class="action-card">
            <h3 class="card-title">
              了解求美者体验调研
            </h3>
            <p class="card-description">
              快速了解求美者体验调研，实时收集求美者反馈，多维度分析满意度数据
            </p>
            <ElButton type="primary" size="large" class="card-button" @click="showSurveyDetailDialog = true">
              立即了解
            </ElButton>
          </ElCard>
        </ElCol>
        <ElCol :xs="24" :sm="12" :md="8">
          <ElCard shadow="hover" class="action-card action-card-green">
            <h3 class="card-title">
              智能报告生成
            </h3>
            <p class="card-description">
              自动生成专业分析报告，洞察服务痛点，提供改进建议，跟踪求美者体验变化趋势，预测未来发展方向
            </p>
            <ElButton size="large" class="card-button card-button-green" @click="viewSatisReport">
              查看满意度分析报告
            </ElButton>
          </ElCard>
        </ElCol>
        <ElCol :xs="24" :sm="12" :md="8">
          <ElCard shadow="hover" class="action-card action-card-gray">
            <h3 class="card-title">
              行业AI报告
            </h3>
            <p class="card-description">
              获取最新的医美行业分析报告
            </p>
            <ElButton type="info" size="large" class="card-button" disabled>
              查看行业报告 (即将上线)
            </ElButton>
          </ElCard>
        </ElCol>
      </ElRow>

      <!-- Data Analytics -->
      <ElRow :gutter="24">
        <!-- Patient Review Research -->
        <ElCol :xs="24" :lg="12">
          <ElCard shadow="always" class="data-card">
            <template #header>
              <div class="card-header">
                <span>求美者评价研究</span>
                <ElButton size="small" round class="feature-button">
                  核心功能
                </ElButton>
              </div>
            </template>
            <div class="card-content">
              <p>在医美行业，真正的销售数据不是靠广告堆砌，而是来自每一位求美者的真实体验与口碑传播。我们深知：满意的客户会成为您最好的代言人，优质的服务体验才能带来持久的业绩增长。</p>
              <div class="sub-section">
                <div class="sub-header">
                  <h4>调研数据概览</h4>
                  <ElButton type="primary" link @click="navigateToGraph">
                    查看图表
                  </ElButton>
                </div>
                <ElRow :gutter="16" class="stats-overview">
                  <ElCol :span="6">
                    <div class="stat-item">
                      <span class="stat-value">{{ stats.satisfaction }}<small>分</small></span>
                      <span class="stat-label">总体满意度</span>
                    </div>
                  </ElCol>
                  <ElCol :span="6">
                    <div class="stat-item">
                      <span class="stat-value text-up">{{ stats.satisfactionYoY }}%</span>
                      <span class="stat-label">环比 ↑</span>
                    </div>
                  </ElCol>
                  <ElCol :span="6">
                    <div class="stat-item">
                      <span class="stat-value">{{ stats.instNps }}%</span>
                      <span class="stat-label">机构NPS</span>
                    </div>
                  </ElCol>
                  <ElCol :span="6">
                    <div class="stat-item">
                      <span class="stat-value">{{ stats.prodNps }}%</span>
                      <span class="stat-label">产品NPS</span>
                    </div>
                  </ElCol>
                </ElRow>
                <div class="chart-container">
                  <Chart class="chart" :option="satisfactionChartOptions" autoresize />
                </div>
              </div>
              <div class="sub-section">
                <div class="sub-header">
                  <h4>研究报告</h4>
                  <ElButton type="primary" link>
                    查看详细分析
                  </ElButton>
                </div>
                <ul class="report-list">
                  <li v-for="report in researchReports" :key="report.title">
                    <span>{{ report.title }}</span>
                    <span class="date">{{ report.date }}</span>
                  </li>
                </ul>
              </div>
            </div>
          </ElCard>
        </ElCol>

        <!-- Industry AI Analysis -->
        <ElCol :xs="24" :lg="12">
          <ElCard shadow="always" class="data-card">
            <template #header>
              <div class="card-header">
                <span>医美行业AI分析</span>
                <ElButton size="small" round class="feature-button">
                  商业洞察
                </ElButton>
              </div>
            </template>
            <div class="card-content">
              <p>基于商业推理模型，解构区域市场格局，洞察品类增长逻辑，助力机构建立差异化竞争优势，实现可持续经营发展。</p>
              <div class="sub-section">
                <div class="sub-header">
                  <h4>AI分析预览</h4>
                </div>
                <div class="ai-preview-title">
                  <h5>2025年Q2医美行业市场分析报告</h5>
                  <ElButton type="primary" link @click="navigateToAnalysisReport">
                    查看报告
                  </ElButton>
                </div>
                <p>本报告全面分析了2025年第二季度医美行业市场情况，包括产品销售数据变化等关键指标。<span class="publish-date">发布于2025-07-15</span></p>
                <div class="chart-container">
                  <Chart class="chart" :option="marketChartOptions" autoresize />
                </div>
              </div>
              <div class="sub-section">
                <div class="sub-header">
                  <h4>最新行业动态</h4>
                  <ElButton type="primary" link>
                    查看全部
                  </ElButton>
                </div>
                <ul class="report-list">
                  <li v-for="update in industryUpdates" :key="update.title">
                    <span>{{ update.title }}</span>
                    <span class="date">{{ update.date }}</span>
                  </li>
                </ul>
              </div>
            </div>
          </ElCard>
        </ElCol>
      </ElRow>

      <!-- Recent Activities -->
      <ElCard shadow="never" class="recent-activities-card">
        <template #header>
          <div class="card-header">
            <span>最近活动</span>
          </div>
        </template>
        <ul class="activity-list">
          <li v-for="activity in recentActivities" :key="activity.time">
            <div class="activity-type">
              {{ activity.type }}
            </div>
            <div class="activity-details">
              <span><time>{{ activity.time }}</time> - {{ activity.event }}</span>
            </div>
          </li>
        </ul>
      </ElCard>

      <!-- Bottom CTA -->
      <div class="bottom-cta">
        <h2>求美者体验研究</h2>
        <p>我们为您提供从零开始的完整解决方案</p>
        <ElRow :gutter="80" class="features" justify="center">
          <ElCol :xs="24" :sm="8" class="feature-item">
            <div class="feature-icon">
              <FaIcon name="i-ph:rocket-launch-light" />
            </div>
            <h3>快速启动</h3>
            <p>只需简单几步，即可开始您的求美者体验研究之旅</p>
          </ElCol>
          <ElCol :xs="24" :sm="8" class="feature-item">
            <div class="feature-icon">
              <FaIcon name="i-ph:brain-light" />
            </div>
            <h3>AI辅助设计</h3>
            <p>专业调研问卷设计，开箱即用</p>
          </ElCol>
          <ElCol :xs="24" :sm="8" class="feature-item">
            <div class="feature-icon">
              <FaIcon name="i-ph:chart-bar-light" />
            </div>
            <h3>数据可视化</h3>
            <p>直观的图表展示，让数据分析变得简单</p>
          </ElCol>
        </ElRow>
        <!-- <ElButton size="large" class="cta-button">
          了解详情
        </ElButton> -->
      </div>
    </main>

    <a-modal
      :open="showReport"
      :title="reportTitle"
      :footer="null"
      :destroyOnClose="true"
      @cancel="closeReport"
      :style="{ width: '1500px' }"
    >
      <Report style="margin-top: 20px;" :isSpecific="true" :params="params"/>
    </a-modal>

    <el-dialog
      v-model="showSurveyDetailDialog"
      width="80%"
      top="5vh"
    >
      <SurveyDesignDetail />
    </el-dialog>
  </div>
</template>

<style scoped>
/* ===== General Page Styles ===== */
.group-homepage {
  color: #333;
  background-color: #f0f2f5;
}

/* ===== Header ===== */
.header-main {
  padding: 40px 60px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1600px;
  margin: 0 auto;
}

.main-title {
  margin: 0 0 10px;
  font-size: 36px;
  font-weight: 600;
  color: #4a4a4a;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.subtitle {
  max-width: 600px;
  margin: 0;
  font-size: 14px;
  line-height: 1.6;
  color: #777;
}

.user-section {
  font-size: 12px;
  color: #555;
}

.username {
  font-weight: 600;
  color: #667eea;
}

/* ===== Main Container ===== */
.main-container {
  max-width: 1600px;
  padding: 24px;
  margin: 0 auto;
}

/* ===== Action Cards ===== */
.action-cards {
  margin-bottom: 24px;
}

.action-card {
  text-align: center;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.action-card:hover {
  box-shadow: 0 12px 20px -10px rgb(0 0 0 / 20%);
  transform: translateY(-5px);
}

.card-title {
  margin: 0 0 24px;
  font-size: 18px;
  font-weight: 500;
  text-align: left;
}

.card-description {
  /* height: 60px; */
  margin: 36px 0;
  font-size: 12px;
  line-height: 1.6;
  color: #666;
  text-align: left;
}

.card-button {
  width: 80%;
  font-size: 12px;
}

.action-card-green .card-button-green {
  color: white;
  background-color: #28a745;
  border-color: #28a745;
}

.action-card-green .card-button-green:hover {
  background-color: #218838;
  border-color: #1e7e34;
}

/* ===== Data Cards ===== */
.data-card {
  margin-bottom: 24px;
  border-radius: 12px;
}

:deep(.el-card__header) {
  /* border-bottom: 1px solid #f0f0f0; */
  border-bottom: none;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;
  font-weight: 600;
}

.card-content p {
  font-size: 12px;
  line-height: 1.6;
  color: #555;
}

.sub-section {
  padding-top: 24px;
  margin: 24px;

  /* border-top: 1px solid #f0f0f0; */
}

.sub-section:first-child {
  padding-top: 0;
  border-top: 0;
}

.sub-header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  /* margin-bottom: 16px; */

  margin: 16px 0 36px;
}

.sub-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

/* Stats Overview */
.stats-overview {
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 28px;
  font-weight: bold;
}

.stat-value small {
  font-size: 14px;
}

.stat-value.text-up {
  color: #f56c6c;
}

.stat-label {
  font-size: 14px;
  color: #888;
}

.chart-container {
  height: 250px;
  margin-top: 50px;
}

.chart {
  width: 100%;
  height: 100%;
}

.chart-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
  background-color: #fafafa;
  border: 1px dashed #ddd;
  border-radius: 8px;
}

/* Report List */
.report-list {
  padding: 0 16px;
  margin: 0;
  list-style: none;
}

.report-list li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  font-size: 14px;
  border-bottom: 1px solid #f0f0f0;
}

.report-list li:last-child {
  border-bottom: 0;
}

.report-list .date {
  color: #888;
}

/* AI Preview */
.ai-preview-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ai-preview-title h5 {
  margin: 0;
  font-size: 16px;
}

.publish-date {
  float: right;
  color: #aaa;
}

/* ===== Recent Activities ===== */
.recent-activities-card {
  margin-bottom: 24px;
  background-color: #fff;
  border: 0;
  border-radius: 12px;
}

.activity-list {
  padding: 0;
  margin: 0;
  list-style-type: none;
}

.activity-list li {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-list li:last-child {
  border-bottom: 0;
}

.activity-type {
  flex-shrink: 0;
  width: 120px;
  padding: 4px 8px;
  margin-right: 20px;
  font-size: 12px;
  color: #1890ff;
  text-align: center;
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 4px;
}

.activity-details {
  font-size: 14px;
}

/* ===== Bottom CTA ===== */
.bottom-cta {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 20px;
  margin-top: 24px;
  text-align: center;
  background-color: #fff;
  border-radius: 12px;
}

.bottom-cta h2 {
  margin-bottom: 8px;
  font-size: 28px;
  font-weight: 600;
}

.bottom-cta > p {
  margin-bottom: 40px;
  color: #777;
}

.features {
  width: 100%;
  max-width: 900px;
  margin-bottom: 40px;
}

.feature-item h3 {
  margin: 16px 0 8px;
  font-size: 18px;
  font-weight: 600;
}

.feature-item p {
  font-size: 14px;
  color: #666;
}

.feature-icon {
  font-size: 48px;
  color: #667eea;
}

.cta-button {
  padding: 0 40px;
  font-size: 16px;
  color: white;
  background-color: #28a745;
  border-color: #28a745;
}

.cta-button:hover {
  background-color: #218838;
  border-color: #1e7e34;
}

.feature-button {
  color: #7c3bed;
  background-color: rgb(124 59 237 / 20%);
  border-color: transparent;
}

.feature-button:hover,
.feature-button:focus {
  color: white;
  background-color: #7c3bed;
  border-color: #7c3bed;
}

/* ===== Responsive ===== */
@media (width <= 992px) {
  .header-main,
  .main-container {
    padding-right: 24px;
    padding-left: 24px;
  }
}

@media (width <= 768px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .action-card {
    margin-bottom: 24px;
  }
}
</style>
