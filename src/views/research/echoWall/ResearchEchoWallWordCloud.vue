<script setup lang="ts">
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { computed } from 'vue'
import VChart from 'vue-echarts'
// import 'echarts-wordcloud'

defineOptions({
  name: 'ResearchEchoWallWordCloud',
})

// 定义接口

// 定义 Props
interface Props {
  title?: string
  data: Array<{ text: string, mentionCount: number }>
  colors?: string[]
  minFontSize?: number
  maxFontSize?: number
  width?: string
  height?: string
  colorMode?: 'gradient' | 'random' | 'frequency'
}

const props = withDefaults(defineProps<Props>(), {
  title: '词云图',
  data: () => [],
  colors: () => [
    '#667eea',
    '#764ba2',
    '#f093fb',
    '#f5576c',
    '#4facfe',
    '#00f2fe',
    '#43e97b',
    '#38f9d7',
    '#ffecd2',
    '#fcb69f',
    '#a8edea',
    '#fed6e3',
    '#ff9a9e',
    '#fecfef',
    '#ffeaa7',
    '#fab2ff',
  ],
  minFontSize: 14,
  maxFontSize: 60,
  width: '100%',
  height: '400px',
  colorMode: 'frequency',
})

// 定义 Emits
const emit = defineEmits<{
  wordClick: [word: { text: string, mentionCount: number }]
  // wordHover: [word: { text: string, mentionCount: number } | null]
}>()

// 注册 ECharts 组件
use([CanvasRenderer])

// 计算属性
const maxValue = computed(() => {
  if (!props.data || !Array.isArray(props.data) || props.data.length === 0) {
    return 1
  }
  try {
    const values = props.data.filter(item => item && typeof item.mentionCount === 'number').map(item => item.mentionCount)
    return values.length > 0 ? Math.max(...values) : 1
  }
  catch (error) {
    console.error('计算最大值失败:', error)
    return 1
  }
})

const minValue = computed(() => {
  if (!props.data || !Array.isArray(props.data) || props.data.length === 0) {
    return 0
  }
  try {
    const values = props.data.filter(item => item && typeof item.mentionCount === 'number').map(item => item.mentionCount)
    return values.length > 0 ? Math.min(...values) : 0
  }
  catch (error) {
    console.error('计算最小值失败:', error)
    return 0
  }
})

// 生成颜色的函数
function generateColor(index: number, mentionCount: number): string {
  if (props.colorMode === 'gradient') {
    // 基于提及率的渐变色
    const ratio = (mentionCount - minValue.value) / (maxValue.value - minValue.value || 1)
    const hue = 220 + (ratio * 140) // 从蓝色到紫红色
    const saturation = 60 + (ratio * 30) // 饱和度变化
    const lightness = 45 + (ratio * 15) // 亮度变化
    return `hsl(${hue}, ${saturation}%, ${lightness}%)`
  }
  if (props.colorMode === 'frequency') {
    // 基于频率分级的颜色
    const ratio = (mentionCount - minValue.value) / (maxValue.value - minValue.value || 1)
    if (ratio > 0.8) {
      return '#667eea' // 最高频率 - 深蓝
    }
    if (ratio > 0.6) {
      return '#764ba2' // 高频率 - 紫色
    }
    if (ratio > 0.4) {
      return '#f093fb' // 中高频率 - 粉紫
    }
    if (ratio > 0.2) {
      return '#4facfe' // 中频率 - 蓝色
    }
    return '#43e97b' // 低频率 - 绿色
  }
  // 随机颜色模式
  return props.colors[index % props.colors.length]
}

// 转换数据格式为ECharts词云所需格式
const chartData = computed(() => {
  if (!props.data || !Array.isArray(props.data) || props.data.length === 0) {
    return []
  }
  try {
    return props.data
      .filter(item => item && item.text && typeof item.mentionCount === 'number' && item.mentionCount > 0)
      .map((item, index) => ({
        name: item.text,
        value: item.mentionCount,
        textStyle: {
          color: generateColor(index, item.mentionCount),
          fontWeight: item.mentionCount > maxValue.value * 0.7 ? 'bold' : 'normal',
        },
      }))
  }
  catch (error) {
    console.error('词云数据转换失败:', error)
    return []
  }
})

// ECharts配置
const chartOption = computed(() => {
  return {
    backgroundColor: 'transparent',
    tooltip: {
      show: true,
      backgroundColor: 'rgba(30, 30, 30, 0.9)',
      borderColor: 'transparent',
      borderRadius: 8,
      padding: [10, 15],
      textStyle: {
        color: '#fff',
        fontSize: 14,
        fontWeight: '500',
      },
      formatter: (params: any) => {
        const percentage = ((params.value / maxValue.value) * 100).toFixed(1)
        return `<div style="text-align: center;">
          <div style="font-size: 16px; font-weight: bold; margin-bottom: 4px;">${params.name}</div>
          <div style="color: #a0a0a0;">提及次数: <span style="color: #fff; font-weight: bold;">${params.value.toLocaleString()}</span></div>
          <div style="color: #a0a0a0;">占比: <span style="color: #fff; font-weight: bold;">${percentage}%</span></div>
        </div>`
      },
    },
    series: [{
      type: 'wordCloud',
      // echarts-wordcloud官方动画配置
      layoutAnimation: true,

      // 词云布局配置
      sizeRange: [props.minFontSize, props.maxFontSize],
      rotationRange: [-45, 45],
      rotationStep: 15,
      gridSize: 6,
      shape: 'circle',

      // 位置和尺寸配置
      left: 'center',
      top: 'center',
      width: '95%',
      height: '95%',
      right: null,
      bottom: null,

      // 其他配置
      drawOutOfBound: false,
      keepAspect: false,
      shrinkToFit: false,

      // 文字样式
      textStyle: {
        fontFamily: 'PingFang SC, Microsoft YaHei, Helvetica Neue, Arial, sans-serif',
        fontWeight: 'normal',
      },

      // 悬停效果
      emphasis: {
        focus: 'self',
        textStyle: {
          textShadowBlur: 0,
          textShadowColor: '#333',
        },
      },

      data: chartData.value,
    }],
  }
})

// 检查是否有有效数据
const hasValidData = computed(() => {
  return props.data && Array.isArray(props.data) && props.data.length > 0 && chartData.value.length > 0
})

// 处理图表事件
function handleChartClick(params: any) {
  if (params && params.name) {
    emit('wordClick', { text: params.name, mentionCount: params.value })
  }
}
</script>

<template>
  <div v-if="hasValidData" class="word-cloud-component">
    <div v-if="title" class="word-cloud-stats">
      <span>总词条: {{ Array.isArray(data) ? data.length : 0 }}</span>
      <span>最高提及: {{ maxValue.toLocaleString() }}</span>
      <span>总提及: {{ Array.isArray(data) ? data.filter(item => item && typeof item.mentionCount === 'number').reduce((sum, item) => sum + item.mentionCount, 0).toLocaleString() : '0' }}</span>
    </div>
    <!-- 词云图表 -->
    <VChart
      class="word-cloud-chart"
      :style="{ width: props.width, height: props.height }"
      :option="chartOption"
      autoresize
      @click="handleChartClick"
    />
  </div>
</template>

<style scoped>
.word-cloud-component {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.8);
  min-height: 350px;
  height: 100%;
  width: 100%;
  font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.word-cloud-component::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.03) 0%, transparent 70%);
  pointer-events: none;
  z-index: 0;
}

.word-cloud-stats {
  display: flex;
  justify-content: flex-end;  /**放在容器末端 */
  gap: 12px;
  font-size: 13px;
  color: #64748b;
  position: relative;
  margin-bottom: 5px;
  z-index: 1;
}

.word-cloud-stats span {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  backdrop-filter: blur(10px);
  padding: 6px 12px;
  border-radius: 20px;
  border: 1px solid rgba(102, 126, 234, 0.2);
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
}

.word-cloud-stats span:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.4);
}

.word-cloud-chart {
  width: 100%;
  flex: 1;
  min-height: 320px;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.8) 100%);
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(226, 232, 240, 0.6);
  backdrop-filter: blur(10px);
  z-index: 1;
}

.word-cloud-chart::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(102, 126, 234, 0.08) 0%, transparent 40%),
    radial-gradient(circle at 80% 70%, rgba(118, 75, 162, 0.06) 0%, transparent 40%),
    radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.4) 0%, transparent 30%);
  pointer-events: none;
  z-index: 1;
}

/* 词云文字基础样式 */
.word-cloud-chart :deep(text) {
  cursor: pointer;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .word-cloud-component {
    padding: 15px;
    min-height: 250px;
  }

  .word-cloud-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .word-cloud-header h3 {
    font-size: 16px;
  }

  .word-cloud-chart {
    min-height: 250px;
  }
}

@media (max-width: 480px) {
  .word-cloud-component {
    padding: 10px;
    min-height: 200px;
  }

  .word-cloud-chart {
    min-height: 200px;
  }
}
</style>
