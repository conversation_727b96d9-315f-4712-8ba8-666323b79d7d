<script setup lang="ts">
import {
  <PERSON><PERSON><PERSON>,
} from 'echarts/charts'
import {
  Grid<PERSON>omponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent,
} from 'echarts/components'
import { use } from 'echarts/core'
import {
  CanvasRenderer,
} from 'echarts/renderers'
import { computed, ref } from 'vue'
import VChart from 'vue-echarts'

defineOptions({
  name: 'ResearchEchoWallChart',
})

// 定义接口
interface ChartDataItem {
  topic: string
  likes: number
  dislikes: number
  netLikes: number
}

interface ChartData {
  title: string
  data: ChartDataItem[]
}

// 定义 Props
interface Props {
  chartData: ChartData
}

const props = defineProps<Props>()

// 检查是否有有效数据
const hasValidData = computed(() => {
  return props.chartData && props.chartData.data && Array.isArray(props.chartData.data) && props.chartData.data.length > 0
})

// 注册 ECharts 组件
use([
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  TitleComponent,
  Toolt<PERSON><PERSON>omponent,
  LegendComponent,
  GridComponent,
])

// 原来项目的美观颜色配置
const colors = {
  title: '#636E85',
  base: '#A57DC4',
  present: '#86A3C5',
  ruler: '#C3CCD6',
  axisText: '#A0ADC7',
  axis: '#CAD5E8',
  splitLine: '#E9F0FB',
  border: '#fff',
  better: '#2E8BFF',
  worse: '#FF6B00',
  markLine: '#7AAF70',
  axisLabel: '#BAC9DA',
  gauge: '#D1DAEE',
}

const fontFamily = 'MicrosoftYaHeiUI-Bold,MicrosoftYaHeiUI'

// 计算 ECharts 配置
const chartOption = computed(() => {
  const topics = props.chartData.data.map(item => item.topic)
  const likesData = props.chartData.data.map(item => item.likes)
  const dislikesData = props.chartData.data.map(item => -item.dislikes) // 负数显示
  const netLikesData = props.chartData.data.map(item => item.netLikes)

  return {
    title: {
      show: false, // 完全隐藏标题避免闪烁
    },
    tooltip: {
      trigger: 'axis',
      confine: true,
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: colors.axis,
      borderWidth: 1,
      textStyle: {
        color: colors.title,
        fontFamily,
      },
      formatter(params: any) {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((param: any) => {
          const value = param.seriesName === '拍砖数' ? Math.abs(param.value) : param.value
          result += `${param.marker}${param.seriesName}: ${value}<br/>`
        })
        return result
      },
    },
    legend: {
      data: ['点赞数', '拍砖数', '净点赞数'],
      right: '2%',
      top: 10,
      itemGap: 26,
      itemWidth: 10,
      icon: 'circle',
      textStyle: {
        fontSize: 12,
        fontWeight: 'normal',
        fontFamily,
        color: colors.title,
      },
    },
    grid: {
      left: '8%',
      right: '8%',
      top: '15%',
      bottom: '10%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      position: 'bottom',
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: false, // 隐藏数轴标签
      },
      splitLine: {
        show: false, // 不显示网格线
        lineStyle: {
          color: colors.splitLine,
          type: 'solid',
        },
      },
    },
    yAxis: {
      type: 'category',
      data: topics,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: colors.axisLabel,
        fontSize: 12,
        fontFamily,
      },
        splitLine: {
        show: false, // 不显示网格线
        lineStyle: {
          color: colors.splitLine,
          type: 'solid',
        },
      },
    },
    series: [
      {
        name: '点赞数',
        type: 'bar',
        data: likesData,
        itemStyle: {
          color: colors.better,
          opacity: 0.8,
          borderRadius: [2, 2, 2, 2],
        },
        barWidth: '20%',
        label: {
          show: true,
          position: 'right', // 点赞数标签在柱子右侧
          color: colors.title,
          fontSize: 11,
          fontFamily,
          distance: 8,
        },
      },
      {
        name: '拍砖数',
        type: 'bar',
        data: dislikesData,
        itemStyle: {
          color: colors.worse,
          opacity: 0.8,
          borderRadius: [2, 2, 2, 2],
        },
        barWidth: '20%',
        label: {
          show: true,
          position: 'left', // 拍砖数标签在柱子左侧
          color: colors.title,
          fontSize: 11,
          fontFamily,
          distance: 8,
          formatter(params: any) {
            return Math.abs(params.value)
          },
        },
      },
      {
        name: '净点赞数',
        type: 'bar',
        data: netLikesData,
        itemStyle: {
          color: colors.base,
          opacity: 0.8,
          borderRadius: [2, 2, 2, 2],
        },
        barWidth: '25%',
        label: {
          show: true,
          position: (params: any) => {
            // 净点赞数：正数在右边，负数在左边
            return params.value >= 0 ? 'right' : 'left'
          },
          color: '#ffffff',
          fontSize: 11,
          fontWeight: 'bold',
          fontFamily,
          distance: 8,
        },
      },
    ],
  }
})
</script>

<template>
  <div v-if="hasValidData" class="chart-container">
    <!-- 图表 -->
    <VChart
      :option="chartOption"
      :autoresize="true"
      class="chart"
    />
  </div>
</template>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
  background: #ffffff;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #E9F0FB;
  font-family: 'MicrosoftYaHeiUI-Bold', 'MicrosoftYaHeiUI', sans-serif;
}

.chart {
  padding: 10px;
  width: 100%;
  height: 500px;
  min-height: 400px;
}

@media (max-width: 768px) {
  .chart-container {
    padding: 15px;
    border-radius: 4px;
  }

  .chart {
    height: 400px;
    min-height: 300px;
  }
}

/* 优化图表在不同屏幕尺寸下的显示效果 */
@media (max-width: 480px) {
  .chart-container {
    padding: 12px;
  }

  .chart {
    height: 350px;
    min-height: 280px;
  }
}
</style>
