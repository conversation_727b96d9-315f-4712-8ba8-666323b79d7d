<script setup lang="ts">
import { Calendar, Download, Filter, Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, ref, watch } from 'vue'
import { DateRangePicker } from '@/utils/research/datePicker'
import { usePagination, PaginationComponent } from '@/utils/research/pagination'
import tableDataJson from '@/mock/research/tableData.json'

defineOptions({
  name: 'ResearchEchoWallTable',
})

// 产品类数据接口
interface ProductDataItem {
  id: string
  storeName: string
  city: string
  visitType: string
  name: string
  phone: string
  gender: string
  ageGroup: string
  surveyResult: number
  projectCategory: string
  projectSubcategory: string
  brand: string
  pushNode: string
  completionTime: string
  originalText: string
  topic: string
}

// 服务类数据接口
interface ServiceDataItem {
  id: string
  storeName: string
  city: string
  visitType: string
  name: string
  phone: string
  gender: string
  ageGroup: string
  surveyResult: number
  completionTime: string
  originalText: string
  topic: string
}

type TableDataItem = ProductDataItem | ServiceDataItem

interface Props {
  title: string
  tableType: 'service' | 'product'
  selectedTags?: string[]
  currentMonth: string
}

interface TableComponentExpose {
  addTagToFilter: (tag: string) => void
  loadData: (filters?: {
    keyword?: string
    tags?: string[]
    dateRange?: [string, string]
  }) => void
  refreshData: () => void
}

const props = defineProps<Props>()

const emit = defineEmits<{
  viewDetail: [item: TableDataItem]
  export: [data: TableDataItem[]]
  tagSelect: [tags: string[]]
}>()

// 内部数据状态
const internalData = ref<TableDataItem[]>([])

// 根据tableType和月份获取数据
function loadInternalData() {
  const dataKey = props.tableType === 'service' ? 'serviceTable' : 'productTable'
  const monthData = (tableDataJson as any)[dataKey]?.[props.currentMonth] || []
  internalData.value = monthData
}

// 检查是否有有效数据
const hasValidData = computed(() => {
  return internalData.value && Array.isArray(internalData.value) && internalData.value.length > 0
})

// 搜索条件
const searchKeyword = ref('')
const selectedTags = ref<string[]>(props.selectedTags || [])
const dateRange = ref<[string, string] | undefined>(undefined)
const showFilters = ref(false)

// 临时筛选条件（用于点击搜索按钮前的暂存）
const tempSearchKeyword = ref('')
const tempSelectedTags = ref<string[]>([])
const tempDateRange = ref<[string, string] | undefined>(undefined)

// 分页和排序
const {
  pagination,
  handleCurrentChange,
  handleSizeChange,
  resetPagination,
  updateTotal
} = usePagination({
  initialPageSize: 20,
  pageSizes: [10, 20, 50, 100]
})

const sortConfig = ref({ prop: 'completionTime', order: 'descending' })

// 获取所有可用标签
const availableTags = computed(() => {
  const tagSet = new Set<string>()
  internalData.value.forEach((item) => {
    if (item.topic) {
      item.topic.split(',').forEach((tag) => {
        tagSet.add(tag.trim())
      })
    }
  })
  return Array.from(tagSet)
})

// 过滤数据
const filteredData = computed(() => {
  let result = [...internalData.value]

  // 月度过滤 - 根据选中的月份过滤完成时间
  if (props.currentMonth) {
    const [year, month] = props.currentMonth.split('-')
    const monthStart = new Date(parseInt(year), parseInt(month) - 1, 1)
    const monthEnd = new Date(parseInt(year), parseInt(month), 0, 23, 59, 59, 999)

    result = result.filter((item) => {
      const itemDate = new Date(item.completionTime)
      // 确保日期对象有效
      if (Number.isNaN(itemDate.getTime()) || Number.isNaN(monthStart.getTime()) || Number.isNaN(monthEnd.getTime())) {
        return false
      }
      return itemDate >= monthStart && itemDate <= monthEnd
    })
  }

  // 关键词过滤
  if (searchKeyword.value) {
    const searchLower = searchKeyword.value.toLowerCase()
    result = result.filter(item =>
      item.originalText.toLowerCase().includes(searchLower),
    )
  }

  // 标签过滤
  if (selectedTags.value.length > 0) {
    result = result.filter((item) => {
      const itemTags = item.topic.split(',').map(tag => tag.trim())
      return selectedTags.value.some(tag => itemTags.includes(tag))
    })
  }

  // 时间范围过滤
  if (dateRange.value) {
    const [startDateStr, endDateStr] = dateRange.value
    const startDate = new Date(startDateStr)
    const endDate = new Date(endDateStr)
    // 设置结束时间为当天的23:59:59
    endDate.setHours(23, 59, 59, 999)

    result = result.filter((item) => {
      const itemDate = new Date(item.completionTime)
      // 确保日期对象有效
      if (Number.isNaN(itemDate.getTime()) || Number.isNaN(startDate.getTime()) || Number.isNaN(endDate.getTime())) {
        return false
      }
      return itemDate >= startDate && itemDate <= endDate
    })
  }

  // 排序
  if (sortConfig.value.prop) {
    result.sort((a: any, b: any) => {
      const aVal = a[sortConfig.value.prop]
      const bVal = b[sortConfig.value.prop]

      if (sortConfig.value.order === 'ascending') {
        return aVal > bVal ? 1 : -1
      }
      else {
        return aVal < bVal ? 1 : -1
      }
    })
  }

  return result
})

// 搜索和过滤处理
function handleSearch() {
  // 将临时条件应用到实际筛选条件
  searchKeyword.value = tempSearchKeyword.value
  selectedTags.value = [...tempSelectedTags.value]
  dateRange.value = tempDateRange.value

  pagination.currentPage = 1
  // 分页总数会由watch自动更新
}

function handleTagChange(tags: string[]) {
  tempSelectedTags.value = tags
}

function handleDateRangeChange() {
  // 临时存储，不立即过滤
}

function clearFilters() {
  tempSearchKeyword.value = ''
  tempSelectedTags.value = []
  tempDateRange.value = undefined
  searchKeyword.value = ''
  selectedTags.value = []
  dateRange.value = undefined

  pagination.currentPage = 1
  // 分页总数会由watch自动更新
}

function toggleFilters() {
  showFilters.value = !showFilters.value
}

// 表格处理
function handleSortChange({ prop, order }: any) {
  sortConfig.value = { prop, order }
}

// 分页处理函数已由 usePagination 钩子提供

function handleExport() {
  emit('export', filteredData.value)
  ElMessage.success('数据导出成功')
}

// 格式化函数
function formatDate(dateStr: string) {
  return new Date(dateStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}

// 展开/收起功能
function toggleTextExpansion(row: any) {
  row._textExpanded = !row._textExpanded
}

function toggleTagsExpansion(row: any) {
  row._tagsExpanded = !row._tagsExpanded
}

// 添加标签到筛选条件
function addTagToFilter(tag: string) {
  if (!tempSelectedTags.value.includes(tag)) {
    tempSelectedTags.value.push(tag)
  }
  // 显示筛选器
  showFilters.value = true
}

// 获取信息项数组
function getInfoItems(row: any) {
  const items = []
  if (row.city) {
    items.push({ label: '城市', value: row.city })
  }
  if (row.name) {
    items.push({ label: '姓名', value: row.name })
  }
  if (row.phone) {
    items.push({ label: '电话', value: row.phone })
  }
  if (row.gender) {
    items.push({ label: '性别', value: row.gender })
  }
  if (row.ageGroup) {
    items.push({ label: '年龄', value: row.ageGroup })
  }
  if (row.visitType) {
    items.push({ label: '到访', value: row.visitType })
  }
  if (row.projectCategory) {
    items.push({ label: '项目', value: row.projectCategory })
  }
  if (row.projectSubcategory) {
    items.push({ label: '子类', value: row.projectSubcategory })
  }
  if (row.brand) {
    items.push({ label: '品牌', value: row.brand })
  }
  if (row.pushNode) {
    items.push({ label: '节点', value: row.pushNode })
  }
  return items
}

// 切换信息展开/收起
function toggleInfoExpansion(row: any) {
  row._infoExpanded = !row._infoExpanded
}



// 计算分页后的数据
const paginatedData = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return filteredData.value.slice(start, end)
})

// 加载数据方法
function loadData(filters?: {
  keyword?: string
  tags?: string[]
  dateRange?: [string, string]
}) {
  // 首先重新加载内部数据
  loadInternalData()

  if (filters) {
    // 应用过滤条件
    if (filters.keyword !== undefined) {
      searchKeyword.value = filters.keyword
      tempSearchKeyword.value = filters.keyword
    }
    if (filters.tags !== undefined) {
      selectedTags.value = [...filters.tags]
      tempSelectedTags.value = [...filters.tags]
    }
    if (filters.dateRange !== undefined) {
      dateRange.value = filters.dateRange
      tempDateRange.value = filters.dateRange
    }
  }

  // 重置分页
  resetPagination()

  // 分页总数会由watch自动更新
}

// 刷新数据方法
function refreshData() {
  loadInternalData()
  resetPagination()
}

// 暴露方法给父组件
defineExpose<TableComponentExpose>({
  addTagToFilter,
  loadData,
  refreshData,
})

// 监听月份和表格类型变化，重新加载数据
watch([() => props.currentMonth, () => props.tableType], () => {
  loadInternalData()
  // 清空筛选条件
  searchKeyword.value = ''
  selectedTags.value = []
  dateRange.value = undefined
  tempSearchKeyword.value = ''
  tempSelectedTags.value = []
  tempDateRange.value = undefined
  resetPagination()
}, { immediate: true })

// 监听过滤后数据变化，更新总数
watch(() => filteredData.value, (newData) => {
  updateTotal(newData.length)
}, { immediate: true })


// 监听外部传入的selectedTags变化
watch(() => props.selectedTags, (newTags) => {
  if (newTags) {
    selectedTags.value = [...newTags]
    tempSelectedTags.value = [...newTags]
  }
}, { immediate: true })
</script>

<template>
  <div v-if="hasValidData" class="table-container">
    <div class="table-header">
      <h3>{{ title }}</h3>
      <div class="table-controls">
        <el-button
          :type="showFilters ? 'primary' : 'default'"
          @click="toggleFilters"
        >
          <el-icon><Filter /></el-icon>
          筛选条件
        </el-button>
        <el-button type="primary" @click="handleExport">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 搜索条件区域 -->
    <div v-show="showFilters" class="search-filters">
      <div class="filter-row">
        <div class="filter-item">
          <label>关键词搜索：</label>
          <el-input
            v-model="tempSearchKeyword"
            placeholder="根据客户原话搜索..."
            prefix-icon="Search"
            clearable
            style="width: 300px;"
          />
        </div>
      </div>

      <div class="filter-row">
        <!-- <div class="filter-item">
          <label>标签筛选：</label>
          <el-select
            v-model="tempSelectedTags"
            multiple
            clearable
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="3"
            placeholder="选择标签"
            style="width: 600px;"
            @change="handleTagChange"
          >
            <el-option
              v-for="tag in availableTags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </div> -->

        <div class="filter-item">
          <label>完成时间：</label>
          <DateRangePicker
            v-model="tempDateRange"
            :placeholder="['开始时间', '结束时间']"
            range-separator="至"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            :init-period="currentMonth"
            style="width: 400px;"
            @change="handleDateRangeChange"
          />
        </div>
      </div>

      <div class="filter-actions">
        <div class="filter-buttons">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="clearFilters">
            清空筛选
          </el-button>
        </div>
        <span class="filter-stats">共找到 {{ filteredData.length }} 条记录</span>
      </div>
    </div>

    <div class="table-content">
      <!-- 表格 -->
      <el-table
        :data="paginatedData"
        stripe
        border
        style="width: 100%;"
        :default-sort="{ prop: 'completionTime', order: 'descending' }"
        @sort-change="handleSortChange"
      >
        <!-- 序号列 -->
        <el-table-column
          type="index"
          label="序号"
          width="60"
          align="center"
        />

        <!-- 唯一码列 -->
        <el-table-column
          prop="id"
          label="唯一码"
          width="100"
          align="center"
          show-overflow-tooltip
        />

        <!-- 机构列 -->
        <!-- <el-table-column
          prop="storeName"
          label="机构"
          width="120"
          show-overflow-tooltip
        /> -->

        <!-- 回访结果列 -->
        <el-table-column
          prop="surveyResult"
          label="回访结果"
          width="150"
          align="center"
          sortable
        >
          <template #default="{ row }">
            <el-rate
              v-model="row.surveyResult"
              disabled
              size="small"
              show-score
              text-color="#ff9900"
            />
          </template>
        </el-table-column>

        <!-- 客户原话列 -->
        <el-table-column
          prop="originalText"
          label="客户原话"
          min-width="350"
        >
          <template #default="{ row }">
            <div class="original-text-container">
              <div
                class="original-text"
                :class="{ expanded: row._textExpanded }"
              >
                {{ row.originalText }}
              </div>
              <el-button
                v-if="row.originalText.length > 100"
                type="text"
                size="small"
                class="expand-btn"
                @click="toggleTextExpansion(row)"
              >
                {{ row._textExpanded ? '收起' : '展开' }}
              </el-button>
            </div>
          </template>
        </el-table-column>

        <!-- 标签列 -->
        <!-- <el-table-column
          prop="topic"
          label="标签"
          width="180"
          style="display: none;"
        >
          <template #default="{ row }">
            <div class="topic-tags-container">
              <div class="topic-tags">
                <template v-for="(topic, index) in row.topic.split(',')" :key="topic">
                  <el-tag
                    v-if="!row._tagsExpanded && index < 3"
                    size="small"
                    class="topic-tag"
                    type="primary"
                    effect="light"
                    @click="addTagToFilter(topic.trim())"
                  >
                    {{ topic.trim() }}
                  </el-tag>
                  <el-tag
                    v-else-if="row._tagsExpanded"
                    size="small"
                    class="topic-tag"
                    type="primary"
                    effect="light"
                    @click="addTagToFilter(topic.trim())"
                  >
                    {{ topic.trim() }}
                  </el-tag>
                </template>
                <el-button
                  v-if="row.topic.split(',').length > 3"
                  type="text"
                  size="small"
                  class="expand-tags-btn"
                  @click="toggleTagsExpansion(row)"
                >
                  {{ row._tagsExpanded ? '收起' : `+${row.topic.split(',').length - 3}` }}
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column> -->

        <!-- 完成时间列 -->
        <el-table-column
          prop="completionTime"
          label="完成时间"
          width="160"
          align="center"
          sortable
        >
          <template #default="{ row }">
            {{ formatDate(row.completionTime) }}
          </template>
        </el-table-column>

        <!-- 其他信息列 -->
        <el-table-column
          label="其他信息"
          width="250"
        >
          <template #default="{ row }">
            <div class="other-info-container">
              <div class="info-tags">
                <template v-for="(info, index) in getInfoItems(row)" :key="info.label">
                  <el-tag
                    v-if="!row._infoExpanded && index < 2"
                    size="small"
                    class="info-tag"
                    type="info"
                    effect="light"
                  >
                    {{ info.label }}：{{ info.value }}
                  </el-tag>
                  <el-tag
                    v-else-if="row._infoExpanded"
                    size="small"
                    class="info-tag"
                    type="info"
                    effect="light"
                  >
                    {{ info.label }}：{{ info.value }}
                  </el-tag>
                </template>
                <el-button
                  v-if="getInfoItems(row).length > 2"
                  type="text"
                  size="small"
                  class="expand-info-btn"
                  @click="toggleInfoExpansion(row)"
                >
                  {{ row._infoExpanded ? '收起' : `+${getInfoItems(row).length - 2}` }}
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <PaginationComponent
      :pagination="pagination"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<style scoped>
.table-container {
  width: 100%;
  height: 100%;
  padding: 24px;
  background: linear-gradient(145deg, #fff 0%, #f8fafc 100%);
  border: 1px solid rgb(255 255 255 / 80%);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgb(0 0 0 / 8%), 0 2px 8px rgb(0 0 0 / 4%);
}

.table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 16px;
  margin-bottom: 20px;
  background: linear-gradient(90deg, rgb(102 126 234 / 10%), rgb(118 75 162 / 10%)) bottom;
  background-repeat: no-repeat;
  background-size: 100% 2px;
  border-bottom: 2px solid transparent;
}

.table-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  letter-spacing: 0.5px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.table-controls {
  display: flex;
  gap: 15px;
  align-items: center;
}

/* 搜索条件样式 */
.search-filters {
  padding: 20px;
  margin-bottom: 20px;
  background: linear-gradient(135deg, rgb(248 250 252 / 80%) 0%, rgb(241 245 249 / 80%) 100%);
  border: 1px solid rgb(226 232 240 / 60%);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
  margin-bottom: 16px;
}

.filter-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

.filter-item label {
  min-width: 80px;
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
}

.filter-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 16px;
  border-top: 1px solid rgb(226 232 240 / 60%);
}

.filter-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.filter-stats {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.table-content {
  margin-bottom: 20px;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgb(0 0 0 / 5%);
}

.original-text-container {
  position: relative;
}

.original-text {
  display: -webkit-box;
  max-height: 72px;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  line-height: 1.5;
  color: #374151;
  transition: all 0.3s ease;
  -webkit-box-orient: vertical;
}

.original-text.expanded {
  display: block;
  max-height: none;
  -webkit-line-clamp: unset;
}

.expand-btn {
  padding: 2px 8px;
  margin-top: 4px;
  font-size: 12px;
  color: #667eea;
  background: rgb(102 126 234 / 10%);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.expand-btn:hover {
  color: #5a67d8;
  background: rgb(102 126 234 / 20%);
}

.topic-tags-container {
  position: relative;
}

.topic-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.topic-tag {
  margin: 2px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.topic-tag:hover {
  box-shadow: 0 2px 8px rgb(102 126 234 / 30%);
  transform: translateY(-1px);
}

.expand-tags-btn {
  min-width: 32px;
  height: 20px;
  padding: 2px 6px;
  margin-left: 4px;
  font-size: 11px;
  line-height: 16px;
  color: #667eea;
  background: rgb(102 126 234 / 10%);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.expand-tags-btn:hover {
  color: #5a67d8;
  background: rgb(102 126 234 / 20%);
}

.other-info-container {
  position: relative;
}

.info-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.info-tag {
  margin: 2px;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.info-tag:hover {
  box-shadow: 0 2px 8px rgb(144 147 153 / 30%);
  transform: translateY(-1px);
}

.expand-info-btn {
  min-width: 32px;
  height: 20px;
  padding: 2px 6px;
  margin-left: 4px;
  font-size: 11px;
  line-height: 16px;
  color: #667eea;
  background: rgb(144 147 153 / 10%);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.expand-info-btn:hover {
  color: #667eea;
  background: rgb(144 147 153 / 20%);
}

:deep(.el-table) {
  overflow: hidden;
  font-size: 13px;
  background: #fff;
  border: 1px solid rgb(226 232 240 / 60%);
  border-radius: 12px;
}

:deep(.el-table__header) {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

:deep(.el-table__header th) {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  letter-spacing: 0.3px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 2px solid rgb(102 126 234 / 10%);
}

:deep(.el-table th) {
  font-weight: 600;
  color: #2c3e50;
  background-color: #f8f9fa;
}

:deep(.el-table__body tr) {
  transition: all 0.3s ease;
}

:deep(.el-table__body tr:hover) {
  background: linear-gradient(135deg, rgb(102 126 234 / 5%) 0%, rgb(118 75 162 / 5%) 100%);
  box-shadow: 0 4px 12px rgb(0 0 0 / 8%);
  transform: translateY(-1px);
}

:deep(.el-table__body td) {
  padding: 12px 8px;
  border-bottom: 1px solid rgb(241 245 249 / 80%);
}

:deep(.el-table td) {
  padding: 8px 0;
}

:deep(.el-table .el-table__row:hover) {
  background-color: #f0f9ff;
}

:deep(.el-button) {
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  box-shadow: 0 4px 12px rgb(102 126 234 / 40%);
  transform: translateY(-1px);
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 5%);
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 4px 8px rgb(0 0 0 / 10%);
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-date-editor .el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-tag) {
  font-weight: 500;
  border: none;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
}

:deep(.el-rate) {
  display: flex;
  align-items: center;
}

@media (width <= 768px) {
  .table-container {
    padding: 16px;
    border-radius: 8px;
  }

  .table-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
    align-items: stretch;
  }

  .table-controls {
    justify-content: space-between;
    justify-content: center;
    width: 100%;
  }

  .filter-row {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .filter-item {
    flex-direction: column;
    gap: 4px;
    align-items: stretch;
  }

  .filter-item label {
    min-width: auto;
  }
}

@media (width <= 480px) {
  .table-controls .el-input {
    width: 200px !important;
  }
}
</style>
