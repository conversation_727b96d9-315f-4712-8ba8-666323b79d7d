<script setup lang="ts">
import {
  Calendar,
  Download,
  Refresh,
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, ref } from 'vue'
import ChartComponent from '@/views/research/echoWall/ResearchEchoWallChart.vue'
import TableComponent from '@/views/research/echoWall/ResearchEchoWallTable.vue'
import WordCloudComponent from '@/views/research/echoWall/ResearchEchoWallWordCloud.vue'
import chartData from '@/mock/research/chartData.json'
import echoWallData from '@/mock/research/echoWallData.json'
import wordCloudData from '@/mock/research/wordCloudData.json'

defineOptions({
  name: 'ResearchEchoWall',
})

// 响应式数据
const activeTab = ref('service')
const selectedMonth = ref('2025-07')
const selectedTags = ref<string[]>([])

// 表格组件引用
const serviceTableRef = ref()
const productTableRef = ref()

// 数据获取计算属性
const currentServiceWordCloudData = computed(() => {
  return (wordCloudData as any).serviceWordCloud?.[selectedMonth.value] || []
})

const currentProductWordCloudData = computed(() => {
  return (wordCloudData as any).productWordCloud?.[selectedMonth.value] || []
})

const currentServiceChartData = computed(() => {
  return (chartData as any).serviceChart?.[selectedMonth.value] || []
})

const currentProductChartData = computed(() => {
  return (chartData as any).productChart?.[selectedMonth.value] || []
})



// 数据获取方法（保持向后兼容）
function getCurrentWordCloudData(type: string) {
  return type === 'service' ? currentServiceWordCloudData.value : currentProductWordCloudData.value
}

function getCurrentChartData(type: string) {
  return type === 'service' ? currentServiceChartData.value : currentProductChartData.value
}

// 词云事件处理
function handleWordClick(item: any) {
  if (item && item.text) {
    const tag = item.text.trim()

    // 添加标签到选中列表（如果不存在）
    if (!selectedTags.value.includes(tag)) {
      selectedTags.value.push(tag)
    }

    // 获取当前活跃的表格组件引用
    const currentTableRef = activeTab.value === 'service' ? serviceTableRef.value : productTableRef.value

    // 调用表格组件的方法添加标签到筛选器
    if (currentTableRef && currentTableRef.addTagToFilter) {
      currentTableRef.addTagToFilter(tag)
    }

    ElMessage.success(`已将"${tag}"添加到标签筛选器`)
  }
}

function handleExportData() {
  ElMessage.success('数据导出成功')
}

// 计算属性
const monthOptions = computed(() => (echoWallData as any).monthOptions || [])

// 方法
function handleTabChange(tabName: string | number) {
  activeTab.value = String(tabName)
  // 切换标签页时清空选中的标签
  selectedTags.value = []
}

// 处理标签选择事件
// function handleTagSelect(tags: string[]) {
//   selectedTags.value = tags
// }

function handleMonthChange() {
  // 月份变化时清空所有筛选状态
  selectedTags.value = []
  // 表格组件会自动响应月份变化并重新加载数据
}

function handleRefreshData() {
  ElMessage.success('数据刷新成功')
}
</script>

<template>
  <div class="research-echo-wall">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
        <h1 class="page-title">
          <FaIcon name="i-ph:chat-circle-text-light" class="title-icon" />
          回音壁
        </h1>
        <p class="page-subtitle">
          洞见客户心声全景：聚焦热议焦点，直击原生反馈的价值内核
        </p>
        </div>
      </div>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <div class="control-left">
        <el-select
          v-model="selectedMonth"
          placeholder="选择月份"
          class="month-selector"
          @change="handleMonthChange"
        >
          <template #prefix>
            <el-icon><Calendar /></el-icon>
          </template>
          <el-option
            v-for="month in monthOptions"
            :key="month.value"
            :label="month.label"
            :value="month.value"
          />
        </el-select>
      </div>
      <div class="control-right">
        <el-button type="primary" @click="handleRefreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <!-- <el-button type="success" @click="handleExportData">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button> -->
      </div>
    </div>

    <!-- Tab切换 -->
    <el-tabs v-model="activeTab" class="echo-tabs" @tab-change="handleTabChange">
      <!-- 服务问卷 -->
      <el-tab-pane label="服务问卷" name="service">
        <div class="tab-content">
          <!-- 图表区域 -->
          <div class="charts-section" style="display: none;">
            <el-row :gutter="24">
              <!-- 词云图 -->
              <el-col :span="12">
                <div class="chart-card">
                  <div class="card-header">
                    字体大小反映话题提及频率
                  </div>
                  <div class="chart-content">
                    <WordCloudComponent
                      :key="`service-wordcloud-${selectedMonth}-${activeTab}`"
                      :data="getCurrentWordCloudData('service')"
                      @word-click="handleWordClick"
                    />
                  </div>
                </div>
              </el-col>

              <!-- 多指标对比图 -->
              <el-col :span="12">
                <div class="chart-card">
                  <div class="card-header">
                    点赞数、拍砖数和净点赞的对比分析
                  </div>
                  <div class="chart-content">
                    <ChartComponent
                      :key="`service-chart-${selectedMonth}-${activeTab}`"
                      :chart-data="getCurrentChartData('service')"
                    />
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 数据表格 -->
          <div class="table-section">
            <TableComponent
              :key="`service-table-${selectedMonth}-${activeTab}`"
              ref="serviceTableRef"
              title="服务客户原话明细"
              table-type="service"
              :selected-tags="selectedTags"
              :current-month="selectedMonth"
              @export="handleExportData"
            />
          </div>
        </div>
      </el-tab-pane>

      <!-- 产品问卷 -->
      <el-tab-pane label="产品问卷" name="product">
        <div class="tab-content">
          <!-- 图表区域 -->
          <div class="charts-section" style="display: none;">
            <el-row :gutter="24">
              <!-- 词云图 -->
              <el-col :span="12">
                <div class="chart-card">
                  <div class="card-header">
                      字体大小反映话题提及频率
                  </div>
                  <div class="chart-content">
                    <WordCloudComponent
                      :key="`product-wordcloud-${selectedMonth}-${activeTab}`"
                      :data="getCurrentWordCloudData('product')"
                      @word-click="handleWordClick"
                    />
                  </div>
                </div>
              </el-col>

              <!-- 多指标对比图 -->
              <el-col :span="12">
                <div class="chart-card">
                  <div class="card-header">
                      点赞数、拍砖数和净点赞的对比分析
                  </div>
                  <div class="chart-content">
                    <ChartComponent
                      :key="`product-chart-${selectedMonth}-${activeTab}`"
                      :chart-data="getCurrentChartData('product')"
                    />
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 数据表格 -->
          <div class="table-section">
            <TableComponent
              :key="`product-table-${selectedMonth}-${activeTab}`"
              ref="productTableRef"
              title="产品客户原话明细"
              table-type="product"
              :selected-tags="selectedTags"
              :current-month="selectedMonth"
              @export="handleExportData"
            />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<style scoped>
.research-echo-wall {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1); /* 柔和阴影 */
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 24px;
}

.title-section {
  text-align: center;
  flex: 1;
  min-width: 300px;
}

.title-icon {
  font-size: 30px; /* 调整图标大小与标题文字更协调 */
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); /* 与标题文字相同的渐变色彩 */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-right: 4px; /* 与文字的间距 */
  transition: all 0.3s ease; /* 平滑过渡动画 */
  filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.2)); /* 图标阴影效果 */
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  color: #303133;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); /* 渐变文字效果 */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}


.page-subtitle {
   margin: 0;
   font-size: 18px;
   color: #606266;
   line-height: 1.5;
}

/* 控制面板 */
.control-panel {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px 24px;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.control-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.control-right {
  display: flex;
  gap: 12px;
}

.month-selector {
  width: 200px;
}

/* Tab样式 */
.echo-tabs {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.echo-tabs :deep(.el-tabs__header) {
  margin: 0;
  background: #f8f9fa;
  padding: 0 24px;
}

.echo-tabs :deep(.el-tabs__nav-wrap) {
  padding: 16px 0;
}

.echo-tabs :deep(.el-tabs__item) {
  font-size: 16px;
  font-weight: 500;
  padding: 0 24px;
  height: 48px;
  line-height: 48px;
}

.echo-tabs :deep(.el-tabs__item.is-active) {
  color: #667eea;
  font-weight: 600;
}

.echo-tabs :deep(.el-tabs__active-bar) {
  background: linear-gradient(90deg, #667eea, #764ba2);
  height: 3px;
}

.tab-content {
  padding: 24px;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 32px;
}

.chart-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  height: 520px;
  display: flex;
  flex-direction: column;
}

.card-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f2f5;
  background: #fafbfc;
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #1f2937;
}

.chart-content {
  flex: 1;
  padding: 0;
  display: flex;
  align-items: stretch;
  justify-content: stretch;
}

.echarts-container {
  width: 100%;
  height: 400px;
}

/* 表格区域 */
.table-section {
  margin-top: 32px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .charts-section .el-col {
    margin-bottom: 24px;
  }

  .chart-card {
    height: 450px;
  }
}

@media (max-width: 768px) {
  .research-echo-wall {
    padding: 16px;
  }

  .page-header {
    padding: 24px 20px;
  }

  .page-title {
    font-size: 24px;
  }

  .control-panel {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .control-right {
    justify-content: center;
  }

  .tab-content {
    padding: 16px;
  }

  .chart-card {
    height: 400px;
  }
}

/* 动画效果 */
.chart-card {
  transition: all 0.3s ease;
}

.chart-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}
</style>
