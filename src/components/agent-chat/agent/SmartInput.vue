<template>
  <div class="smart-input">
    <!-- 输入建议提示 -->
    <div v-if="showSuggestions && filteredSuggestions.length > 0" class="suggestions-popup">
      <div class="suggestions-header">
        <h6>智能建议</h6>
        <button @click="hideSuggestions" class="btn-close">
          <i class="i-carbon-close"></i>
        </button>
      </div>
      <div class="suggestions-list">
        <button
          v-for="(suggestion, index) in filteredSuggestions"
          :key="suggestion.id"
          @click="applySuggestion(suggestion)"
          class="suggestion-item"
          :class="{ active: selectedSuggestionIndex === index }"
        >
          <div class="suggestion-icon">
            <i :class="suggestion.icon || 'i-carbon-idea'"></i>
          </div>
          <div class="suggestion-content">
            <div class="suggestion-text">{{ suggestion.text }}</div>
            <div v-if="suggestion.description" class="suggestion-description">
              {{ suggestion.description }}
            </div>
          </div>
          <div class="suggestion-category">{{ suggestion.category }}</div>
        </button>
      </div>
    </div>

    <!-- 主输入区域 -->
    <div class="input-container">
      <!-- 快捷按钮 -->
      <!-- <div class="quick-actions">
        <button
          v-for="action in quickActions"
          :key="action.id"
          @click="insertQuickAction(action)"
          class="quick-action-btn"
          :title="action.description"
        >
          <i :class="action.icon"></i>
          <span>{{ action.text }}</span>
        </button>
      </div> -->

      <!-- 输入框 -->
      <div class="input-wrapper">
        <div class="input-main">
          <textarea
            ref="textareaRef"
            v-model="inputValue"
            @input="handleInput"
            @keydown="handleKeydown"
            @focus="handleFocus"
            @blur="handleBlur"
            :placeholder="placeholder"
            :disabled="disabled"
            class="input-textarea"
            rows="1"
          ></textarea>

          <!-- 字符计数 -->
          <div v-if="showCharCount" class="char-count">
            {{ inputValue.length }}/{{ maxLength }}
          </div>
        </div>

        <!-- 工具按钮 -->
        <div class="input-tools">
          <!-- 语音输入按钮 -->
          <!-- <button
            v-if="enableVoiceInput"
            @click="toggleVoiceInput"
            class="tool-btn"
            :class="{ active: isRecording }"
            :title="isRecording ? '停止录音' : '语音输入'"
          >
            <i :class="isRecording ? 'i-carbon-microphone-filled' : 'i-carbon-microphone'"></i>
          </button> -->

          <!-- 文件上传按钮 -->
          <!-- <button
            v-if="enableFileUpload"
            @click="triggerFileUpload"
            class="tool-btn"
            title="上传文件"
          >
            <i class="i-carbon-attachment"></i>
          </button>
          <input
            ref="fileInputRef"
            type="file"
            @change="handleFileUpload"
            :accept="acceptedFileTypes"
            multiple
            class="hidden"
          > -->

          <!-- 建议按钮 -->
          <button
            @click="toggleSuggestions"
            class="tool-btn"
            :class="{ active: showSuggestions }"
            title="智能建议"
          >
            <i class="i-carbon-idea"></i>
          </button>

          <!-- 发送按钮 -->
          <button
            @click="sendMessage"
            :disabled="!canSend"
            class="send-btn"
            :class="{ disabled: !canSend }"
            title="发送消息 (Ctrl+Enter)"
          >
            <i v-if="loading" class="i-carbon-in-progress animate-spin"></i>
            <i v-else class="i-carbon-send-alt"></i>
          </button>
        </div>
      </div>

      <!-- 附件预览 -->
      <div v-if="attachedFiles.length > 0" class="attached-files">
        <div
          v-for="file in attachedFiles"
          :key="file.id"
          class="attached-file"
        >
          <div class="file-info">
            <i :class="getFileIcon(file.type)"></i>
            <span class="file-name">{{ file.name }}</span>
            <span class="file-size">{{ formatFileSize(file.size) }}</span>
          </div>
          <button @click="removeFile(file.id)" class="file-remove">
            <i class="i-carbon-close"></i>
          </button>
        </div>
      </div>

      <!-- 语音输入状态 -->
      <div v-if="isRecording" class="voice-recording">
        <div class="recording-indicator">
          <div class="recording-dot"></div>
          <span>正在录音...</span>
        </div>
        <div class="recording-waveform">
          <!-- 简单的录音波形显示 -->
          <div
            v-for="i in 8"
            :key="i"
            class="wave-bar"
            :style="{ animationDelay: `${i * 0.1}s` }"
          ></div>
        </div>
        <button @click="stopVoiceInput" class="recording-stop">
          <i class="i-carbon-stop-filled"></i>
          停止录音
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import type { InputSuggestion } from '@/types/agent'

interface AttachedFile {
  id: string
  name: string
  size: number
  type: string
  file: File
}

interface QuickAction {
  id: string
  text: string
  description: string
  icon: string
  template: string
}

interface Props {
  modelValue?: string
  placeholder?: string
  disabled?: boolean
  loading?: boolean
  suggestions?: InputSuggestion[]
  enableVoiceInput?: boolean
  enableFileUpload?: boolean
  acceptedFileTypes?: string
  maxLength?: number
  showCharCount?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '输入您的问题或指令...',
  disabled: false,
  loading: false,
  suggestions: () => [],
  enableVoiceInput: true,
  enableFileUpload: true,
  acceptedFileTypes: '.txt,.pdf,.doc,.docx,.xls,.xlsx,.csv',
  maxLength: 10000,
  showCharCount: false
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  send: [content: string, files: AttachedFile[]]
  'voice-input': [transcript: string]
  'file-upload': [files: File[]]
}>()

// 响应式数据
const textareaRef = ref<HTMLTextAreaElement>()
const fileInputRef = ref<HTMLInputElement>()
const inputValue = ref(props.modelValue)
const showSuggestions = ref(false)
const selectedSuggestionIndex = ref(-1)
const attachedFiles = ref<AttachedFile[]>([])
const isRecording = ref(false)
const recognition = ref<SpeechRecognition | null>(null)

// 快捷操作
const quickActions: QuickAction[] = [
  {
    id: 'analyze',
    text: '分析',
    description: '分析数据或文档',
    icon: 'i-carbon-analytics',
    template: '请帮我分析'
  },
  {
    id: 'summarize',
    text: '总结',
    description: '总结内容要点',
    icon: 'i-carbon-document',
    template: '请帮我总结'
  },
  {
    id: 'translate',
    text: '翻译',
    description: '翻译文本',
    icon: 'i-carbon-translate',
    template: '请帮我翻译'
  },
  {
    id: 'optimize',
    text: '优化',
    description: '优化流程或方案',
    icon: 'i-carbon-rocket',
    template: '请帮我优化'
  }
]

// 计算属性
const canSend = computed(() => {
  return !props.disabled && !props.loading && (inputValue.value.trim() || attachedFiles.value.length > 0)
})

const filteredSuggestions = computed(() => {
  if (!inputValue.value.trim()) return props.suggestions.slice(0, 5)

  const query = inputValue.value.toLowerCase()
  return props.suggestions.filter(suggestion =>
    suggestion.text.toLowerCase().includes(query) ||
    suggestion.description?.toLowerCase().includes(query)
  ).slice(0, 5)
})

// 监听器
watch(() => props.modelValue, (newValue) => {
  inputValue.value = newValue
})

watch(inputValue, (newValue) => {
  emit('update:modelValue', newValue)
  autoResize()
})

// 生命周期
onMounted(() => {
  initSpeechRecognition()
  autoResize()
})

onUnmounted(() => {
  if (recognition.value) {
    recognition.value.stop()
  }
})

// 方法
function handleInput() {
  // 输入变化时的处理逻辑已在 watch 中处理
}

function handleKeydown(event: KeyboardEvent) {
  // Ctrl+Enter 发送消息
  if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
    event.preventDefault()
    sendMessage()
    return
  }

  // 建议选择快捷键
  if (showSuggestions.value && filteredSuggestions.value.length > 0) {
    if (event.key === 'ArrowUp') {
      event.preventDefault()
      selectedSuggestionIndex.value = Math.max(-1, selectedSuggestionIndex.value - 1)
    } else if (event.key === 'ArrowDown') {
      event.preventDefault()
      selectedSuggestionIndex.value = Math.min(
        filteredSuggestions.value.length - 1,
        selectedSuggestionIndex.value + 1
      )
    } else if (event.key === 'Tab' || event.key === 'Enter') {
      if (selectedSuggestionIndex.value >= 0) {
        event.preventDefault()
        applySuggestion(filteredSuggestions.value[selectedSuggestionIndex.value])
      }
    } else if (event.key === 'Escape') {
      hideSuggestions()
    }
  }

  // @ 符号触发建议
  if (event.key === '@' && !showSuggestions.value) {
    showSuggestions.value = true
  }
}

function handleFocus() {
  // 聚焦时的处理
}

function handleBlur() {
  // 延迟隐藏建议，给点击建议留时间
  setTimeout(() => {
    showSuggestions.value = false
  }, 200)
}

function sendMessage() {
  if (!canSend.value) return

  const content = inputValue.value.trim()
  const files = [...attachedFiles.value]

  if (content || files.length > 0) {
    emit('send', content, files)
    inputValue.value = ''
    attachedFiles.value = []
    autoResize()
  }
}

function autoResize() {
  nextTick(() => {
    if (textareaRef.value) {
      textareaRef.value.style.height = 'auto'
      const scrollHeight = textareaRef.value.scrollHeight
      const maxHeight = 120 // 最大高度
      textareaRef.value.style.height = Math.min(scrollHeight, maxHeight) + 'px'
    }
  })
}

function toggleSuggestions() {
  showSuggestions.value = !showSuggestions.value
  selectedSuggestionIndex.value = -1
}

function hideSuggestions() {
  showSuggestions.value = false
  selectedSuggestionIndex.value = -1
}

function applySuggestion(suggestion: InputSuggestion) {
  inputValue.value = suggestion.text
  hideSuggestions()
  textareaRef.value?.focus()
}

function insertQuickAction(action: QuickAction) {
  const cursorPos = textareaRef.value?.selectionStart || 0
  const beforeCursor = inputValue.value.substring(0, cursorPos)
  const afterCursor = inputValue.value.substring(cursorPos)

  inputValue.value = beforeCursor + action.template + afterCursor

  nextTick(() => {
    if (textareaRef.value) {
      const newPos = cursorPos + action.template.length
      textareaRef.value.setSelectionRange(newPos, newPos)
      textareaRef.value.focus()
    }
  })
}

function initSpeechRecognition() {
  if (!('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
    return
  }

  const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
  recognition.value = new SpeechRecognition()

  recognition.value.continuous = false
  recognition.value.interimResults = false
  recognition.value.lang = 'zh-CN'

  recognition.value.onresult = (event) => {
    const result = event.results[0][0].transcript
    emit('voice-input', result)
    inputValue.value = result
    isRecording.value = false
  }

  recognition.value.onerror = (event) => {
    console.error('Speech recognition error:', event.error)
    isRecording.value = false
  }

  recognition.value.onend = () => {
    isRecording.value = false
  }
}

function toggleVoiceInput() {
  if (isRecording.value) {
    stopVoiceInput()
  } else {
    startVoiceInput()
  }
}

function startVoiceInput() {
  if (!recognition.value) return

  try {
    recognition.value.start()
    isRecording.value = true
  } catch (error) {
    console.error('Failed to start speech recognition:', error)
  }
}

function stopVoiceInput() {
  if (!recognition.value) return

  recognition.value.stop()
  isRecording.value = false
}

function triggerFileUpload() {
  fileInputRef.value?.click()
}

function handleFileUpload(event: Event) {
  const target = event.target as HTMLInputElement
  const files = Array.from(target.files || [])

  const newFiles: AttachedFile[] = files.map(file => ({
    id: `file-${Date.now()}-${Math.random()}`,
    name: file.name,
    size: file.size,
    type: file.type,
    file
  }))

  attachedFiles.value.push(...newFiles)
  emit('file-upload', files)

  // 清空 input
  if (target) {
    target.value = ''
  }
}

function removeFile(fileId: string) {
  attachedFiles.value = attachedFiles.value.filter(file => file.id !== fileId)
}

function getFileIcon(type: string): string {
  if (type.includes('image')) return 'i-carbon-image'
  if (type.includes('pdf')) return 'i-carbon-document-pdf'
  if (type.includes('text')) return 'i-carbon-document-text'
  if (type.includes('spreadsheet') || type.includes('excel')) return 'i-carbon-table'
  if (type.includes('presentation') || type.includes('powerpoint')) return 'i-carbon-presentation'
  return 'i-carbon-document'
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}
</script>

<style scoped>
.smart-input {
  @apply relative;
}

.suggestions-popup {
  @apply absolute bottom-full left-0 right-0 mb-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 max-h-60 overflow-hidden;
}

.suggestions-header {
  @apply flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700;
}

.suggestions-header h6 {
  @apply text-sm font-medium text-gray-900 dark:text-white m-0;
}

.btn-close {
  @apply w-6 h-6 rounded-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 flex items-center justify-center text-gray-500 dark:text-gray-400 transition-colors;
}

.suggestions-list {
  @apply max-h-48 overflow-y-auto;
}

.suggestion-item {
  @apply w-full flex items-center gap-3 p-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-left;
}

.suggestion-item.active {
  @apply bg-blue-50 dark:bg-blue-900/20;
}

.suggestion-icon {
  @apply w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400 flex-shrink-0;
}

.suggestion-content {
  @apply flex-1 min-w-0;
}

.suggestion-text {
  @apply text-sm font-medium text-gray-900 dark:text-white;
}

.suggestion-description {
  @apply text-xs text-gray-600 dark:text-gray-400 mt-1;
}

.suggestion-category {
  @apply text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full;
}

.input-container {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden;

  /* 确保输入容器在新布局中正常工作 */
  margin: 1rem;
  margin-bottom: 1rem;
}

.quick-actions {
  @apply flex gap-2 p-2 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900;
}

.quick-action-btn {
  @apply flex items-center gap-1 px-2 py-1 text-xs bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors;
}

.input-wrapper {
  @apply flex items-end gap-2 p-3;

  /* 确保在小屏幕上有合适的内边距 */
  min-height: 60px;
}

.input-main {
  @apply flex-1 relative;
}

.input-textarea {
  @apply w-full min-h-[40px] max-h-[120px] resize-none border-0 outline-none bg-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400;
}

.input-textarea:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.char-count {
  @apply absolute bottom-0 right-0 text-xs text-gray-400 dark:text-gray-500;
}

.input-tools {
  @apply flex items-center gap-1;
}

.tool-btn {
  @apply w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 flex items-center justify-center text-gray-600 dark:text-gray-400 transition-colors;
}

.tool-btn.active {
  @apply bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400;
}

.send-btn {
  @apply w-10 h-10 rounded-full bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 dark:disabled:bg-gray-600 flex items-center justify-center text-white transition-colors;
}

.send-btn.disabled {
  @apply cursor-not-allowed opacity-50;
}

.attached-files {
  @apply border-t border-gray-200 dark:border-gray-700 p-3 space-y-2;
}

.attached-file {
  @apply flex items-center justify-between bg-gray-50 dark:bg-gray-700 rounded-lg p-2;
}

.file-info {
  @apply flex items-center gap-2;
}

.file-name {
  @apply text-sm font-medium text-gray-900 dark:text-white;
}

.file-size {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

.file-remove {
  @apply w-6 h-6 rounded-full bg-red-100 hover:bg-red-200 dark:bg-red-900 dark:hover:bg-red-800 flex items-center justify-center text-red-600 dark:text-red-400 transition-colors;
}

.voice-recording {
  @apply border-t border-gray-200 dark:border-gray-700 p-3 bg-blue-50 dark:bg-blue-900/20;
}

.recording-indicator {
  @apply flex items-center gap-2 mb-2;
}

.recording-dot {
  @apply w-3 h-3 bg-red-500 rounded-full animate-pulse;
}

.recording-waveform {
  @apply flex items-center gap-1 mb-3;
}

.wave-bar {
  @apply w-1 bg-blue-500 rounded-full animate-bounce;

  height: 4px;
  animation-duration: 0.6s;
  animation-iteration-count: infinite;
}

.recording-stop {
  @apply flex items-center gap-2 px-3 py-1 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors text-sm;
}

.hidden {
  @apply sr-only;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%,
  100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }

  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

/* 响应式优化 */
@media (width <= 768px) {
  .input-container {
    margin: 0.75rem;
  }

  .input-wrapper {
    padding: 0.75rem;
  }

  .suggestions-popup {
    margin-bottom: 0.5rem;
  }
}

@media (width <= 640px) {
  .input-container {
    margin: 0.5rem;
  }

  .input-wrapper {
    padding: 0.5rem;
  }

  .input-textarea {
    font-size: 16px; /* 防止iOS自动缩放 */
  }

  .attached-files {
    padding: 0.5rem;
  }
}

/* 适配iPhone和其他有安全区域的设备 */
@supports (padding: env(safe-area-inset-bottom)) {
  .input-container {
    margin-bottom: calc(1rem + env(safe-area-inset-bottom));
  }

  @media (width <= 640px) {
    .input-container {
      margin-bottom: calc(0.5rem + env(safe-area-inset-bottom));
    }
  }
}
</style>
