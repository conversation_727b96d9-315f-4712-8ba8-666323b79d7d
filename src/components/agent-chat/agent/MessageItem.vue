<template>
  <div
    class="message-item"
    :class="{
      'message-user': message.role === 'user',
      'message-agent': message.role === 'assistant' || message.type === 'agent',
      'message-system': message.type === 'system',
      'message-temporary': message.isTemporary
    }"
  >
    <!-- 用户消息 -->
    <div v-if="message.role === 'user'" class="user-message">
      <div class="message-content">
        <div class="message-bubble" :class="{ 'message-bubble-error': messageStatus === 'error', 'message-bubble-sending': messageStatus === 'sending' }">
          <div class="message-text">{{ message.content }}</div>
          <!-- 错误详情 -->
          <div v-if="messageStatus === 'error' && message.metadata?.error" class="error-details">
            <i class="i-carbon-warning-filled"></i>
            <span>{{ message.metadata.error }}</span>
          </div>
          <div class="message-meta">
            <span class="message-time">{{ formatTime(message.timestamp) }}</span>
            <!-- 消息状态指示器 -->
            <div class="message-status">
              <!-- 发送中状态 -->
              <div v-if="messageStatus === 'sending'" class="status-sending" title="发送中">
                <i class="i-carbon-in-progress animate-spin"></i>
                <span class="status-text">发送中</span>
              </div>
              <!-- 已发送状态 -->
              <div v-else-if="messageStatus === 'sent'" class="status-sent" title="已发送">
                <i class="i-carbon-checkmark"></i>
              </div>
              <!-- 错误状态 -->
              <div v-else-if="messageStatus === 'error'" class="status-error" title="发送失败">
                <i class="i-carbon-warning-filled"></i>
                <span class="status-text">发送失败</span>
                <button @click="retryMessage" class="retry-btn-mini" title="重试发送">
                  <i class="i-carbon-renew"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="message-avatar">
        <i class="i-carbon-user"></i>
      </div>
    </div>

    <!-- Agent/系统消息 -->
    <div v-else class="agent-message">
      <div class="message-avatar">
        <i :class="getAgentIcon()"></i>
        <div v-if="isAgentTyping || isThinkingMessage" class="typing-indicator">
          <div class="typing-dot"></div>
          <div class="typing-dot"></div>
          <div class="typing-dot"></div>
        </div>
      </div>
      <div class="message-content">
        <!-- 消息头部 -->
        <div class="message-header">
          <div class="agent-info">
            <span class="agent-name">{{ getAgentName() }}</span>
            <span v-if="message.metadata?.agentName" class="agent-role">{{ message.metadata.agentName }}</span>
          </div>
          <div class="message-actions">
            <button
              v-if="!isAgentTyping"
              @click="copyMessage"
              class="action-btn"
              title="复制消息"
            >
              <i class="i-carbon-copy"></i>
            </button>
            <button
              v-if="message.role === 'assistant' && !isAgentTyping"
              @click="regenerateResponse"
              class="action-btn"
              title="重新生成"
            >
              <i class="i-carbon-renew"></i>
            </button>
            <button
              @click="toggleExpanded"
              class="action-btn"
              :title="isExpanded ? '收起' : '展开'"
            >
              <i :class="isExpanded ? 'i-carbon-chevron-up' : 'i-carbon-chevron-down'"></i>
            </button>
          </div>
        </div>

        <!-- 消息内容 -->
        <div class="message-bubble" :class="{ 'thinking-bubble': isThinkingMessage }">
          <!-- 思考指示器 -->
          <div v-if="isThinkingMessage" class="thinking-indicator">
            <i class="i-carbon-cognitive animate-pulse"></i>
            <span class="thinking-text">{{ message.metadata?.agentName || 'Agent' }} 正在思考...</span>
          </div>

          <!-- 思维链展示 -->
          <div
            v-if="chainSteps && chainSteps.length > 0 && showChainOfThought"
            class="chain-of-thought"
          >
            <div class="chain-header">
              <h6>思维过程</h6>
              <button @click="toggleChainExpanded" class="chain-toggle">
                <i :class="isChainExpanded ? 'i-carbon-chevron-up' : 'i-carbon-chevron-down'"></i>
              </button>
            </div>
            <div v-if="isChainExpanded" class="chain-steps">
              <div
                v-for="(step, index) in chainSteps"
                :key="step.id || index"
                class="chain-step"
                :class="{ active: step.isActive }"
              >
                <div class="step-indicator">
                  <div class="step-number">{{ index + 1 }}</div>
                  <div class="step-status">
                    <i v-if="step.isActive" class="i-carbon-in-progress animate-spin"></i>
                    <i v-else-if="step.type === 'execution'" class="i-carbon-checkmark"></i>
                    <i v-else class="i-carbon-dot-mark"></i>
                  </div>
                </div>
                <div class="step-content">
                  <div class="step-text">{{ step.content }}</div>
                  <div v-if="step.duration" class="step-time">
                    {{ formatDuration(step.duration) }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 主要消息文本 -->
          <div class="message-text" :class="{ collapsed: !isExpanded && isLongMessage }">
            <div v-if="isMarkdown" v-html="formattedContent" class="markdown-content"></div>
            <div v-else class="plain-content">{{ message.content }}</div>
          </div>

          <!-- 展开/收起按钮 -->
          <div v-if="isLongMessage" class="expand-toggle">
            <button @click="toggleExpanded" class="expand-btn">
              {{ isExpanded ? '收起' : '展开全文' }}
            </button>
          </div>

          <!-- 附件展示 -->
          <div v-if="message.metadata?.attachments" class="message-attachments">
            <div
              v-for="attachment in message.metadata.attachments"
              :key="attachment.id"
              class="attachment"
            >
              <i :class="getAttachmentIcon(attachment.type)"></i>
              <span class="attachment-name">{{ attachment.name }}</span>
              <button @click="downloadAttachment(attachment)" class="download-btn">
                <i class="i-carbon-download"></i>
              </button>
            </div>
          </div>

          <!-- 消息元信息 -->
          <div class="message-meta">
            <span class="message-time">{{ formatTime(message.timestamp) }}</span>
            <span v-if="message.metadata?.tokensUsed" class="token-usage">
              <i class="i-carbon-chip"></i>
              {{ message.metadata.tokensUsed }} tokens
            </span>
            <span v-if="message.metadata?.duration" class="response-time">
              <i class="i-carbon-stopwatch"></i>
              {{ formatDuration(message.metadata.duration) }}
            </span>
          </div>
        </div>

        <!-- 反馈区域 -->
        <div v-if="message.role === 'assistant' && !isAgentTyping" class="message-feedback">
          <div class="feedback-buttons">
            <button
              @click="giveFeedback('helpful')"
              class="feedback-btn"
              :class="{ active: feedback === 'helpful' }"
              title="有帮助"
            >
              <i class="i-carbon-thumbs-up"></i>
            </button>
            <button
              @click="giveFeedback('not-helpful')"
              class="feedback-btn"
              :class="{ active: feedback === 'not-helpful' }"
              title="没有帮助"
            >
              <i class="i-carbon-thumbs-down"></i>
            </button>
          </div>
          <div v-if="showFeedbackInput" class="feedback-input">
            <textarea
              v-model="feedbackText"
              placeholder="请告诉我们如何改进..."
              class="feedback-textarea"
              rows="2"
            ></textarea>
            <div class="feedback-actions">
              <button @click="cancelFeedback" class="btn-cancel">取消</button>
              <button @click="submitFeedback" class="btn-submit">提交</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-if="message.metadata?.error" class="message-error">
      <div class="error-content">
        <i class="i-carbon-warning-filled"></i>
        <span>{{ message.metadata.error }}</span>
        <button @click="retryMessage" class="retry-btn">
          <i class="i-carbon-renew"></i>
          重试
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { Message } from '@/types/agent'

interface Props {
  message: Message
  showChainOfThought?: boolean
  enableMarkdown?: boolean
  isAgentTyping?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showChainOfThought: true,
  enableMarkdown: true,
  isAgentTyping: false
})

const emit = defineEmits<{
  copy: [content: string]
  regenerate: [messageId: string]
  feedback: [messageId: string, type: 'helpful' | 'not-helpful', text?: string]
  retry: [messageId: string]
}>()

// 响应式数据
const isExpanded = ref(false)
const isChainExpanded = ref(false)
const feedback = ref<'helpful' | 'not-helpful' | null>(null)
const showFeedbackInput = ref(false)
const feedbackText = ref('')

// 计算属性
const isLongMessage = computed(() => {
  return props.message.content.length > 500
})

const chainSteps = computed(() => {
  if (!props.message.chainOfThought) return []

  // 如果是数组，直接返回
  if (Array.isArray(props.message.chainOfThought)) {
    return props.message.chainOfThought
  }

  // 如果是对象并且有 steps 属性，返回 steps
  if (props.message.chainOfThought.steps) {
    return props.message.chainOfThought.steps
  }

  return []
})

const messageStatus = computed(() => {
  // 只有用户消息需要显示状态
  if (props.message.role !== 'user') return null

  // 检查 metadata 中的状态
  if (props.message.metadata?.status) {
    return props.message.metadata.status
  }

  // 兼容旧的 isTemporary 字段
  if (props.message.isTemporary) {
    return 'sending'
  }

  // 检查是否有错误
  if (props.message.metadata?.error) {
    return 'error'
  }

  // 默认为已发送状态
  return 'sent'
})

const isMarkdown = computed(() => {
  return props.enableMarkdown &&
    (props.message.content.includes('```') ||
     props.message.content.includes('**') ||
     props.message.content.includes('##'))
})

const isThinkingMessage = computed(() => {
  return props.message.metadata?.isThinking === true ||
         props.message.metadata?.status === 'thinking'
})

const formattedContent = computed(() => {
  if (!isMarkdown.value) return props.message.content

  // 简单的 Markdown 转换，实际项目中应该使用专门的 Markdown 库
  let content = props.message.content

  // 代码块
  content = content.replace(/```(\w+)?\n([\s\S]*?)```/g,
    '<pre class="code-block"><code class="language-$1">$2</code></pre>')

  // 行内代码
  content = content.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>')

  // 粗体
  content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')

  // 标题
  content = content.replace(/^### (.*$)/gm, '<h3>$1</h3>')
  content = content.replace(/^## (.*$)/gm, '<h2>$1</h2>')
  content = content.replace(/^# (.*$)/gm, '<h1>$1</h1>')

  // 换行
  content = content.replace(/\n/g, '<br>')

  return content
})

// 监听器
watch(() => props.message.content, () => {
  if (isLongMessage.value) {
    isExpanded.value = false
  }
})

// 方法
function getAgentIcon(): string {
  if (props.message.type === 'system') return 'i-carbon-settings'
  if (props.message.metadata?.agentName) {
    // 根据 Agent 类型返回不同图标
    const name = props.message.metadata.agentName.toLowerCase()
    if (name.includes('分析')) return 'i-carbon-analytics'
    if (name.includes('数据')) return 'i-carbon-data-table'
    if (name.includes('报告')) return 'i-carbon-document'
    if (name.includes('可视化')) return 'i-carbon-chart-line'
  }
  return 'i-carbon-machine-learning'
}

function getAgentName(): string {
  if (props.message.type === 'system') return '系统'
  if (props.message.metadata?.agentName) return props.message.metadata.agentName
  return 'AI Assistant'
}

function toggleExpanded() {
  isExpanded.value = !isExpanded.value
}

function toggleChainExpanded() {
  isChainExpanded.value = !isChainExpanded.value
}

function copyMessage() {
  navigator.clipboard.writeText(props.message.content)
  emit('copy', props.message.content)
}

function regenerateResponse() {
  emit('regenerate', props.message.id)
}

function retryMessage() {
  // 对于用户消息的重试，发出重试事件
  emit('retry', props.message.id)
}

function giveFeedback(type: 'helpful' | 'not-helpful') {
  feedback.value = type
  showFeedbackInput.value = type === 'not-helpful'

  if (type === 'helpful') {
    emit('feedback', props.message.id, type)
  }
}

function submitFeedback() {
  emit('feedback', props.message.id, feedback.value!, feedbackText.value)
  showFeedbackInput.value = false
  feedbackText.value = ''
}

function cancelFeedback() {
  feedback.value = null
  showFeedbackInput.value = false
  feedbackText.value = ''
}

function getAttachmentIcon(type: string): string {
  if (type.includes('image')) return 'i-carbon-image'
  if (type.includes('pdf')) return 'i-carbon-document-pdf'
  if (type.includes('text')) return 'i-carbon-document-text'
  if (type.includes('spreadsheet')) return 'i-carbon-table'
  return 'i-carbon-document'
}

function downloadAttachment(attachment: any) {
  // 下载附件的实现
  //console.log('Download attachment:', attachment)
}

function formatTime(timestamp: string): string {
  const time = new Date(timestamp)
  return time.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

function formatDuration(ms: number): string {
  if (ms < 1000) return `${ms}ms`
  const seconds = (ms / 1000).toFixed(1)
  return `${seconds}s`
}
</script>

<style scoped>
.message-item {
  @apply mb-6;
}

.message-temporary {
  @apply opacity-70;
}

.user-message {
  @apply flex justify-end gap-3;
}

.agent-message {
  @apply flex gap-3;
}

.message-avatar {
  @apply relative w-8 h-8 rounded-full flex items-center justify-center text-white flex-shrink-0;
}

.user-message .message-avatar {
  @apply bg-blue-500 order-last;
}

.agent-message .message-avatar {
  @apply bg-gray-500 dark:bg-gray-600;
}

.typing-indicator {
  @apply absolute -bottom-1 -right-1 bg-green-500 rounded-full p-1 flex gap-0.5;
}

.typing-dot {
  @apply w-1 h-1 bg-white rounded-full animate-bounce;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.1s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.2s;
}

.message-content {
  @apply flex-1 max-w-3xl;
}

.user-message .message-content {
  @apply max-w-2xl;
}

.message-header {
  @apply flex items-center justify-between mb-2;
}

.agent-info {
  @apply flex items-center gap-2;
}

.agent-name {
  @apply text-sm font-medium text-gray-900 dark:text-white;
}

.agent-role {
  @apply text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full;
}

.message-actions {
  @apply flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity;
}

.message-item:hover .message-actions {
  @apply opacity-100;
}

.action-btn {
  @apply w-6 h-6 rounded-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 flex items-center justify-center text-gray-600 dark:text-gray-400 transition-colors;
}

.message-bubble {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-sm;
}

.user-message .message-bubble {
  @apply bg-blue-500 text-white border-blue-500;
}

.message-bubble-sending {
  @apply opacity-80 animate-pulse;
}

.message-bubble-error {
  @apply border-red-400 bg-red-50 dark:bg-red-900/20;
}

.user-message .message-bubble-error {
  @apply bg-red-500 border-red-500;
}

.error-details {
  @apply flex items-center gap-2 mt-2 p-2 bg-red-100 dark:bg-red-800/30 border border-red-300 dark:border-red-700 rounded text-red-700 dark:text-red-300 text-sm;
}

.user-message .error-details {
  @apply bg-red-400/20 border-red-300 text-red-100;
}

.chain-of-thought {
  @apply mb-4 p-3 bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg;
}

.chain-header {
  @apply flex items-center justify-between mb-3;
}

.chain-header h6 {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300 m-0;
}

.chain-toggle {
  @apply w-6 h-6 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-600 dark:text-gray-400 transition-colors;
}

.chain-steps {
  @apply space-y-2;
}

.chain-step {
  @apply flex gap-3 p-2 rounded;
}

.chain-step.active {
  @apply bg-blue-50 dark:bg-blue-900/20;
}

.step-indicator {
  @apply flex items-center gap-2;
}

.step-number {
  @apply w-5 h-5 bg-gray-300 dark:bg-gray-600 text-white text-xs rounded-full flex items-center justify-center font-medium;
}

.chain-step.active .step-number {
  @apply bg-blue-500;
}

.step-status {
  @apply text-gray-400 dark:text-gray-500;
}

.chain-step.active .step-status {
  @apply text-blue-500;
}

.step-content {
  @apply flex-1;
}

.step-text {
  @apply text-sm text-gray-700 dark:text-gray-300;
}

.step-time {
  @apply text-xs text-gray-500 dark:text-gray-400 mt-1;
}

.message-text {
  @apply text-gray-900 dark:text-white leading-relaxed;
}

.user-message .message-text {
  @apply text-white;
}

.message-text.collapsed {
  @apply max-h-32 overflow-hidden relative;
}

.message-text.collapsed::after {
  @apply absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white dark:from-gray-800 to-transparent content-[""];
}

.user-message .message-text.collapsed::after {
  @apply from-blue-500;
}

.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3) {
  @apply font-semibold mb-2 mt-4;
}

.markdown-content :deep(h1) {
  @apply text-xl;
}

.markdown-content :deep(h2) {
  @apply text-lg;
}

.markdown-content :deep(h3) {
  @apply text-base;
}

.markdown-content :deep(.code-block) {
  @apply bg-gray-900 text-green-400 p-4 rounded-lg my-3 overflow-x-auto;
}

.markdown-content :deep(.inline-code) {
  @apply bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-1.5 py-0.5 rounded text-sm font-mono;
}

.expand-toggle {
  @apply mt-3 pt-3 border-t border-gray-200 dark:border-gray-700;
}

.user-message .expand-toggle {
  @apply border-blue-400;
}

.expand-btn {
  @apply text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors;
}

.user-message .expand-btn {
  @apply text-blue-200 hover:text-white;
}

.message-attachments {
  @apply mt-3 space-y-2;
}

.attachment {
  @apply flex items-center gap-2 p-2 bg-gray-50 dark:bg-gray-700 rounded-lg;
}

.attachment-name {
  @apply flex-1 text-sm text-gray-700 dark:text-gray-300 truncate;
}

.download-btn {
  @apply w-6 h-6 rounded-full bg-gray-200 hover:bg-gray-300 dark:bg-gray-600 dark:hover:bg-gray-500 flex items-center justify-center text-gray-600 dark:text-gray-400 transition-colors;
}

.message-meta {
  @apply flex items-center gap-3 mt-3 pt-3 border-t border-gray-200 dark:border-gray-700 text-xs text-gray-500 dark:text-gray-400;
}

.user-message .message-meta {
  @apply border-blue-400 text-blue-200;
}

.message-status {
  @apply flex items-center gap-2;
}

.status-sending {
  @apply flex items-center gap-1 text-blue-300;
}

.status-sent {
  @apply flex items-center text-green-400;
}

.status-error {
  @apply flex items-center gap-1 text-red-400;
}

.status-text {
  @apply text-xs;
}

.user-message .status-text {
  @apply text-blue-200;
}

.user-message .status-error .status-text {
  @apply text-red-200;
}

.retry-btn-mini {
  @apply w-4 h-4 rounded-full bg-red-500/20 hover:bg-red-500/40 flex items-center justify-center text-red-300 hover:text-red-200 transition-all text-xs;
}

.user-message .retry-btn-mini {
  @apply bg-red-300/20 hover:bg-red-300/40 text-red-200 hover:text-white;
}

.token-usage,
.response-time {
  @apply flex items-center gap-1;
}

.status-icon {
  @apply text-blue-500 dark:text-blue-400;
}

.message-feedback {
  @apply mt-3 pt-3 border-t border-gray-200 dark:border-gray-700;
}

.feedback-buttons {
  @apply flex gap-2;
}

.feedback-btn {
  @apply w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 flex items-center justify-center text-gray-600 dark:text-gray-400 transition-colors;
}

.feedback-btn.active {
  @apply bg-blue-500 text-white;
}

.feedback-input {
  @apply mt-3 space-y-3;
}

.feedback-textarea {
  @apply w-full p-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.feedback-actions {
  @apply flex gap-2 justify-end;
}

.btn-cancel {
  @apply px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors;
}

.btn-submit {
  @apply px-4 py-1.5 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-lg transition-colors;
}

.message-error {
  @apply mt-2;
}

.error-content {
  @apply flex items-center gap-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg text-red-700 dark:text-red-400;
}

.retry-btn {
  @apply flex items-center gap-1 px-3 py-1 bg-red-500 hover:bg-red-600 text-white text-sm rounded transition-colors;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }

  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

/* 思考消息样式 */
.thinking-bubble {
  @apply bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700;
}

.thinking-indicator {
  @apply flex items-center gap-3 p-3 bg-blue-100 dark:bg-blue-800/30 border border-blue-200 dark:border-blue-600 rounded-lg mb-3;
}

.thinking-text {
  @apply text-blue-700 dark:text-blue-300 font-medium;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}
</style>
