<template>
  <div class="conversation-list">
    <!-- 头部 -->
    <div class="list-header">
      <h3 class="header-title">对话历史</h3>
      <div class="header-actions">
        <button
          @click="refreshConversations"
          class="action-btn"
          :disabled="loading"
          title="刷新列表"
        >
          <i :class="loading ? 'i-carbon-in-progress animate-spin' : 'i-carbon-renew'"></i>
        </button>
        <button
          @click="toggleSortOrder"
          class="action-btn"
          :title="sortOrder === 'desc' ? '按时间升序' : '按时间降序'"
        >
          <i :class="sortOrder === 'desc' ? 'i-carbon-sort-descending' : 'i-carbon-sort-ascending'"></i>
        </button>
      </div>
    </div>

    <!-- 搜索框 -->
    <div class="search-container">
      <div class="search-input">
        <i class="i-carbon-search search-icon"></i>
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索对话..."
          class="search-field"
        >
        <button
          v-if="searchQuery"
          @click="clearSearch"
          class="clear-search"
        >
          <i class="i-carbon-close"></i>
        </button>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filter-tabs">
      <button
        v-for="filter in filters"
        :key="filter.key"
        @click="setActiveFilter(filter.key)"
        class="filter-tab"
        :class="{ active: activeFilter === filter.key }"
      >
        <i :class="filter.icon"></i>
        <span>{{ filter.label }}</span>
        <span v-if="filter.count > 0" class="filter-count">{{ filter.count }}</span>
      </button>
    </div>

    <!-- 对话列表 -->
    <div class="conversations-container" :class="{ 'has-loading': !!loadingConversationId }">
      <!-- 全局加载遮罩 -->
      <div v-if="loadingConversationId" class="global-loading-overlay">
        <div class="global-loading-content">
          <i class="i-carbon-in-progress animate-spin"></i>
          <p>正在加载对话...</p>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading && filteredConversations.length === 0" class="loading-state">
        <div class="loading-spinner">
          <i class="i-carbon-in-progress animate-spin"></i>
        </div>
        <p>加载对话列表...</p>
      </div>

      <!-- 空状态 -->
      <div v-else-if="filteredConversations.length === 0" class="empty-state">
        <div class="empty-content">
          <i class="i-carbon-chat"></i>
          <h4>{{ getEmptyStateTitle() }}</h4>
          <p>{{ getEmptyStateMessage() }}</p>
          <button v-if="searchQuery || activeFilter !== 'all'" @click="resetFilters" class="reset-btn">
            清除筛选条件
          </button>
        </div>
      </div>

      <!-- 对话项列表 -->
      <div v-else class="conversation-items" :class="{ 'has-loading': !!loadingConversationId }">
        <div
          v-for="conversation in filteredConversations"
          :key="conversation.id"
          class="conversation-item"
          :class="{
            active: currentConversation?.id === conversation.id,
            archived: conversation.status === 'archived',
            loading: loadingConversationId === conversation.id,
            disabled: !!loadingConversationId && loadingConversationId !== conversation.id
          }"
          @click="selectConversation(conversation)"
        >
          <!-- 对话头像/图标 -->
          <div class="conversation-avatar">
            <i v-if="loadingConversationId === conversation.id"
               class="i-carbon-in-progress animate-spin"></i>
            <i v-else class="i-carbon-machine-learning"></i>
            <div
              v-if="conversation.status === 'active' && loadingConversationId !== conversation.id"
              class="status-indicator active"
            ></div>
            <div
              v-if="loadingConversationId === conversation.id"
              class="status-indicator loading"
            ></div>
          </div>

          <!-- 对话信息 -->
          <div class="conversation-info">
            <div class="conversation-header">
              <h4 class="conversation-title">{{ conversation.title }}</h4>
              <div class="conversation-meta">
                <span class="message-count">
                  <i class="i-carbon-chat"></i>
                  {{ conversation.messageCount }}
                </span>
                <span class="conversation-time">{{ formatTime(conversation.lastMessageAt || conversation.createdAt) }}</span>
              </div>
            </div>

            <div class="conversation-summary">
              <p class="summary-text">{{ getConversationSummary(conversation) }}</p>
            </div>

            <div class="conversation-tags">
              <span class="tag tag-type">
                <i class="i-carbon-ai-launch"></i>
                AI Agent
              </span>
              <span
                v-if="conversation.status === 'archived'"
                class="tag tag-archived"
              >
                <i class="i-carbon-archive"></i>
                已归档
              </span>
            </div>
          </div>

          <!-- 对话操作 -->
          <div class="conversation-actions">
            <button
              @click.stop="toggleFavorite(conversation)"
              class="action-btn"
              :class="{ favorited: conversation.metadata?.favorited }"
              title="收藏"
            >
              <i :class="conversation.metadata?.favorited ? 'i-carbon-star-filled' : 'i-carbon-star'"></i>
            </button>
            <button
              @click.stop="showContextMenu(conversation, $event)"
              class="action-btn"
              title="更多操作"
            >
              <i class="i-carbon-overflow-menu-vertical"></i>
            </button>
          </div>

          <!-- 进度指示器 -->
          <div
            v-if="conversation.metadata?.hasActiveExecution"
            class="execution-indicator"
          >
            <div class="execution-progress">
              <div
                class="progress-fill"
                :style="{ width: `${conversation.metadata.executionProgress || 0}%` }"
              ></div>
            </div>
            <span class="execution-text">执行中...</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 右键菜单 -->
    <div
      v-if="contextMenu.visible"
      ref="contextMenuRef"
      class="context-menu"
      :style="{ top: contextMenu.y + 'px', left: contextMenu.x + 'px' }"
    >
      <button
        v-for="action in contextMenuActions"
        :key="action.key"
        @click="handleContextAction(action.key)"
        class="context-menu-item"
        :class="{ danger: action.danger }"
      >
        <i :class="action.icon"></i>
        <span>{{ action.label }}</span>
      </button>
    </div>

    <!-- 删除确认对话框 -->
    <div v-if="deleteDialog.visible" class="dialog-overlay">
      <div class="dialog">
        <div class="dialog-header">
          <h4>确认删除</h4>
          <button @click="closeDeleteDialog" class="dialog-close">
            <i class="i-carbon-close"></i>
          </button>
        </div>
        <div class="dialog-content">
          <p>确定要删除对话 "{{ deleteDialog.conversation?.title }}" 吗？</p>
          <p class="warning-text">此操作不可撤销，所有消息和历史记录将被永久删除。</p>
        </div>
        <div class="dialog-actions">
          <button @click="closeDeleteDialog" class="btn-secondary">
            取消
          </button>
          <button @click="confirmDelete" class="btn-danger" :disabled="deleting">
            <i v-if="deleting" class="i-carbon-in-progress animate-spin"></i>
            {{ deleting ? '删除中...' : '确认删除' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import type { Conversation } from '@/types/agent'

interface Filter {
  key: string
  label: string
  icon: string
  count: number
}

interface Props {
  conversations: Conversation[]
  currentConversation?: Conversation | null
  loading?: boolean
  loadingConversationId?: string | null
}

const props = withDefaults(defineProps<Props>(), {
  currentConversation: null,
  loading: false,
  loadingConversationId: null
})

const emit = defineEmits<{
  select: [conversation: Conversation]
  delete: [conversationId: string]
  archive: [conversationId: string]
  unarchive: [conversationId: string]
  refresh: []
  rename: [conversationId: string, newTitle: string]
}>()

// 响应式数据
const searchQuery = ref('')
const activeFilter = ref('all')
const sortOrder = ref<'asc' | 'desc'>('desc')

const contextMenu = ref({
  visible: false,
  x: 0,
  y: 0,
  conversation: null as Conversation | null
})

const deleteDialog = ref({
  visible: false,
  conversation: null as Conversation | null
})

const deleting = ref(false)
const contextMenuRef = ref<HTMLElement>()

// 计算属性
const filters = computed((): Filter[] => {
  const active = props.conversations.filter(c => c.status === 'active').length
  const archived = props.conversations.filter(c => c.status === 'archived').length
  const favorited = props.conversations.filter(c => c.metadata?.favorited).length

  return [
    { key: 'all', label: '全部', icon: 'i-carbon-chat', count: props.conversations.length },
    { key: 'active', label: '活跃', icon: 'i-carbon-circle-filled', count: active },
    { key: 'archived', label: '已归档', icon: 'i-carbon-archive', count: archived },
    { key: 'favorited', label: '收藏', icon: 'i-carbon-star-filled', count: favorited }
  ]
})

const filteredConversations = computed(() => {
  //console.log('ConversationList props.conversations:', props.conversations)
  let result = props.conversations

  // 状态筛选
  if (activeFilter.value !== 'all') {
    switch (activeFilter.value) {
      case 'active':
        result = result.filter(c => c.status === 'active')
        break
      case 'archived':
        result = result.filter(c => c.status === 'archived')
        break
      case 'favorited':
        result = result.filter(c => c.metadata?.favorited)
        break
    }
  }

  // 搜索筛选
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim()
    result = result.filter(c =>
      c.title.toLowerCase().includes(query) ||
      (c.metadata?.summary?.toLowerCase().includes(query))
    )
  }

  // 排序
  result.sort((a, b) => {
    const timeA = new Date(a.lastMessageAt || a.createdAt).getTime()
    const timeB = new Date(b.lastMessageAt || b.createdAt).getTime()
    return sortOrder.value === 'desc' ? timeB - timeA : timeA - timeB
  })

  return result
})

const contextMenuActions = computed(() => [
  { key: 'rename', label: '重命名', icon: 'i-carbon-edit' },
  { key: 'favorite', label: '收藏', icon: 'i-carbon-star' },
  { key: 'archive', label: '归档', icon: 'i-carbon-archive' },
  { key: 'export', label: '导出', icon: 'i-carbon-download' },
  { key: 'delete', label: '删除', icon: 'i-carbon-trash-can', danger: true }
])

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// 方法
function selectConversation(conversation: Conversation) {
  // 如果已经选中当前对话或任何对话正在加载中，则忽略点击
  if (props.currentConversation?.id === conversation.id ||
      props.loadingConversationId) {
    return
  }

  emit('select', conversation)
}

function refreshConversations() {
  emit('refresh')
}

function toggleSortOrder() {
  sortOrder.value = sortOrder.value === 'desc' ? 'asc' : 'desc'
}

function clearSearch() {
  searchQuery.value = ''
}

function setActiveFilter(filterKey: string) {
  activeFilter.value = filterKey
}

function resetFilters() {
  searchQuery.value = ''
  activeFilter.value = 'all'
}

function toggleFavorite(conversation: Conversation) {
  // 这里可以发送事件给父组件处理
  conversation.metadata = conversation.metadata || {}
  conversation.metadata.favorited = !conversation.metadata.favorited
}

function showContextMenu(conversation: Conversation, event: MouseEvent) {
  event.preventDefault()
  contextMenu.value = {
    visible: true,
    x: event.clientX,
    y: event.clientY,
    conversation
  }
}

function hideContextMenu() {
  contextMenu.value.visible = false
  contextMenu.value.conversation = null
}

function handleClickOutside(event: Event) {
  if (contextMenu.value.visible &&
      contextMenuRef.value &&
      !contextMenuRef.value.contains(event.target as Node)) {
    hideContextMenu()
  }
}

function handleContextAction(action: string) {
  const conversation = contextMenu.value.conversation
  if (!conversation) return

  switch (action) {
    case 'rename':
      handleRename(conversation)
      break
    case 'favorite':
      toggleFavorite(conversation)
      break
    case 'archive':
      handleArchive(conversation)
      break
    case 'export':
      handleExport(conversation)
      break
    case 'delete':
      showDeleteDialog(conversation)
      break
  }

  hideContextMenu()
}

function handleRename(conversation: Conversation) {
  const newTitle = prompt('请输入新的对话标题:', conversation.title)
  if (newTitle && newTitle !== conversation.title) {
    emit('rename', conversation.id, newTitle)
  }
}

function handleArchive(conversation: Conversation) {
  if (conversation.status === 'archived') {
    emit('unarchive', conversation.id)
  } else {
    emit('archive', conversation.id)
  }
}

function handleExport(conversation: Conversation) {
  // 导出功能实现
  //console.log('Export conversation:', conversation.id)
}

function showDeleteDialog(conversation: Conversation) {
  deleteDialog.value = {
    visible: true,
    conversation
  }
}

function closeDeleteDialog() {
  deleteDialog.value.visible = false
  deleteDialog.value.conversation = null
}

async function confirmDelete() {
  const conversation = deleteDialog.value.conversation
  if (!conversation) return

  try {
    deleting.value = true
    emit('delete', conversation.id)
    closeDeleteDialog()
  } catch (error) {
    console.error('Delete conversation failed:', error)
  } finally {
    deleting.value = false
  }
}

function formatTime(timeString: string): string {
  const time = new Date(timeString)
  const now = new Date()
  const diff = now.getTime() - time.getTime()

  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`

  return time.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric'
  })
}

function getConversationSummary(conversation: Conversation): string {
  if (conversation.metadata?.summary) {
    return conversation.metadata.summary
  }
  if (conversation.messageCount === 0) {
    return '新建对话'
  }
  return `包含 ${conversation.messageCount} 条消息`
}

function getEmptyStateTitle(): string {
  if (searchQuery.value) return '无搜索结果'
  if (activeFilter.value === 'favorited') return '暂无收藏的对话'
  if (activeFilter.value === 'archived') return '暂无已归档的对话'
  return '暂无对话记录'
}

function getEmptyStateMessage(): string {
  if (searchQuery.value) return `未找到包含 "${searchQuery.value}" 的对话`
  if (activeFilter.value === 'favorited') return '收藏的对话会显示在这里'
  if (activeFilter.value === 'archived') return '归档的对话会显示在这里'
  return '开始您的第一个 AI Agent 对话吧'
}
</script>

<style scoped>
.conversation-list {
  @apply h-full flex flex-col bg-white dark:bg-gray-800;
}

.list-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700;
}

.header-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white m-0;
}

.header-actions {
  @apply flex gap-2;
}

.action-btn {
  @apply w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 flex items-center justify-center text-gray-600 dark:text-gray-400 transition-colors disabled:opacity-50;
}

.action-btn.favorited {
  @apply text-yellow-500 dark:text-yellow-400;
}

.search-container {
  @apply p-3 border-b border-gray-200 dark:border-gray-700;
}

.search-input {
  @apply relative;
}

.search-icon {
  @apply absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500;
}

.search-field {
  @apply w-full pl-10 pr-10 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg text-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.clear-search {
  @apply absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors;
}

.filter-tabs {
  @apply flex border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900;
}

.filter-tab {
  @apply flex-1 flex items-center justify-center gap-1 px-3 py-2 text-xs font-medium text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors relative;
}

.filter-tab.active {
  @apply text-blue-600 dark:text-blue-400 bg-white dark:bg-gray-800;
}

.filter-tab.active::after {
  @apply absolute bottom-0 left-0 right-0 h-0.5 bg-blue-600 dark:bg-blue-400 content-[""];
}

.filter-count {
  @apply bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-1.5 py-0.5 rounded-full text-xs;
}

.filter-tab.active .filter-count {
  @apply bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300;
}

.conversations-container {
  @apply flex-1 overflow-y-auto relative;
}

.conversations-container.has-loading {
  @apply pointer-events-none select-none;
}

.global-loading-overlay {
  @apply absolute inset-0 bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm z-50 flex items-center justify-center;
}

.global-loading-content {
  @apply flex flex-col items-center gap-3 p-6 bg-white dark:bg-gray-700 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600;
}

.global-loading-content i {
  @apply text-2xl text-blue-500 dark:text-blue-400;
}

.global-loading-content p {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300 m-0;
}

.loading-state,
.empty-state {
  @apply flex flex-col items-center justify-center h-64 text-center;
}

.loading-spinner {
  @apply text-2xl text-gray-400 dark:text-gray-500 mb-3;
}

.empty-content {
  @apply space-y-3;
}

.empty-content i {
  @apply text-4xl text-gray-400 dark:text-gray-500;
}

.empty-content h4 {
  @apply text-lg font-medium text-gray-900 dark:text-white m-0;
}

.empty-content p {
  @apply text-sm text-gray-600 dark:text-gray-400 m-0;
}

.reset-btn {
  @apply px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm transition-colors;
}

.conversation-items {
  @apply divide-y divide-gray-200 dark:divide-gray-700;
}

.conversation-item {
  @apply flex items-start gap-3 p-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors relative;
}

.conversation-item.active {
  @apply bg-blue-50 dark:bg-blue-900/20 border-r-2 border-blue-500;
}

.conversation-item.archived {
  @apply opacity-60;
}

.conversation-item.loading {
  @apply pointer-events-none relative;
}

.conversation-item.loading::before {
  @apply absolute inset-0 bg-blue-50 dark:bg-blue-900/10 content-[""] z-10 border-2 border-blue-300 dark:border-blue-600 rounded-lg;
}

.conversation-item.disabled {
  @apply opacity-40 pointer-events-none cursor-not-allowed;
}

.conversation-avatar {
  @apply relative w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center text-blue-600 dark:text-blue-400 flex-shrink-0;
}

.status-indicator {
  @apply absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-gray-800;
}

.status-indicator.active {
  @apply bg-green-500;
}

.status-indicator.loading {
  @apply bg-blue-500 animate-pulse;
}

.conversation-info {
  @apply flex-1 min-w-0 space-y-2;
}

.conversation-header {
  @apply flex items-start justify-between gap-2;
}

.conversation-title {
  @apply text-sm font-medium text-gray-900 dark:text-white m-0 truncate flex-1;
}

.conversation-meta {
  @apply flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400 flex-shrink-0;
}

.message-count {
  @apply flex items-center gap-1;
}

.conversation-summary {
  @apply text-xs text-gray-600 dark:text-gray-400;
}

.summary-text {
  @apply m-0 line-clamp-2;
}

.conversation-tags {
  @apply flex gap-1;
}

.tag {
  @apply inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full;
}

.tag-type {
  @apply bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300;
}

.tag-archived {
  @apply bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300;
}

.conversation-actions {
  @apply flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity;
}

.conversation-item:hover .conversation-actions {
  @apply opacity-100;
}

.execution-indicator {
  @apply absolute bottom-0 left-0 right-0 bg-blue-50 dark:bg-blue-900/20 px-4 py-2 border-t border-blue-200 dark:border-blue-800;
}

.execution-progress {
  @apply w-full h-1 bg-blue-200 dark:bg-blue-800 rounded-full overflow-hidden mb-1;
}

.execution-text {
  @apply text-xs text-blue-600 dark:text-blue-400 font-medium;
}

.context-menu {
  @apply fixed bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg py-1 z-50 min-w-[150px];
}

.context-menu-item {
  @apply w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left;
}

.context-menu-item.danger {
  @apply text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20;
}

.dialog-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.dialog {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4;
}

.dialog-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700;
}

.dialog-header h4 {
  @apply text-lg font-semibold text-gray-900 dark:text-white m-0;
}

.dialog-close {
  @apply w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 flex items-center justify-center text-gray-500 dark:text-gray-400 transition-colors;
}

.dialog-content {
  @apply p-4 space-y-3;
}

.dialog-content p {
  @apply text-sm text-gray-700 dark:text-gray-300 m-0;
}

.warning-text {
  @apply text-red-600 dark:text-red-400 font-medium;
}

.dialog-actions {
  @apply flex gap-3 p-4 border-t border-gray-200 dark:border-gray-700;
}

.btn-secondary {
  @apply flex-1 px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors;
}

.btn-danger {
  @apply flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-red-500 hover:bg-red-600 disabled:bg-red-300 text-white rounded-lg transition-colors;
}

.line-clamp-2 {
  display: -webkit-box;
  overflow: hidden;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
