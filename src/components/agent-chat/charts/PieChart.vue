<template>
  <div class="pie-chart-container" ref="chartContainer">
    <div v-if="loading" class="chart-loading">
      <i class="i-carbon-in-progress animate-spin"></i>
      <span>加载图表中...</span>
    </div>

    <div v-else-if="error" class="chart-error">
      <i class="i-carbon-warning-filled"></i>
      <span>{{ error }}</span>
      <button @click="$emit('retry')" class="retry-btn">重试</button>
    </div>

    <div v-else-if="!hasData" class="chart-empty">
      <i class="i-carbon-chart-pie"></i>
      <span>暂无数据</span>
    </div>

    <div v-else class="chart-content">
      <canvas
        ref="chartRef"
        class="chart-canvas"
        :width="canvasSize"
        :height="canvasSize"
        @mousemove="handleMouseMove"
        @mouseleave="handleMouseLeave"
        @click="handleClick"
      ></canvas>

      <!-- 图例 -->
      <div v-if="showLegend" class="chart-legend" :class="legendPosition">
        <div
          v-for="(item, index) in legendItems"
          :key="index"
          class="legend-item"
          :class="{ active: hoveredIndex === index, hidden: hiddenSeries.includes(index) }"
          @click="toggleSeries(index)"
          @mouseenter="handleLegendHover(index)"
          @mouseleave="handleLegendLeave"
        >
          <span
            class="legend-color"
            :style="{ backgroundColor: item.color }"
          ></span>
          <span class="legend-label">{{ item.label }}</span>
          <span class="legend-value">{{ formatValue(item.value) }}</span>
          <span class="legend-percentage">({{ item.percentage }}%)</span>
        </div>
      </div>

      <!-- 中心文本 -->
      <div v-if="showCenterText" class="center-text">
        <div class="center-value">{{ centerValue }}</div>
        <div class="center-label">{{ centerLabel }}</div>
      </div>

      <!-- 悬浮提示 -->
      <div
        v-if="tooltip.visible"
        class="chart-tooltip"
        :style="{ left: tooltip.x + 'px', top: tooltip.y + 'px' }"
      >
        <div class="tooltip-title">{{ tooltip.label }}</div>
        <div class="tooltip-content">
          <span class="tooltip-value">{{ formatValue(tooltip.value) }}</span>
          <span class="tooltip-percentage">({{ tooltip.percentage }}%)</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import type { ChartData } from '@/api/agentManagement'

interface Props {
  data: ChartData
  options?: any
  size?: number
  loading?: boolean
  error?: string
  showLegend?: boolean
  legendPosition?: 'right' | 'bottom' | 'left' | 'top'
  showCenterText?: boolean
  centerValue?: string | number
  centerLabel?: string
  responsive?: boolean
  donut?: boolean
  donutWidth?: number
}

const props = withDefaults(defineProps<Props>(), {
  size: 300,
  showLegend: true,
  legendPosition: 'right',
  responsive: true,
  donut: false,
  donutWidth: 60
})

const emit = defineEmits<{
  retry: []
  segmentClick: [segment: any, index: number]
  segmentHover: [segment: any, index: number]
}>()

// 响应式数据
const chartContainer = ref<HTMLElement>()
const chartRef = ref<HTMLCanvasElement>()
const hiddenSeries = ref<number[]>([])
const hoveredIndex = ref(-1)
const isDestroyed = ref(false)

const tooltip = ref({
  visible: false,
  x: 0,
  y: 0,
  label: '',
  value: 0,
  percentage: ''
})

// 图表尺寸
const canvasSize = ref(props.size)

// 计算属性
const hasData = computed(() => {
  return props.data &&
         props.data.datasets &&
         props.data.datasets.length > 0 &&
         props.data.datasets[0].data &&
         props.data.datasets[0].data.length > 0
})

const chartData = computed(() => {
  if (!hasData.value) return { values: [], labels: [], colors: [] }

  const dataset = props.data.datasets[0]
  const values = dataset.data.filter((_, index) => !hiddenSeries.value.includes(index))
  const labels = props.data.labels?.filter((_, index) => !hiddenSeries.value.includes(index)) || []
  const colors = dataset.backgroundColor?.filter((_, index) => !hiddenSeries.value.includes(index)) ||
                generateColors(values.length)

  return { values, labels, colors }
})

const total = computed(() => {
  return chartData.value.values.reduce((sum, value) => sum + value, 0)
})

const legendItems = computed(() => {
  if (!hasData.value) return []

  const dataset = props.data.datasets[0]
  return props.data.labels?.map((label, index) => ({
    label,
    value: dataset.data[index],
    color: dataset.backgroundColor?.[index] || generateColors(1)[0],
    percentage: total.value > 0 ? ((dataset.data[index] / total.value) * 100).toFixed(1) : '0'
  })) || []
})

// 生命周期
onMounted(() => {
  if (props.responsive) {
    updateDimensions()
    window.addEventListener('resize', updateDimensions)
  }

  nextTick(() => {
    if (hasData.value) {
      drawChart()
    }
  })
})

onUnmounted(() => {
  isDestroyed.value = true
  if (props.responsive) {
    window.removeEventListener('resize', updateDimensions)
  }
})

// 监听器
watch(() => props.data, () => {
  if (!isDestroyed.value && hasData.value) {
    nextTick(() => drawChart())
  }
}, { deep: true })

watch(hiddenSeries, () => {
  if (!isDestroyed.value && hasData.value) {
    nextTick(() => drawChart())
  }
}, { deep: true })

// 方法
function updateDimensions() {
  if (!chartContainer.value) return

  const rect = chartContainer.value.getBoundingClientRect()
  canvasSize.value = Math.min(rect.width, props.size)

  if (!isDestroyed.value && hasData.value) {
    nextTick(() => drawChart())
  }
}

function generateColors(count: number): string[] {
  const colors = []
  for (let i = 0; i < count; i++) {
    const hue = (i * 137.5) % 360
    colors.push(`hsl(${hue}, 70%, 50%)`)
  }
  return colors
}

function drawChart() {
  if (!chartRef.value || !hasData.value) return

  const canvas = chartRef.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // 设置高DPI支持
  const dpr = window.devicePixelRatio || 1
  const size = canvasSize.value

  canvas.width = size * dpr
  canvas.height = size * dpr

  ctx.scale(dpr, dpr)

  // 清空画布
  ctx.clearRect(0, 0, size, size)

  // 设置画布样式
  canvas.style.width = size + 'px'
  canvas.style.height = size + 'px'

  // 绘制饼图
  drawPieChart(ctx, size)
}

function drawPieChart(ctx: CanvasRenderingContext2D, size: number) {
  const { values, labels, colors } = chartData.value

  if (values.length === 0 || total.value === 0) return

  const centerX = size / 2
  const centerY = size / 2
  const radius = Math.min(centerX, centerY) - 20
  const innerRadius = props.donut ? radius - props.donutWidth : 0

  let currentAngle = -Math.PI / 2 // 从顶部开始

  values.forEach((value, index) => {
    const sliceAngle = (value / total.value) * 2 * Math.PI
    const endAngle = currentAngle + sliceAngle

    // 绘制扇形
    ctx.beginPath()
    ctx.arc(centerX, centerY, radius, currentAngle, endAngle)

    if (props.donut) {
      ctx.arc(centerX, centerY, innerRadius, endAngle, currentAngle, true)
    } else {
      ctx.lineTo(centerX, centerY)
    }

    ctx.closePath()

    // 设置颜色
    ctx.fillStyle = colors[index]

    // 高亮悬浮的扇形
    if (hoveredIndex.value === index) {
      ctx.shadowColor = 'rgba(0, 0, 0, 0.3)'
      ctx.shadowBlur = 10
      ctx.shadowOffsetX = 2
      ctx.shadowOffsetY = 2
    }

    ctx.fill()

    // 重置阴影
    ctx.shadowColor = 'transparent'
    ctx.shadowBlur = 0
    ctx.shadowOffsetX = 0
    ctx.shadowOffsetY = 0

    // 绘制边框
    ctx.strokeStyle = '#fff'
    ctx.lineWidth = 2
    ctx.stroke()

    // 绘制标签（如果饼图足够大）
    if (radius > 80 && sliceAngle > 0.2) {
      const labelAngle = currentAngle + sliceAngle / 2
      const labelRadius = (radius + innerRadius) / 2
      const labelX = centerX + Math.cos(labelAngle) * labelRadius
      const labelY = centerY + Math.sin(labelAngle) * labelRadius

      ctx.fillStyle = '#fff'
      ctx.font = 'bold 12px sans-serif'
      ctx.textAlign = 'center'
      ctx.textBaseline = 'middle'

      const percentage = ((value / total.value) * 100).toFixed(1)
      ctx.fillText(`${percentage}%`, labelX, labelY)
    }

    currentAngle = endAngle
  })
}

function getSegmentFromPoint(x: number, y: number): number {
  if (!chartRef.value || !hasData.value) return -1

  const rect = chartRef.value.getBoundingClientRect()
  const centerX = rect.width / 2
  const centerY = rect.height / 2
  const radius = Math.min(centerX, centerY) - 20
  const innerRadius = props.donut ? radius - props.donutWidth : 0

  const dx = x - rect.left - centerX
  const dy = y - rect.top - centerY
  const distance = Math.sqrt(dx * dx + dy * dy)

  // 检查是否在饼图范围内
  if (distance < innerRadius || distance > radius) return -1

  // 计算角度
  let angle = Math.atan2(dy, dx) + Math.PI / 2
  if (angle < 0) angle += 2 * Math.PI

  // 找到对应的扇形
  const { values } = chartData.value
  let currentAngle = 0

  for (let i = 0; i < values.length; i++) {
    const sliceAngle = (values[i] / total.value) * 2 * Math.PI

    if (angle >= currentAngle && angle < currentAngle + sliceAngle) {
      return i
    }

    currentAngle += sliceAngle
  }

  return -1
}

function handleMouseMove(event: MouseEvent) {
  const segmentIndex = getSegmentFromPoint(event.clientX, event.clientY)

  if (segmentIndex !== -1 && segmentIndex !== hoveredIndex.value) {
    hoveredIndex.value = segmentIndex

    // 显示提示框
    const { labels, values } = chartData.value
    tooltip.value = {
      visible: true,
      x: event.clientX - chartRef.value!.getBoundingClientRect().left + 10,
      y: event.clientY - chartRef.value!.getBoundingClientRect().top - 10,
      label: labels[segmentIndex] || `数据 ${segmentIndex + 1}`,
      value: values[segmentIndex],
      percentage: ((values[segmentIndex] / total.value) * 100).toFixed(1)
    }

    emit('segmentHover', { label: labels[segmentIndex], value: values[segmentIndex] }, segmentIndex)
    drawChart()
  } else if (segmentIndex === -1 && hoveredIndex.value !== -1) {
    hoveredIndex.value = -1
    tooltip.value.visible = false
    drawChart()
  }
}

function handleMouseLeave() {
  hoveredIndex.value = -1
  tooltip.value.visible = false
  drawChart()
}

function handleClick(event: MouseEvent) {
  const segmentIndex = getSegmentFromPoint(event.clientX, event.clientY)

  if (segmentIndex !== -1) {
    const { labels, values } = chartData.value
    emit('segmentClick', { label: labels[segmentIndex], value: values[segmentIndex] }, segmentIndex)
  }
}

function handleLegendHover(index: number) {
  hoveredIndex.value = index
  drawChart()
}

function handleLegendLeave() {
  hoveredIndex.value = -1
  drawChart()
}

function toggleSeries(index: number) {
  const hiddenIndex = hiddenSeries.value.indexOf(index)
  if (hiddenIndex > -1) {
    hiddenSeries.value.splice(hiddenIndex, 1)
  } else {
    hiddenSeries.value.push(index)
  }
}

function formatValue(value: number): string {
  if (value >= 1000000) {
    return (value / 1000000).toFixed(1) + 'M'
  } else if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'K'
  } else {
    return value.toLocaleString()
  }
}
</script>

<style scoped>
.pie-chart-container {
  @apply relative w-full;
}

.chart-loading,
.chart-error,
.chart-empty {
  @apply flex flex-col items-center justify-center py-12 text-gray-500 dark:text-gray-400;
}

.chart-loading i,
.chart-error i,
.chart-empty i {
  @apply text-2xl mb-2;
}

.retry-btn {
  @apply mt-2 px-3 py-1 text-sm bg-blue-500 hover:bg-blue-600 text-white rounded transition-colors;
}

.chart-content {
  @apply relative flex items-start gap-4;
}

.chart-canvas {
  @apply cursor-pointer;
}

.chart-legend {
  @apply flex flex-col gap-2;
}

.chart-legend.right {
  @apply flex-col;
}

.chart-legend.bottom {
  @apply flex-row flex-wrap justify-center absolute bottom-0 left-1/2 transform -translate-x-1/2;
}

.chart-legend.left {
  @apply flex-col order-first;
}

.chart-legend.top {
  @apply flex-row flex-wrap justify-center absolute top-0 left-1/2 transform -translate-x-1/2;
}

.legend-item {
  @apply flex items-center gap-2 px-2 py-1 rounded cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors;
}

.legend-item.active {
  @apply bg-blue-50 dark:bg-blue-900/20;
}

.legend-item.hidden {
  @apply opacity-50;
}

.legend-color {
  @apply w-3 h-3 rounded-full flex-shrink-0;
}

.legend-label {
  @apply text-sm text-gray-700 dark:text-gray-300 flex-1;
}

.legend-value {
  @apply text-sm font-medium text-gray-900 dark:text-white;
}

.legend-percentage {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

.center-text {
  @apply absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center pointer-events-none;
}

.center-value {
  @apply text-2xl font-bold text-gray-900 dark:text-white;
}

.center-label {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

.chart-tooltip {
  @apply absolute bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 px-3 py-2 rounded shadow-lg pointer-events-none z-10;
}

.tooltip-title {
  @apply font-medium text-sm;
}

.tooltip-content {
  @apply text-xs opacity-90;
}

.tooltip-value {
  @apply font-medium;
}

.tooltip-percentage {
  @apply ml-1;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
