<template>
  <div class="line-chart-container" ref="chartContainer">
    <div v-if="loading" class="chart-loading">
      <i class="i-carbon-in-progress animate-spin"></i>
      <span>加载图表中...</span>
    </div>

    <div v-else-if="error" class="chart-error">
      <i class="i-carbon-warning-filled"></i>
      <span>{{ error }}</span>
      <button @click="$emit('retry')" class="retry-btn">重试</button>
    </div>

    <div v-else-if="!hasData" class="chart-empty">
      <i class="i-carbon-chart-line"></i>
      <span>暂无数据</span>
    </div>

    <canvas
      v-else
      ref="chartRef"
      class="chart-canvas"
      :width="canvasWidth"
      :height="canvasHeight"
    ></canvas>

    <!-- 图表控制 -->
    <div v-if="showControls && hasData" class="chart-controls">
      <div class="control-group">
        <label>数据系列:</label>
        <div class="series-toggles">
          <button
            v-for="(series, index) in data.datasets"
            :key="index"
            @click="toggleSeries(index)"
            class="series-toggle"
            :class="{ active: !hiddenSeries.includes(index) }"
            :style="{ borderColor: series.borderColor }"
          >
            <span class="series-dot" :style="{ backgroundColor: series.borderColor }"></span>
            {{ series.label }}
          </button>
        </div>
      </div>

      <div class="control-group">
        <button @click="resetZoom" class="control-btn">
          <i class="i-carbon-zoom-reset"></i>
          重置缩放
        </button>
        <button @click="downloadChart" class="control-btn">
          <i class="i-carbon-download"></i>
          下载
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import type { ChartData } from '@/api/agentManagement'

interface Props {
  data: ChartData
  options?: any
  height?: string | number
  width?: string | number
  loading?: boolean
  error?: string
  showControls?: boolean
  responsive?: boolean
  smooth?: boolean
  showPoints?: boolean
  showGrid?: boolean
  showAxis?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  height: 300,
  responsive: true,
  smooth: true,
  showPoints: true,
  showGrid: true,
  showAxis: true,
  showControls: false
})

const emit = defineEmits<{
  retry: []
  dataPointClick: [point: any, datasetIndex: number, pointIndex: number]
}>()

// 响应式数据
const chartContainer = ref<HTMLElement>()
const chartRef = ref<HTMLCanvasElement>()
const hiddenSeries = ref<number[]>([])
const isDestroyed = ref(false)

// 图表尺寸
const canvasWidth = ref(800)
const canvasHeight = ref(typeof props.height === 'number' ? props.height : parseInt(props.height))

// 计算属性
const hasData = computed(() => {
  return props.data &&
         props.data.datasets &&
         props.data.datasets.length > 0 &&
         props.data.datasets.some(dataset => dataset.data && dataset.data.length > 0)
})

// 生命周期
onMounted(() => {
  if (props.responsive) {
    updateDimensions()
    window.addEventListener('resize', updateDimensions)
  }

  nextTick(() => {
    if (hasData.value) {
      drawChart()
    }
  })
})

onUnmounted(() => {
  isDestroyed.value = true
  if (props.responsive) {
    window.removeEventListener('resize', updateDimensions)
  }
})

// 监听器
watch(() => props.data, () => {
  if (!isDestroyed.value && hasData.value) {
    nextTick(() => drawChart())
  }
}, { deep: true })

watch(() => props.height, () => {
  canvasHeight.value = typeof props.height === 'number' ? props.height : parseInt(props.height)
  nextTick(() => drawChart())
})

watch(hiddenSeries, () => {
  if (!isDestroyed.value && hasData.value) {
    nextTick(() => drawChart())
  }
}, { deep: true })

// 方法
function updateDimensions() {
  if (!chartContainer.value) return

  const rect = chartContainer.value.getBoundingClientRect()
  canvasWidth.value = rect.width

  if (!isDestroyed.value && hasData.value) {
    nextTick(() => drawChart())
  }
}

function drawChart() {
  if (!chartRef.value || !hasData.value) return

  const canvas = chartRef.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // 设置高DPI支持
  const dpr = window.devicePixelRatio || 1
  const rect = canvas.getBoundingClientRect()

  canvas.width = rect.width * dpr
  canvas.height = rect.height * dpr

  ctx.scale(dpr, dpr)

  // 清空画布
  ctx.clearRect(0, 0, rect.width, rect.height)

  // 设置画布样式
  canvas.style.width = rect.width + 'px'
  canvas.style.height = rect.height + 'px'

  // 绘制图表
  drawLineChart(ctx, rect.width, rect.height)
}

function drawLineChart(ctx: CanvasRenderingContext2D, width: number, height: number) {
  const padding = { top: 20, right: 20, bottom: 40, left: 60 }
  const chartWidth = width - padding.left - padding.right
  const chartHeight = height - padding.top - padding.bottom

  // 获取可见数据集
  const visibleDatasets = props.data.datasets.filter((_, index) => !hiddenSeries.value.includes(index))
  if (visibleDatasets.length === 0) return

  // 计算数据范围
  const allValues = visibleDatasets.flatMap(dataset => dataset.data)
  const minValue = Math.min(...allValues)
  const maxValue = Math.max(...allValues)
  const valueRange = maxValue - minValue || 1

  const labels = props.data.labels || []
  const labelCount = labels.length

  // 绘制网格
  if (props.showGrid) {
    drawGrid(ctx, padding, chartWidth, chartHeight, labelCount)
  }

  // 绘制坐标轴
  if (props.showAxis) {
    drawAxis(ctx, padding, chartWidth, chartHeight, labels, minValue, maxValue)
  }

  // 绘制数据线
  visibleDatasets.forEach((dataset, datasetIndex) => {
    const originalIndex = props.data.datasets.indexOf(dataset)
    drawDataLine(ctx, dataset, padding, chartWidth, chartHeight, minValue, valueRange, originalIndex)
  })
}

function drawGrid(ctx: CanvasRenderingContext2D, padding: any, chartWidth: number, chartHeight: number, labelCount: number) {
  ctx.strokeStyle = '#e5e5e5'
  ctx.lineWidth = 1
  ctx.setLineDash([2, 2])

  // 垂直网格线
  for (let i = 0; i <= labelCount - 1; i++) {
    const x = padding.left + (chartWidth / (labelCount - 1)) * i
    ctx.beginPath()
    ctx.moveTo(x, padding.top)
    ctx.lineTo(x, padding.top + chartHeight)
    ctx.stroke()
  }

  // 水平网格线
  const gridLines = 5
  for (let i = 0; i <= gridLines; i++) {
    const y = padding.top + (chartHeight / gridLines) * i
    ctx.beginPath()
    ctx.moveTo(padding.left, y)
    ctx.lineTo(padding.left + chartWidth, y)
    ctx.stroke()
  }

  ctx.setLineDash([])
}

function drawAxis(ctx: CanvasRenderingContext2D, padding: any, chartWidth: number, chartHeight: number, labels: string[], minValue: number, maxValue: number) {
  ctx.strokeStyle = '#666'
  ctx.lineWidth = 1
  ctx.font = '12px sans-serif'
  ctx.fillStyle = '#666'

  // X轴
  ctx.beginPath()
  ctx.moveTo(padding.left, padding.top + chartHeight)
  ctx.lineTo(padding.left + chartWidth, padding.top + chartHeight)
  ctx.stroke()

  // Y轴
  ctx.beginPath()
  ctx.moveTo(padding.left, padding.top)
  ctx.lineTo(padding.left, padding.top + chartHeight)
  ctx.stroke()

  // X轴标签
  labels.forEach((label, index) => {
    const x = padding.left + (chartWidth / (labels.length - 1)) * index
    ctx.textAlign = 'center'
    ctx.fillText(label, x, padding.top + chartHeight + 20)
  })

  // Y轴标签
  const gridLines = 5
  for (let i = 0; i <= gridLines; i++) {
    const value = maxValue - (maxValue - minValue) * (i / gridLines)
    const y = padding.top + (chartHeight / gridLines) * i
    ctx.textAlign = 'right'
    ctx.fillText(formatNumber(value), padding.left - 10, y + 4)
  }
}

function drawDataLine(ctx: CanvasRenderingContext2D, dataset: any, padding: any, chartWidth: number, chartHeight: number, minValue: number, valueRange: number, datasetIndex: number) {
  const data = dataset.data
  const labelCount = data.length

  if (labelCount < 2) return

  const color = dataset.borderColor || `hsl(${datasetIndex * 137.5}, 70%, 50%)`

  ctx.strokeStyle = color
  ctx.lineWidth = dataset.borderWidth || 2
  ctx.lineCap = 'round'
  ctx.lineJoin = 'round'

  // 绘制线条
  ctx.beginPath()

  data.forEach((value: number, index: number) => {
    const x = padding.left + (chartWidth / (labelCount - 1)) * index
    const y = padding.top + chartHeight - ((value - minValue) / valueRange) * chartHeight

    if (index === 0) {
      ctx.moveTo(x, y)
    } else if (props.smooth) {
      // 平滑曲线
      const prevX = padding.left + (chartWidth / (labelCount - 1)) * (index - 1)
      const prevY = padding.top + chartHeight - ((data[index - 1] - minValue) / valueRange) * chartHeight
      const cpX = (prevX + x) / 2
      ctx.quadraticCurveTo(cpX, prevY, x, y)
    } else {
      ctx.lineTo(x, y)
    }
  })

  ctx.stroke()

  // 绘制数据点
  if (props.showPoints) {
    ctx.fillStyle = color
    data.forEach((value: number, index: number) => {
      const x = padding.left + (chartWidth / (labelCount - 1)) * index
      const y = padding.top + chartHeight - ((value - minValue) / valueRange) * chartHeight

      ctx.beginPath()
      ctx.arc(x, y, 4, 0, Math.PI * 2)
      ctx.fill()

      // 白色边框
      ctx.strokeStyle = '#fff'
      ctx.lineWidth = 2
      ctx.stroke()
      ctx.strokeStyle = color
      ctx.lineWidth = dataset.borderWidth || 2
    })
  }

  // 填充区域（可选）
  if (dataset.fill) {
    ctx.globalAlpha = 0.3
    ctx.fillStyle = color

    ctx.beginPath()
    ctx.moveTo(padding.left, padding.top + chartHeight)

    data.forEach((value: number, index: number) => {
      const x = padding.left + (chartWidth / (labelCount - 1)) * index
      const y = padding.top + chartHeight - ((value - minValue) / valueRange) * chartHeight
      ctx.lineTo(x, y)
    })

    ctx.lineTo(padding.left + chartWidth, padding.top + chartHeight)
    ctx.closePath()
    ctx.fill()

    ctx.globalAlpha = 1
  }
}

function formatNumber(value: number): string {
  if (value >= 1000000) {
    return (value / 1000000).toFixed(1) + 'M'
  } else if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'K'
  } else if (value % 1 === 0) {
    return value.toString()
  } else {
    return value.toFixed(1)
  }
}

function toggleSeries(index: number) {
  const hiddenIndex = hiddenSeries.value.indexOf(index)
  if (hiddenIndex > -1) {
    hiddenSeries.value.splice(hiddenIndex, 1)
  } else {
    hiddenSeries.value.push(index)
  }
}

function resetZoom() {
  // 重置缩放功能的实现
  drawChart()
}

function downloadChart() {
  if (!chartRef.value) return

  const link = document.createElement('a')
  link.download = 'chart.png'
  link.href = chartRef.value.toDataURL()
  link.click()
}
</script>

<style scoped>
.line-chart-container {
  @apply relative w-full;
}

.chart-loading,
.chart-error,
.chart-empty {
  @apply flex flex-col items-center justify-center py-12 text-gray-500 dark:text-gray-400;
}

.chart-loading i,
.chart-error i,
.chart-empty i {
  @apply text-2xl mb-2;
}

.retry-btn {
  @apply mt-2 px-3 py-1 text-sm bg-blue-500 hover:bg-blue-600 text-white rounded transition-colors;
}

.chart-canvas {
  @apply w-full h-full;
}

.chart-controls {
  @apply mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg space-y-3;
}

.control-group {
  @apply flex items-center gap-3;
}

.control-group label {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300;
}

.series-toggles {
  @apply flex flex-wrap gap-2;
}

.series-toggle {
  @apply flex items-center gap-1 px-3 py-1 text-sm border rounded-full transition-colors;
}

.series-toggle.active {
  @apply bg-white dark:bg-gray-700 shadow-sm;
}

.series-toggle:not(.active) {
  @apply opacity-50 bg-gray-100 dark:bg-gray-600;
}

.series-dot {
  @apply w-2 h-2 rounded-full;
}

.control-btn {
  @apply flex items-center gap-1 px-3 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
