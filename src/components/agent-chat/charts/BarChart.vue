<template>
  <div class="bar-chart-container" ref="chartContainer">
    <div v-if="loading" class="chart-loading">
      <i class="i-carbon-in-progress animate-spin"></i>
      <span>加载图表中...</span>
    </div>

    <div v-else-if="error" class="chart-error">
      <i class="i-carbon-warning-filled"></i>
      <span>{{ error }}</span>
      <button @click="$emit('retry')" class="retry-btn">重试</button>
    </div>

    <div v-else-if="!hasData" class="chart-empty">
      <i class="i-carbon-chart-column"></i>
      <span>暂无数据</span>
    </div>

    <canvas
      v-else
      ref="chartRef"
      class="chart-canvas"
      :width="canvasWidth"
      :height="canvasHeight"
      @mousemove="handleMouseMove"
      @mouseleave="handleMouseLeave"
      @click="handleClick"
    ></canvas>

    <!-- 悬浮提示 -->
    <div
      v-if="tooltip.visible"
      class="chart-tooltip"
      :style="{ left: tooltip.x + 'px', top: tooltip.y + 'px' }"
    >
      <div class="tooltip-title">{{ tooltip.label }}</div>
      <div class="tooltip-content">
        <div
          v-for="(item, index) in tooltip.datasets"
          :key="index"
          class="tooltip-item"
        >
          <span
            class="tooltip-color"
            :style="{ backgroundColor: item.color }"
          ></span>
          <span class="tooltip-dataset">{{ item.label }}:</span>
          <span class="tooltip-value">{{ formatValue(item.value) }}</span>
        </div>
      </div>
    </div>

    <!-- 图表控制 -->
    <div v-if="showControls && hasData" class="chart-controls">
      <div class="control-group">
        <label>显示类型:</label>
        <div class="type-toggles">
          <button
            @click="chartType = 'column'"
            :class="{ active: chartType === 'column' }"
            class="type-btn"
          >
            柱状图
          </button>
          <button
            @click="chartType = 'bar'"
            :class="{ active: chartType === 'bar' }"
            class="type-btn"
          >
            条形图
          </button>
          <button
            @click="chartType = 'stacked'"
            :class="{ active: chartType === 'stacked' }"
            class="type-btn"
          >
            堆叠图
          </button>
        </div>
      </div>

      <div class="control-group">
        <label>数据系列:</label>
        <div class="series-toggles">
          <button
            v-for="(series, index) in data.datasets"
            :key="index"
            @click="toggleSeries(index)"
            class="series-toggle"
            :class="{ active: !hiddenSeries.includes(index) }"
            :style="{ borderColor: series.backgroundColor }"
          >
            <span
              class="series-dot"
              :style="{ backgroundColor: series.backgroundColor }"
            ></span>
            {{ series.label }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import type { ChartData } from '@/api/agentManagement'

interface Props {
  data: ChartData
  options?: any
  height?: string | number
  width?: string | number
  loading?: boolean
  error?: string
  showControls?: boolean
  responsive?: boolean
  type?: 'column' | 'bar' | 'stacked'
  showGrid?: boolean
  showAxis?: boolean
  showValues?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  height: 300,
  responsive: true,
  type: 'column',
  showGrid: true,
  showAxis: true,
  showValues: false,
  showControls: false
})

const emit = defineEmits<{
  retry: []
  barClick: [bar: any, datasetIndex: number, barIndex: number]
  barHover: [bar: any, datasetIndex: number, barIndex: number]
}>()

// 响应式数据
const chartContainer = ref<HTMLElement>()
const chartRef = ref<HTMLCanvasElement>()
const hiddenSeries = ref<number[]>([])
const hoveredBar = ref<{ datasetIndex: number; barIndex: number } | null>(null)
const chartType = ref(props.type)
const isDestroyed = ref(false)

const tooltip = ref({
  visible: false,
  x: 0,
  y: 0,
  label: '',
  datasets: [] as Array<{ label: string; value: number; color: string }>
})

// 图表尺寸
const canvasWidth = ref(800)
const canvasHeight = ref(typeof props.height === 'number' ? props.height : parseInt(props.height))

// 计算属性
const hasData = computed(() => {
  return props.data &&
         props.data.datasets &&
         props.data.datasets.length > 0 &&
         props.data.datasets.some(dataset => dataset.data && dataset.data.length > 0)
})

const visibleDatasets = computed(() => {
  return props.data.datasets?.filter((_, index) => !hiddenSeries.value.includes(index)) || []
})

// 生命周期
onMounted(() => {
  if (props.responsive) {
    updateDimensions()
    window.addEventListener('resize', updateDimensions)
  }

  nextTick(() => {
    if (hasData.value) {
      drawChart()
    }
  })
})

onUnmounted(() => {
  isDestroyed.value = true
  if (props.responsive) {
    window.removeEventListener('resize', updateDimensions)
  }
})

// 监听器
watch(() => props.data, () => {
  if (!isDestroyed.value && hasData.value) {
    nextTick(() => drawChart())
  }
}, { deep: true })

watch([() => props.height, chartType, hiddenSeries], () => {
  canvasHeight.value = typeof props.height === 'number' ? props.height : parseInt(props.height)
  if (!isDestroyed.value && hasData.value) {
    nextTick(() => drawChart())
  }
}, { deep: true })

// 方法
function updateDimensions() {
  if (!chartContainer.value) return

  const rect = chartContainer.value.getBoundingClientRect()
  canvasWidth.value = rect.width

  if (!isDestroyed.value && hasData.value) {
    nextTick(() => drawChart())
  }
}

function drawChart() {
  if (!chartRef.value || !hasData.value) return

  const canvas = chartRef.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // 设置高DPI支持
  const dpr = window.devicePixelRatio || 1
  const rect = canvas.getBoundingClientRect()

  canvas.width = rect.width * dpr
  canvas.height = rect.height * dpr

  ctx.scale(dpr, dpr)

  // 清空画布
  ctx.clearRect(0, 0, rect.width, rect.height)

  // 设置画布样式
  canvas.style.width = rect.width + 'px'
  canvas.style.height = rect.height + 'px'

  // 绘制图表
  if (chartType.value === 'bar') {
    drawHorizontalBarChart(ctx, rect.width, rect.height)
  } else {
    drawVerticalBarChart(ctx, rect.width, rect.height)
  }
}

function drawVerticalBarChart(ctx: CanvasRenderingContext2D, width: number, height: number) {
  const padding = { top: 20, right: 20, bottom: 60, left: 60 }
  const chartWidth = width - padding.left - padding.right
  const chartHeight = height - padding.top - padding.bottom

  const datasets = visibleDatasets.value
  if (datasets.length === 0) return

  const labels = props.data.labels || []
  const labelCount = labels.length

  // 计算数据范围
  let maxValue = 0
  let minValue = 0

  if (chartType.value === 'stacked') {
    // 堆叠图：计算每个标签的总和
    for (let i = 0; i < labelCount; i++) {
      let sum = 0
      datasets.forEach(dataset => {
        sum += dataset.data[i] || 0
      })
      maxValue = Math.max(maxValue, sum)
    }
  } else {
    // 普通柱状图：找到最大值
    datasets.forEach(dataset => {
      dataset.data.forEach(value => {
        maxValue = Math.max(maxValue, value)
        minValue = Math.min(minValue, value)
      })
    })
  }

  const valueRange = maxValue - minValue || 1

  // 绘制网格
  if (props.showGrid) {
    drawVerticalGrid(ctx, padding, chartWidth, chartHeight, labelCount)
  }

  // 绘制坐标轴
  if (props.showAxis) {
    drawVerticalAxis(ctx, padding, chartWidth, chartHeight, labels, minValue, maxValue)
  }

  // 绘制柱子
  const barWidth = chartWidth / labelCount * 0.8
  const barSpacing = chartWidth / labelCount * 0.2
  const datasetWidth = barWidth / datasets.length

  for (let labelIndex = 0; labelIndex < labelCount; labelIndex++) {
    const baseX = padding.left + (chartWidth / labelCount) * labelIndex + barSpacing / 2
    let stackedHeight = 0

    datasets.forEach((dataset, datasetIndex) => {
      const value = dataset.data[labelIndex] || 0
      const barHeight = (Math.abs(value) / valueRange) * chartHeight

      let barX: number
      let barY: number

      if (chartType.value === 'stacked') {
        barX = baseX
        barY = padding.top + chartHeight - stackedHeight - barHeight
        stackedHeight += barHeight
      } else {
        barX = baseX + datasetWidth * datasetIndex
        barY = value >= 0
          ? padding.top + chartHeight - barHeight
          : padding.top + chartHeight
      }

      // 绘制柱子
      ctx.fillStyle = dataset.backgroundColor || generateColor(datasetIndex)

      // 高亮悬浮的柱子
      if (hoveredBar.value?.datasetIndex === datasetIndex && hoveredBar.value?.barIndex === labelIndex) {
        ctx.shadowColor = 'rgba(0, 0, 0, 0.3)'
        ctx.shadowBlur = 10
        ctx.shadowOffsetX = 2
        ctx.shadowOffsetY = 2
      }

      const actualBarWidth = chartType.value === 'stacked' ? barWidth : datasetWidth * 0.9
      ctx.fillRect(barX, barY, actualBarWidth, barHeight)

      // 重置阴影
      ctx.shadowColor = 'transparent'
      ctx.shadowBlur = 0
      ctx.shadowOffsetX = 0
      ctx.shadowOffsetY = 0

      // 绘制数值标签
      if (props.showValues && barHeight > 20) {
        ctx.fillStyle = '#fff'
        ctx.font = '12px sans-serif'
        ctx.textAlign = 'center'
        ctx.textBaseline = 'middle'

        const textX = barX + actualBarWidth / 2
        const textY = barY + barHeight / 2

        ctx.fillText(formatValue(value), textX, textY)
      }
    })
  }
}

function drawHorizontalBarChart(ctx: CanvasRenderingContext2D, width: number, height: number) {
  const padding = { top: 20, right: 60, bottom: 40, left: 100 }
  const chartWidth = width - padding.left - padding.right
  const chartHeight = height - padding.top - padding.bottom

  const datasets = visibleDatasets.value
  if (datasets.length === 0) return

  const labels = props.data.labels || []
  const labelCount = labels.length

  // 计算数据范围
  let maxValue = 0
  let minValue = 0

  datasets.forEach(dataset => {
    dataset.data.forEach(value => {
      maxValue = Math.max(maxValue, value)
      minValue = Math.min(minValue, value)
    })
  })

  const valueRange = maxValue - minValue || 1

  // 绘制网格
  if (props.showGrid) {
    drawHorizontalGrid(ctx, padding, chartWidth, chartHeight, labelCount)
  }

  // 绘制坐标轴
  if (props.showAxis) {
    drawHorizontalAxis(ctx, padding, chartWidth, chartHeight, labels, minValue, maxValue)
  }

  // 绘制柱子
  const barHeight = chartHeight / labelCount * 0.8
  const barSpacing = chartHeight / labelCount * 0.2
  const datasetHeight = barHeight / datasets.length

  for (let labelIndex = 0; labelIndex < labelCount; labelIndex++) {
    const baseY = padding.top + (chartHeight / labelCount) * labelIndex + barSpacing / 2

    datasets.forEach((dataset, datasetIndex) => {
      const value = dataset.data[labelIndex] || 0
      const barWidth = (Math.abs(value) / valueRange) * chartWidth

      const barX = value >= 0 ? padding.left : padding.left - barWidth
      const barY = baseY + datasetHeight * datasetIndex

      // 绘制柱子
      ctx.fillStyle = dataset.backgroundColor || generateColor(datasetIndex)

      // 高亮悬浮的柱子
      if (hoveredBar.value?.datasetIndex === datasetIndex && hoveredBar.value?.barIndex === labelIndex) {
        ctx.shadowColor = 'rgba(0, 0, 0, 0.3)'
        ctx.shadowBlur = 10
        ctx.shadowOffsetX = 2
        ctx.shadowOffsetY = 2
      }

      ctx.fillRect(barX, barY, barWidth, datasetHeight * 0.9)

      // 重置阴影
      ctx.shadowColor = 'transparent'
      ctx.shadowBlur = 0
      ctx.shadowOffsetX = 0
      ctx.shadowOffsetY = 0

      // 绘制数值标签
      if (props.showValues && barWidth > 30) {
        ctx.fillStyle = '#fff'
        ctx.font = '12px sans-serif'
        ctx.textAlign = 'center'
        ctx.textBaseline = 'middle'

        const textX = barX + barWidth / 2
        const textY = barY + datasetHeight * 0.9 / 2

        ctx.fillText(formatValue(value), textX, textY)
      }
    })
  }
}

function drawVerticalGrid(ctx: CanvasRenderingContext2D, padding: any, chartWidth: number, chartHeight: number, labelCount: number) {
  ctx.strokeStyle = '#e5e5e5'
  ctx.lineWidth = 1
  ctx.setLineDash([2, 2])

  // 垂直网格线
  for (let i = 0; i <= labelCount; i++) {
    const x = padding.left + (chartWidth / labelCount) * i
    ctx.beginPath()
    ctx.moveTo(x, padding.top)
    ctx.lineTo(x, padding.top + chartHeight)
    ctx.stroke()
  }

  // 水平网格线
  const gridLines = 5
  for (let i = 0; i <= gridLines; i++) {
    const y = padding.top + (chartHeight / gridLines) * i
    ctx.beginPath()
    ctx.moveTo(padding.left, y)
    ctx.lineTo(padding.left + chartWidth, y)
    ctx.stroke()
  }

  ctx.setLineDash([])
}

function drawHorizontalGrid(ctx: CanvasRenderingContext2D, padding: any, chartWidth: number, chartHeight: number, labelCount: number) {
  ctx.strokeStyle = '#e5e5e5'
  ctx.lineWidth = 1
  ctx.setLineDash([2, 2])

  // 水平网格线
  for (let i = 0; i <= labelCount; i++) {
    const y = padding.top + (chartHeight / labelCount) * i
    ctx.beginPath()
    ctx.moveTo(padding.left, y)
    ctx.lineTo(padding.left + chartWidth, y)
    ctx.stroke()
  }

  // 垂直网格线
  const gridLines = 5
  for (let i = 0; i <= gridLines; i++) {
    const x = padding.left + (chartWidth / gridLines) * i
    ctx.beginPath()
    ctx.moveTo(x, padding.top)
    ctx.lineTo(x, padding.top + chartHeight)
    ctx.stroke()
  }

  ctx.setLineDash([])
}

function drawVerticalAxis(ctx: CanvasRenderingContext2D, padding: any, chartWidth: number, chartHeight: number, labels: string[], minValue: number, maxValue: number) {
  ctx.strokeStyle = '#666'
  ctx.lineWidth = 1
  ctx.font = '12px sans-serif'
  ctx.fillStyle = '#666'

  // X轴
  ctx.beginPath()
  ctx.moveTo(padding.left, padding.top + chartHeight)
  ctx.lineTo(padding.left + chartWidth, padding.top + chartHeight)
  ctx.stroke()

  // Y轴
  ctx.beginPath()
  ctx.moveTo(padding.left, padding.top)
  ctx.lineTo(padding.left, padding.top + chartHeight)
  ctx.stroke()

  // X轴标签
  labels.forEach((label, index) => {
    const x = padding.left + (chartWidth / labels.length) * index + (chartWidth / labels.length) / 2
    ctx.save()
    ctx.translate(x, padding.top + chartHeight + 15)
    ctx.rotate(-Math.PI / 4)
    ctx.textAlign = 'right'
    ctx.fillText(label, 0, 0)
    ctx.restore()
  })

  // Y轴标签
  const gridLines = 5
  for (let i = 0; i <= gridLines; i++) {
    const value = maxValue - (maxValue - minValue) * (i / gridLines)
    const y = padding.top + (chartHeight / gridLines) * i
    ctx.textAlign = 'right'
    ctx.fillText(formatNumber(value), padding.left - 10, y + 4)
  }
}

function drawHorizontalAxis(ctx: CanvasRenderingContext2D, padding: any, chartWidth: number, chartHeight: number, labels: string[], minValue: number, maxValue: number) {
  ctx.strokeStyle = '#666'
  ctx.lineWidth = 1
  ctx.font = '12px sans-serif'
  ctx.fillStyle = '#666'

  // X轴
  ctx.beginPath()
  ctx.moveTo(padding.left, padding.top + chartHeight)
  ctx.lineTo(padding.left + chartWidth, padding.top + chartHeight)
  ctx.stroke()

  // Y轴
  ctx.beginPath()
  ctx.moveTo(padding.left, padding.top)
  ctx.lineTo(padding.left, padding.top + chartHeight)
  ctx.stroke()

  // Y轴标签
  labels.forEach((label, index) => {
    const y = padding.top + (chartHeight / labels.length) * index + (chartHeight / labels.length) / 2
    ctx.textAlign = 'right'
    ctx.textBaseline = 'middle'
    ctx.fillText(label, padding.left - 10, y)
  })

  // X轴标签
  const gridLines = 5
  for (let i = 0; i <= gridLines; i++) {
    const value = minValue + (maxValue - minValue) * (i / gridLines)
    const x = padding.left + (chartWidth / gridLines) * i
    ctx.textAlign = 'center'
    ctx.fillText(formatNumber(value), x, padding.top + chartHeight + 20)
  }
}

function generateColor(index: number): string {
  const hue = (index * 137.5) % 360
  return `hsl(${hue}, 70%, 50%)`
}

function formatNumber(value: number): string {
  if (value >= 1000000) {
    return (value / 1000000).toFixed(1) + 'M'
  } else if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'K'
  } else if (value % 1 === 0) {
    return value.toString()
  } else {
    return value.toFixed(1)
  }
}

function formatValue(value: number): string {
  return value.toLocaleString()
}

function getBarFromPoint(x: number, y: number): { datasetIndex: number; barIndex: number } | null {
  if (!chartRef.value || !hasData.value) return null

  const rect = chartRef.value.getBoundingClientRect()
  const relativeX = x - rect.left
  const relativeY = y - rect.top

  const padding = chartType.value === 'bar'
    ? { top: 20, right: 60, bottom: 40, left: 100 }
    : { top: 20, right: 20, bottom: 60, left: 60 }

  const chartWidth = rect.width - padding.left - padding.right
  const chartHeight = rect.height - padding.top - padding.bottom

  const labels = props.data.labels || []
  const datasets = visibleDatasets.value

  if (chartType.value === 'bar') {
    // 水平柱状图
    const barHeight = chartHeight / labels.length * 0.8
    const datasetHeight = barHeight / datasets.length

    for (let labelIndex = 0; labelIndex < labels.length; labelIndex++) {
      const baseY = padding.top + (chartHeight / labels.length) * labelIndex + (chartHeight / labels.length * 0.2) / 2

      for (let datasetIndex = 0; datasetIndex < datasets.length; datasetIndex++) {
        const barY = baseY + datasetHeight * datasetIndex

        if (relativeY >= barY && relativeY <= barY + datasetHeight * 0.9) {
          return { datasetIndex, barIndex: labelIndex }
        }
      }
    }
  } else {
    // 垂直柱状图
    const barWidth = chartWidth / labels.length * 0.8
    const datasetWidth = barWidth / datasets.length

    for (let labelIndex = 0; labelIndex < labels.length; labelIndex++) {
      const baseX = padding.left + (chartWidth / labels.length) * labelIndex + (chartWidth / labels.length * 0.2) / 2

      for (let datasetIndex = 0; datasetIndex < datasets.length; datasetIndex++) {
        const barX = chartType.value === 'stacked' ? baseX : baseX + datasetWidth * datasetIndex
        const actualBarWidth = chartType.value === 'stacked' ? barWidth : datasetWidth * 0.9

        if (relativeX >= barX && relativeX <= barX + actualBarWidth) {
          return { datasetIndex, barIndex: labelIndex }
        }
      }
    }
  }

  return null
}

function handleMouseMove(event: MouseEvent) {
  const bar = getBarFromPoint(event.clientX, event.clientY)

  if (bar && (bar.datasetIndex !== hoveredBar.value?.datasetIndex || bar.barIndex !== hoveredBar.value?.barIndex)) {
    hoveredBar.value = bar

    // 显示提示框
    const label = props.data.labels?.[bar.barIndex] || `数据 ${bar.barIndex + 1}`
    const datasets = visibleDatasets.value.map((dataset, index) => ({
      label: dataset.label || `数据集 ${index + 1}`,
      value: dataset.data[bar.barIndex] || 0,
      color: dataset.backgroundColor || generateColor(index)
    }))

    tooltip.value = {
      visible: true,
      x: event.clientX - chartRef.value!.getBoundingClientRect().left + 10,
      y: event.clientY - chartRef.value!.getBoundingClientRect().top - 10,
      label,
      datasets
    }

    emit('barHover', { label, value: datasets[bar.datasetIndex].value }, bar.datasetIndex, bar.barIndex)
    drawChart()
  } else if (!bar && hoveredBar.value) {
    hoveredBar.value = null
    tooltip.value.visible = false
    drawChart()
  }
}

function handleMouseLeave() {
  hoveredBar.value = null
  tooltip.value.visible = false
  drawChart()
}

function handleClick(event: MouseEvent) {
  const bar = getBarFromPoint(event.clientX, event.clientY)

  if (bar) {
    const label = props.data.labels?.[bar.barIndex] || `数据 ${bar.barIndex + 1}`
    const dataset = visibleDatasets.value[bar.datasetIndex]
    const value = dataset?.data[bar.barIndex] || 0

    emit('barClick', { label, value }, bar.datasetIndex, bar.barIndex)
  }
}

function toggleSeries(index: number) {
  const hiddenIndex = hiddenSeries.value.indexOf(index)
  if (hiddenIndex > -1) {
    hiddenSeries.value.splice(hiddenIndex, 1)
  } else {
    hiddenSeries.value.push(index)
  }
}
</script>

<style scoped>
.bar-chart-container {
  @apply relative w-full;
}

.chart-loading,
.chart-error,
.chart-empty {
  @apply flex flex-col items-center justify-center py-12 text-gray-500 dark:text-gray-400;
}

.chart-loading i,
.chart-error i,
.chart-empty i {
  @apply text-2xl mb-2;
}

.retry-btn {
  @apply mt-2 px-3 py-1 text-sm bg-blue-500 hover:bg-blue-600 text-white rounded transition-colors;
}

.chart-canvas {
  @apply w-full h-full cursor-pointer;
}

.chart-tooltip {
  @apply absolute bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 px-3 py-2 rounded shadow-lg pointer-events-none z-10;
}

.tooltip-title {
  @apply font-medium text-sm mb-1;
}

.tooltip-content {
  @apply space-y-1;
}

.tooltip-item {
  @apply flex items-center gap-2 text-xs;
}

.tooltip-color {
  @apply w-2 h-2 rounded-full;
}

.tooltip-dataset {
  @apply flex-1;
}

.tooltip-value {
  @apply font-medium;
}

.chart-controls {
  @apply mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg space-y-3;
}

.control-group {
  @apply flex items-center gap-3;
}

.control-group label {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300;
}

.type-toggles,
.series-toggles {
  @apply flex flex-wrap gap-2;
}

.type-btn {
  @apply px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded transition-colors;
}

.type-btn.active {
  @apply bg-blue-500 text-white border-blue-500;
}

.type-btn:not(.active) {
  @apply bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600;
}

.series-toggle {
  @apply flex items-center gap-1 px-3 py-1 text-sm border rounded-full transition-colors;
}

.series-toggle.active {
  @apply bg-white dark:bg-gray-700 shadow-sm;
}

.series-toggle:not(.active) {
  @apply opacity-50 bg-gray-100 dark:bg-gray-600;
}

.series-dot {
  @apply w-2 h-2 rounded-full;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
