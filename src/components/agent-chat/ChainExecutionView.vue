<template>
  <div class="chain-execution-view" :class="{ 'full-screen': isFullScreen }">
    <!-- 顶部控制栏 -->
    <div class="execution-header">
      <div class="header-left">
        <div class="execution-info">
          <h3 class="execution-title">
            <i class="i-carbon-watson-machine-learning"></i>
            AI Agent 链式执行
          </h3>
          <div class="execution-meta">
            <span v-if="execution?.flowName" class="flow-name">
              {{ execution.flowName }}
            </span>
            <span class="execution-status" :class="`status-${execution?.status || 'pending'}`">
              <i :class="getStatusIcon(execution?.status)"></i>
              {{ getStatusText(execution?.status) }}
            </span>
            <span v-if="execution?.duration" class="execution-duration">
              <i class="i-carbon-time"></i>
              {{ formatDuration(execution.duration) }}
            </span>
          </div>
        </div>
      </div>

      <div class="header-actions">
        <button
          @click="toggleThinkingMode"
          class="action-btn"
          :class="{ active: showThinkingProcess }"
          title="显示/隐藏思维过程"
        >
          <i class="i-carbon-brain"></i>
          思维链
        </button>
        <button
          @click="toggleFullScreen"
          class="action-btn"
          :title="isFullScreen ? '退出全屏' : '进入全屏'"
        >
          <i :class="isFullScreen ? 'i-carbon-minimize' : 'i-carbon-maximize'"></i>
        </button>
        <button
          v-if="execution?.status === 'running'"
          @click="pauseExecution"
          class="action-btn pause-btn"
          title="暂停执行"
        >
          <i class="i-carbon-pause"></i>
        </button>
        <button
          v-else-if="execution?.status === 'paused'"
          @click="resumeExecution"
          class="action-btn resume-btn"
          title="恢复执行"
        >
          <i class="i-carbon-play"></i>
        </button>
        <button
          v-if="execution?.status === 'running' || execution?.status === 'paused'"
          @click="stopExecution"
          class="action-btn stop-btn"
          title="停止执行"
        >
          <i class="i-carbon-stop"></i>
        </button>
      </div>
    </div>

    <!-- 执行进度概览 -->
    <div class="execution-progress">
      <div class="progress-info">
        <span class="progress-label">执行进度</span>
        <span class="progress-stats">
          {{ execution?.currentStep || 0 }}/{{ execution?.totalSteps || 0 }} 步骤
        </span>
      </div>
      <div class="progress-bar">
        <div
          class="progress-fill"
          :style="{ width: `${overallProgress}%` }"
        ></div>
      </div>
      <div v-if="execution?.eta" class="eta-info">
        <i class="i-carbon-time"></i>
        预计剩余: {{ formatDuration(execution.eta) }}
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="execution-content">
      <!-- 左侧：流程链路图 -->
      <div class="chain-flow-panel" :class="{ collapsed: chainPanelCollapsed }">
        <div class="panel-header">
          <h4>执行链路</h4>
          <button
            @click="toggleChainPanel"
            class="collapse-btn"
            :title="chainPanelCollapsed ? '展开链路图' : '收起链路图'"
          >
            <i :class="chainPanelCollapsed ? 'i-carbon-chevron-right' : 'i-carbon-chevron-left'"></i>
          </button>
        </div>

        <div v-if="!chainPanelCollapsed" class="panel-content">
          <!-- Agent 链路可视化 -->
          <div class="agent-chain">
            <div
              v-for="(step, index) in chainSteps"
              :key="step.id"
              class="chain-step"
              :class="getStepClass(step)"
              @click="selectStep(step)"
            >
              <!-- 步骤节点 -->
              <div class="step-node">
                <div class="step-number">{{ index + 1 }}</div>
                <div class="step-icon">
                  <i v-if="step.status === 'running'" class="i-carbon-in-progress animate-spin"></i>
                  <i v-else-if="step.status === 'completed'" class="i-carbon-checkmark-filled"></i>
                  <i v-else-if="step.status === 'failed'" class="i-carbon-error-filled"></i>
                  <i v-else-if="step.status === 'paused'" class="i-carbon-pause-filled"></i>
                  <i v-else class="i-carbon-circle-dash"></i>
                </div>
              </div>

              <!-- 步骤信息 -->
              <div class="step-info">
                <div class="step-name">{{ step.agentName || step.name }}</div>
                <div class="step-description">{{ step.description }}</div>
                <div v-if="step.status === 'running' && step.progress" class="step-progress">
                  <div class="progress-mini">
                    <div
                      class="progress-mini-fill"
                      :style="{ width: `${step.progress}%` }"
                    ></div>
                  </div>
                  <span class="progress-text">{{ Math.round(step.progress) }}%</span>
                </div>
                <div v-if="step.duration" class="step-duration">
                  <i class="i-carbon-stopwatch"></i>
                  {{ formatDuration(step.duration) }}
                </div>
              </div>

              <!-- 连接线 -->
              <div v-if="index < chainSteps.length - 1" class="step-connector">
                <div class="connector-line" :class="{ active: step.status === 'completed' }"></div>
                <div v-if="step.type === 'parallel'" class="connector-branch"></div>
              </div>
            </div>
          </div>

          <!-- 并行执行状态 -->
          <div v-if="parallelSteps.length > 0" class="parallel-execution">
            <h5>
              <i class="i-carbon-network-3"></i>
              并行执行
            </h5>
            <div class="parallel-steps">
              <div
                v-for="step in parallelSteps"
                :key="step.id"
                class="parallel-step"
                :class="`parallel-${step.status}`"
              >
                <i :class="getStepIcon(step.status)"></i>
                <span>{{ step.agentName }}</span>
                <div v-if="step.progress" class="parallel-progress">
                  {{ Math.round(step.progress) }}%
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：思维过程和执行详情 -->
      <div class="thinking-process-panel">
        <div class="panel-header">
          <div class="header-tabs">
            <button
              @click="activeTab = 'thinking'"
              class="tab-btn"
              :class="{ active: activeTab === 'thinking' }"
            >
              <i class="i-carbon-brain"></i>
              思维过程
            </button>
            <button
              @click="activeTab = 'execution'"
              class="tab-btn"
              :class="{ active: activeTab === 'execution' }"
            >
              <i class="i-carbon-log-file"></i>
              执行日志
            </button>
            <button
              @click="activeTab = 'result'"
              class="tab-btn"
              :class="{ active: activeTab === 'result' }"
            >
              <i class="i-carbon-result"></i>
              执行结果
            </button>
          </div>
          <div class="tab-actions">
            <button
              @click="toggleAutoScroll"
              class="action-btn"
              :class="{ active: autoScroll }"
              title="自动滚动"
            >
              <i class="i-carbon-arrow-down"></i>
            </button>
            <button
              @click="clearLogs"
              class="action-btn"
              title="清空日志"
            >
              <i class="i-carbon-clean"></i>
            </button>
          </div>
        </div>

        <div class="panel-content" ref="contentPanel">
          <!-- 思维过程面板 -->
          <div v-if="activeTab === 'thinking'" class="thinking-content">
            <div v-if="!thinkingChain.length" class="empty-thinking">
              <i class="i-carbon-brain"></i>
              <p>AI Agent 正在分析和思考...</p>
            </div>
            <div v-else class="thinking-chain">
              <div
                v-for="(thought, index) in thinkingChain"
                :key="index"
                class="thinking-step"
                :class="getThinkingStepClass(thought)"
              >
                <div class="thinking-header">
                  <div class="thinking-agent">
                    <i class="i-carbon-robot"></i>
                    {{ thought.agentName }}
                  </div>
                  <div class="thinking-timestamp">
                    {{ formatTimestamp(thought.timestamp) }}
                  </div>
                </div>
                <div class="thinking-content-text">
                  <div class="thinking-analysis">
                    <h6>分析过程:</h6>
                    <p>{{ thought.analysis }}</p>
                  </div>
                  <div v-if="thought.reasoning" class="thinking-reasoning">
                    <h6>推理逻辑:</h6>
                    <p>{{ thought.reasoning }}</p>
                  </div>
                  <div v-if="thought.confidence" class="thinking-confidence">
                    <span class="confidence-label">置信度:</span>
                    <div class="confidence-bar">
                      <div
                        class="confidence-fill"
                        :style="{ width: `${thought.confidence * 100}%` }"
                      ></div>
                    </div>
                    <span class="confidence-value">{{ Math.round(thought.confidence * 100) }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 执行日志面板 -->
          <div v-if="activeTab === 'execution'" class="execution-logs">
            <div v-if="!executionLogs.length" class="empty-logs">
              <i class="i-carbon-log-file"></i>
              <p>等待执行日志...</p>
            </div>
            <div v-else class="log-entries">
              <div
                v-for="(log, index) in executionLogs"
                :key="index"
                class="log-entry"
                :class="`log-${log.level.toLowerCase()}`"
              >
                <div class="log-timestamp">{{ formatTimestamp(log.timestamp) }}</div>
                <div class="log-level">{{ log.level }}</div>
                <div class="log-agent">{{ log.agentName }}</div>
                <div class="log-message">{{ log.message }}</div>
                <div v-if="log.data" class="log-data">
                  <button @click="toggleLogData(index)" class="data-toggle">
                    <i class="i-carbon-code"></i>
                    {{ expandedLogs.has(index) ? '隐藏' : '查看' }}数据
                  </button>
                  <pre v-if="expandedLogs.has(index)" class="log-data-content">{{ formatLogData(log.data) }}</pre>
                </div>
              </div>
            </div>
          </div>

          <!-- 执行结果面板 -->
          <div v-if="activeTab === 'result'" class="execution-results">
            <div v-if="!executionResult" class="empty-result">
              <i class="i-carbon-result"></i>
              <p>执行完成后将显示结果...</p>
            </div>
            <div v-else class="result-content">
              <div class="result-summary">
                <h5>执行摘要</h5>
                <div class="summary-stats">
                  <div class="stat-item">
                    <span class="stat-label">总耗时:</span>
                    <span class="stat-value">{{ formatDuration(executionResult.totalDuration) }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">成功步骤:</span>
                    <span class="stat-value">{{ executionResult.successSteps }}/{{ executionResult.totalSteps }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">Token 消耗:</span>
                    <span class="stat-value">{{ executionResult.totalTokens }}</span>
                  </div>
                </div>
              </div>

              <div class="result-details">
                <h5>执行结果</h5>
                <div class="result-data">
                  <pre>{{ formatResult(executionResult.finalResult) }}</pre>
                </div>
              </div>

              <div v-if="executionResult.artifacts?.length" class="result-artifacts">
                <h5>生成的文件</h5>
                <div class="artifact-list">
                  <div
                    v-for="artifact in executionResult.artifacts"
                    :key="artifact.id"
                    class="artifact-item"
                  >
                    <i :class="getArtifactIcon(artifact.type)"></i>
                    <span class="artifact-name">{{ artifact.name }}</span>
                    <span class="artifact-size">{{ formatFileSize(artifact.size) }}</span>
                    <button @click="downloadArtifact(artifact)" class="download-btn">
                      <i class="i-carbon-download"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 实时状态指示器 -->
    <div class="real-time-indicator" :class="{ active: isLive }">
      <div class="indicator-dot"></div>
      <span>{{ isLive ? '实时同步' : '连接中断' }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import type {
  ChainExecution,
  ChainStep,
  ThinkingProcess,
  ExecutionLog,
  ExecutionResult,
  Artifact
} from '@/types/agent'

interface Props {
  execution?: ChainExecution | null
  autoStart?: boolean
  showToolbar?: boolean
  enableFullScreen?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  execution: null,
  autoStart: true,
  showToolbar: true,
  enableFullScreen: true
})

const emit = defineEmits<{
  'pause': []
  'resume': []
  'stop': []
  'step-select': [step: ChainStep]
  'export-result': [result: ExecutionResult]
}>()

// 响应式数据
const isFullScreen = ref(false)
const showThinkingProcess = ref(true)
const chainPanelCollapsed = ref(false)
const activeTab = ref<'thinking' | 'execution' | 'result'>('thinking')
const autoScroll = ref(true)
const selectedStep = ref<ChainStep | null>(null)
const expandedLogs = ref<Set<number>>(new Set())
const contentPanel = ref<HTMLElement>()
const isLive = ref(true)

// 模拟数据
const thinkingChain = ref<ThinkingProcess[]>([])
const executionLogs = ref<ExecutionLog[]>([])
const executionResult = ref<ExecutionResult | null>(null)

// 计算属性
const chainSteps = computed(() => {
  if (!props.execution?.steps) return []
  return props.execution.steps.sort((a, b) => (a.order || 0) - (b.order || 0))
})

const parallelSteps = computed(() => {
  return chainSteps.value.filter(step => step.type === 'parallel' && step.status === 'running')
})

const overallProgress = computed(() => {
  if (!chainSteps.value.length) return 0
  const completedSteps = chainSteps.value.filter(step => step.status === 'completed').length
  return (completedSteps / chainSteps.value.length) * 100
})

// 生命周期
onMounted(() => {
  if (props.autoStart && props.execution) {
    startMonitoring()
  }
})

onUnmounted(() => {
  stopMonitoring()
})

// 监听器
watch(() => props.execution, (newExecution) => {
  if (newExecution) {
    startMonitoring()
  } else {
    stopMonitoring()
  }
})

watch(autoScroll, (enabled) => {
  if (enabled) {
    scrollToBottom()
  }
})

// 方法
function startMonitoring() {
  // 模拟实时数据更新
  simulateThinkingProcess()
  simulateExecutionLogs()
}

function stopMonitoring() {
  isLive.value = false
}

function simulateThinkingProcess() {
  const thoughts: ThinkingProcess[] = [
    {
      agentName: '意图识别Agent',
      timestamp: new Date().toISOString(),
      analysis: '分析用户请求："分析我的销售数据"，识别出这是一个数据分析需求',
      reasoning: '根据关键词"分析"和"销售数据"，判断需要调用数据查询Agent和数据分析Agent',
      confidence: 0.95
    },
    {
      agentName: '数据查询Agent',
      timestamp: new Date(Date.now() + 1000).toISOString(),
      analysis: '开始查询销售数据库，检索最近3个月的销售记录',
      reasoning: '使用SQL查询获取销售表中的相关数据，包括产品、金额、时间等字段',
      confidence: 0.88
    },
    {
      agentName: '数据分析Agent',
      timestamp: new Date(Date.now() + 2000).toISOString(),
      analysis: '对查询到的销售数据进行统计分析，计算同环比、趋势等指标',
      reasoning: '应用时间序列分析和统计算法，识别销售趋势和关键指标',
      confidence: 0.92
    }
  ]

  thinkingChain.value = thoughts
}

function simulateExecutionLogs() {
  const logs: ExecutionLog[] = [
    {
      timestamp: new Date().toISOString(),
      level: 'INFO',
      agentName: '意图识别Agent',
      message: '开始处理用户请求',
      data: { userInput: '分析我的销售数据', sessionId: 'sess_123' }
    },
    {
      timestamp: new Date(Date.now() + 500).toISOString(),
      level: 'INFO',
      agentName: '意图识别Agent',
      message: '意图识别完成，置信度: 95%',
      data: { intent: 'data_analysis', confidence: 0.95, selectedAgents: ['data_query', 'data_analysis'] }
    },
    {
      timestamp: new Date(Date.now() + 1000).toISOString(),
      level: 'INFO',
      agentName: '数据查询Agent',
      message: '开始执行数据查询',
      data: { query: 'SELECT * FROM sales WHERE date >= DATE_SUB(NOW(), INTERVAL 3 MONTH)' }
    },
    {
      timestamp: new Date(Date.now() + 1500).toISOString(),
      level: 'SUCCESS',
      agentName: '数据查询Agent',
      message: '数据查询完成，共检索到 1,247 条记录',
      data: { recordCount: 1247, executionTime: '0.34s' }
    }
  ]

  executionLogs.value = logs
}

function toggleFullScreen() {
  isFullScreen.value = !isFullScreen.value
}

function toggleThinkingMode() {
  showThinkingProcess.value = !showThinkingProcess.value
}

function toggleChainPanel() {
  chainPanelCollapsed.value = !chainPanelCollapsed.value
}

function toggleAutoScroll() {
  autoScroll.value = !autoScroll.value
}

function clearLogs() {
  executionLogs.value = []
}

function pauseExecution() {
  emit('pause')
}

function resumeExecution() {
  emit('resume')
}

function stopExecution() {
  emit('stop')
}

function selectStep(step: ChainStep) {
  selectedStep.value = selectedStep.value?.id === step.id ? null : step
  emit('step-select', step)
}

function toggleLogData(index: number) {
  if (expandedLogs.value.has(index)) {
    expandedLogs.value.delete(index)
  } else {
    expandedLogs.value.add(index)
  }
}

function scrollToBottom() {
  nextTick(() => {
    if (contentPanel.value) {
      contentPanel.value.scrollTop = contentPanel.value.scrollHeight
    }
  })
}

// 工具函数
function getStepClass(step: ChainStep) {
  return {
    [`step-${step.status || 'pending'}`]: true,
    'step-selected': selectedStep.value?.id === step.id
  }
}

function getThinkingStepClass(thought: ThinkingProcess) {
  return {
    'high-confidence': thought.confidence >= 0.9,
    'medium-confidence': thought.confidence >= 0.7 && thought.confidence < 0.9,
    'low-confidence': thought.confidence < 0.7
  }
}

function getStatusIcon(status?: string) {
  switch (status) {
    case 'running': return 'i-carbon-in-progress'
    case 'completed': return 'i-carbon-checkmark-filled'
    case 'failed': return 'i-carbon-error-filled'
    case 'paused': return 'i-carbon-pause-filled'
    default: return 'i-carbon-circle-dash'
  }
}

function getStepIcon(status?: string) {
  return getStatusIcon(status)
}

function getStatusText(status?: string) {
  switch (status) {
    case 'running': return '执行中'
    case 'completed': return '已完成'
    case 'failed': return '执行失败'
    case 'paused': return '已暂停'
    default: return '等待中'
  }
}

function getArtifactIcon(type: string) {
  switch (type) {
    case 'chart': return 'i-carbon-chart-line'
    case 'report': return 'i-carbon-document'
    case 'data': return 'i-carbon-table'
    default: return 'i-carbon-document-attachment'
  }
}

function formatDuration(ms: number): string {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)

  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`
  } else {
    return `${seconds}s`
  }
}

function formatTimestamp(timestamp: string): string {
  return new Date(timestamp).toLocaleTimeString('zh-CN')
}

function formatLogData(data: any): string {
  return JSON.stringify(data, null, 2)
}

function formatResult(result: any): string {
  if (typeof result === 'string') return result
  return JSON.stringify(result, null, 2)
}

function formatFileSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB']
  let size = bytes
  let unitIndex = 0

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }

  return `${size.toFixed(1)} ${units[unitIndex]}`
}

function downloadArtifact(artifact: Artifact) {
  // 实现文件下载逻辑
  //console.log('Downloading artifact:', artifact)
}
</script>

<style scoped>
.chain-execution-view {
  @apply flex flex-col h-full bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden;
}

.chain-execution-view.full-screen {
  @apply fixed inset-0 z-50 rounded-none;
}

.execution-header {
  @apply flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-b border-gray-200 dark:border-gray-700;
}

.header-left {
  @apply flex items-center gap-4;
}

.execution-info {
  @apply space-y-1;
}

.execution-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white m-0 flex items-center gap-2;
}

.execution-meta {
  @apply flex items-center gap-3 text-sm;
}

.flow-name {
  @apply text-gray-600 dark:text-gray-400 font-medium;
}

.execution-status {
  @apply flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium;
}

.status-pending {
  @apply bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300;
}

.status-running {
  @apply bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300;
}

.status-completed {
  @apply bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300;
}

.status-failed {
  @apply bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300;
}

.status-paused {
  @apply bg-yellow-100 dark:bg-yellow-900 text-yellow-700 dark:text-yellow-300;
}

.execution-duration {
  @apply flex items-center gap-1 text-gray-600 dark:text-gray-400;
}

.header-actions {
  @apply flex items-center gap-2;
}

.action-btn {
  @apply flex items-center gap-1 px-3 py-2 text-sm font-medium rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors;
}

.action-btn.active {
  @apply bg-blue-500 text-white border-blue-500;
}

.pause-btn {
  @apply border-yellow-500 text-yellow-600 hover:bg-yellow-50 dark:hover:bg-yellow-900/20;
}

.resume-btn {
  @apply border-green-500 text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20;
}

.stop-btn {
  @apply border-red-500 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20;
}

.execution-progress {
  @apply px-4 py-3 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700;
}

.progress-info {
  @apply flex items-center justify-between mb-2;
}

.progress-label {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300;
}

.progress-stats {
  @apply text-sm text-gray-600 dark:text-gray-400 font-mono;
}

.progress-bar {
  @apply w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden;
}

.progress-fill {
  @apply h-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-500 ease-out;
}

.eta-info {
  @apply flex items-center gap-1 mt-2 text-sm text-gray-600 dark:text-gray-400;
}

.execution-content {
  @apply flex flex-1 overflow-hidden;
}

.chain-flow-panel {
  @apply w-80 bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col transition-all duration-300;
}

.chain-flow-panel.collapsed {
  @apply w-12;
}

.panel-header {
  @apply flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900;
}

.panel-header h4 {
  @apply text-sm font-medium text-gray-900 dark:text-white m-0;
}

.collapse-btn {
  @apply w-6 h-6 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 flex items-center justify-center text-gray-500 dark:text-gray-400 transition-colors;
}

.panel-content {
  @apply flex-1 overflow-y-auto p-3;
}

.agent-chain {
  @apply space-y-3;
}

.chain-step {
  @apply relative p-3 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-sm;
}

.chain-step.step-selected {
  @apply ring-2 ring-blue-500 border-blue-500;
}

.chain-step.step-running {
  @apply border-blue-500 bg-blue-50 dark:bg-blue-900/10;
}

.chain-step.step-completed {
  @apply border-green-500 bg-green-50 dark:bg-green-900/10;
}

.chain-step.step-failed {
  @apply border-red-500 bg-red-50 dark:bg-red-900/10;
}

.step-node {
  @apply flex items-center gap-3 mb-2;
}

.step-number {
  @apply w-6 h-6 bg-gray-500 text-white text-xs rounded-full flex items-center justify-center font-medium;
}

.step-running .step-number {
  @apply bg-blue-500;
}

.step-completed .step-number {
  @apply bg-green-500;
}

.step-failed .step-number {
  @apply bg-red-500;
}

.step-icon {
  @apply text-lg;
}

.step-info {
  @apply space-y-1;
}

.step-name {
  @apply text-sm font-medium text-gray-900 dark:text-white;
}

.step-description {
  @apply text-xs text-gray-600 dark:text-gray-400;
}

.step-progress {
  @apply flex items-center gap-2;
}

.progress-mini {
  @apply flex-1 h-1 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden;
}

.progress-mini-fill {
  @apply h-full bg-blue-500 transition-all duration-300;
}

.progress-text {
  @apply text-xs text-gray-600 dark:text-gray-400 font-mono;
}

.step-duration {
  @apply flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400;
}

.step-connector {
  @apply absolute left-3 -bottom-3 w-0.5 h-3 bg-gray-300 dark:bg-gray-600;
}

.connector-line.active {
  @apply bg-green-500;
}

.parallel-execution {
  @apply mt-6 p-3 bg-purple-50 dark:bg-purple-900/10 border border-purple-200 dark:border-purple-800 rounded-lg;
}

.parallel-execution h5 {
  @apply text-sm font-medium text-purple-700 dark:text-purple-300 m-0 mb-2 flex items-center gap-1;
}

.parallel-steps {
  @apply space-y-2;
}

.parallel-step {
  @apply flex items-center gap-2 p-2 bg-white dark:bg-gray-800 rounded text-sm;
}

.parallel-step i {
  @apply w-4 h-4;
}

.parallel-progress {
  @apply ml-auto text-xs text-gray-500 dark:text-gray-400 font-mono;
}

.thinking-process-panel {
  @apply flex-1 flex flex-col;
}

.header-tabs {
  @apply flex gap-1;
}

.tab-btn {
  @apply flex items-center gap-1 px-3 py-2 text-sm font-medium rounded-t-lg border-b-2 border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors;
}

.tab-btn.active {
  @apply text-blue-600 dark:text-blue-400 border-blue-500 bg-blue-50 dark:bg-blue-900/10;
}

.tab-actions {
  @apply flex gap-2;
}

.thinking-content,
.execution-logs,
.execution-results {
  @apply h-full overflow-y-auto p-4;
}

.empty-thinking,
.empty-logs,
.empty-result {
  @apply flex flex-col items-center justify-center h-32 text-gray-500 dark:text-gray-400;
}

.empty-thinking i,
.empty-logs i,
.empty-result i {
  @apply text-3xl mb-2;
}

.thinking-chain {
  @apply space-y-4;
}

.thinking-step {
  @apply p-4 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg;
}

.thinking-step.high-confidence {
  @apply border-green-300 dark:border-green-700 bg-green-50 dark:bg-green-900/10;
}

.thinking-step.medium-confidence {
  @apply border-yellow-300 dark:border-yellow-700 bg-yellow-50 dark:bg-yellow-900/10;
}

.thinking-step.low-confidence {
  @apply border-red-300 dark:border-red-700 bg-red-50 dark:bg-red-900/10;
}

.thinking-header {
  @apply flex items-center justify-between mb-3;
}

.thinking-agent {
  @apply flex items-center gap-1 text-sm font-medium text-gray-900 dark:text-white;
}

.thinking-timestamp {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

.thinking-content-text {
  @apply space-y-3;
}

.thinking-analysis,
.thinking-reasoning {
  @apply space-y-1;
}

.thinking-analysis h6,
.thinking-reasoning h6 {
  @apply text-xs font-medium text-gray-700 dark:text-gray-300 m-0;
}

.thinking-analysis p,
.thinking-reasoning p {
  @apply text-sm text-gray-900 dark:text-white m-0;
}

.thinking-confidence {
  @apply flex items-center gap-2;
}

.confidence-label {
  @apply text-xs text-gray-600 dark:text-gray-400;
}

.confidence-bar {
  @apply flex-1 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden;
}

.confidence-fill {
  @apply h-full bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 transition-all duration-300;
}

.confidence-value {
  @apply text-xs font-mono text-gray-600 dark:text-gray-400;
}

.log-entries {
  @apply space-y-2;
}

.log-entry {
  @apply p-3 bg-gray-50 dark:bg-gray-800 border-l-4 border-gray-300 dark:border-gray-600 rounded-r;
}

.log-info {
  @apply border-blue-500;
}

.log-success {
  @apply border-green-500;
}

.log-warning {
  @apply border-yellow-500;
}

.log-error {
  @apply border-red-500;
}

.log-entry > div {
  @apply text-sm;
}

.log-timestamp {
  @apply text-xs text-gray-500 dark:text-gray-400 font-mono;
}

.log-level {
  @apply inline-block px-2 py-1 text-xs font-medium rounded;
}

.log-info .log-level {
  @apply bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300;
}

.log-success .log-level {
  @apply bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300;
}

.log-warning .log-level {
  @apply bg-yellow-100 dark:bg-yellow-900 text-yellow-700 dark:text-yellow-300;
}

.log-error .log-level {
  @apply bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300;
}

.log-agent {
  @apply text-gray-700 dark:text-gray-300 font-medium;
}

.log-message {
  @apply text-gray-900 dark:text-white;
}

.log-data {
  @apply mt-2;
}

.data-toggle {
  @apply flex items-center gap-1 text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors;
}

.log-data-content {
  @apply mt-2 p-2 bg-gray-900 dark:bg-gray-950 text-green-400 text-xs rounded font-mono overflow-auto max-h-32;
}

.result-content {
  @apply space-y-6;
}

.result-summary h5,
.result-details h5,
.result-artifacts h5 {
  @apply text-base font-medium text-gray-900 dark:text-white m-0 mb-3;
}

.summary-stats {
  @apply grid grid-cols-1 md:grid-cols-3 gap-4;
}

.stat-item {
  @apply flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg;
}

.stat-label {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.stat-value {
  @apply text-sm font-medium text-gray-900 dark:text-white;
}

.result-data pre {
  @apply p-3 bg-gray-900 dark:bg-gray-950 text-green-400 text-sm rounded font-mono overflow-auto max-h-64;
}

.artifact-list {
  @apply space-y-2;
}

.artifact-item {
  @apply flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg;
}

.artifact-name {
  @apply flex-1 text-sm text-gray-900 dark:text-white;
}

.artifact-size {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

.download-btn {
  @apply w-8 h-8 rounded bg-blue-500 hover:bg-blue-600 text-white flex items-center justify-center transition-colors;
}

.real-time-indicator {
  @apply absolute top-4 right-4 flex items-center gap-1 px-2 py-1 text-xs bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-full shadow-sm;
}

.real-time-indicator.active {
  @apply text-green-600 dark:text-green-400;
}

.real-time-indicator:not(.active) {
  @apply text-red-600 dark:text-red-400;
}

.indicator-dot {
  @apply w-2 h-2 rounded-full;
}

.real-time-indicator.active .indicator-dot {
  @apply bg-green-500 animate-pulse;
}

.real-time-indicator:not(.active) .indicator-dot {
  @apply bg-red-500;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (width <= 1024px) {
  .chain-flow-panel {
    @apply w-64;
  }

  .summary-stats {
    @apply grid-cols-1;
  }
}

@media (width <= 768px) {
  .execution-header {
    @apply flex-col items-stretch gap-3;
  }

  .header-actions {
    @apply justify-center;
  }

  .execution-content {
    @apply flex-col;
  }

  .chain-flow-panel {
    @apply w-full h-64 border-r-0 border-b border-gray-200 dark:border-gray-700;
  }

  .header-tabs {
    @apply justify-center;
  }
}
</style>
