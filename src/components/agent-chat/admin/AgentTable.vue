<template>
  <div class="agent-table">
    <div class="table-container">
      <table class="table">
        <thead class="table-header">
          <tr>
            <th>
              <input
                type="checkbox"
                v-model="selectAll"
                @change="handleSelectAll"
                class="checkbox"
              >
            </th>
            <th @click="handleSort('name')" class="sortable">
              <div class="header-cell">
                <span>名称</span>
                <i :class="getSortIcon('name')" class="sort-icon"></i>
              </div>
            </th>
            <th>类别</th>
            <th>状态</th>
            <th>能力</th>
            <th @click="handleSort('priority')" class="sortable">
              <div class="header-cell">
                <span>优先级</span>
                <i :class="getSortIcon('priority')" class="sort-icon"></i>
              </div>
            </th>
            <th @click="handleSort('updatedAt')" class="sortable">
              <div class="header-cell">
                <span>更新时间</span>
                <i :class="getSortIcon('updatedAt')" class="sort-icon"></i>
              </div>
            </th>
            <th class="actions-column">操作</th>
          </tr>
        </thead>

        <tbody class="table-body">
          <!-- 加载状态 -->
          <tr v-if="loading" class="loading-row">
            <td colspan="8" class="loading-cell">
              <div class="loading-content">
                <i class="i-carbon-in-progress animate-spin loading-icon"></i>
                <span>加载中...</span>
              </div>
            </td>
          </tr>

          <!-- 空状态 -->
          <tr v-else-if="agents.length === 0" class="empty-row">
            <td colspan="8" class="empty-cell">
              <div class="empty-content">
                <i class="i-carbon-robot empty-icon"></i>
                <h4>暂无 Agent</h4>
                <p>还没有创建任何 Agent，点击上方按钮开始创建。</p>
              </div>
            </td>
          </tr>

          <!-- Agent 数据行 -->
          <tr
            v-for="agent in agents"
            :key="agent.id"
            class="data-row"
            :class="{
              selected: selectedIds.includes(agent.id!),
              disabled: agent.status !== 'ACTIVE'
            }"
            @click="handleRowClick(agent)"
          >
            <td @click.stop>
              <input
                type="checkbox"
                :checked="selectedIds.includes(agent.id!)"
                @change="handleSelect(agent.id!)"
                class="checkbox"
              >
            </td>

            <td class="name-cell">
              <div class="agent-info">
                <div class="agent-avatar">
                  <i :class="getCategoryIcon(agent.category)"></i>
                </div>
                <div class="agent-details">
                  <div class="agent-name">{{ agent.name }}</div>
                  <div class="agent-description">{{ agent.description }}</div>
                </div>
              </div>
            </td>

            <td>
              <span class="category-tag" :class="`category-${agent.category?.toLowerCase()}`">
                {{ getCategoryLabel(agent.category) }}
              </span>
            </td>

            <td>
              <span class="status-badge" :class="`status-${agent.status?.toLowerCase()}`">
                <div class="status-indicator"></div>
                {{ getStatusLabel(agent.status) }}
              </span>
            </td>

            <td>
              <div class="capabilities-cell">
                <div class="capability-tags">
                  <span
                    v-for="capability in getDisplayCapabilities(agent.capabilities)"
                    :key="capability"
                    class="capability-tag"
                  >
                    {{ getCapabilityLabel(capability) }}
                  </span>
                  <span
                    v-if="agent.capabilities && agent.capabilities.length > 2"
                    class="capability-more"
                    :title="getFullCapabilities(agent.capabilities)"
                  >
                    +{{ agent.capabilities.length - 2 }}
                  </span>
                </div>
              </div>
            </td>

            <td>
              <div class="priority-cell">
                <div class="priority-bar">
                  <div
                    class="priority-fill"
                    :style="{ width: `${agent.priority || 50}%` }"
                  ></div>
                </div>
                <span class="priority-value">{{ agent.priority || 50 }}</span>
              </div>
            </td>

            <td>
              <div class="time-cell">
                <div class="time-value">{{ formatTime(agent.updatedAt || agent.createdAt) }}</div>
                <div class="time-relative">{{ formatRelativeTime(agent.updatedAt || agent.createdAt) }}</div>
              </div>
            </td>

            <td @click.stop>
              <div class="actions-cell">
                <button
                  @click="toggleStatus(agent)"
                  class="action-btn"
                  :class="{ active: agent.status === 'ACTIVE' }"
                  :title="agent.status === 'ACTIVE' ? '禁用' : '启用'"
                >
                  <i :class="agent.status === 'ACTIVE' ? 'i-carbon-pause' : 'i-carbon-play'"></i>
                </button>

                <button
                  @click="$emit('edit', agent)"
                  class="action-btn"
                  title="编辑"
                >
                  <i class="i-carbon-edit"></i>
                </button>

                <button
                  @click="showRowActions(agent, $event)"
                  class="action-btn"
                  title="更多操作"
                >
                  <i class="i-carbon-overflow-menu-horizontal"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 批量操作栏 -->
    <div v-if="selectedIds.length > 0" class="bulk-actions">
      <div class="bulk-info">
        <span>已选择 {{ selectedIds.length }} 个 Agent</span>
        <button @click="clearSelection" class="clear-btn">清除选择</button>
      </div>

      <div class="bulk-buttons">
        <button @click="bulkEnable" class="bulk-btn">
          <i class="i-carbon-play"></i>
          批量启用
        </button>
        <button @click="bulkDisable" class="bulk-btn">
          <i class="i-carbon-pause"></i>
          批量禁用
        </button>
        <button @click="bulkExport" class="bulk-btn">
          <i class="i-carbon-download"></i>
          批量导出
        </button>
        <button @click="bulkDelete" class="bulk-btn danger">
          <i class="i-carbon-trash-can"></i>
          批量删除
        </button>
      </div>
    </div>

    <!-- 行操作菜单 -->
    <div
      v-if="contextMenu.visible"
      ref="contextMenuRef"
      class="context-menu"
      :style="{ top: contextMenu.y + 'px', left: contextMenu.x + 'px' }"
    >
      <button @click="duplicateAgent" class="menu-item">
        <i class="i-carbon-copy"></i>
        <span>复制</span>
      </button>
      <button @click="testAgent" class="menu-item">
        <i class="i-carbon-play-filled"></i>
        <span>测试</span>
      </button>
      <button @click="exportAgent" class="menu-item">
        <i class="i-carbon-download"></i>
        <span>导出</span>
      </button>
      <div class="menu-divider"></div>
      <button @click="deleteAgent" class="menu-item danger">
        <i class="i-carbon-trash-can"></i>
        <span>删除</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import type { AgentDefinition } from '@/api/agentManagement'

interface Props {
  agents: AgentDefinition[]
  loading?: boolean
  sortField?: string
  sortOrder?: 'asc' | 'desc'
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  sortField: 'updatedAt',
  sortOrder: 'desc'
})

const emit = defineEmits<{
  edit: [agent: AgentDefinition]
  delete: [agent: AgentDefinition]
  duplicate: [agent: AgentDefinition]
  'toggle-status': [agent: AgentDefinition]
  sort: [field: string, order: 'asc' | 'desc']
  'bulk-action': [action: string, agents: AgentDefinition[]]
}>()

// 响应式数据
const selectedIds = ref<number[]>([])
const contextMenu = ref({
  visible: false,
  x: 0,
  y: 0,
  agent: null as AgentDefinition | null
})
const contextMenuRef = ref<HTMLElement>()

// 计算属性
const selectAll = computed({
  get: () => props.agents.length > 0 && selectedIds.value.length === props.agents.length,
  set: () => {}
})

const selectedAgents = computed(() =>
  props.agents.filter(agent => selectedIds.value.includes(agent.id!))
)

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// 方法
function getCategoryIcon(category?: string): string {
  const icons = {
    ANALYSIS: 'i-carbon-analytics',
    PROCESSING: 'i-carbon-data-processing',
    INFERENCE: 'i-carbon-machine-learning',
    REPORTING: 'i-carbon-document',
    VISUALIZATION: 'i-carbon-chart-line'
  }
  return icons[category as keyof typeof icons] || 'i-carbon-robot'
}

function getCategoryLabel(category?: string): string {
  const labels = {
    ANALYSIS: '数据分析',
    PROCESSING: '数据处理',
    INFERENCE: '推理',
    REPORTING: '报告生成',
    VISUALIZATION: '可视化'
  }
  return labels[category as keyof typeof labels] || category || '未分类'
}

function getStatusLabel(status?: string): string {
  const labels = {
    ACTIVE: '启用',
    INACTIVE: '禁用',
    DRAFT: '草稿'
  }
  return labels[status as keyof typeof labels] || status || '未知'
}

function getCapabilityLabel(capability: string): string {
  const labels = {
    data_analysis: '数据分析',
    chart_generation: '图表生成',
    report_writing: '报告撰写',
    data_processing: '数据处理',
    pattern_recognition: '模式识别',
    prediction: '预测分析'
  }
  return labels[capability as keyof typeof labels] || capability
}

function getDisplayCapabilities(capabilities?: string[]): string[] {
  if (!capabilities) return []
  return capabilities.slice(0, 2)
}

function getFullCapabilities(capabilities?: string[]): string {
  if (!capabilities) return ''
  return capabilities.map(cap => getCapabilityLabel(cap)).join(', ')
}

function formatTime(timeString?: string): string {
  if (!timeString) return '未知'

  const time = new Date(timeString)
  return time.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

function formatRelativeTime(timeString?: string): string {
  if (!timeString) return ''

  const time = new Date(timeString)
  const now = new Date()
  const diff = now.getTime() - time.getTime()

  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`

  return ''
}

function getSortIcon(field: string): string {
  if (props.sortField !== field) return 'i-carbon-chevron-sort'

  return props.sortOrder === 'asc'
    ? 'i-carbon-chevron-up'
    : 'i-carbon-chevron-down'
}

function handleSort(field: string) {
  const order = props.sortField === field && props.sortOrder === 'asc' ? 'desc' : 'asc'
  emit('sort', field, order)
}

function handleSelectAll() {
  if (selectAll.value) {
    selectedIds.value = []
  } else {
    selectedIds.value = props.agents.map(agent => agent.id!).filter(id => id !== undefined)
  }
}

function handleSelect(agentId: number) {
  const index = selectedIds.value.indexOf(agentId)
  if (index > -1) {
    selectedIds.value.splice(index, 1)
  } else {
    selectedIds.value.push(agentId)
  }
}

function handleRowClick(agent: AgentDefinition) {
  emit('edit', agent)
}

function clearSelection() {
  selectedIds.value = []
}

function toggleStatus(agent: AgentDefinition) {
  emit('toggle-status', agent)
}

function showRowActions(agent: AgentDefinition, event: MouseEvent) {
  contextMenu.value = {
    visible: true,
    x: event.clientX,
    y: event.clientY,
    agent
  }
}

function hideContextMenu() {
  contextMenu.value.visible = false
  contextMenu.value.agent = null
}

function handleClickOutside(event: Event) {
  if (contextMenu.value.visible &&
      contextMenuRef.value &&
      !contextMenuRef.value.contains(event.target as Node)) {
    hideContextMenu()
  }
}

function duplicateAgent() {
  if (contextMenu.value.agent) {
    emit('duplicate', contextMenu.value.agent)
  }
  hideContextMenu()
}

function testAgent() {
  //console.log('Test agent:', contextMenu.value.agent?.id)
  hideContextMenu()
}

function exportAgent() {
  if (contextMenu.value.agent) {
    const data = JSON.stringify(contextMenu.value.agent, null, 2)
    const blob = new Blob([data], { type: 'application/json' })
    const url = URL.createObjectURL(blob)

    const a = document.createElement('a')
    a.href = url
    a.download = `agent-${contextMenu.value.agent.name}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)

    URL.revokeObjectURL(url)
  }
  hideContextMenu()
}

function deleteAgent() {
  if (contextMenu.value.agent) {
    emit('delete', contextMenu.value.agent)
  }
  hideContextMenu()
}

// 批量操作
function bulkEnable() {
  emit('bulk-action', 'enable', selectedAgents.value)
  clearSelection()
}

function bulkDisable() {
  emit('bulk-action', 'disable', selectedAgents.value)
  clearSelection()
}

function bulkExport() {
  const data = JSON.stringify(selectedAgents.value, null, 2)
  const blob = new Blob([data], { type: 'application/json' })
  const url = URL.createObjectURL(blob)

  const a = document.createElement('a')
  a.href = url
  a.download = `agents-batch-export.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)

  URL.revokeObjectURL(url)
  clearSelection()
}

function bulkDelete() {
  emit('bulk-action', 'delete', selectedAgents.value)
  clearSelection()
}
</script>

<style scoped>
.agent-table {
  @apply relative;
}

.table-container {
  @apply overflow-x-auto border border-gray-200 dark:border-gray-700 rounded-lg;
}

.table {
  @apply w-full table-auto;
}

.table-header {
  @apply bg-gray-50 dark:bg-gray-900;
}

.table-header th {
  @apply px-4 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wider border-b border-gray-200 dark:border-gray-700;
}

.table-header th.sortable {
  @apply cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors;
}

.header-cell {
  @apply flex items-center gap-1;
}

.sort-icon {
  @apply text-gray-400 dark:text-gray-500;
}

.actions-column {
  @apply w-32;
}

.table-body {
  @apply bg-white dark:bg-gray-800;
}

.loading-row,
.empty-row {
  @apply h-64;
}

.loading-cell,
.empty-cell {
  @apply text-center;
}

.loading-content,
.empty-content {
  @apply flex flex-col items-center justify-center space-y-3 text-gray-500 dark:text-gray-400;
}

.loading-icon {
  @apply text-2xl;
}

.empty-icon {
  @apply text-4xl;
}

.empty-content h4 {
  @apply text-lg font-medium text-gray-900 dark:text-white m-0;
}

.empty-content p {
  @apply text-sm m-0;
}

.data-row {
  @apply border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer;
}

.data-row.selected {
  @apply bg-blue-50 dark:bg-blue-900/20;
}

.data-row.disabled {
  @apply opacity-60;
}

.data-row td {
  @apply px-4 py-3 whitespace-nowrap;
}

.checkbox {
  @apply w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600;
}

.name-cell {
  @apply max-w-xs;
}

.agent-info {
  @apply flex items-center gap-3;
}

.agent-avatar {
  @apply w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center text-blue-600 dark:text-blue-400 flex-shrink-0;
}

.agent-details {
  @apply min-w-0 flex-1;
}

.agent-name {
  @apply font-medium text-gray-900 dark:text-white truncate;
}

.agent-description {
  @apply text-sm text-gray-500 dark:text-gray-400 truncate;
}

.category-tag {
  @apply inline-flex items-center px-2 py-1 text-xs font-medium rounded-full;
}

.category-analysis {
  @apply bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300;
}

.category-processing {
  @apply bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300;
}

.category-inference {
  @apply bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300;
}

.category-reporting {
  @apply bg-orange-100 dark:bg-orange-900 text-orange-700 dark:text-orange-300;
}

.category-visualization {
  @apply bg-pink-100 dark:bg-pink-900 text-pink-700 dark:text-pink-300;
}

.status-badge {
  @apply inline-flex items-center gap-1.5 px-2 py-1 text-xs font-medium rounded-full;
}

.status-active {
  @apply bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300;
}

.status-active .status-indicator {
  @apply w-2 h-2 bg-green-500 rounded-full;
}

.status-inactive {
  @apply bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300;
}

.status-inactive .status-indicator {
  @apply w-2 h-2 bg-gray-400 rounded-full;
}

.status-draft {
  @apply bg-yellow-100 dark:bg-yellow-900 text-yellow-700 dark:text-yellow-300;
}

.status-draft .status-indicator {
  @apply w-2 h-2 bg-yellow-500 rounded-full;
}

.capabilities-cell {
  @apply max-w-xs;
}

.capability-tags {
  @apply flex flex-wrap gap-1;
}

.capability-tag {
  @apply inline-flex items-center px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded;
}

.capability-more {
  @apply inline-flex items-center px-2 py-1 text-xs bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-400 rounded cursor-help;
}

.priority-cell {
  @apply flex items-center gap-2;
}

.priority-bar {
  @apply w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden;
}

.priority-fill {
  @apply h-full bg-blue-500 transition-all duration-300;
}

.priority-value {
  @apply text-xs text-gray-600 dark:text-gray-400 font-medium;
}

.time-cell {
  @apply space-y-1;
}

.time-value {
  @apply text-sm text-gray-900 dark:text-white;
}

.time-relative {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

.actions-cell {
  @apply flex items-center gap-1;
}

.action-btn {
  @apply w-7 h-7 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 flex items-center justify-center text-gray-600 dark:text-gray-400 transition-colors;
}

.action-btn.active {
  @apply bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400;
}

.bulk-actions {
  @apply flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 border-x border-b border-gray-200 dark:border-gray-700 rounded-b-lg;
}

.bulk-info {
  @apply flex items-center gap-3;
}

.bulk-info span {
  @apply text-sm font-medium text-blue-700 dark:text-blue-300;
}

.clear-btn {
  @apply text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors;
}

.bulk-buttons {
  @apply flex gap-2;
}

.bulk-btn {
  @apply flex items-center gap-1 px-3 py-1.5 text-sm bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors;
}

.bulk-btn.danger {
  @apply text-red-600 dark:text-red-400 border-red-300 dark:border-red-600 hover:bg-red-50 dark:hover:bg-red-900/20;
}

.context-menu {
  @apply fixed bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg py-1 z-50 min-w-[120px];
}

.menu-item {
  @apply w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left;
}

.menu-item.danger {
  @apply text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20;
}

.menu-divider {
  @apply h-px bg-gray-200 dark:bg-gray-700 my-1;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
