<template>
  <div class="log-viewer" :style="{ height }">
    <div class="log-header">
      <div class="log-controls">
        <div class="control-group">
          <label>级别筛选:</label>
          <select v-model="levelFilter" class="level-select">
            <option value="">所有级别</option>
            <option value="ERROR">ERROR</option>
            <option value="WARN">WARN</option>
            <option value="INFO">INFO</option>
            <option value="DEBUG">DEBUG</option>
          </select>
        </div>

        <div class="control-group">
          <label>来源筛选:</label>
          <select v-model="sourceFilter" class="source-select">
            <option value="">所有来源</option>
            <option
              v-for="source in uniqueSources"
              :key="source"
              :value="source"
            >
              {{ source }}
            </option>
          </select>
        </div>

        <div class="control-group">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索日志..."
            class="search-input"
          >
        </div>
      </div>

      <div class="log-actions">
        <button @click="toggleAutoScroll" class="action-btn" :class="{ active: autoScroll }">
          <i class="i-carbon-arrow-down"></i>
          自动滚动
        </button>
        <button @click="clearLogs" class="action-btn">
          <i class="i-carbon-clean"></i>
          清空
        </button>
        <button @click="downloadLogs" class="action-btn">
          <i class="i-carbon-download"></i>
          下载
        </button>
      </div>
    </div>

    <div class="log-content" ref="logContainer">
      <div v-if="loading" class="log-loading">
        <i class="i-carbon-in-progress animate-spin"></i>
        <span>加载日志中...</span>
      </div>

      <div v-else-if="filteredLogs.length === 0" class="log-empty">
        <i class="i-carbon-document-blank"></i>
        <span>{{ getEmptyMessage() }}</span>
      </div>

      <div v-else class="log-list">
        <div
          v-for="(log, index) in displayedLogs"
          :key="log.id || index"
          class="log-entry"
          :class="[
            `level-${log.level.toLowerCase()}`,
            { expanded: expandedLogs.includes(log.id || index) }
          ]"
          @click="toggleLogExpansion(log.id || index)"
        >
          <div class="log-main">
            <div class="log-timestamp">
              {{ formatTime(log.timestamp) }}
            </div>

            <div class="log-level" :class="`level-${log.level.toLowerCase()}`">
              <i :class="getLevelIcon(log.level)"></i>
              {{ log.level }}
            </div>

            <div v-if="log.source" class="log-source">
              {{ log.source }}
            </div>

            <div class="log-message">
              <span v-html="highlightSearch(log.message)"></span>
              <button
                v-if="log.details || isLongMessage(log.message)"
                class="expand-btn"
                @click.stop="toggleLogExpansion(log.id || index)"
              >
                <i :class="expandedLogs.includes(log.id || index) ? 'i-carbon-chevron-up' : 'i-carbon-chevron-down'"></i>
              </button>
            </div>
          </div>

          <div
            v-if="expandedLogs.includes(log.id || index)"
            class="log-details"
          >
            <div v-if="isLongMessage(log.message)" class="full-message">
              <h6>完整消息:</h6>
              <pre>{{ log.message }}</pre>
            </div>

            <div v-if="log.details" class="log-extra">
              <h6>详细信息:</h6>
              <pre>{{ formatDetails(log.details) }}</pre>
            </div>

            <div class="log-actions-expanded">
              <button @click="copyLog(log)" class="copy-btn">
                <i class="i-carbon-copy"></i>
                复制
              </button>
              <button @click="searchSimilar(log)" class="search-btn">
                <i class="i-carbon-search"></i>
                相似日志
              </button>
            </div>
          </div>
        </div>

        <!-- 加载更多 -->
        <div v-if="hasMore" class="load-more">
          <button @click="loadMore" class="load-more-btn" :disabled="loadingMore">
            <i v-if="loadingMore" class="i-carbon-in-progress animate-spin"></i>
            {{ loadingMore ? '加载中...' : '加载更多' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="log-stats">
      <div class="stats-item">
        <span class="stats-label">总计:</span>
        <span class="stats-value">{{ filteredLogs.length }}</span>
      </div>
      <div class="stats-item level-error">
        <span class="stats-label">错误:</span>
        <span class="stats-value">{{ getLogCountByLevel('ERROR') }}</span>
      </div>
      <div class="stats-item level-warn">
        <span class="stats-label">警告:</span>
        <span class="stats-value">{{ getLogCountByLevel('WARN') }}</span>
      </div>
      <div class="stats-item level-info">
        <span class="stats-label">信息:</span>
        <span class="stats-value">{{ getLogCountByLevel('INFO') }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import type { LogEntry } from '@/api/agentManagement'

interface Props {
  logs: LogEntry[]
  height?: string
  autoScroll?: boolean
  loading?: boolean
  pageSize?: number
}

const props = withDefaults(defineProps<Props>(), {
  height: '400px',
  autoScroll: false,
  pageSize: 100
})

const emit = defineEmits<{
  clear: []
  loadMore: []
  searchSimilar: [log: LogEntry]
}>()

// 响应式数据
const logContainer = ref<HTMLElement>()
const levelFilter = ref('')
const sourceFilter = ref('')
const searchQuery = ref('')
const expandedLogs = ref<(string | number)[]>([])
const autoScroll = ref(props.autoScroll)
const currentPage = ref(1)
const loadingMore = ref(false)

// 计算属性
const uniqueSources = computed(() => {
  const sources = new Set<string>()
  props.logs.forEach(log => {
    if (log.source) {
      sources.add(log.source)
    }
  })
  return Array.from(sources).sort()
})

const filteredLogs = computed(() => {
  let result = props.logs

  // 级别筛选
  if (levelFilter.value) {
    result = result.filter(log => log.level === levelFilter.value)
  }

  // 来源筛选
  if (sourceFilter.value) {
    result = result.filter(log => log.source === sourceFilter.value)
  }

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(log =>
      log.message.toLowerCase().includes(query) ||
      (log.source && log.source.toLowerCase().includes(query))
    )
  }

  return result.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
})

const displayedLogs = computed(() => {
  const endIndex = currentPage.value * props.pageSize
  return filteredLogs.value.slice(0, endIndex)
})

const hasMore = computed(() => {
  return displayedLogs.value.length < filteredLogs.value.length
})

// 监听器
watch(() => props.logs, () => {
  if (autoScroll.value) {
    nextTick(() => scrollToBottom())
  }
}, { deep: true })

watch(autoScroll, (newVal) => {
  if (newVal) {
    nextTick(() => scrollToBottom())
  }
})

// 生命周期
onMounted(() => {
  if (autoScroll.value) {
    nextTick(() => scrollToBottom())
  }
})

// 方法
function formatTime(timestamp: string): string {
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    month: 'short',
    day: 'numeric'
  })
}

function getLevelIcon(level: string): string {
  switch (level.toUpperCase()) {
    case 'ERROR':
      return 'i-carbon-error-filled'
    case 'WARN':
      return 'i-carbon-warning-filled'
    case 'INFO':
      return 'i-carbon-information-filled'
    case 'DEBUG':
      return 'i-carbon-debug'
    default:
      return 'i-carbon-circle-filled'
  }
}

function highlightSearch(text: string): string {
  if (!searchQuery.value) return text

  const query = searchQuery.value
  const regex = new RegExp(`(${query})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

function isLongMessage(message: string): boolean {
  return message.length > 200 || message.includes('\n')
}

function formatDetails(details: Record<string, any>): string {
  return JSON.stringify(details, null, 2)
}

function toggleLogExpansion(id: string | number) {
  const index = expandedLogs.value.indexOf(id)
  if (index > -1) {
    expandedLogs.value.splice(index, 1)
  } else {
    expandedLogs.value.push(id)
  }
}

function toggleAutoScroll() {
  autoScroll.value = !autoScroll.value
}

function scrollToBottom() {
  if (!logContainer.value) return

  logContainer.value.scrollTop = logContainer.value.scrollHeight
}

function clearLogs() {
  emit('clear')
  expandedLogs.value = []
  currentPage.value = 1
}

function downloadLogs() {
  const logText = filteredLogs.value
    .map(log => `${log.timestamp} [${log.level}] ${log.source || ''}: ${log.message}`)
    .join('\n')

  const blob = new Blob([logText], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)

  const a = document.createElement('a')
  a.href = url
  a.download = `logs-${new Date().toISOString().slice(0, 19)}.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)

  URL.revokeObjectURL(url)
}

async function loadMore() {
  if (loadingMore.value || !hasMore.value) return

  loadingMore.value = true

  try {
    currentPage.value += 1
    emit('loadMore')

    // 模拟加载延迟
    await new Promise(resolve => setTimeout(resolve, 500))
  } finally {
    loadingMore.value = false
  }
}

function copyLog(log: LogEntry) {
  const logText = `${log.timestamp} [${log.level}] ${log.source || ''}: ${log.message}`

  navigator.clipboard.writeText(logText).then(() => {
    // 这里可以显示复制成功的提示
    //console.log('日志已复制到剪贴板')
  }).catch(err => {
    console.error('复制失败:', err)
  })
}

function searchSimilar(log: LogEntry) {
  emit('searchSimilar', log)
}

function getLogCountByLevel(level: string): number {
  return filteredLogs.value.filter(log => log.level === level).length
}

function getEmptyMessage(): string {
  if (searchQuery.value) {
    return `未找到包含 "${searchQuery.value}" 的日志`
  }
  if (levelFilter.value || sourceFilter.value) {
    return '无符合筛选条件的日志'
  }
  return '暂无日志数据'
}
</script>

<style scoped>
.log-viewer {
  @apply flex flex-col bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden;
}

.log-header {
  @apply flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900;
}

.log-controls {
  @apply flex items-center gap-4;
}

.control-group {
  @apply flex items-center gap-2;
}

.control-group label {
  @apply text-sm text-gray-600 dark:text-gray-400 whitespace-nowrap;
}

.level-select,
.source-select {
  @apply px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white;
}

.search-input {
  @apply px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400;
}

.log-actions {
  @apply flex items-center gap-2;
}

.action-btn {
  @apply flex items-center gap-1 px-3 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors;
}

.action-btn.active {
  @apply bg-blue-500 text-white border-blue-500;
}

.log-content {
  @apply flex-1 overflow-y-auto;
}

.log-loading,
.log-empty {
  @apply flex flex-col items-center justify-center py-12 text-gray-500 dark:text-gray-400;
}

.log-loading i,
.log-empty i {
  @apply text-2xl mb-2;
}

.log-list {
  @apply divide-y divide-gray-200 dark:divide-gray-700;
}

.log-entry {
  @apply hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors;
}

.log-entry.expanded {
  @apply bg-gray-50 dark:bg-gray-700;
}

.log-main {
  @apply flex items-center gap-3 p-3;
}

.log-timestamp {
  @apply text-xs text-gray-500 dark:text-gray-400 font-mono whitespace-nowrap;
}

.log-level {
  @apply flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full whitespace-nowrap;
}

.log-level.level-error {
  @apply bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300;
}

.log-level.level-warn {
  @apply bg-yellow-100 dark:bg-yellow-900 text-yellow-700 dark:text-yellow-300;
}

.log-level.level-info {
  @apply bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300;
}

.log-level.level-debug {
  @apply bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300;
}

.log-source {
  @apply text-xs text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900 px-2 py-1 rounded whitespace-nowrap;
}

.log-message {
  @apply flex-1 text-sm text-gray-900 dark:text-white truncate flex items-center gap-2;
}

.log-message :deep(mark) {
  @apply bg-yellow-200 dark:bg-yellow-800 text-yellow-900 dark:text-yellow-200 px-1 rounded;
}

.expand-btn {
  @apply w-5 h-5 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-600 dark:hover:bg-gray-500 flex items-center justify-center text-gray-500 dark:text-gray-400 transition-colors;
}

.log-details {
  @apply px-6 pb-4 space-y-3 bg-gray-50 dark:bg-gray-800;
}

.log-details h6 {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300 m-0 mb-1;
}

.log-details pre {
  @apply text-xs font-mono bg-gray-100 dark:bg-gray-700 p-3 rounded overflow-x-auto text-gray-800 dark:text-gray-200;
}

.log-actions-expanded {
  @apply flex gap-2;
}

.copy-btn,
.search-btn {
  @apply flex items-center gap-1 px-2 py-1 text-xs bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded hover:bg-gray-50 dark:hover:bg-gray-500 transition-colors;
}

.load-more {
  @apply p-4 text-center;
}

.load-more-btn {
  @apply flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white rounded transition-colors mx-auto;
}

.log-stats {
  @apply flex items-center gap-4 p-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900 text-sm;
}

.stats-item {
  @apply flex items-center gap-1;
}

.stats-label {
  @apply text-gray-600 dark:text-gray-400;
}

.stats-value {
  @apply font-medium text-gray-900 dark:text-white;
}

.stats-item.level-error .stats-value {
  @apply text-red-600 dark:text-red-400;
}

.stats-item.level-warn .stats-value {
  @apply text-yellow-600 dark:text-yellow-400;
}

.stats-item.level-info .stats-value {
  @apply text-blue-600 dark:text-blue-400;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (width <= 768px) {
  .log-header {
    @apply flex-col items-stretch gap-3;
  }

  .log-controls {
    @apply flex-wrap gap-2;
  }

  .log-main {
    @apply flex-wrap gap-2;
  }

  .log-timestamp {
    @apply order-1 w-full;
  }

  .log-level {
    @apply order-2;
  }

  .log-source {
    @apply order-3;
  }

  .log-message {
    @apply order-4 w-full;
  }

  .log-stats {
    @apply grid grid-cols-2 gap-2;
  }
}
</style>
