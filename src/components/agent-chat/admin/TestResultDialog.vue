<template>
  <div v-if="visible" class="dialog-overlay">
    <div class="dialog" @click.stop>
      <div class="dialog-header">
        <div class="header-left">
          <i :class="getStatusIcon()" class="status-icon"></i>
          <h4>{{ getDialogTitle() }}</h4>
        </div>
        <button @click="handleClose" class="dialog-close">
          <i class="i-carbon-close"></i>
        </button>
      </div>

      <div class="dialog-content">
        <!-- 测试进行中 -->
        <div v-if="status === 'testing'" class="testing-state">
          <div class="testing-animation">
            <div class="loading-spinner">
              <div class="spinner-ring"></div>
              <div class="spinner-ring"></div>
              <div class="spinner-ring"></div>
            </div>
          </div>
          <h3>正在测试 Agent...</h3>
          <p class="testing-message">{{ testingMessage }}</p>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: `${progress}%` }"></div>
          </div>
          <div class="progress-text">{{ Math.round(progress) }}%</div>
        </div>

        <!-- 测试成功 -->
        <div v-else-if="status === 'success'" class="result-state success">
          <div class="result-header">
            <div class="success-badge">
              <i class="i-carbon-checkmark-filled"></i>
              <span>测试成功</span>
            </div>
            <div class="test-metrics">
              <div class="metric-item">
                <span class="metric-label">执行时间</span>
                <span class="metric-value">{{ formatDuration(result?.metrics?.duration) }}</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">响应状态</span>
                <span class="metric-value status-success">正常</span>
              </div>
            </div>
          </div>

          <div class="result-content">
            <div class="section">
              <h5 class="section-title">
                <i class="i-carbon-chat-bot"></i>
                Agent 响应
              </h5>
              <div class="response-content">
                <pre class="response-text">{{ result?.output || '无响应内容' }}</pre>
              </div>
            </div>

            <div v-if="result?.metrics" class="section">
              <h5 class="section-title">
                <i class="i-carbon-analytics"></i>
                执行指标
              </h5>
              <div class="metrics-grid">
                <div
                  v-for="(value, key) in result.metrics"
                  :key="key"
                  class="metric-card"
                >
                  <span class="metric-name">{{ formatMetricName(key) }}</span>
                  <span class="metric-data">{{ formatMetricValue(key, value) }}</span>
                </div>
              </div>
            </div>

            <div v-if="testInput" class="section">
              <h5 class="section-title">
                <i class="i-carbon-send-alt"></i>
                测试输入
              </h5>
              <div class="input-content">
                <pre class="input-text">{{ testInput }}</pre>
              </div>
            </div>
          </div>
        </div>

        <!-- 测试失败 -->
        <div v-else-if="status === 'error'" class="result-state error">
          <div class="result-header">
            <div class="error-badge">
              <i class="i-carbon-error-filled"></i>
              <span>测试失败</span>
            </div>
          </div>

          <div class="result-content">
            <div class="section">
              <h5 class="section-title">
                <i class="i-carbon-warning"></i>
                错误信息
              </h5>
              <div class="error-content">
                <div class="error-message">{{ errorMessage }}</div>
                <div v-if="errorDetails" class="error-details">
                  <details>
                    <summary>详细信息</summary>
                    <pre class="error-stack">{{ errorDetails }}</pre>
                  </details>
                </div>
              </div>
            </div>

            <div class="section">
              <h5 class="section-title">
                <i class="i-carbon-light-bulb"></i>
                建议解决方案
              </h5>
              <div class="suggestions">
                <ul class="suggestion-list">
                  <li>检查 Agent 配置是否正确</li>
                  <li>确认系统提示词格式是否有效</li>
                  <li>验证网络连接是否正常</li>
                  <li>查看服务端日志获取更多信息</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="dialog-footer">
        <button
          v-if="status === 'testing'"
          @click="cancelTest"
          class="btn-secondary"
          :disabled="!canCancel"
        >
          <i class="i-carbon-stop"></i>
          取消测试
        </button>

        <template v-else>
          <button @click="handleClose" class="btn-secondary">
            关闭
          </button>

          <button
            v-if="status === 'success'"
            @click="copyResult"
            class="btn-outline"
          >
            <i class="i-carbon-copy"></i>
            复制结果
          </button>

          <button
            v-if="onRetry"
            @click="retryTest"
            class="btn-primary"
            :class="{ 'btn-danger': status === 'error' }"
          >
            <i class="i-carbon-restart"></i>
            重新测试
          </button>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

interface TestResult {
  output: string
  metrics?: Record<string, any>
}

interface Props {
  visible: boolean
  status: 'testing' | 'success' | 'error'
  result?: TestResult | null
  errorMessage?: string
  errorDetails?: string
  testInput?: string
  onRetry?: () => void
  canCancel?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  status: 'testing',
  result: null,
  errorMessage: '',
  errorDetails: '',
  testInput: '',
  onRetry: undefined,
  canCancel: true
})

const emit = defineEmits<{
  'update:visible': [value: boolean]
  close: []
  cancel: []
  retry: []
}>()

// 响应式数据
const progress = ref(0)
const testingMessage = ref('初始化测试环境...')

// 计算属性
const getDialogTitle = () => {
  switch (props.status) {
    case 'testing':
      return 'Agent 测试中'
    case 'success':
      return 'Agent 测试完成'
    case 'error':
      return 'Agent 测试失败'
    default:
      return 'Agent 测试'
  }
}

const getStatusIcon = () => {
  switch (props.status) {
    case 'testing':
      return 'i-carbon-in-progress animate-spin'
    case 'success':
      return 'i-carbon-checkmark-filled text-green-500'
    case 'error':
      return 'i-carbon-error-filled text-red-500'
    default:
      return 'i-carbon-test-tool'
  }
}

// 进度模拟
let progressInterval: number | null = null

function startProgress() {
  if (props.status === 'testing') {
    progress.value = 0

    const messages = [
      '初始化测试环境...',
      '连接 Agent 服务...',
      '发送测试请求...',
      '等待 Agent 响应...',
      '处理响应数据...',
      '分析执行指标...'
    ]

    let messageIndex = 0
    let currentProgress = 0

    progressInterval = setInterval(() => {
      if (currentProgress < 90) {
        currentProgress += Math.random() * 15
        progress.value = Math.min(currentProgress, 90)

        if (messageIndex < messages.length - 1 && progress.value > (messageIndex + 1) * 15) {
          messageIndex++
          testingMessage.value = messages[messageIndex]
        }
      }
    }, 300)
  }
}

function stopProgress() {
  if (progressInterval) {
    clearInterval(progressInterval)
    progressInterval = null
  }

  if (props.status === 'success') {
    progress.value = 100
    testingMessage.value = '测试完成'
  }
}

// 格式化方法
function formatDuration(duration?: number): string {
  if (typeof duration !== 'number') return '未知'

  if (duration < 1000) {
    return `${duration}ms`
  } else if (duration < 60000) {
    return `${(duration / 1000).toFixed(1)}s`
  } else {
    return `${(duration / 60000).toFixed(1)}min`
  }
}

function formatMetricName(key: string): string {
  const nameMap: Record<string, string> = {
    duration: '执行时间',
    memory: '内存使用',
    tokens: 'Token 消耗',
    requests: '请求次数',
    errors: '错误次数',
    success_rate: '成功率',
    latency: '延迟',
    throughput: '吞吐量'
  }

  return nameMap[key] || key
}

function formatMetricValue(key: string, value: any): string {
  if (value === null || value === undefined) return '-'

  switch (key) {
    case 'duration':
    case 'latency':
      return formatDuration(value)
    case 'memory':
      return `${(value / 1024 / 1024).toFixed(1)}MB`
    case 'success_rate':
      return `${(value * 100).toFixed(1)}%`
    case 'tokens':
    case 'requests':
    case 'errors':
      return value.toLocaleString()
    default:
      return String(value)
  }
}

// 事件处理
function handleClose() {
  emit('update:visible', false)
  emit('close')
}

function cancelTest() {
  emit('cancel')
  handleClose()
}

function retryTest() {
  emit('retry')
  if (props.onRetry) {
    props.onRetry()
  }
}

async function copyResult() {
  if (!props.result) return

  const text = `Agent 测试结果：\n\n响应内容：\n${props.result.output}\n\n执行指标：\n${JSON.stringify(props.result.metrics, null, 2)}`

  try {
    await navigator.clipboard.writeText(text)
    // 这里可以显示复制成功提示
  } catch (error) {
    console.error('复制失败:', error)
  }
}

// 生命周期
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.status === 'testing') {
    startProgress()
  } else {
    stopProgress()
  }
})

watch(() => props.status, (newStatus) => {
  if (newStatus === 'testing') {
    startProgress()
  } else {
    stopProgress()
  }
})

onMounted(() => {
  if (props.visible && props.status === 'testing') {
    startProgress()
  }
})

onUnmounted(() => {
  stopProgress()
})

// 键盘事件
function handleKeyDown(event: KeyboardEvent) {
  if (event.key === 'Escape' && props.visible) {
    handleClose()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped>
.dialog-overlay {
  @apply fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-[100] p-4;

  backdrop-filter: blur(4px);
}

.dialog {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-2xl w-full max-h-[85vh] overflow-hidden flex flex-col;

  animation: dialog-enter 0.3s ease-out;
}

@keyframes dialog-enter {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }

  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.dialog-header {
  @apply flex items-center justify-between p-6 pb-4 border-b border-gray-200 dark:border-gray-700;
}

.header-left {
  @apply flex items-center gap-3;
}

.status-icon {
  @apply text-xl;
}

.dialog-header h4 {
  @apply text-lg font-semibold text-gray-900 dark:text-white m-0;
}

.dialog-close {
  @apply w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 flex items-center justify-center text-gray-500 dark:text-gray-400 transition-colors;
}

.dialog-content {
  @apply flex-1 overflow-y-auto p-6;
}

.dialog-footer {
  @apply flex gap-3 p-6 pt-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900;
}

/* 测试进行中样式 */
.testing-state {
  @apply text-center space-y-6;
}

.testing-animation {
  @apply flex justify-center mb-6;
}

.loading-spinner {
  @apply relative w-16 h-16;
}

.spinner-ring {
  @apply absolute border-4 rounded-full;

  width: 64px;
  height: 64px;
  animation: spinner 2s linear infinite;
}

.spinner-ring:nth-child(1) {
  @apply border-blue-200 dark:border-blue-800;

  animation-delay: -0.45s;
}

.spinner-ring:nth-child(2) {
  @apply border-blue-300 dark:border-blue-700;

  animation-delay: -0.3s;
}

.spinner-ring:nth-child(3) {
  @apply border-blue-500;

  animation-delay: -0.15s;
}

@keyframes spinner {
  0% { opacity: 1; transform: rotate(0deg) scale(1); }
  50% { opacity: 0.7; transform: rotate(180deg) scale(0.8); }
  100% { opacity: 1; transform: rotate(360deg) scale(1); }
}

.testing-state h3 {
  @apply text-xl font-semibold text-gray-900 dark:text-white;
}

.testing-message {
  @apply text-gray-600 dark:text-gray-400;
}

.progress-bar {
  @apply w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden;
}

.progress-fill {
  @apply h-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-300 ease-out;
}

.progress-text {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

/* 结果状态样式 */
.result-state {
  @apply space-y-6;
}

.result-header {
  @apply space-y-4;
}

.success-badge,
.error-badge {
  @apply inline-flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium;
}

.success-badge {
  @apply bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200;
}

.error-badge {
  @apply bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200;
}

.test-metrics {
  @apply flex gap-6;
}

.metric-item {
  @apply flex flex-col;
}

.metric-label {
  @apply text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide;
}

.metric-value {
  @apply text-sm font-semibold text-gray-900 dark:text-white;
}

.status-success {
  @apply text-green-600 dark:text-green-400;
}

/* 内容区域样式 */
.result-content {
  @apply space-y-6;
}

.section {
  @apply space-y-3;
}

.section-title {
  @apply flex items-center gap-2 text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wide;
}

.response-content,
.input-content {
  @apply bg-gray-50 dark:bg-gray-900 rounded-lg p-4;
}

.response-text,
.input-text {
  @apply text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap break-words font-mono;
}

.metrics-grid {
  @apply grid grid-cols-2 gap-3;
}

.metric-card {
  @apply bg-gray-50 dark:bg-gray-900 rounded-lg p-3 flex flex-col;
}

.metric-name {
  @apply text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide;
}

.metric-data {
  @apply text-sm font-semibold text-gray-900 dark:text-white;
}

/* 错误样式 */
.error-content {
  @apply space-y-3;
}

.error-message {
  @apply bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 text-red-800 dark:text-red-200;
}

.error-details summary {
  @apply cursor-pointer text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white;
}

.error-stack {
  @apply text-xs text-gray-500 dark:text-gray-400 mt-2 font-mono;
}

.suggestions {
  @apply bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4;
}

.suggestion-list {
  @apply text-sm text-blue-800 dark:text-blue-200 space-y-1;
}

.suggestion-list li {
  @apply before:content-["•"] before:text-blue-500 before:mr-2;
}

/* 按钮样式 */
.btn-primary {
  @apply flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white rounded-lg transition-colors;
}

.btn-secondary {
  @apply flex items-center gap-2 px-4 py-2 bg-white hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors;
}

.btn-outline {
  @apply flex items-center gap-2 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors;
}

.btn-danger {
  @apply bg-red-500 hover:bg-red-600 disabled:bg-red-300 text-white border-red-500;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (width <= 640px) {
  .dialog {
    @apply max-w-full m-4;
  }

  .test-metrics {
    @apply flex-col gap-3;
  }

  .metrics-grid {
    @apply grid-cols-1;
  }

  .dialog-footer {
    @apply flex-col;
  }
}
</style>
