<template>
  <div class="system-config">
    <div class="config-header">
      <h2 class="config-title">
        <i class="i-carbon-settings"></i>
        系统基础配置
      </h2>
      <div class="config-description">
        配置应用程序的基本运行参数和环境设置
      </div>
    </div>

    <div v-if="loading" class="config-loading">
      <i class="i-carbon-in-progress animate-spin"></i>
      <span>加载配置中...</span>
    </div>

    <div v-else class="config-content">
      <!-- 应用程序信息 -->
      <div class="config-section">
        <h3 class="section-title">应用程序信息</h3>
        <div class="form-grid">
          <div class="form-item">
            <label class="form-label">应用名称</label>
            <input
              v-model="formData.applicationName"
              type="text"
              class="form-input"
              placeholder="请输入应用名称"
              @input="handleChange"
            />
          </div>

          <div class="form-item">
            <label class="form-label">应用版本</label>
            <input
              v-model="formData.applicationVersion"
              type="text"
              class="form-input"
              placeholder="请输入应用版本"
              @input="handleChange"
            />
          </div>

          <div class="form-item">
            <label class="form-label">运行环境</label>
            <select
              v-model="formData.environment"
              class="form-select"
              @change="handleChange"
            >
              <option value="development">开发环境</option>
              <option value="test">测试环境</option>
              <option value="production">生产环境</option>
            </select>
          </div>

          <div class="form-item">
            <label class="form-label">调试模式</label>
            <div class="switch-container">
              <label class="switch">
                <input
                  v-model="formData.debugMode"
                  type="checkbox"
                  @change="handleChange"
                />
                <span class="switch-slider"></span>
              </label>
              <span class="switch-label">
                {{ formData.debugMode ? '启用' : '禁用' }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 服务器配置 -->
      <div class="config-section">
        <h3 class="section-title">服务器配置</h3>
        <div class="form-grid">
          <div class="form-item">
            <label class="form-label">服务端口</label>
            <input
              v-model.number="formData.serverPort"
              type="number"
              class="form-input"
              placeholder="8080"
              min="1"
              max="65535"
              @input="handleChange"
            />
            <div class="form-help">服务器监听端口，范围: 1-65535</div>
          </div>

          <div class="form-item">
            <label class="form-label">最大连接数</label>
            <input
              v-model.number="formData.maxConnections"
              type="number"
              class="form-input"
              placeholder="200"
              min="1"
              @input="handleChange"
            />
            <div class="form-help">并发连接数限制</div>
          </div>

          <div class="form-item">
            <label class="form-label">请求超时时间 (秒)</label>
            <input
              v-model.number="formData.requestTimeout"
              type="number"
              class="form-input"
              placeholder="30"
              min="1"
              max="300"
              @input="handleChange"
            />
            <div class="form-help">HTTP 请求超时时间</div>
          </div>
        </div>
      </div>

      <!-- 数据库配置 -->
      <div class="config-section">
        <h3 class="section-title">数据库配置</h3>
        <div class="form-grid">
          <div class="form-item">
            <label class="form-label">连接池大小</label>
            <input
              v-model.number="formData.database.poolSize"
              type="number"
              class="form-input"
              placeholder="10"
              min="1"
              max="100"
              @input="handleChange"
            />
            <div class="form-help">数据库连接池最大连接数</div>
          </div>

          <div class="form-item">
            <label class="form-label">连接超时 (秒)</label>
            <input
              v-model.number="formData.database.connectionTimeout"
              type="number"
              class="form-input"
              placeholder="30"
              min="1"
              max="300"
              @input="handleChange"
            />
          </div>

          <div class="form-item">
            <label class="form-label">查询超时 (秒)</label>
            <input
              v-model.number="formData.database.queryTimeout"
              type="number"
              class="form-input"
              placeholder="60"
              min="1"
              max="600"
              @input="handleChange"
            />
          </div>

          <div class="form-item">
            <label class="form-label">自动重连</label>
            <div class="switch-container">
              <label class="switch">
                <input
                  v-model="formData.database.autoReconnect"
                  type="checkbox"
                  @change="handleChange"
                />
                <span class="switch-slider"></span>
              </label>
              <span class="switch-label">
                {{ formData.database.autoReconnect ? '启用' : '禁用' }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 地区和时间配置 -->
      <div class="config-section">
        <h3 class="section-title">地区和时间设置</h3>
        <div class="form-grid">
          <div class="form-item">
            <label class="form-label">时区</label>
            <select
              v-model="formData.timezone"
              class="form-select"
              @change="handleChange"
            >
              <option value="Asia/Shanghai">亚洲/上海 (UTC+8)</option>
              <option value="Asia/Tokyo">亚洲/东京 (UTC+9)</option>
              <option value="America/New_York">美洲/纽约 (UTC-5)</option>
              <option value="Europe/London">欧洲/伦敦 (UTC+0)</option>
              <option value="UTC">协调世界时 (UTC)</option>
            </select>
          </div>

          <div class="form-item">
            <label class="form-label">语言地区</label>
            <select
              v-model="formData.locale"
              class="form-select"
              @change="handleChange"
            >
              <option value="zh-CN">简体中文</option>
              <option value="zh-TW">繁体中文</option>
              <option value="en-US">英语 (美国)</option>
              <option value="ja-JP">日语</option>
              <option value="ko-KR">韩语</option>
            </select>
          </div>

          <div class="form-item">
            <label class="form-label">日期格式</label>
            <select
              v-model="formData.dateFormat"
              class="form-select"
              @change="handleChange"
            >
              <option value="YYYY-MM-DD">YYYY-MM-DD</option>
              <option value="MM/DD/YYYY">MM/DD/YYYY</option>
              <option value="DD/MM/YYYY">DD/MM/YYYY</option>
              <option value="YYYY年MM月DD日">YYYY年MM月DD日</option>
            </select>
            <div class="format-preview">
              预览: {{ formatDatePreview() }}
            </div>
          </div>

          <div class="form-item">
            <label class="form-label">时间格式</label>
            <select
              v-model="formData.timeFormat"
              class="form-select"
              @change="handleChange"
            >
              <option value="HH:mm:ss">24小时制 (HH:mm:ss)</option>
              <option value="hh:mm:ss A">12小时制 (hh:mm:ss AM/PM)</option>
              <option value="HH:mm">24小时制简短 (HH:mm)</option>
              <option value="hh:mm A">12小时制简短 (hh:mm AM/PM)</option>
            </select>
            <div class="format-preview">
              预览: {{ formatTimePreview() }}
            </div>
          </div>
        </div>
      </div>

      <!-- 配置预览 -->
      <div class="config-section">
        <h3 class="section-title">配置预览</h3>
        <div class="config-preview">
          <div class="preview-header">
            <span>当前配置概览</span>
            <button @click="copyConfig" class="copy-btn">
              <i class="i-carbon-copy"></i>
              复制配置
            </button>
          </div>
          <pre class="preview-content">{{ JSON.stringify(formData, null, 2) }}</pre>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="config-actions">
      <button @click="handleReset" class="action-btn secondary">
        <i class="i-carbon-reset"></i>
        重置为默认
      </button>
      <button @click="validateConfig" class="action-btn secondary">
        <i class="i-carbon-checkmark"></i>
        验证配置
      </button>
      <button @click="previewChanges" class="action-btn secondary">
        <i class="i-carbon-view"></i>
        预览更改
      </button>
    </div>

    <!-- 配置验证结果 -->
    <div v-if="validationResult" class="validation-result" :class="validationResult.valid ? 'success' : 'error'">
      <div class="validation-header">
        <i :class="validationResult.valid ? 'i-carbon-checkmark-filled' : 'i-carbon-error-filled'"></i>
        <span>{{ validationResult.valid ? '配置验证通过' : '配置验证失败' }}</span>
      </div>

      <div v-if="validationResult.errors.length > 0" class="validation-errors">
        <h4>错误:</h4>
        <ul>
          <li v-for="error in validationResult.errors" :key="error">{{ error }}</li>
        </ul>
      </div>

      <div v-if="validationResult.warnings.length > 0" class="validation-warnings">
        <h4>警告:</h4>
        <ul>
          <li v-for="warning in validationResult.warnings" :key="warning">{{ warning }}</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import type { SystemConfig, ConfigValidationResult } from '@/api/configManagement'
import { ConfigManagementService } from '@/api/configManagement'

interface Props {
  config: SystemConfig | null
  loading: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  update: [config: SystemConfig]
  reset: []
}>()

// 响应式数据
const formData = reactive<SystemConfig>({
  applicationName: '',
  applicationVersion: '',
  environment: 'development',
  debugMode: false,
  serverPort: 8080,
  maxConnections: 200,
  requestTimeout: 30,
  database: {
    poolSize: 10,
    connectionTimeout: 30,
    queryTimeout: 60,
    autoReconnect: true
  },
  timezone: 'Asia/Shanghai',
  locale: 'zh-CN',
  dateFormat: 'YYYY-MM-DD',
  timeFormat: 'HH:mm:ss'
})

const validationResult = ref<ConfigValidationResult | null>(null)

// 监听器
watch(() => props.config, (newConfig) => {
  if (newConfig) {
    Object.assign(formData, newConfig)
  }
}, { immediate: true, deep: true })

// 计算属性
const hasChanges = computed(() => {
  if (!props.config) return false
  return JSON.stringify(formData) !== JSON.stringify(props.config)
})

// 方法
function handleChange() {
  validationResult.value = null
  emit('update', { ...formData })
}

function handleReset() {
  emit('reset')
}

async function validateConfig() {
  try {
    const result = await ConfigManagementService.validateSystemConfig(formData)
    validationResult.value = result
  } catch (error) {
    console.error('Validate config failed:', error)
    validationResult.value = {
      valid: false,
      errors: ['配置验证失败，请检查网络连接'],
      warnings: []
    }
  }
}

function previewChanges() {
  if (!props.config) return

  const changes: any = {}
  const original = props.config
  const current = formData

  // 比较配置变化
  Object.keys(current).forEach(key => {
    if (typeof current[key as keyof SystemConfig] === 'object') {
      const originalObj = original[key as keyof SystemConfig] as any
      const currentObj = current[key as keyof SystemConfig] as any

      Object.keys(currentObj).forEach(subKey => {
        if (originalObj[subKey] !== currentObj[subKey]) {
          if (!changes[key]) changes[key] = {}
          changes[key][subKey] = {
            from: originalObj[subKey],
            to: currentObj[subKey]
          }
        }
      })
    } else if (original[key as keyof SystemConfig] !== current[key as keyof SystemConfig]) {
      changes[key] = {
        from: original[key as keyof SystemConfig],
        to: current[key as keyof SystemConfig]
      }
    }
  })

  //console.log('Configuration changes:', changes)
  // 这里可以打开一个预览对话框显示变化
}

function copyConfig() {
  const configText = JSON.stringify(formData, null, 2)
  navigator.clipboard.writeText(configText).then(() => {
    // 这里可以显示复制成功的提示
    //console.log('配置已复制到剪贴板')
  }).catch(err => {
    console.error('复制失败:', err)
  })
}

function formatDatePreview(): string {
  const now = new Date()
  const format = formData.dateFormat

  switch (format) {
    case 'YYYY-MM-DD':
      return now.toISOString().split('T')[0]
    case 'MM/DD/YYYY':
      return `${(now.getMonth() + 1).toString().padStart(2, '0')}/${now.getDate().toString().padStart(2, '0')}/${now.getFullYear()}`
    case 'DD/MM/YYYY':
      return `${now.getDate().toString().padStart(2, '0')}/${(now.getMonth() + 1).toString().padStart(2, '0')}/${now.getFullYear()}`
    case 'YYYY年MM月DD日':
      return `${now.getFullYear()}年${(now.getMonth() + 1).toString().padStart(2, '0')}月${now.getDate().toString().padStart(2, '0')}日`
    default:
      return now.toLocaleDateString()
  }
}

function formatTimePreview(): string {
  const now = new Date()
  const format = formData.timeFormat

  switch (format) {
    case 'HH:mm:ss':
      return now.toLocaleTimeString('en-GB', { hour12: false })
    case 'hh:mm:ss A':
      return now.toLocaleTimeString('en-US', { hour12: true })
    case 'HH:mm':
      return now.toLocaleTimeString('en-GB', { hour12: false, hour: '2-digit', minute: '2-digit' })
    case 'hh:mm A':
      return now.toLocaleTimeString('en-US', { hour12: true, hour: '2-digit', minute: '2-digit' })
    default:
      return now.toLocaleTimeString()
  }
}
</script>

<style scoped>
.system-config {
  @apply space-y-6;
}

.config-header {
  @apply space-y-2;
}

.config-title {
  @apply text-xl font-semibold text-gray-900 dark:text-white m-0 flex items-center gap-2;
}

.config-description {
  @apply text-gray-600 dark:text-gray-400;
}

.config-loading {
  @apply flex items-center justify-center py-12 text-gray-500 dark:text-gray-400;
}

.config-loading i {
  @apply text-2xl mr-2;
}

.config-content {
  @apply space-y-8;
}

.config-section {
  @apply bg-gray-50 dark:bg-gray-700 rounded-lg p-6;
}

.section-title {
  @apply text-lg font-medium text-gray-900 dark:text-white mb-4;
}

.form-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.form-item {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.form-input,
.form-select {
  @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.form-help {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

.switch-container {
  @apply flex items-center gap-3;
}

.switch {
  @apply relative inline-block w-12 h-6;
}

.switch input {
  @apply opacity-0 w-0 h-0;
}

.switch-slider {
  @apply absolute cursor-pointer top-0 left-0 right-0 bottom-0 bg-gray-300 dark:bg-gray-600 rounded-full transition-all duration-300;
}

.switch-slider::before {
  @apply absolute content-[""] h-5 w-5 left-0.5 bottom-0.5 bg-white rounded-full transition-all duration-300;
}

.switch input:checked + .switch-slider {
  @apply bg-blue-500;
}

.switch input:checked + .switch-slider::before {
  @apply translate-x-6;
}

.switch-label {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.format-preview {
  @apply text-xs text-blue-600 dark:text-blue-400 font-mono;
}

.config-preview {
  @apply border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden;
}

.preview-header {
  @apply flex items-center justify-between p-3 bg-gray-100 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600;
}

.copy-btn {
  @apply flex items-center gap-1 px-2 py-1 text-xs bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded hover:bg-gray-50 dark:hover:bg-gray-500 transition-colors;
}

.preview-content {
  @apply p-4 bg-gray-50 dark:bg-gray-800 text-sm font-mono text-gray-800 dark:text-gray-200 overflow-x-auto;
}

.config-actions {
  @apply flex flex-wrap gap-3;
}

.action-btn {
  @apply flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-lg transition-colors;
}

.action-btn.secondary {
  @apply bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600;
}

.validation-result {
  @apply border rounded-lg p-4;
}

.validation-result.success {
  @apply bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700;
}

.validation-result.error {
  @apply bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-700;
}

.validation-header {
  @apply flex items-center gap-2 font-medium mb-3;
}

.validation-result.success .validation-header {
  @apply text-green-800 dark:text-green-200;
}

.validation-result.error .validation-header {
  @apply text-red-800 dark:text-red-200;
}

.validation-errors,
.validation-warnings {
  @apply space-y-2;
}

.validation-errors h4 {
  @apply text-sm font-medium text-red-800 dark:text-red-200 m-0;
}

.validation-warnings h4 {
  @apply text-sm font-medium text-yellow-800 dark:text-yellow-200 m-0;
}

.validation-errors ul,
.validation-warnings ul {
  @apply list-disc list-inside space-y-1 text-sm;
}

.validation-errors li {
  @apply text-red-700 dark:text-red-300;
}

.validation-warnings li {
  @apply text-yellow-700 dark:text-yellow-300;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (width <= 768px) {
  .form-grid {
    @apply grid-cols-1;
  }

  .config-actions {
    @apply grid grid-cols-2 gap-2;
  }
}
</style>
