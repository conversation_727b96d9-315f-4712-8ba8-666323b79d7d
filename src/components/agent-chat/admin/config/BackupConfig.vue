<template>
  <div class="backup-config">
    <div class="config-header">
      <h2 class="config-title">
        <i class="i-carbon-backup-restore"></i>
        备份和恢复
      </h2>
      <div class="config-description">
        管理系统配置的备份、恢复和自动备份计划
      </div>
    </div>

    <div v-if="loading" class="config-loading">
      <i class="i-carbon-in-progress animate-spin"></i>
      <span>加载备份列表...</span>
    </div>

    <div v-else class="config-content">
      <!-- 创建备份 -->
      <div class="config-section">
        <h3 class="section-title">创建新备份</h3>
        <div class="backup-create">
          <div class="create-form">
            <div class="form-row">
              <div class="form-item">
                <label class="form-label">备份名称</label>
                <input
                  v-model="newBackup.name"
                  type="text"
                  class="form-input"
                  placeholder="输入备份名称"
                />
              </div>
              <div class="form-item">
                <label class="form-label">备份描述</label>
                <input
                  v-model="newBackup.description"
                  type="text"
                  class="form-input"
                  placeholder="备份说明（可选）"
                />
              </div>
            </div>
            <div class="create-actions">
              <button @click="createBackup" class="create-btn" :disabled="!newBackup.name.trim()">
                <i class="i-carbon-save"></i>
                创建备份
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 备份列表 -->
      <div class="config-section">
        <h3 class="section-title">备份列表</h3>

        <div class="backup-stats">
          <div class="stat-item">
            <span class="stat-label">总备份数:</span>
            <span class="stat-value">{{ backups.length }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">总大小:</span>
            <span class="stat-value">{{ formatFileSize(totalSize) }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">最新备份:</span>
            <span class="stat-value">{{ latestBackup || '无' }}</span>
          </div>
        </div>

        <div v-if="backups.length === 0" class="backup-empty">
          <i class="i-carbon-cloud-backup"></i>
          <h4>暂无备份</h4>
          <p>创建第一个备份来保护您的配置</p>
        </div>

        <div v-else class="backup-list">
          <div
            v-for="backup in sortedBackups"
            :key="backup.id"
            class="backup-item"
            :class="{ 'backup-failed': backup.status === 'FAILED' }"
          >
            <div class="backup-info">
              <div class="backup-header">
                <h4 class="backup-name">{{ backup.name }}</h4>
                <div class="backup-status" :class="`status-${backup.status.toLowerCase()}`">
                  <i :class="getStatusIcon(backup.status)"></i>
                  {{ getStatusText(backup.status) }}
                </div>
              </div>

              <div class="backup-meta">
                <div class="meta-item">
                  <i class="i-carbon-time"></i>
                  <span>{{ formatTime(backup.createdAt) }}</span>
                </div>
                <div class="meta-item">
                  <i class="i-carbon-user"></i>
                  <span>{{ backup.createdBy }}</span>
                </div>
                <div class="meta-item">
                  <i class="i-carbon-data-base"></i>
                  <span>{{ formatFileSize(backup.size) }}</span>
                </div>
              </div>

              <div v-if="backup.description" class="backup-description">
                {{ backup.description }}
              </div>

              <div v-if="backup.errorMessage" class="backup-error">
                <i class="i-carbon-warning-filled"></i>
                {{ backup.errorMessage }}
              </div>
            </div>

            <div class="backup-actions">
              <button
                @click="restoreBackup(backup)"
                class="action-btn restore"
                :disabled="backup.status !== 'COMPLETED'"
                title="恢复此备份"
              >
                <i class="i-carbon-restore"></i>
                恢复
              </button>
              <button
                @click="downloadBackup(backup)"
                class="action-btn download"
                :disabled="backup.status !== 'COMPLETED'"
                title="下载备份文件"
              >
                <i class="i-carbon-download"></i>
                下载
              </button>
              <button
                @click="deleteBackup(backup)"
                class="action-btn delete"
                title="删除备份"
              >
                <i class="i-carbon-trash-can"></i>
                删除
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 自动备份设置 -->
      <div class="config-section">
        <h3 class="section-title">自动备份设置</h3>
        <div class="schedule-config">
          <div class="schedule-toggle">
            <label class="switch">
              <input
                v-model="schedule.enabled"
                type="checkbox"
                @change="updateSchedule"
              />
              <span class="switch-slider"></span>
            </label>
            <span class="switch-label">启用自动备份</span>
          </div>

          <div v-if="schedule.enabled" class="schedule-settings">
            <div class="form-grid">
              <div class="form-item">
                <label class="form-label">备份频率</label>
                <select v-model="schedule.frequency" class="form-select" @change="updateSchedule">
                  <option value="daily">每日</option>
                  <option value="weekly">每周</option>
                  <option value="monthly">每月</option>
                </select>
              </div>

              <div class="form-item">
                <label class="form-label">备份时间</label>
                <input
                  v-model="schedule.time"
                  type="time"
                  class="form-input"
                  @change="updateSchedule"
                />
              </div>

              <div v-if="schedule.frequency === 'weekly'" class="form-item">
                <label class="form-label">星期几</label>
                <select v-model="schedule.dayOfWeek" class="form-select" @change="updateSchedule">
                  <option :value="0">星期日</option>
                  <option :value="1">星期一</option>
                  <option :value="2">星期二</option>
                  <option :value="3">星期三</option>
                  <option :value="4">星期四</option>
                  <option :value="5">星期五</option>
                  <option :value="6">星期六</option>
                </select>
              </div>

              <div v-if="schedule.frequency === 'monthly'" class="form-item">
                <label class="form-label">每月几号</label>
                <input
                  v-model.number="schedule.dayOfMonth"
                  type="number"
                  min="1"
                  max="31"
                  class="form-input"
                  @change="updateSchedule"
                />
              </div>

              <div class="form-item">
                <label class="form-label">保留天数</label>
                <input
                  v-model.number="schedule.retentionDays"
                  type="number"
                  min="1"
                  max="365"
                  class="form-input"
                  @change="updateSchedule"
                />
                <div class="form-help">超过此天数的备份将自动删除</div>
              </div>

              <div class="form-item">
                <label class="form-label">启用压缩</label>
                <div class="switch-container">
                  <label class="switch">
                    <input
                      v-model="schedule.enableCompression"
                      type="checkbox"
                      @change="updateSchedule"
                    />
                    <span class="switch-slider"></span>
                  </label>
                  <span class="switch-label">
                    {{ schedule.enableCompression ? '启用' : '禁用' }}
                  </span>
                </div>
              </div>

              <div class="form-item">
                <label class="form-label">启用加密</label>
                <div class="switch-container">
                  <label class="switch">
                    <input
                      v-model="schedule.enableEncryption"
                      type="checkbox"
                      @change="updateSchedule"
                    />
                    <span class="switch-slider"></span>
                  </label>
                  <span class="switch-label">
                    {{ schedule.enableEncryption ? '启用' : '禁用' }}
                  </span>
                </div>
              </div>
            </div>

            <div class="schedule-preview">
              <h4>下次自动备份时间</h4>
              <p class="next-backup-time">{{ getNextBackupTime() }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import type { BackupConfig, BackupSchedule } from '@/api/configManagement'

interface Props {
  backups: BackupConfig[]
  loading: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  createBackup: [name: string, description?: string]
  restoreBackup: [backupId: string]
  deleteBackup: [backupId: string]
  scheduleBackup: [schedule: BackupSchedule]
}>()

// 响应式数据
const newBackup = reactive({
  name: '',
  description: ''
})

const schedule = reactive<BackupSchedule>({
  enabled: false,
  frequency: 'daily',
  time: '02:00',
  dayOfWeek: 0,
  dayOfMonth: 1,
  retentionDays: 30,
  enableCompression: true,
  enableEncryption: false
})

// 计算属性
const sortedBackups = computed(() => {
  return [...props.backups].sort((a, b) =>
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  )
})

const totalSize = computed(() => {
  return props.backups.reduce((total, backup) => total + backup.size, 0)
})

const latestBackup = computed(() => {
  const latest = sortedBackups.value[0]
  return latest ? formatTime(latest.createdAt) : null
})

// 方法
function createBackup() {
  if (!newBackup.name.trim()) return

  emit('createBackup', newBackup.name, newBackup.description || undefined)

  // 清空表单
  newBackup.name = ''
  newBackup.description = ''
}

function restoreBackup(backup: BackupConfig) {
  emit('restoreBackup', backup.id)
}

function deleteBackup(backup: BackupConfig) {
  emit('deleteBackup', backup.id)
}

function downloadBackup(backup: BackupConfig) {
  // 下载逻辑在父组件中处理
  //console.log('Download backup:', backup.id)
}

function updateSchedule() {
  emit('scheduleBackup', { ...schedule })
}

function getStatusIcon(status: string): string {
  switch (status) {
    case 'COMPLETED': return 'i-carbon-checkmark-filled'
    case 'IN_PROGRESS': return 'i-carbon-in-progress'
    case 'FAILED': return 'i-carbon-error-filled'
    default: return 'i-carbon-unknown'
  }
}

function getStatusText(status: string): string {
  switch (status) {
    case 'COMPLETED': return '已完成'
    case 'IN_PROGRESS': return '进行中'
    case 'FAILED': return '失败'
    default: return '未知'
  }
}

function formatTime(timestamp: string): string {
  return new Date(timestamp).toLocaleString('zh-CN')
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

function getNextBackupTime(): string {
  if (!schedule.enabled) return '未启用自动备份'

  const now = new Date()
  const [hours, minutes] = schedule.time.split(':').map(Number)

  let nextBackup = new Date()
  nextBackup.setHours(hours, minutes, 0, 0)

  if (schedule.frequency === 'daily') {
    if (nextBackup <= now) {
      nextBackup.setDate(nextBackup.getDate() + 1)
    }
  } else if (schedule.frequency === 'weekly') {
    const daysDiff = (schedule.dayOfWeek! - now.getDay() + 7) % 7
    nextBackup.setDate(now.getDate() + daysDiff)
    if (daysDiff === 0 && nextBackup <= now) {
      nextBackup.setDate(nextBackup.getDate() + 7)
    }
  } else if (schedule.frequency === 'monthly') {
    nextBackup.setDate(schedule.dayOfMonth!)
    if (nextBackup <= now) {
      nextBackup.setMonth(nextBackup.getMonth() + 1)
    }
  }

  return nextBackup.toLocaleString('zh-CN')
}
</script>

<style scoped>
.backup-config {
  @apply space-y-6;
}

.config-header {
  @apply space-y-2;
}

.config-title {
  @apply text-xl font-semibold text-gray-900 dark:text-white m-0 flex items-center gap-2;
}

.config-description {
  @apply text-gray-600 dark:text-gray-400;
}

.config-loading {
  @apply flex items-center justify-center py-12 text-gray-500 dark:text-gray-400;
}

.config-loading i {
  @apply text-2xl mr-2;
}

.config-content {
  @apply space-y-8;
}

.config-section {
  @apply bg-gray-50 dark:bg-gray-700 rounded-lg p-6;
}

.section-title {
  @apply text-lg font-medium text-gray-900 dark:text-white mb-4;
}

.backup-create {
  @apply space-y-4;
}

.create-form {
  @apply space-y-4;
}

.form-row {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.form-item {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.form-input,
.form-select {
  @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.form-help {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

.create-actions {
  @apply flex justify-end;
}

.create-btn {
  @apply flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white rounded-lg transition-colors;
}

.backup-stats {
  @apply grid grid-cols-1 md:grid-cols-3 gap-4 mb-6;
}

.stat-item {
  @apply flex justify-between items-center p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600;
}

.stat-label {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.stat-value {
  @apply font-medium text-gray-900 dark:text-white;
}

.backup-empty {
  @apply text-center py-12 text-gray-500 dark:text-gray-400;
}

.backup-empty i {
  @apply text-4xl mb-4;
}

.backup-empty h4 {
  @apply text-lg font-medium mb-2;
}

.backup-list {
  @apply space-y-4;
}

.backup-item {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4;
}

.backup-item.backup-failed {
  @apply border-red-200 dark:border-red-700 bg-red-50 dark:bg-red-900/20;
}

.backup-info {
  @apply space-y-3;
}

.backup-header {
  @apply flex items-center justify-between;
}

.backup-name {
  @apply font-medium text-gray-900 dark:text-white m-0;
}

.backup-status {
  @apply flex items-center gap-1 text-sm px-2 py-1 rounded-full;
}

.status-completed {
  @apply bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200;
}

.status-in_progress {
  @apply bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200;
}

.status-failed {
  @apply bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200;
}

.backup-meta {
  @apply flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-400;
}

.meta-item {
  @apply flex items-center gap-1;
}

.backup-description {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.backup-error {
  @apply flex items-center gap-2 text-sm text-red-600 dark:text-red-400;
}

.backup-actions {
  @apply flex gap-2 mt-4;
}

.action-btn {
  @apply flex items-center gap-1 px-3 py-1 text-sm rounded transition-colors;
}

.action-btn.restore {
  @apply bg-green-100 hover:bg-green-200 disabled:bg-gray-100 text-green-700 disabled:text-gray-400;
}

.action-btn.download {
  @apply bg-blue-100 hover:bg-blue-200 disabled:bg-gray-100 text-blue-700 disabled:text-gray-400;
}

.action-btn.delete {
  @apply bg-red-100 hover:bg-red-200 text-red-700;
}

.schedule-config {
  @apply space-y-6;
}

.schedule-toggle {
  @apply flex items-center gap-3;
}

.schedule-settings {
  @apply space-y-6;
}

.form-grid {
  @apply grid grid-cols-1 md:grid-cols-3 gap-4;
}

.switch-container {
  @apply flex items-center gap-3;
}

.switch {
  @apply relative inline-block w-12 h-6;
}

.switch input {
  @apply opacity-0 w-0 h-0;
}

.switch-slider {
  @apply absolute cursor-pointer top-0 left-0 right-0 bottom-0 bg-gray-300 dark:bg-gray-600 rounded-full transition-all duration-300;
}

.switch-slider::before {
  @apply absolute content-[""] h-5 w-5 left-0.5 bottom-0.5 bg-white rounded-full transition-all duration-300;
}

.switch input:checked + .switch-slider {
  @apply bg-blue-500;
}

.switch input:checked + .switch-slider::before {
  @apply translate-x-6;
}

.switch-label {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.schedule-preview {
  @apply p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg;
}

.schedule-preview h4 {
  @apply text-sm font-medium text-blue-800 dark:text-blue-200 m-0 mb-2;
}

.next-backup-time {
  @apply text-blue-600 dark:text-blue-400 font-mono m-0;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (width <= 768px) {
  .form-row {
    @apply grid-cols-1;
  }

  .backup-stats {
    @apply grid-cols-1;
  }

  .form-grid {
    @apply grid-cols-1;
  }

  .backup-actions {
    @apply grid grid-cols-3 gap-2;
  }
}
</style>
