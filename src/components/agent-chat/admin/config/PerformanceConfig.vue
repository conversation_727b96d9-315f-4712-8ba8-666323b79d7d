<template>
  <div class="performance-config">
    <el-form
      ref="formRef"
      :model="configData"
      :rules="rules"
      label-width="140px"
      class="config-form"
    >
      <!-- 缓存配置 -->
      <el-card class="config-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Lightning /></el-icon>
            <span>缓存配置</span>
          </div>
        </template>

        <el-form-item label="启用Redis缓存">
          <el-switch v-model="configData.enableRedisCache" />
        </el-form-item>

        <el-form-item
          label="Redis地址"
          prop="redisHost"
          v-if="configData.enableRedisCache"
        >
          <el-input
            v-model="configData.redisHost"
            placeholder="Redis服务器地址"
          />
        </el-form-item>

        <el-form-item
          label="Redis端口"
          prop="redisPort"
          v-if="configData.enableRedisCache"
        >
          <el-input-number
            v-model="configData.redisPort"
            :min="1"
            :max="65535"
            placeholder="端口号"
          />
        </el-form-item>

        <el-form-item label="缓存过期时间" prop="cacheExpiration">
          <el-input-number
            v-model="configData.cacheExpiration"
            :min="60"
            :max="86400"
            placeholder="秒"
            :disabled="!configData.enableRedisCache"
          />
        </el-form-item>

        <el-form-item label="最大缓存大小" prop="maxCacheSize">
          <el-input
            v-model="configData.maxCacheSize"
            placeholder="如: 512MB"
            :disabled="!configData.enableRedisCache"
          >
            <template #append>MB</template>
          </el-input>
        </el-form-item>
      </el-card>

      <!-- 数据库连接池 -->
      <el-card class="config-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Coin /></el-icon>
            <span>数据库连接池</span>
          </div>
        </template>

        <el-form-item label="最小连接数" prop="minConnections">
          <el-input-number
            v-model="configData.minConnections"
            :min="1"
            :max="20"
            placeholder="最小连接数"
          />
        </el-form-item>

        <el-form-item label="最大连接数" prop="maxConnections">
          <el-input-number
            v-model="configData.maxConnections"
            :min="5"
            :max="200"
            placeholder="最大连接数"
          />
        </el-form-item>

        <el-form-item label="连接超时时间" prop="connectionTimeout">
          <el-input-number
            v-model="configData.connectionTimeout"
            :min="1000"
            :max="30000"
            placeholder="毫秒"
          />
        </el-form-item>

        <el-form-item label="空闲超时时间" prop="idleTimeout">
          <el-input-number
            v-model="configData.idleTimeout"
            :min="30"
            :max="1800"
            placeholder="秒"
          />
        </el-form-item>

        <el-form-item label="连接验证间隔" prop="validationInterval">
          <el-input-number
            v-model="configData.validationInterval"
            :min="10"
            :max="300"
            placeholder="秒"
          />
        </el-form-item>
      </el-card>

      <!-- 请求限制 -->
      <el-card class="config-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Timer /></el-icon>
            <span>请求限制</span>
          </div>
        </template>

        <el-form-item label="启用请求限制">
          <el-switch v-model="configData.enableRateLimit" />
        </el-form-item>

        <el-form-item
          label="每分钟请求数"
          prop="requestsPerMinute"
          v-if="configData.enableRateLimit"
        >
          <el-input-number
            v-model="configData.requestsPerMinute"
            :min="10"
            :max="10000"
            placeholder="请求数"
          />
        </el-form-item>

        <el-form-item
          label="每小时请求数"
          prop="requestsPerHour"
          v-if="configData.enableRateLimit"
        >
          <el-input-number
            v-model="configData.requestsPerHour"
            :min="100"
            :max="100000"
            placeholder="请求数"
          />
        </el-form-item>

        <el-form-item label="请求超时时间" prop="requestTimeout">
          <el-input-number
            v-model="configData.requestTimeout"
            :min="1000"
            :max="60000"
            placeholder="毫秒"
          />
        </el-form-item>

        <el-form-item label="最大请求体大小" prop="maxRequestSize">
          <el-input
            v-model="configData.maxRequestSize"
            placeholder="如: 10MB"
          >
            <template #append>MB</template>
          </el-input>
        </el-form-item>
      </el-card>

      <!-- 并发控制 -->
      <el-card class="config-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Operation /></el-icon>
            <span>并发控制</span>
          </div>
        </template>

        <el-form-item label="最大并发线程" prop="maxConcurrentThreads">
          <el-input-number
            v-model="configData.maxConcurrentThreads"
            :min="1"
            :max="100"
            placeholder="线程数"
          />
        </el-form-item>

        <el-form-item label="队列最大长度" prop="maxQueueSize">
          <el-input-number
            v-model="configData.maxQueueSize"
            :min="10"
            :max="10000"
            placeholder="队列长度"
          />
        </el-form-item>

        <el-form-item label="线程池保持时间" prop="threadKeepAliveTime">
          <el-input-number
            v-model="configData.threadKeepAliveTime"
            :min="60"
            :max="3600"
            placeholder="秒"
          />
        </el-form-item>

        <el-form-item label="启用任务拒绝策略">
          <el-switch v-model="configData.enableRejectionPolicy" />
        </el-form-item>

        <el-form-item
          label="拒绝策略"
          prop="rejectionPolicy"
          v-if="configData.enableRejectionPolicy"
        >
          <el-select v-model="configData.rejectionPolicy" placeholder="选择拒绝策略">
            <el-option label="AbortPolicy" value="abort" />
            <el-option label="CallerRunsPolicy" value="caller_runs" />
            <el-option label="DiscardPolicy" value="discard" />
            <el-option label="DiscardOldestPolicy" value="discard_oldest" />
          </el-select>
        </el-form-item>
      </el-card>

      <!-- 性能优化 -->
      <el-card class="config-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><TrendCharts /></el-icon>
            <span>性能优化</span>
          </div>
        </template>

        <el-form-item label="启用GZIP压缩">
          <el-switch v-model="configData.enableGzipCompression" />
        </el-form-item>

        <el-form-item label="压缩阈值" prop="compressionThreshold" v-if="configData.enableGzipCompression">
          <el-input
            v-model="configData.compressionThreshold"
            placeholder="如: 1KB"
          >
            <template #append>KB</template>
          </el-input>
        </el-form-item>

        <el-form-item label="启用静态资源缓存">
          <el-switch v-model="configData.enableStaticCache" />
        </el-form-item>

        <el-form-item label="静态资源缓存时间" prop="staticCacheTime" v-if="configData.enableStaticCache">
          <el-input-number
            v-model="configData.staticCacheTime"
            :min="3600"
            :max="31536000"
            placeholder="秒"
          />
        </el-form-item>

        <el-form-item label="启用预加载">
          <el-switch v-model="configData.enablePreloading" />
        </el-form-item>

        <el-form-item label="预加载资源类型" v-if="configData.enablePreloading">
          <el-checkbox-group v-model="configData.preloadTypes">
            <el-checkbox label="css">CSS文件</el-checkbox>
            <el-checkbox label="js">JavaScript文件</el-checkbox>
            <el-checkbox label="fonts">字体文件</el-checkbox>
            <el-checkbox label="images">图片文件</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-card>
    </el-form>

    <div class="form-actions">
      <el-button @click="handleReset">重置</el-button>
      <el-button type="primary" @click="handleSave" :loading="saving">
        保存配置
      </el-button>
      <el-button @click="handleTest" :loading="testing">
        测试配置
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Lightning, Coin, Timer, Operation, TrendCharts } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

interface PerformanceConfig {
  // 缓存配置
  enableRedisCache: boolean
  redisHost: string
  redisPort: number
  cacheExpiration: number
  maxCacheSize: string

  // 数据库连接池
  minConnections: number
  maxConnections: number
  connectionTimeout: number
  idleTimeout: number
  validationInterval: number

  // 请求限制
  enableRateLimit: boolean
  requestsPerMinute: number
  requestsPerHour: number
  requestTimeout: number
  maxRequestSize: string

  // 并发控制
  maxConcurrentThreads: number
  maxQueueSize: number
  threadKeepAliveTime: number
  enableRejectionPolicy: boolean
  rejectionPolicy: string

  // 性能优化
  enableGzipCompression: boolean
  compressionThreshold: string
  enableStaticCache: boolean
  staticCacheTime: number
  enablePreloading: boolean
  preloadTypes: string[]
}

interface Emits {
  (e: 'save', config: PerformanceConfig): void
  (e: 'test', config: PerformanceConfig): void
}

const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const saving = ref(false)
const testing = ref(false)

const configData = reactive<PerformanceConfig>({
  // 缓存配置
  enableRedisCache: true,
  redisHost: 'localhost',
  redisPort: 6379,
  cacheExpiration: 3600,
  maxCacheSize: '512',

  // 数据库连接池
  minConnections: 5,
  maxConnections: 20,
  connectionTimeout: 10000,
  idleTimeout: 300,
  validationInterval: 30,

  // 请求限制
  enableRateLimit: true,
  requestsPerMinute: 100,
  requestsPerHour: 1000,
  requestTimeout: 30000,
  maxRequestSize: '10',

  // 并发控制
  maxConcurrentThreads: 10,
  maxQueueSize: 100,
  threadKeepAliveTime: 300,
  enableRejectionPolicy: true,
  rejectionPolicy: 'caller_runs',

  // 性能优化
  enableGzipCompression: true,
  compressionThreshold: '1',
  enableStaticCache: true,
  staticCacheTime: 86400,
  enablePreloading: true,
  preloadTypes: ['css', 'js']
})

const rules: FormRules = {
  redisHost: [
    { required: true, message: '请输入Redis地址', trigger: 'blur' }
  ],
  redisPort: [
    { required: true, message: '请输入Redis端口', trigger: 'blur' }
  ],
  cacheExpiration: [
    { required: true, message: '请输入缓存过期时间', trigger: 'blur' }
  ],
  maxCacheSize: [
    { required: true, message: '请输入最大缓存大小', trigger: 'blur' }
  ],
  minConnections: [
    { required: true, message: '请输入最小连接数', trigger: 'blur' }
  ],
  maxConnections: [
    { required: true, message: '请输入最大连接数', trigger: 'blur' }
  ],
  connectionTimeout: [
    { required: true, message: '请输入连接超时时间', trigger: 'blur' }
  ],
  idleTimeout: [
    { required: true, message: '请输入空闲超时时间', trigger: 'blur' }
  ],
  validationInterval: [
    { required: true, message: '请输入连接验证间隔', trigger: 'blur' }
  ],
  requestsPerMinute: [
    { required: true, message: '请输入每分钟请求数', trigger: 'blur' }
  ],
  requestsPerHour: [
    { required: true, message: '请输入每小时请求数', trigger: 'blur' }
  ],
  requestTimeout: [
    { required: true, message: '请输入请求超时时间', trigger: 'blur' }
  ],
  maxRequestSize: [
    { required: true, message: '请输入最大请求体大小', trigger: 'blur' }
  ],
  maxConcurrentThreads: [
    { required: true, message: '请输入最大并发线程数', trigger: 'blur' }
  ],
  maxQueueSize: [
    { required: true, message: '请输入队列最大长度', trigger: 'blur' }
  ],
  threadKeepAliveTime: [
    { required: true, message: '请输入线程池保持时间', trigger: 'blur' }
  ],
  rejectionPolicy: [
    { required: true, message: '请选择拒绝策略', trigger: 'change' }
  ],
  compressionThreshold: [
    { required: true, message: '请输入压缩阈值', trigger: 'blur' }
  ],
  staticCacheTime: [
    { required: true, message: '请输入静态资源缓存时间', trigger: 'blur' }
  ]
}

const handleSave = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    saving.value = true

    emit('save', { ...configData })
    ElMessage.success('性能配置保存成功')
  } catch (error) {
    ElMessage.error('请检查表单填写是否正确')
  } finally {
    saving.value = false
  }
}

const handleTest = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    testing.value = true

    emit('test', { ...configData })
  } catch (error) {
    ElMessage.error('请检查表单填写是否正确')
  } finally {
    testing.value = false
  }
}

const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

onMounted(() => {
  // 这里可以从API加载现有配置
  //console.log('性能配置组件已挂载')
})
</script>

<style scoped>
.performance-config {
  padding: 0;
}

.config-form {
  max-width: 800px;
}

.config-section {
  margin-bottom: 24px;
  border: 1px solid #ebeef5;
}

.config-section:last-of-type {
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  gap: 8px;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 20px 0;
  border-top: 1px solid #ebeef5;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  background: #fafafa;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__body) {
  padding: 24px 20px;
}

:deep(.el-form-item) {
  margin-bottom: 22px;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style>
