<template>
  <div class="monitoring-config">
    <el-form
      ref="formRef"
      :model="configData"
      :rules="rules"
      label-width="140px"
      class="config-form"
    >
      <!-- 日志配置 -->
      <el-card class="config-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Document /></el-icon>
            <span>日志配置</span>
          </div>
        </template>

        <el-form-item label="日志级别" prop="logLevel">
          <el-select v-model="configData.logLevel" placeholder="选择日志级别">
            <el-option label="DEBUG" value="DEBUG" />
            <el-option label="INFO" value="INFO" />
            <el-option label="WARN" value="WARN" />
            <el-option label="ERROR" value="ERROR" />
          </el-select>
        </el-form-item>

        <el-form-item label="日志保留天数" prop="logRetentionDays">
          <el-input-number
            v-model="configData.logRetentionDays"
            :min="1"
            :max="365"
            placeholder="天数"
          />
        </el-form-item>

        <el-form-item label="日志文件大小" prop="maxLogFileSize">
          <el-input
            v-model="configData.maxLogFileSize"
            placeholder="如: 100MB"
          >
            <template #append>MB</template>
          </el-input>
        </el-form-item>

        <el-form-item label="启用访问日志">
          <el-switch v-model="configData.enableAccessLog" />
        </el-form-item>

        <el-form-item label="启用错误日志">
          <el-switch v-model="configData.enableErrorLog" />
        </el-form-item>
      </el-card>

      <!-- 性能监控 -->
      <el-card class="config-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Monitor /></el-icon>
            <span>性能监控</span>
          </div>
        </template>

        <el-form-item label="启用性能监控">
          <el-switch v-model="configData.enablePerformanceMonitoring" />
        </el-form-item>

        <el-form-item label="监控采样率" prop="samplingRate">
          <el-slider
            v-model="configData.samplingRate"
            :min="0.1"
            :max="1"
            :step="0.1"
            :format-tooltip="formatPercentage"
            :disabled="!configData.enablePerformanceMonitoring"
          />
        </el-form-item>

        <el-form-item label="响应时间阈值" prop="responseTimeThreshold">
          <el-input-number
            v-model="configData.responseTimeThreshold"
            :min="100"
            :max="10000"
            placeholder="毫秒"
            :disabled="!configData.enablePerformanceMonitoring"
          />
        </el-form-item>

        <el-form-item label="内存使用阈值" prop="memoryThreshold">
          <el-input-number
            v-model="configData.memoryThreshold"
            :min="50"
            :max="95"
            placeholder="百分比"
            :disabled="!configData.enablePerformanceMonitoring"
          />
        </el-form-item>
      </el-card>

      <!-- 告警配置 -->
      <el-card class="config-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Bell /></el-icon>
            <span>告警配置</span>
          </div>
        </template>

        <el-form-item label="启用邮件告警">
          <el-switch v-model="configData.enableEmailAlert" />
        </el-form-item>

        <el-form-item
          label="告警邮箱"
          prop="alertEmail"
          v-if="configData.enableEmailAlert"
        >
          <el-input
            v-model="configData.alertEmail"
            placeholder="接收告警的邮箱地址"
          />
        </el-form-item>

        <el-form-item label="启用钉钉告警">
          <el-switch v-model="configData.enableDingTalkAlert" />
        </el-form-item>

        <el-form-item
          label="钉钉Webhook"
          prop="dingTalkWebhook"
          v-if="configData.enableDingTalkAlert"
        >
          <el-input
            v-model="configData.dingTalkWebhook"
            placeholder="钉钉机器人Webhook地址"
          />
        </el-form-item>

        <el-form-item label="告警频率限制" prop="alertCooldown">
          <el-input-number
            v-model="configData.alertCooldown"
            :min="1"
            :max="1440"
            placeholder="分钟"
          />
          <span class="form-tip">同类告警最小间隔时间</span>
        </el-form-item>
      </el-card>

      <!-- 健康检查 -->
      <el-card class="config-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><CircleCheck /></el-icon>
            <span>健康检查</span>
          </div>
        </template>

        <el-form-item label="启用健康检查">
          <el-switch v-model="configData.enableHealthCheck" />
        </el-form-item>

        <el-form-item
          label="检查间隔"
          prop="healthCheckInterval"
          v-if="configData.enableHealthCheck"
        >
          <el-input-number
            v-model="configData.healthCheckInterval"
            :min="10"
            :max="3600"
            placeholder="秒"
          />
        </el-form-item>

        <el-form-item label="检查项目" v-if="configData.enableHealthCheck">
          <el-checkbox-group v-model="configData.healthCheckItems">
            <el-checkbox label="database">数据库连接</el-checkbox>
            <el-checkbox label="redis">Redis连接</el-checkbox>
            <el-checkbox label="disk">磁盘空间</el-checkbox>
            <el-checkbox label="memory">内存使用</el-checkbox>
            <el-checkbox label="cpu">CPU使用率</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-card>
    </el-form>

    <div class="form-actions">
      <el-button @click="handleReset">重置</el-button>
      <el-button type="primary" @click="handleSave" :loading="saving">
        保存配置
      </el-button>
      <el-button @click="handleTest" :loading="testing">
        测试配置
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Document, Monitor, Bell, CircleCheck } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

interface MonitoringConfig {
  // 日志配置
  logLevel: string
  logRetentionDays: number
  maxLogFileSize: string
  enableAccessLog: boolean
  enableErrorLog: boolean

  // 性能监控
  enablePerformanceMonitoring: boolean
  samplingRate: number
  responseTimeThreshold: number
  memoryThreshold: number

  // 告警配置
  enableEmailAlert: boolean
  alertEmail: string
  enableDingTalkAlert: boolean
  dingTalkWebhook: string
  alertCooldown: number

  // 健康检查
  enableHealthCheck: boolean
  healthCheckInterval: number
  healthCheckItems: string[]
}

interface Emits {
  (e: 'save', config: MonitoringConfig): void
  (e: 'test', config: MonitoringConfig): void
}

const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const saving = ref(false)
const testing = ref(false)

const configData = reactive<MonitoringConfig>({
  logLevel: 'INFO',
  logRetentionDays: 30,
  maxLogFileSize: '100',
  enableAccessLog: true,
  enableErrorLog: true,

  enablePerformanceMonitoring: true,
  samplingRate: 0.1,
  responseTimeThreshold: 1000,
  memoryThreshold: 80,

  enableEmailAlert: false,
  alertEmail: '',
  enableDingTalkAlert: false,
  dingTalkWebhook: '',
  alertCooldown: 60,

  enableHealthCheck: true,
  healthCheckInterval: 60,
  healthCheckItems: ['database', 'redis', 'memory']
})

const rules: FormRules = {
  logRetentionDays: [
    { required: true, message: '请输入日志保留天数', trigger: 'blur' }
  ],
  maxLogFileSize: [
    { required: true, message: '请输入日志文件大小', trigger: 'blur' }
  ],
  responseTimeThreshold: [
    { required: true, message: '请输入响应时间阈值', trigger: 'blur' }
  ],
  memoryThreshold: [
    { required: true, message: '请输入内存使用阈值', trigger: 'blur' }
  ],
  alertEmail: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  dingTalkWebhook: [
    { type: 'url', message: '请输入正确的Webhook地址', trigger: 'blur' }
  ],
  alertCooldown: [
    { required: true, message: '请输入告警频率限制', trigger: 'blur' }
  ],
  healthCheckInterval: [
    { required: true, message: '请输入健康检查间隔', trigger: 'blur' }
  ]
}

const formatPercentage = (value: number) => {
  return `${(value * 100).toFixed(0)}%`
}

const handleSave = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    saving.value = true

    emit('save', { ...configData })
    ElMessage.success('监控配置保存成功')
  } catch (error) {
    ElMessage.error('请检查表单填写是否正确')
  } finally {
    saving.value = false
  }
}

const handleTest = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    testing.value = true

    emit('test', { ...configData })
  } catch (error) {
    ElMessage.error('请检查表单填写是否正确')
  } finally {
    testing.value = false
  }
}

const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

onMounted(() => {
  // 这里可以从API加载现有配置
  //console.log('监控配置组件已挂载')
})
</script>

<style scoped>
.monitoring-config {
  padding: 0;
}

.config-form {
  max-width: 800px;
}

.config-section {
  margin-bottom: 24px;
  border: 1px solid #ebeef5;
}

.config-section:last-of-type {
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  gap: 8px;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.form-tip {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 20px 0;
  border-top: 1px solid #ebeef5;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  background: #fafafa;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__body) {
  padding: 24px 20px;
}

:deep(.el-form-item) {
  margin-bottom: 22px;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style>
