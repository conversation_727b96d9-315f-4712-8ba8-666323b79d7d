<template>
  <div class="security-config">
    <el-form
      ref="formRef"
      :model="configData"
      :rules="rules"
      label-width="140px"
      class="config-form"
    >
      <!-- JWT配置 -->
      <el-card class="config-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Key /></el-icon>
            <span>JWT配置</span>
          </div>
        </template>

        <el-form-item label="JWT密钥" prop="jwtSecret">
          <el-input
            v-model="configData.jwtSecret"
            type="password"
            placeholder="JWT签名密钥"
            show-password
          />
        </el-form-item>

        <el-form-item label="访问令牌过期时间" prop="accessTokenExpiration">
          <el-input-number
            v-model="configData.accessTokenExpiration"
            :min="300"
            :max="86400"
            placeholder="秒"
          />
        </el-form-item>

        <el-form-item label="刷新令牌过期时间" prop="refreshTokenExpiration">
          <el-input-number
            v-model="configData.refreshTokenExpiration"
            :min="3600"
            :max="2592000"
            placeholder="秒"
          />
        </el-form-item>

        <el-form-item label="发行者" prop="jwtIssuer">
          <el-input
            v-model="configData.jwtIssuer"
            placeholder="JWT发行者标识"
          />
        </el-form-item>

        <el-form-item label="启用令牌刷新">
          <el-switch v-model="configData.enableTokenRefresh" />
        </el-form-item>
      </el-card>

      <!-- 密码策略 -->
      <el-card class="config-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Lock /></el-icon>
            <span>密码策略</span>
          </div>
        </template>

        <el-form-item label="最小密码长度" prop="minPasswordLength">
          <el-input-number
            v-model="configData.minPasswordLength"
            :min="6"
            :max="32"
            placeholder="字符数"
          />
        </el-form-item>

        <el-form-item label="最大密码长度" prop="maxPasswordLength">
          <el-input-number
            v-model="configData.maxPasswordLength"
            :min="8"
            :max="128"
            placeholder="字符数"
          />
        </el-form-item>

        <el-form-item label="密码复杂度要求">
          <el-checkbox-group v-model="configData.passwordRequirements">
            <el-checkbox label="uppercase">包含大写字母</el-checkbox>
            <el-checkbox label="lowercase">包含小写字母</el-checkbox>
            <el-checkbox label="numbers">包含数字</el-checkbox>
            <el-checkbox label="symbols">包含特殊字符</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="密码过期天数" prop="passwordExpirationDays">
          <el-input-number
            v-model="configData.passwordExpirationDays"
            :min="30"
            :max="365"
            placeholder="天数"
          />
        </el-form-item>

        <el-form-item label="禁止重复密码个数" prop="passwordHistoryCount">
          <el-input-number
            v-model="configData.passwordHistoryCount"
            :min="1"
            :max="10"
            placeholder="个数"
          />
        </el-form-item>
      </el-card>

      <!-- 登录安全 -->
      <el-card class="config-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><UserFilled /></el-icon>
            <span>登录安全</span>
          </div>
        </template>

        <el-form-item label="最大登录失败次数" prop="maxLoginAttempts">
          <el-input-number
            v-model="configData.maxLoginAttempts"
            :min="3"
            :max="10"
            placeholder="次数"
          />
        </el-form-item>

        <el-form-item label="账户锁定时间" prop="accountLockoutDuration">
          <el-input-number
            v-model="configData.accountLockoutDuration"
            :min="300"
            :max="86400"
            placeholder="秒"
          />
        </el-form-item>

        <el-form-item label="启用验证码">
          <el-switch v-model="configData.enableCaptcha" />
        </el-form-item>

        <el-form-item label="验证码触发次数" prop="captchaTriggerAttempts" v-if="configData.enableCaptcha">
          <el-input-number
            v-model="configData.captchaTriggerAttempts"
            :min="1"
            :max="5"
            placeholder="次数"
          />
        </el-form-item>

        <el-form-item label="启用双因素认证">
          <el-switch v-model="configData.enableTwoFactorAuth" />
        </el-form-item>

        <el-form-item label="会话超时时间" prop="sessionTimeout">
          <el-input-number
            v-model="configData.sessionTimeout"
            :min="900"
            :max="86400"
            placeholder="秒"
          />
        </el-form-item>
      </el-card>

      <!-- API安全 -->
      <el-card class="config-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Connection /></el-icon>
            <span>API安全</span>
          </div>
        </template>

        <el-form-item label="启用API密钥">
          <el-switch v-model="configData.enableApiKey" />
        </el-form-item>

        <el-form-item label="API密钥" prop="apiKey" v-if="configData.enableApiKey">
          <el-input
            v-model="configData.apiKey"
            type="password"
            placeholder="API访问密钥"
            show-password
          >
            <template #append>
              <el-button @click="generateApiKey">生成</el-button>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="允许的IP地址">
          <el-input
            v-model="configData.allowedIps"
            type="textarea"
            :rows="3"
            placeholder="每行一个IP地址或CIDR，留空表示允许所有IP"
          />
        </el-form-item>

        <el-form-item label="启用CORS">
          <el-switch v-model="configData.enableCors" />
        </el-form-item>

        <el-form-item label="允许的域名" prop="allowedOrigins" v-if="configData.enableCors">
          <el-input
            v-model="configData.allowedOrigins"
            type="textarea"
            :rows="2"
            placeholder="每行一个域名，如：https://example.com"
          />
        </el-form-item>

        <el-form-item label="启用HTTPS重定向">
          <el-switch v-model="configData.enforceHttps" />
        </el-form-item>
      </el-card>

      <!-- 数据加密 -->
      <el-card class="config-section" shadow="never">
        <template #header>
          <div class="section-header">
            <!-- <el-icon><Shield /></el-icon> -->
            <span>数据加密</span>
          </div>
        </template>

        <el-form-item label="数据库加密算法" prop="databaseEncryption">
          <el-select v-model="configData.databaseEncryption" placeholder="选择加密算法">
            <el-option label="AES-256" value="AES-256" />
            <el-option label="AES-192" value="AES-192" />
            <el-option label="AES-128" value="AES-128" />
          </el-select>
        </el-form-item>

        <el-form-item label="传输加密" prop="transportEncryption">
          <el-select v-model="configData.transportEncryption" placeholder="选择传输加密">
            <el-option label="TLS 1.3" value="TLS1.3" />
            <el-option label="TLS 1.2" value="TLS1.2" />
          </el-select>
        </el-form-item>

        <el-form-item label="启用敏感数据掩码">
          <el-switch v-model="configData.enableDataMasking" />
        </el-form-item>

        <el-form-item label="数据保留期限" prop="dataRetentionDays">
          <el-input-number
            v-model="configData.dataRetentionDays"
            :min="30"
            :max="2555"
            placeholder="天数"
          />
        </el-form-item>

        <el-form-item label="启用数据备份加密">
          <el-switch v-model="configData.enableBackupEncryption" />
        </el-form-item>
      </el-card>
    </el-form>

    <div class="form-actions">
      <el-button @click="handleReset">重置</el-button>
      <el-button type="primary" @click="handleSave" :loading="saving">
        保存配置
      </el-button>
      <el-button @click="handleTest" :loading="testing">
        测试配置
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Key, Lock, UserFilled, Connection } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

interface SecurityConfig {
  // JWT配置
  jwtSecret: string
  accessTokenExpiration: number
  refreshTokenExpiration: number
  jwtIssuer: string
  enableTokenRefresh: boolean

  // 密码策略
  minPasswordLength: number
  maxPasswordLength: number
  passwordRequirements: string[]
  passwordExpirationDays: number
  passwordHistoryCount: number

  // 登录安全
  maxLoginAttempts: number
  accountLockoutDuration: number
  enableCaptcha: boolean
  captchaTriggerAttempts: number
  enableTwoFactorAuth: boolean
  sessionTimeout: number

  // API安全
  enableApiKey: boolean
  apiKey: string
  allowedIps: string
  enableCors: boolean
  allowedOrigins: string
  enforceHttps: boolean

  // 数据加密
  databaseEncryption: string
  transportEncryption: string
  enableDataMasking: boolean
  dataRetentionDays: number
  enableBackupEncryption: boolean
}

interface Emits {
  (e: 'save', config: SecurityConfig): void
  (e: 'test', config: SecurityConfig): void
}

const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const saving = ref(false)
const testing = ref(false)

const configData = reactive<SecurityConfig>({
  // JWT配置
  jwtSecret: '',
  accessTokenExpiration: 3600,
  refreshTokenExpiration: 604800,
  jwtIssuer: 'dips-pro',
  enableTokenRefresh: true,

  // 密码策略
  minPasswordLength: 8,
  maxPasswordLength: 32,
  passwordRequirements: ['uppercase', 'lowercase', 'numbers'],
  passwordExpirationDays: 90,
  passwordHistoryCount: 5,

  // 登录安全
  maxLoginAttempts: 5,
  accountLockoutDuration: 1800,
  enableCaptcha: true,
  captchaTriggerAttempts: 3,
  enableTwoFactorAuth: false,
  sessionTimeout: 3600,

  // API安全
  enableApiKey: true,
  apiKey: '',
  allowedIps: '',
  enableCors: true,
  allowedOrigins: '',
  enforceHttps: true,

  // 数据加密
  databaseEncryption: 'AES-256',
  transportEncryption: 'TLS1.3',
  enableDataMasking: true,
  dataRetentionDays: 365,
  enableBackupEncryption: true
})

const rules: FormRules = {
  jwtSecret: [
    { required: true, message: '请输入JWT密钥', trigger: 'blur' },
    { min: 32, message: 'JWT密钥长度至少32位', trigger: 'blur' }
  ],
  accessTokenExpiration: [
    { required: true, message: '请输入访问令牌过期时间', trigger: 'blur' }
  ],
  refreshTokenExpiration: [
    { required: true, message: '请输入刷新令牌过期时间', trigger: 'blur' }
  ],
  jwtIssuer: [
    { required: true, message: '请输入JWT发行者', trigger: 'blur' }
  ],
  minPasswordLength: [
    { required: true, message: '请输入最小密码长度', trigger: 'blur' }
  ],
  maxPasswordLength: [
    { required: true, message: '请输入最大密码长度', trigger: 'blur' }
  ],
  passwordExpirationDays: [
    { required: true, message: '请输入密码过期天数', trigger: 'blur' }
  ],
  passwordHistoryCount: [
    { required: true, message: '请输入禁止重复密码个数', trigger: 'blur' }
  ],
  maxLoginAttempts: [
    { required: true, message: '请输入最大登录失败次数', trigger: 'blur' }
  ],
  accountLockoutDuration: [
    { required: true, message: '请输入账户锁定时间', trigger: 'blur' }
  ],
  captchaTriggerAttempts: [
    { required: true, message: '请输入验证码触发次数', trigger: 'blur' }
  ],
  sessionTimeout: [
    { required: true, message: '请输入会话超时时间', trigger: 'blur' }
  ],
  apiKey: [
    { required: true, message: '请输入API密钥', trigger: 'blur' },
    { min: 16, message: 'API密钥长度至少16位', trigger: 'blur' }
  ],
  allowedOrigins: [
    { required: true, message: '请输入允许的域名', trigger: 'blur' }
  ],
  databaseEncryption: [
    { required: true, message: '请选择数据库加密算法', trigger: 'change' }
  ],
  transportEncryption: [
    { required: true, message: '请选择传输加密方式', trigger: 'change' }
  ],
  dataRetentionDays: [
    { required: true, message: '请输入数据保留期限', trigger: 'blur' }
  ]
}

const generateApiKey = () => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < 32; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  configData.apiKey = result
  ElMessage.success('API密钥已生成')
}

const handleSave = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    saving.value = true

    emit('save', { ...configData })
    ElMessage.success('安全配置保存成功')
  } catch (error) {
    ElMessage.error('请检查表单填写是否正确')
  } finally {
    saving.value = false
  }
}

const handleTest = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    testing.value = true

    emit('test', { ...configData })
  } catch (error) {
    ElMessage.error('请检查表单填写是否正确')
  } finally {
    testing.value = false
  }
}

const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

onMounted(() => {
  // 这里可以从API加载现有配置
  //console.log('安全配置组件已挂载')
})
</script>

<style scoped>
.security-config {
  padding: 0;
}

.config-form {
  max-width: 800px;
}

.config-section {
  margin-bottom: 24px;
  border: 1px solid #ebeef5;
}

.config-section:last-of-type {
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 0;
  border-top: 1px solid #ebeef5;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  background: #fafafa;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__body) {
  padding: 24px 20px;
}

:deep(.el-form-item) {
  margin-bottom: 22px;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style>
