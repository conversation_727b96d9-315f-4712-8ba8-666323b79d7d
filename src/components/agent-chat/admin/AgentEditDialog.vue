<template>
  <div v-if="visible" class="dialog-overlay" :class="{ testing: testing }">
    <div class="dialog" ref="dialogRef">
      <div class="dialog-header">
        <h3 class="dialog-title">
          {{ mode === 'create' ? '创建 Agent' : '编辑 Agent' }}
        </h3>
        <button @click="handleClose" class="dialog-close">
          <i class="i-carbon-close"></i>
        </button>
      </div>

      <div class="dialog-body">
        <form @submit.prevent="handleSave" class="agent-form">
          <!-- 基本信息 -->
          <div class="form-section">
            <h4 class="section-title">基本信息</h4>

            <div class="form-grid">
              <div class="form-group">
                <label class="form-label" for="agent-name">
                  Agent 名称 <span class="required">*</span>
                </label>
                <input
                  id="agent-name"
                  v-model="formData.name"
                  type="text"
                  class="form-input"
                  :class="{ error: errors.name }"
                  placeholder="请输入 Agent 名称"
                  required
                >
                <div v-if="errors.name" class="error-message">{{ errors.name }}</div>
              </div>

              <div class="form-group">
                <label class="form-label" for="agent-category">
                  类别 <span class="required">*</span>
                </label>
                <select
                  id="agent-category"
                  v-model="formData.category"
                  class="form-select"
                  :class="{ error: errors.category }"
                  required
                >
                  <option value="">请选择类别</option>
                  <option value="INTENT_RECOGNITION">意图识别</option>
                  <option value="ANALYSIS">数据分析</option>
                  <option value="PROCESSING">数据处理</option>
                  <option value="INFERENCE">推理</option>
                  <option value="REPORTING">报告生成</option>
                  <option value="VISUALIZATION">可视化</option>
                </select>
                <div v-if="errors.category" class="error-message">{{ errors.category }}</div>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label" for="agent-description">
                描述 <span class="required">*</span>
              </label>
              <textarea
                id="agent-description"
                v-model="formData.description"
                class="form-textarea"
                :class="{ error: errors.description }"
                rows="3"
                placeholder="请描述这个 Agent 的功能和用途"
                required
              ></textarea>
              <div v-if="errors.description" class="error-message">{{ errors.description }}</div>
            </div>
          </div>

          <!-- 能力配置 -->
          <div class="form-section">
            <h4 class="section-title">能力配置</h4>

            <div class="form-group">
              <label class="form-label">Agent 能力</label>
              <div class="checkbox-group">
                <label
                  v-for="capability in availableCapabilities"
                  :key="capability.value"
                  class="checkbox-item"
                >
                  <input
                    type="checkbox"
                    :value="capability.value"
                    v-model="formData.capabilities"
                    class="checkbox-input"
                  >
                  <span class="checkbox-label">{{ capability.label }}</span>
                </label>
              </div>
            </div>

            <div class="form-grid">
              <div class="form-group">
                <label class="form-label" for="agent-priority">
                  优先级: {{ formData.priority }}
                </label>
                <input
                  id="agent-priority"
                  v-model.number="formData.priority"
                  type="range"
                  min="0"
                  max="100"
                  step="10"
                  class="range-input"
                >
                <div class="range-labels">
                  <span>低</span>
                  <span>高</span>
                </div>
                <div class="form-help">数值越高优先级越高，相同意图下优先级高的 Agent 将被优先选择</div>
              </div>

              <div class="form-group">
                <label class="form-label" for="agent-status">状态</label>
                <select
                  id="agent-status"
                  v-model="formData.status"
                  class="form-select"
                >
                  <option value="ACTIVE">启用</option>
                  <option value="INACTIVE">禁用</option>
                  <option value="DRAFT">草稿</option>
                </select>
              </div>
            </div>
          </div>

          <!-- 提示词配置 -->
          <div class="form-section">
            <h4 class="section-title">提示词配置</h4>

            <div class="form-group">
              <label class="form-label" for="system-prompt">
                系统提示词 <span class="required">*</span>
              </label>
              <div class="prompt-editor">
                <textarea
                  id="system-prompt"
                  v-model="formData.systemPrompt"
                  class="form-textarea prompt-textarea"
                  :class="{ error: errors.systemPrompt }"
                  rows="8"
                  placeholder="请输入系统提示词..."
                  required
                ></textarea>
                <div class="prompt-toolbar">
                  <button
                    type="button"
                    @click="insertTemplate('analysis')"
                    class="toolbar-btn"
                  >
                    分析模板
                  </button>
                  <button
                    type="button"
                    @click="insertTemplate('processing')"
                    class="toolbar-btn"
                  >
                    处理模板
                  </button>
                  <button
                    type="button"
                    @click="previewPrompt"
                    class="toolbar-btn"
                  >
                    预览效果
                  </button>
                </div>
              </div>
              <div v-if="errors.systemPrompt" class="error-message">{{ errors.systemPrompt }}</div>
            </div>

            <!-- 提示词变量 -->
            <div class="form-group">
              <div class="variables-header">
                <label class="form-label">提示词变量</label>
                <button
                  type="button"
                  @click="addVariable"
                  class="btn-small btn-primary"
                >
                  <i class="i-carbon-add"></i>
                  添加变量
                </button>
              </div>

              <div v-if="formData.promptVariables.length > 0" class="variables-list">
                <div
                  v-for="(variable, index) in formData.promptVariables"
                  :key="index"
                  class="variable-item"
                >
                  <input
                    v-model="variable.name"
                    type="text"
                    placeholder="变量名"
                    class="variable-input name"
                  >
                  <input
                    v-model="variable.description"
                    type="text"
                    placeholder="变量描述"
                    class="variable-input description"
                  >
                  <input
                    v-model="variable.defaultValue"
                    type="text"
                    placeholder="默认值"
                    class="variable-input default"
                  >
                  <button
                    type="button"
                    @click="removeVariable(index)"
                    class="remove-btn"
                  >
                    <i class="i-carbon-trash-can"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 高级配置 -->
          <div class="form-section">
            <h4 class="section-title">高级配置</h4>

            <div class="form-grid">
              <div class="form-group">
                <label class="form-label" for="agent-timeout">超时时间(秒)</label>
                <input
                  id="agent-timeout"
                  v-model.number="formData.timeout"
                  type="number"
                  min="1"
                  max="300"
                  class="form-input"
                >
              </div>

              <div class="form-group">
                <label class="form-label" for="agent-retries">重试次数</label>
                <input
                  id="agent-retries"
                  v-model.number="formData.maxRetries"
                  type="number"
                  min="0"
                  max="5"
                  class="form-input"
                >
              </div>
            </div>

            <div class="form-group">
              <label class="form-label" for="agent-config">配置参数 (JSON)</label>
              <textarea
                id="agent-config"
                v-model="configJson"
                class="form-textarea code-textarea"
                :class="{ error: errors.configuration }"
                rows="6"
                placeholder='{"temperature": 0.7, "maxTokens": 2048}'
              ></textarea>
              <div v-if="errors.configuration" class="error-message">{{ errors.configuration }}</div>
            </div>
          </div>
        </form>
      </div>

      <div class="dialog-footer">
        <button type="button" @click="handleClose" class="btn-secondary">
          取消
        </button>
        <button
          type="button"
          @click="testAgent"
          class="btn-outline"
          :disabled="!isFormValid || testing"
        >
          <i v-if="testing" class="i-carbon-in-progress animate-spin"></i>
          <i v-else class="i-carbon-play-filled"></i>
          {{ testing ? '测试中...' : '测试' }}
        </button>
        <button
          type="button"
          @click="handleSave"
          class="btn-primary"
          :disabled="!isFormValid || saving"
        >
          <i v-if="saving" class="i-carbon-in-progress animate-spin"></i>
          {{ saving ? '保存中...' : (mode === 'create' ? '创建' : '保存') }}
        </button>
      </div>
    </div>
  </div>

  <!-- 测试结果对话框 -->
  <TestResultDialog
    v-model:visible="testDialogVisible"
    :status="testStatus"
    :result="testResult"
    :error-message="testErrorMessage"
    :error-details="testErrorDetails"
    :test-input="testInput"
    :on-retry="retryTest"
    @cancel="cancelTest"
    @retry="retryTest"
  />
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import type { AgentDefinition, PromptVariable } from '@/api/agentManagement'
import { AgentManagementService } from '@/api/agentManagement'
import TestResultDialog from './TestResultDialog.vue'

interface Props {
  visible: boolean
  agent?: AgentDefinition | null
  mode: 'create' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  agent: null
})

const emit = defineEmits<{
  'update:visible': [value: boolean]
  save: [agent: AgentDefinition]
  cancel: []
}>()

// 响应式数据
const dialogRef = ref<HTMLElement>()
const saving = ref(false)
const testing = ref(false)

// 测试相关状态
const testDialogVisible = ref(false)
const testStatus = ref<'testing' | 'success' | 'error'>('testing')
const testResult = ref<any>(null)
const testErrorMessage = ref('')
const testErrorDetails = ref('')
const testInput = ref('')

const formData = ref({
  id: undefined as number | undefined,
  name: '',
  description: '',
  category: '',
  capabilities: [] as string[],
  systemPrompt: '',
  promptVariables: [] as PromptVariable[],
  priority: 50,
  timeout: 30,
  maxRetries: 3,
  status: 'DRAFT' as 'ACTIVE' | 'INACTIVE' | 'DRAFT',
  configuration: {}
})

const configJson = ref('{}')
const errors = ref<Record<string, string>>({})

// 可用能力选项
const availableCapabilities = [
  { value: 'data_analysis', label: '数据分析' },
  { value: 'chart_generation', label: '图表生成' },
  { value: 'report_writing', label: '报告撰写' },
  { value: 'data_processing', label: '数据处理' },
  { value: 'pattern_recognition', label: '模式识别' },
  { value: 'prediction', label: '预测分析' }
]

// 计算属性
const isFormValid = computed(() => {
  return formData.value.name.trim() &&
         formData.value.description.trim() &&
         formData.value.category &&
         formData.value.systemPrompt.trim() &&
         Object.keys(errors.value).length === 0
})

// 监听器
watch(() => props.visible, (newVal) => {
  if (newVal) {
    if (props.agent && props.mode === 'edit') {
      // 编辑模式，填充表单数据
      Object.assign(formData.value, {
        ...props.agent,
        promptVariables: props.agent.promptVariables || []
      })
      configJson.value = JSON.stringify(props.agent.configuration || {}, null, 2)
    } else {
      // 创建模式，重置表单
      resetForm()
    }

    nextTick(() => {
      validateForm()
    })
  }
})

watch(configJson, (newVal) => {
  try {
    formData.value.configuration = JSON.parse(newVal)
    delete errors.value.configuration
  } catch (error) {
    errors.value.configuration = 'JSON 格式错误'
  }
})

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
})

// 方法
function resetForm() {
  formData.value = {
    id: undefined,
    name: '',
    description: '',
    category: '',
    capabilities: [],
    systemPrompt: '',
    promptVariables: [],
    priority: 50,
    timeout: 30,
    maxRetries: 3,
    status: 'DRAFT',
    configuration: {}
  }
  configJson.value = '{}'
  errors.value = {}
}

function validateForm() {
  errors.value = {}

  if (!formData.value.name.trim()) {
    errors.value.name = '请输入 Agent 名称'
  } else if (formData.value.name.length < 2 || formData.value.name.length > 50) {
    errors.value.name = '名称长度在 2 到 50 个字符'
  }

  if (!formData.value.description.trim()) {
    errors.value.description = '请输入 Agent 描述'
  } else if (formData.value.description.length > 500) {
    errors.value.description = '描述不能超过 500 个字符'
  }

  if (!formData.value.category) {
    errors.value.category = '请选择 Agent 类别'
  }

  if (!formData.value.systemPrompt.trim()) {
    errors.value.systemPrompt = '请输入系统提示词'
  } else if (formData.value.systemPrompt.length < 10) {
    errors.value.systemPrompt = '提示词至少需要 10 个字符'
  }

  try {
    JSON.parse(configJson.value)
    delete errors.value.configuration
  } catch (error) {
    errors.value.configuration = 'JSON 格式错误'
  }
}

async function handleSave() {
  validateForm()

  if (!isFormValid.value) {
    return
  }

  saving.value = true

  try {
    const agentData = {
      ...formData.value,
      promptVariables: formData.value.promptVariables.filter(v => v.name.trim())
    }

    emit('save', agentData)
  } catch (error) {
    console.error('Save failed:', error)
  } finally {
    saving.value = false
  }
}

function handleClose() {
  emit('update:visible', false)
  emit('cancel')
}

function addVariable() {
  formData.value.promptVariables.push({
    name: '',
    description: '',
    defaultValue: ''
  })
}

function removeVariable(index: number) {
  formData.value.promptVariables.splice(index, 1)
}

function insertTemplate(type: string) {
  const templates = {
    analysis: `你是一个专业的数据分析师。请根据用户提供的数据进行深入分析，包括：

1. 数据概览和基本统计
2. 趋势分析和模式识别
3. 异常值检测
4. 关键洞察和建议

请用专业、清晰的语言回答用户的问题。`,

    processing: `你是一个数据处理专家。请根据用户需求对数据进行处理，包括：

1. 数据清洗和去重
2. 格式转换和标准化
3. 数据筛选和聚合
4. 质量检查和验证

处理过程中请保持数据的准确性和完整性。`
  }

  const template = templates[type as keyof typeof templates]
  if (template) {
    formData.value.systemPrompt = template
  }
}

function previewPrompt() {
  // 这里可以打开预览对话框
  //console.log('Preview prompt:', formData.value.systemPrompt)
}

async function testAgent() {
  validateForm()

  if (!isFormValid.value) {
    return
  }

  // 如果是创建模式且没有保存，不能测试
  if (props.mode === 'create' && !formData.value.id) {
    // 使用优美的通知替代alert
    showTestError('请先保存Agent后再进行测试', '需要先保存Agent配置才能进行测试')
    return
  }

  // 开始测试
  startTest()

  try {
    const testData = {
      input: '这是一个测试消息，用于验证Agent的响应能力',
      parameters: {
        systemPrompt: formData.value.systemPrompt,
        configuration: formData.value.configuration
      }
    }

    // 保存测试输入用于显示
    testInput.value = testData.input

    const response = await AgentManagementService.testAgent(
      formData.value.id!,
      testData
    )

    if (response.success) {
      showTestSuccess(response.data)
    } else {
      throw new Error(response.message || response.error || '测试失败')
    }
  } catch (error: any) {
    console.error('Agent测试失败:', error)
    showTestError(error.message || '网络连接错误', error.stack || '')
  } finally {
    testing.value = false
  }
}

// 测试相关辅助方法
function startTest() {
  testing.value = true
  testStatus.value = 'testing'
  testResult.value = null
  testErrorMessage.value = ''
  testErrorDetails.value = ''
  testDialogVisible.value = true
}

function showTestSuccess(result: any) {
  testStatus.value = 'success'
  testResult.value = result
}

function showTestError(message: string, details?: string) {
  testStatus.value = 'error'
  testErrorMessage.value = message
  testErrorDetails.value = details || ''
  testDialogVisible.value = true
}

function cancelTest() {
  testing.value = false
  testDialogVisible.value = false
}

function retryTest() {
  testDialogVisible.value = false
  // 延迟一下再重新测试，让对话框完全关闭
  setTimeout(() => {
    testAgent()
  }, 100)
}

function handleKeyDown(event: KeyboardEvent) {
  if (props.visible && event.key === 'Escape') {
    handleClose()
  }
}
</script>

<style scoped>
.dialog-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4;

  /* 测试时增加更强的遮罩效果 */
}

.dialog-overlay.testing {
  @apply bg-black bg-opacity-70;

  backdrop-filter: blur(2px);
}

.dialog {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col;
}

.dialog-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700;
}

.dialog-title {
  @apply text-xl font-semibold text-gray-900 dark:text-white m-0;
}

.dialog-close {
  @apply w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 flex items-center justify-center text-gray-500 dark:text-gray-400 transition-colors;
}

.dialog-body {
  @apply flex-1 overflow-y-auto p-6;
}

.agent-form {
  @apply space-y-6;
}

.form-section {
  @apply space-y-4;
}

.section-title {
  @apply text-lg font-medium text-gray-900 dark:text-white m-0 pb-2 border-b border-gray-200 dark:border-gray-700;
}

.form-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.required {
  @apply text-red-500;
}

.form-input,
.form-select,
.form-textarea {
  @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors;
}

.form-input.error,
.form-select.error,
.form-textarea.error {
  @apply border-red-500 ring-2 ring-red-200;
}

.form-textarea {
  @apply resize-none;
}

.range-input {
  @apply w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer;
}

.range-input::-webkit-slider-thumb {
  @apply appearance-none w-4 h-4 bg-blue-500 rounded-full cursor-pointer;
}

.range-labels {
  @apply flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1;
}

.form-help {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

.checkbox-group {
  @apply grid grid-cols-1 sm:grid-cols-2 gap-3;
}

.checkbox-item {
  @apply flex items-center gap-2 cursor-pointer;
}

.checkbox-input {
  @apply w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600;
}

.checkbox-label {
  @apply text-sm text-gray-700 dark:text-gray-300;
}

.prompt-editor {
  @apply space-y-3;
}

.prompt-textarea {
  @apply font-mono text-sm;
}

.prompt-toolbar {
  @apply flex flex-wrap gap-2;
}

.toolbar-btn {
  @apply px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded transition-colors;
}

.variables-header {
  @apply flex items-center justify-between;
}

.variables-list {
  @apply space-y-3 p-3 bg-gray-50 dark:bg-gray-900 rounded-lg;
}

.variable-item {
  @apply grid grid-cols-1 sm:grid-cols-[1fr_2fr_1fr_auto] gap-2 items-center;
}

.variable-input {
  @apply px-3 py-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.remove-btn {
  @apply w-8 h-8 rounded bg-red-100 hover:bg-red-200 dark:bg-red-900 dark:hover:bg-red-800 flex items-center justify-center text-red-600 dark:text-red-400 transition-colors;
}

.code-textarea {
  @apply font-mono text-sm;
}

.error-message {
  @apply text-sm text-red-600 dark:text-red-400;
}

.dialog-footer {
  @apply flex items-center justify-end gap-3 p-6 border-t border-gray-200 dark:border-gray-700;
}

.btn-primary {
  @apply flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white rounded-lg transition-colors;
}

.btn-secondary {
  @apply flex items-center gap-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors;
}

.btn-outline {
  @apply flex items-center gap-2 px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg transition-colors;
}

.btn-small {
  @apply flex items-center gap-1 px-3 py-1.5 text-sm rounded-lg transition-colors;
}

.btn-small.btn-primary {
  @apply bg-blue-500 hover:bg-blue-600 text-white;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (width <= 768px) {
  .dialog {
    @apply max-w-full mx-2;
  }

  .dialog-body {
    @apply p-4;
  }

  .form-grid {
    @apply grid-cols-1;
  }

  .variable-item {
    @apply grid-cols-1;
  }

  .dialog-footer {
    @apply flex-col items-stretch;
  }
}
</style>
