<template>
  <div class="alert-item" :class="[`alert-${alert.type}`, { dismissed: alert.dismissed }]">
    <div class="alert-icon">
      <i :class="getAlertIcon()"></i>
    </div>

    <div class="alert-content">
      <div class="alert-header">
        <h4 class="alert-title">{{ alert.title }}</h4>
        <div class="alert-meta">
          <span class="alert-time">{{ formatTime(alert.timestamp) }}</span>
          <span v-if="alert.dismissed" class="alert-status">已忽略</span>
        </div>
      </div>

      <div class="alert-message">{{ alert.message }}</div>

      <div v-if="alert.details" class="alert-details">
        <button
          @click="showDetails = !showDetails"
          class="details-toggle"
        >
          <i :class="showDetails ? 'i-carbon-chevron-up' : 'i-carbon-chevron-down'"></i>
          {{ showDetails ? '收起详情' : '查看详情' }}
        </button>

        <div v-if="showDetails" class="details-content">
          <pre>{{ formatDetails(alert.details) }}</pre>
        </div>
      </div>
    </div>

    <div class="alert-actions">
      <button
        v-if="!alert.dismissed"
        @click="handleDismiss"
        class="action-btn dismiss-btn"
        title="忽略此告警"
      >
        <i class="i-carbon-close"></i>
      </button>

      <button
        @click="handleCopy"
        class="action-btn copy-btn"
        title="复制告警信息"
      >
        <i class="i-carbon-copy"></i>
      </button>

      <div class="action-dropdown" v-if="showDropdown">
        <button
          @click="toggleDropdown"
          class="action-btn dropdown-btn"
          :class="{ active: dropdownVisible }"
        >
          <i class="i-carbon-overflow-menu-vertical"></i>
        </button>

        <div v-if="dropdownVisible" class="dropdown-menu">
          <button @click="handleViewDetails" class="dropdown-item">
            <i class="i-carbon-view"></i>
            查看详情
          </button>
          <button @click="handleCreateIssue" class="dropdown-item">
            <i class="i-carbon-add-alt"></i>
            创建工单
          </button>
          <button @click="handleMarkResolved" class="dropdown-item">
            <i class="i-carbon-checkmark"></i>
            标记已解决
          </button>
          <div class="dropdown-divider"></div>
          <button @click="handleDelete" class="dropdown-item danger">
            <i class="i-carbon-trash-can"></i>
            删除
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import type { Alert } from '@/api/agentManagement'

interface Props {
  alert: Alert
  showDropdown?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showDropdown: true
})

const emit = defineEmits<{
  dismiss: [id: string]
  copy: [alert: Alert]
  viewDetails: [alert: Alert]
  createIssue: [alert: Alert]
  markResolved: [alert: Alert]
  delete: [alert: Alert]
}>()

// 响应式数据
const showDetails = ref(false)
const dropdownVisible = ref(false)

// 计算属性
const alertAge = computed(() => {
  const now = new Date()
  const alertTime = new Date(props.alert.timestamp)
  const diffMs = now.getTime() - alertTime.getTime()

  const minutes = Math.floor(diffMs / (1000 * 60))
  const hours = Math.floor(diffMs / (1000 * 60 * 60))
  const days = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (days > 0) return `${days}天前`
  if (hours > 0) return `${hours}小时前`
  if (minutes > 0) return `${minutes}分钟前`
  return '刚刚'
})

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// 方法
function getAlertIcon(): string {
  switch (props.alert.type) {
    case 'error':
      return 'i-carbon-error-filled'
    case 'warning':
      return 'i-carbon-warning-filled'
    case 'info':
      return 'i-carbon-information-filled'
    default:
      return 'i-carbon-notification'
  }
}

function formatTime(timestamp: string): string {
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

function formatDetails(details: any): string {
  if (typeof details === 'string') {
    return details
  }
  return JSON.stringify(details, null, 2)
}

function handleDismiss() {
  emit('dismiss', props.alert.id)
}

function handleCopy() {
  const alertText = `[${props.alert.type.toUpperCase()}] ${props.alert.title}\n${props.alert.message}\n时间: ${props.alert.timestamp}`

  navigator.clipboard.writeText(alertText).then(() => {
    // 这里可以显示复制成功的提示
    //console.log('告警信息已复制到剪贴板')
  }).catch(err => {
    console.error('复制失败:', err)
  })

  emit('copy', props.alert)
}

function toggleDropdown() {
  dropdownVisible.value = !dropdownVisible.value
}

function handleClickOutside(event: Event) {
  const target = event.target as HTMLElement
  if (!target.closest('.action-dropdown')) {
    dropdownVisible.value = false
  }
}

function handleViewDetails() {
  dropdownVisible.value = false
  emit('viewDetails', props.alert)
}

function handleCreateIssue() {
  dropdownVisible.value = false
  emit('createIssue', props.alert)
}

function handleMarkResolved() {
  dropdownVisible.value = false
  emit('markResolved', props.alert)
}

function handleDelete() {
  dropdownVisible.value = false
  emit('delete', props.alert)
}
</script>

<style scoped>
.alert-item {
  @apply flex gap-3 p-4 rounded-lg border-l-4 transition-all duration-200;
}

.alert-error {
  @apply bg-red-50 dark:bg-red-900/20 border-red-500 border-l-red-500;
}

.alert-warning {
  @apply bg-yellow-50 dark:bg-yellow-900/20 border-yellow-500 border-l-yellow-500;
}

.alert-info {
  @apply bg-blue-50 dark:bg-blue-900/20 border-blue-500 border-l-blue-500;
}

.alert-item.dismissed {
  @apply opacity-60;
}

.alert-icon {
  @apply w-6 h-6 flex-shrink-0 flex items-center justify-center text-lg;
}

.alert-error .alert-icon {
  @apply text-red-600 dark:text-red-400;
}

.alert-warning .alert-icon {
  @apply text-yellow-600 dark:text-yellow-400;
}

.alert-info .alert-icon {
  @apply text-blue-600 dark:text-blue-400;
}

.alert-content {
  @apply flex-1 space-y-2;
}

.alert-header {
  @apply flex items-start justify-between gap-3;
}

.alert-title {
  @apply text-lg font-semibold m-0;
}

.alert-error .alert-title {
  @apply text-red-800 dark:text-red-200;
}

.alert-warning .alert-title {
  @apply text-yellow-800 dark:text-yellow-200;
}

.alert-info .alert-title {
  @apply text-blue-800 dark:text-blue-200;
}

.alert-meta {
  @apply flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400;
}

.alert-time {
  @apply whitespace-nowrap;
}

.alert-status {
  @apply px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded text-xs;
}

.alert-message {
  @apply text-gray-700 dark:text-gray-300 leading-relaxed;
}

.alert-details {
  @apply space-y-2;
}

.details-toggle {
  @apply flex items-center gap-1 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors;
}

.details-content {
  @apply bg-gray-100 dark:bg-gray-800 rounded p-3 overflow-x-auto;
}

.details-content pre {
  @apply text-sm font-mono text-gray-800 dark:text-gray-200 whitespace-pre-wrap;
}

.alert-actions {
  @apply flex items-start gap-1;
}

.action-btn {
  @apply w-8 h-8 rounded bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 flex items-center justify-center text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:border-gray-400 dark:hover:border-gray-500 transition-colors;
}

.dismiss-btn:hover {
  @apply text-red-600 dark:text-red-400 border-red-300 dark:border-red-600;
}

.copy-btn:hover {
  @apply text-blue-600 dark:text-blue-400 border-blue-300 dark:border-blue-600;
}

.action-dropdown {
  @apply relative;
}

.dropdown-btn.active {
  @apply bg-gray-100 dark:bg-gray-600 border-gray-400 dark:border-gray-500;
}

.dropdown-menu {
  @apply absolute top-full right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg py-1 z-10 min-w-[150px];
}

.dropdown-item {
  @apply w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left;
}

.dropdown-item.danger {
  @apply text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20;
}

.dropdown-divider {
  @apply h-px bg-gray-200 dark:bg-gray-700 my-1;
}

/* 动画效果 */
.alert-item {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media (width <= 768px) {
  .alert-item {
    @apply flex-col gap-3;
  }

  .alert-header {
    @apply flex-col items-start gap-1;
  }

  .alert-actions {
    @apply flex-row justify-end;
  }
}
</style>
