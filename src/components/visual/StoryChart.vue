<script setup lang="ts">
import {onMounted, ref} from 'vue'
import VueECharts from 'vue-echarts'
import * as echarts from 'echarts/core'
import {CanvasRenderer} from 'echarts/renderers'
import * as components from 'echarts/components';
import * as charts from 'echarts/charts';

const allComponents = Object.values(components);
const allCharts = Object.values(charts);

echarts.use([
  CanvasRenderer,
  ...allComponents,
  ...allCharts
]);

const props = defineProps<{
  optionArray: any[]
  index: number
  height: number
}>()

const chartOption = ref<any>({
  title: {
    show: true,
    text: '本图表暂无数据',
    left: 'center',
    top: 'middle',
    textStyle: {
      color: '#969696',
      fontSize: 12,
      fontWeight: 'normal'
    }
  },
  series: []
})

const optionConfig = (chartOption: any) => {
  if (chartOption.xAxis && Array.isArray(chartOption.xAxis)) {
    for (let i = 0; i < chartOption.xAxis.length; i++) {
      const xAxis = chartOption.xAxis[i]
      if (xAxis.axisLabel && xAxis.axisLabel.vertical) {
        xAxis.axisLabel = {
          fontWeight: 'normal',
          rotate: 0,
          interval: 0,
          formatter: (value: string) => value.split("").join("\n")
        }
      }
    }
  }

  if (chartOption.tooltip?.formatter) {
    chartOption.tooltip.formatter = new Function("params", chartOption.tooltip.formatter + "")
  }

  if (chartOption.geoName) {
    const json = new Function("return " + chartOption.geoName)()
    echarts.registerMap('vkMap', json)
    chartOption.geoName = null
  }
}

const seriesConfig = (chartOption: any) => {
  if (!chartOption.series || !Array.isArray(chartOption.series)) return

  for (let i = 0; i < chartOption.series.length; i++) {
    const series = chartOption.series[i]
    if (series.label?.normal?.formatter) {
      series.label.normal.formatter = new Function("params", series.label.normal.formatter + "")
    }
    if (series.markLine?.itemStyle?.normal?.label?.formatter) {
      series.markLine.itemStyle.normal.label.formatter =
        new Function("params", series.markLine.itemStyle.normal.label.formatter + "")
    }
    if (series.animationDelay !== undefined && series.animationDelay !== null) {
      series.animationDelay = new Function("idx", series.animationDelay + "")
    }
  }
}

const handleAxis = (axis: any): any => {
  if (Array.isArray(axis)) {
    return axis.map(handleAxis)
  }

  if (axis && typeof axis === 'object') {
    if (!axis.axisLine || axis.axisLine.show !== false) {
      axis.axisLine = {
        show: true,
        ...(axis.axisLine || {})
      }
    }
    if (!axis.axisTick || axis.axisTick.show !== false) {
      axis.axisTick = {
        show: true,
        ...(axis.axisTick || {})
      }
    }
  }
  return axis
}

const upgradeConfig = (obj: any): any => {
  if (!obj || typeof obj !== 'object') return obj

  if (obj.xAxis)
    obj.xAxis = handleAxis(obj.xAxis)

  if (obj.yAxis)
    obj.yAxis = handleAxis(obj.yAxis)

  if (obj.axisLabel?.textStyle) {
    obj.axisLabel = {...obj.axisLabel, ...obj.axisLabel.textStyle};
    delete obj.axisLabel.textStyle;
  }

  if (obj.itemStyle?.normal) {
    obj.itemStyle = {...obj.itemStyle.normal, ...obj.itemStyle}
    delete obj.itemStyle.normal
  }

  if (obj.label) {
    if (obj.label.normal) {
      obj.label = {...obj.label.normal, ...obj.label}
      delete obj.label.normal
    }

    if (obj.label.textStyle) {
      obj.label = {...obj.label, ...obj.label.textStyle}
      delete obj.label.textStyle
    }

    if (!obj.label.color && obj.type !== 'effectScatter') {
      obj.label.color = 'inherit'
    }
  }

  if (obj.lineStyle?.normal) {
    obj.lineStyle = {...obj.lineStyle.normal, ...obj.lineStyle}
    delete obj.lineStyle.normal
  }

  if (obj.markLine) {
    if (obj.markLine.itemStyle?.normal) {
      obj.markLine = {...obj.markLine.itemStyle.normal, ...obj.markLine.itemStyle, ...obj.markLine}
      delete obj.markLine.itemStyle
      delete obj.markLine.normal

      const color = obj.markLine.color
      if (color) {
        obj.markLine.lineStyle = obj.markLine.lineStyle || {}
        obj.markLine.lineStyle.color = color
      }
    }

    if (obj.markLine.label?.normal) {
      obj.markLine.label = {...obj.markLine.label.normal, ...obj.markLine.label}
      delete obj.markLine.label.normal
    }
  }

  Object.keys(obj).forEach(key => {
    if (typeof obj[key] === 'object') {
      obj[key] = upgradeConfig(obj[key])
    }
  })

  return obj
}

const loadChart = () => {
  const chartOptionStr = JSON.stringify(props.optionArray[props.index])
  const newChartOption = JSON.parse(chartOptionStr)
  let show = false

  if (newChartOption?.series) {
    optionConfig(newChartOption)
    seriesConfig(newChartOption)
    show = true
  }
  if (newChartOption?.baseOption && newChartOption?.options) {
    optionConfig(newChartOption.baseOption)
    newChartOption.options
      .filter((option: any) => option.series)
      .forEach((option: any) => {
        seriesConfig(option)
      })
    show = true
  }

  if (show) {
    chartOption.value = upgradeConfig(newChartOption)
  }
}

onMounted(() => {
  loadChart()
})
</script>

<template>
  <div class="container">
    <div>
      <VueECharts
        :option="chartOption"
        :style="{ height: `${height}px`, width: '100%' }"
      />
    </div>
  </div>
</template>

<style scoped>
.container {
  width: 100%;
  position: relative;
}
</style>
