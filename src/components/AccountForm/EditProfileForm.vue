<i18n lang="json">
{
  "zh-cn": {
    "intro": "基本设置",
    "subIntro": "管理您的账号基本信息，包括头像、昵称等",
    "form": {
      "avatar": "头像",
      "realName": "真实姓名",
      "nickname": "昵称",
      "email": "邮箱",
      "phone": "手机号",
      "gender": "性别",
      "birthday": "生日",
      "save": "保存",
      "uploading": "上传中...",
      "uploadAvatar": "上传头像"
    },
    "gender": {
      "unknown": "未知",
      "male": "男",
      "female": "女"
    },
    "rules": {
      "realName": "请输入真实姓名",
      "realNameLength": "真实姓名长度不能超过64个字符",
      "nickname": "昵称长度不能超过64个字符",
      "email": "请输入正确的邮箱格式",
      "emailLength": "邮箱长度不能超过128个字符",
      "phone": "请输入正确的手机号格式"
    },
    "messages": {
      "updateSuccess": "个人信息更新成功",
      "updateFailed": "个人信息更新失败",
      "loadFailed": "加载用户信息失败"
    }
  },
  "zh-tw": {
    "intro": "基本設置",
    "subIntro": "管理您的賬號基本信息，包括頭像、昵稱等",
    "form": {
      "avatar": "頭像",
      "realName": "真實姓名",
      "nickname": "昵稱",
      "email": "郵箱",
      "phone": "手機號",
      "gender": "性別",
      "birthday": "生日",
      "save": "保存",
      "uploading": "上傳中...",
      "uploadAvatar": "上傳頭像"
    },
    "gender": {
      "unknown": "未知",
      "male": "男",
      "female": "女"
    },
    "rules": {
      "realName": "請輸入真實姓名",
      "realNameLength": "真實姓名長度不能超過64個字符",
      "nickname": "昵稱長度不能超過64個字符",
      "email": "請輸入正確的郵箱格式",
      "emailLength": "郵箱長度不能超過128個字符",
      "phone": "請輸入正確的手機號格式"
    },
    "messages": {
      "updateSuccess": "個人信息更新成功",
      "updateFailed": "個人信息更新失敗",
      "loadFailed": "加載用戶信息失敗"
    }
  },
  "en": {
    "intro": "Basic Settings",
    "subIntro": "Manage your account basic information, including avatar, nickname, etc.",
    "form": {
      "avatar": "Avatar",
      "realName": "Real Name",
      "nickname": "Nickname",
      "email": "Email",
      "phone": "Phone",
      "gender": "Gender",
      "birthday": "Birthday",
      "save": "Save",
      "uploading": "Uploading...",
      "uploadAvatar": "Upload Avatar"
    },
    "gender": {
      "unknown": "Unknown",
      "male": "Male",
      "female": "Female"
    },
    "rules": {
      "realName": "Please enter real name",
      "realNameLength": "Real name length cannot exceed 64 characters",
      "nickname": "Nickname length cannot exceed 64 characters",
      "email": "Please enter correct email format",
      "emailLength": "Email length cannot exceed 128 characters",
      "phone": "Please enter correct phone format"
    },
    "messages": {
      "updateSuccess": "Profile updated successfully",
      "updateFailed": "Profile update failed",
      "loadFailed": "Failed to load user information"
    }
  }
}
</i18n>

<script setup lang="ts">
import apiSysUser from '@/api/modules/sys_user'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/ui/shadcn/ui/form'
import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'
import { useI18n } from 'vue-i18n'
import { toast } from 'vue-sonner'
import * as z from 'zod'

defineOptions({
  name: 'EditProfileForm',
})

const { t } = useI18n()

const loading = ref(false)
const loadingProfile = ref(false)
const uploadingAvatar = ref(false)
const fileInputRef = ref<HTMLInputElement>()

// 性别选项
const genderOptions = [
  { value: 0, label: t('gender.unknown') },
  { value: 1, label: t('gender.male') },
  { value: 2, label: t('gender.female') },
]

// 表单验证规则
const formSchema = z.object({
  realName: z.string().min(1, t('rules.realName')).max(64, t('rules.realNameLength')),
  nickname: z.string().max(64, t('rules.nickname')).optional(),
  email: z.string().email(t('rules.email')).max(128, t('rules.emailLength')).optional(),
  phone: z.string().regex(/^1[3-9]\d{9}$/, t('rules.phone')).optional(),
  gender: z.number().optional(),
  birthday: z.string().optional(),
  avatar: z.string().optional(),
  avatarKey: z.string().optional(),
})

const form = useForm({
  validationSchema: toTypedSchema(formSchema),
  initialValues: {
    realName: '',
    nickname: '',
    email: '',
    phone: '',
    gender: 0,
    birthday: '',
    avatar: '',
    avatarKey: '',
  },
})

/**
 * 加载用户信息
 */
async function loadUserProfile() {
  try {
    loadingProfile.value = true
    const response = await apiSysUser.getCurrentProfile()
    //console.log('🔍 API响应数据:', response)

    // 安全地获取用户数据
    const userData = response?.data?.data || response?.data || {}
    //console.log('👤 用户数据:', userData)
    //console.log('🖼️ 用户头像字段值:', userData.avatar)

    // 设置表单值
    form.setValues({
      realName: userData.realName || '',
      nickname: userData.nickname || '',
      email: userData.email || '',
      phone: userData.phone || '',
      gender: userData.gender || 0,
      birthday: userData.birthday || '',
      avatar: '',
      avatarKey: userData.avatar || '',
    })

    // 如果用户有头像，根据七牛key获取URL显示
    if (userData.avatar) {
      //console.log('🔄 开始处理头像:', userData.avatar)
      try {
        // 如果avatar字段已经是完整URL，直接使用
        if (userData.avatar.startsWith('http')) {
          //console.log('✅ 头像是完整URL，直接使用:', userData.avatar)
          form.setFieldValue('avatar', userData.avatar)
        } else {
          //console.log('🔗 头像是key，需要获取URL:', userData.avatar)
          // 否则根据key获取URL
          const avatarResponse = await apiSysUser.getAvatarUrl(userData.avatar)
          //console.log('🌐 七牛云URL响应:', avatarResponse)

          if (avatarResponse?.data?.status === 1 && avatarResponse?.data?.data) {
            const avatarUrl = avatarResponse.data.data
            //console.log('✅ 成功获取头像URL:', avatarUrl)
            form.setFieldValue('avatar', avatarUrl)
          } else {
            console.warn('⚠️ 获取头像URL失败，响应格式不正确:', avatarResponse)
            // 尝试直接拼接七牛云域名
            const fallbackUrl = `https://file.dipsai.cn/${userData.avatar}`
            //console.log('🔄 尝试回退URL:', fallbackUrl)
            form.setFieldValue('avatar', fallbackUrl)
          }
        }
      } catch (error) {
        console.error('❌ 获取头像URL失败:', error)
        // 尝试直接拼接七牛云域名作为回退方案
        const fallbackUrl = `https://file.dipsai.cn/${userData.avatar}`
        //console.log('🔄 错误回退，尝试直接URL:', fallbackUrl)
        form.setFieldValue('avatar', fallbackUrl)
      }
    } else {
      //console.log('ℹ️ 用户没有设置头像')
    }
  } catch (error) {
    console.error('❌ 加载用户信息失败:', error)
    toast.error(t('messages.loadFailed'))
  } finally {
    loadingProfile.value = false
  }
}

/**
 * 提交表单
 */
const onSubmit = form.handleSubmit(async (values) => {
  try {
    loading.value = true

    // 过滤空值并处理头像字段
    const updateData = Object.fromEntries(
      Object.entries(values).filter(([key, value]) => {
        // 排除avatarKey字段，不发送给后端
        if (key === 'avatarKey') return false
        return value !== '' && value !== null && value !== undefined
      })
    )

    // 如果有avatarKey，使用key作为avatar字段值
    if (values.avatarKey) {
      updateData.avatar = values.avatarKey
    }

    await apiSysUser.updateCurrentProfile(updateData)
    toast.success(t('messages.updateSuccess'))
  } catch (error: any) {
    console.error('更新用户信息失败:', error)
    const errorMessage = error?.response?.data?.message || error?.message || t('messages.updateFailed')
    toast.error(t('messages.updateFailed'), {
      description: errorMessage
    })
  } finally {
    loading.value = false
  }
})

/**
 * 触发文件选择
 */
function triggerFileUpload() {
  fileInputRef.value?.click()
}

/**
 * 处理文件选择
 */
function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file) return

  // 验证文件类型
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png']
  if (!allowedTypes.includes(file.type)) {
    toast.error('请选择 JPG 或 PNG 格式的图片')
    return
  }

  // 验证文件大小 (2MB)
  const maxSize = 2 * 1024 * 1024
  if (file.size > maxSize) {
    toast.error('文件大小不能超过 2MB')
    return
  }

  uploadAvatar(file)
}

/**
 * 上传头像到七牛云
 */
async function uploadAvatar(file: File) {
  try {
    uploadingAvatar.value = true

    //console.log('🚀 开始上传头像:', {
    //   fileName: file.name,
    //   fileSize: file.size,
    //   fileType: file.type
    // })

    // 调用七牛云上传API
    const response: any = await apiSysUser.uploadAvatar(file)
    //console.log('📡 七牛云上传完整响应:', response)
    //console.log('📊 响应状态:', response?.status)
    //console.log('📋 响应数据:', response?.data)

    // 检查响应格式
    if (!response) {
      throw new Error('服务器无响应')
    }

    //console.log('🔍 检查响应格式:', {
    //   hasResponse: !!response,
    //   httpStatus: response.status,
    //   responseData: response.data,
    //   dataStatus: response.status,
    //   dataData: response.data,
    //   dataMessage: response.message,
    // })

    // 处理后端返回的数据格式
    // 后端返回格式: {status: 1, data: {...}, message: "..."}
    if (response.status === 1 && response.data) {
      const uploadResult = response.data

      //console.log('✅ 上传成功，处理结果:', uploadResult)

      // 更新头像预览（使用七牛云返回的URL用于显示）
      form.setFieldValue('avatar', uploadResult.url)

      // 同时保存key用于后端存储（在提交表单时会用到）
      form.setFieldValue('avatarKey', uploadResult.key)

      toast.success('头像上传成功')

      //console.log('🎉 头像上传成功:', {
      //   key: uploadResult.key,
      //   url: uploadResult.url,
      //   fileName: uploadResult.fileName
      // })

      // 头像上传成功后刷新用户信息，确保显示最新的头像
      await loadUserProfile()
    } else {
      console.error('❌ 上传失败，响应格式不正确:', {
        httpStatus: response.status,
        dataStatus: response.status,
        dataMessage: response.message,
        dataData: response.data,
      })
      throw new Error(response.message || '上传失败')
    }
  } catch (error: any) {
    console.error('💥 头像上传异常:', error)

    // 详细错误信息
    if (error.response) {
      console.error('🔴 HTTP错误响应:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      })

      const errorMessage = error.response.data?.message || error.response.statusText || '服务器错误'
      toast.error(`上传失败: ${errorMessage}`)
    } else if (error.request) {
      console.error('🔴 网络错误:', error.request)
      toast.error('网络连接失败，请检查网络连接')
    } else {
      console.error('🔴 其他错误:', error.message)
      toast.error(error instanceof Error ? error.message : '头像上传失败，请重试')
    }
  } finally {
    uploadingAvatar.value = false
  }
}

/**
 * 处理头像上传完成
 */
function handleAvatarUpload(url: string) {
  form.setFieldValue('avatar', url)
}

// 组件挂载时加载用户信息
onMounted(() => {
  loadUserProfile()
})
</script>

<template>
  <div class="w-full flex-col-stretch-center">
    <!-- <FaBlurReveal :delay="0.2" :duration="0.4" class="mb-6 space-y-2">
      <h3 class="text-4xl color-[var(--el-text-color-primary)] font-bold">
        {{ t('intro') }}
      </h3>
      <p class="text-sm text-muted-foreground lg:text-base">
        {{ t('subIntro') }}
      </p>
    </FaBlurReveal> -->

    <div v-if="loadingProfile" class="flex-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    </div>

    <form v-else @submit="onSubmit" class="space-y-4">
      <!-- 头像上传 -->
      <FormField v-slot="{ componentField }" name="avatar">
        <FormItem class="space-y-2 col-span-2 mb-[50px]">
          <FormLabel>{{ t('form.avatar') }}</FormLabel>
          <FormControl>
            <div class="flex items-center space-x-4">
              <div class="relative">
                <img
                  v-if="form.values.avatar"
                  :src="form.values.avatar"
                  alt="Avatar"
                  class="w-20 h-20 rounded-full object-cover border-2 border-border"
                >
                <div
                  v-else
                  class="w-20 h-20 rounded-full bg-muted flex-center border-2 border-border"
                >
                  <i class="i-tabler-user text-2xl text-muted-foreground"></i>
                </div>
              </div>
              <div class="space-y-2">
                <input
                  ref="fileInputRef"
                  type="file"
                  accept="image/jpeg,image/jpg,image/png"
                  class="hidden"
                  @change="handleFileSelect"
                >
                <FaButton
                  type="button"
                  variant="outline"
                  size="sm"
                  :loading="uploadingAvatar"
                  @click="triggerFileUpload"
                >
                  {{ uploadingAvatar ? t('form.uploading') : t('form.uploadAvatar') }}
                </FaButton>
                <p class="text-xs text-muted-foreground">
                  支持 JPG、PNG 格式，文件大小不超过 2MB
                </p>
              </div>
            </div>
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <!-- 两列布局容器 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- 真实姓名 -->
        <FormField v-slot="{ componentField, errors }" name="realName">
          <FormItem class="relative pb-2 space-y-0">
            <FormLabel>{{ t('form.realName') }}</FormLabel>
            <FormControl>
              <FaInput
                :placeholder="t('form.realName')"
                class="w-full"
                :class="errors.length && 'border-destructive'"
                v-bind="componentField"
              />
            </FormControl>
            <Transition enter-active-class="transition-opacity" enter-from-class="opacity-0" leave-active-class="transition-opacity" leave-to-class="opacity-0">
              <FormMessage class="absolute bottom-1 text-xs" />
            </Transition>
          </FormItem>
        </FormField>

        <!-- 昵称 -->
        <FormField v-slot="{ componentField, errors }" name="nickname">
          <FormItem class="relative pb-2 space-y-0">
            <FormLabel>{{ t('form.nickname') }}</FormLabel>
            <FormControl>
              <FaInput
                :placeholder="t('form.nickname')"
                class="w-full"
                :class="errors.length && 'border-destructive'"
                v-bind="componentField"
              />
            </FormControl>
            <Transition enter-active-class="transition-opacity" enter-from-class="opacity-0" leave-active-class="transition-opacity" leave-to-class="opacity-0">
              <FormMessage class="absolute bottom-1 text-xs" />
            </Transition>
          </FormItem>
        </FormField>

        <!-- 邮箱 -->
        <FormField v-slot="{ componentField, errors }" name="email">
          <FormItem class="relative pb-2 space-y-0">
            <FormLabel>{{ t('form.email') }}</FormLabel>
            <FormControl>
              <FaInput
                type="email"
                :placeholder="t('form.email')"
                class="w-full"
                :class="errors.length && 'border-destructive'"
                v-bind="componentField"
              />
            </FormControl>
            <Transition enter-active-class="transition-opacity" enter-from-class="opacity-0" leave-active-class="transition-opacity" leave-to-class="opacity-0">
              <FormMessage class="absolute bottom-1 text-xs" />
            </Transition>
          </FormItem>
        </FormField>

        <!-- 手机号 -->
        <FormField v-slot="{ componentField, errors }" name="phone">
          <FormItem class="relative pb-2 space-y-0">
            <FormLabel>{{ t('form.phone') }}</FormLabel>
            <FormControl>
              <FaInput
                :placeholder="t('form.phone')"
                class="w-full"
                :class="errors.length && 'border-destructive'"
                v-bind="componentField"
              />
            </FormControl>
            <Transition enter-active-class="transition-opacity" enter-from-class="opacity-0" leave-active-class="transition-opacity" leave-to-class="opacity-0">
              <FormMessage class="absolute bottom-1 text-xs" />
            </Transition>
          </FormItem>
        </FormField>

        <!-- 性别 -->
        <FormField v-slot="{ componentField }" name="gender">
          <FormItem class="space-y-2">
            <FormLabel>{{ t('form.gender') }}</FormLabel>
            <FormControl>
              <FaSelect
                :model-value="componentField.modelValue?.toString()"
                @update:model-value="(value) => componentField.onChange(Number(value))"
                :options="genderOptions.map(opt => ({ label: opt.label, value: opt.value.toString() }))"
                class="w-full"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>

        <!-- 生日 -->
        <FormField v-slot="{ componentField, errors }" name="birthday">
          <FormItem class="relative pb-2 space-y-0">
            <FormLabel>{{ t('form.birthday') }}</FormLabel>
            <FormControl>
              <FaInput
                type="date"
                :placeholder="t('form.birthday')"
                class="w-full"
                :class="errors.length && 'border-destructive'"
                v-bind="componentField"
              />
            </FormControl>
            <Transition enter-active-class="transition-opacity" enter-from-class="opacity-0" leave-active-class="transition-opacity" leave-to-class="opacity-0">
              <FormMessage class="absolute bottom-1 text-xs" />
            </Transition>
          </FormItem>
        </FormField>
      </div>

      <!-- 提交按钮 -->
      <FaButton :loading="loading" size="lg" class="mt-6 w-full" type="submit">
        {{ t('form.save') }}
      </FaButton>
    </form>
  </div>
</template>
