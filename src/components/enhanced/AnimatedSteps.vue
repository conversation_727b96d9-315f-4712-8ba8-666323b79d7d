<template>
  <div :class="['animated-steps', themeClass, orientationClass]">
    <div 
      v-for="(step, index) in steps" 
      :key="step.id || index"
      :class="[
        'step-item',
        {
          'is-active': isActive(index),
          'is-completed': isCompleted(index),
          'is-disabled': isDisabled(index)
        }
      ]"
      @click="handleStepClick(index, step)"
    >
      <!-- 步骤圆圈 -->
      <div 
        :class="[
          'step-circle',
          {
            'step-activate': isActivating(index),
            'step-complete': isCompleting(index)
          }
        ]"
        :style="getStepCircleStyle(index)"
      >
        <div v-if="isCompleted(index)" class="step-check">
          <slot name="check-icon">
            <svg viewBox="0 0 24 24" class="check-icon">
              <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
            </svg>
          </slot>
        </div>
        <div v-else-if="step.icon" class="step-icon">
          <component :is="step.icon" />
        </div>
        <div v-else class="step-number">
          {{ index + 1 }}
        </div>
      </div>
      
      <!-- 步骤内容 -->
      <div v-if="showLabels" class="step-content">
        <div class="step-title">{{ step.title }}</div>
        <div v-if="step.description" class="step-description">
          {{ step.description }}
        </div>
      </div>
      
      <!-- 连接线 -->
      <div 
        v-if="index < steps.length - 1" 
        :class="[
          'step-connector',
          {
            'is-completed': isCompleted(index)
          }
        ]"
        :style="getConnectorStyle(index)"
      >
        <div class="connector-progress" :style="getProgressStyle(index)"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, nextTick } from 'vue'
import { themeManager } from '@/utils/themeManager'

export interface Step {
  id?: string | number
  title: string
  description?: string
  icon?: string | Component
  disabled?: boolean
}

interface Props {
  steps: Step[]
  currentStep: number
  theme?: string
  orientation?: 'horizontal' | 'vertical'
  showLabels?: boolean
  clickable?: boolean
  animationDuration?: number
  allowStepClick?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  theme: '',
  orientation: 'horizontal',
  showLabels: true,
  clickable: false,
  animationDuration: 400,
  allowStepClick: false
})

const emit = defineEmits<{
  stepClick: [index: number, step: Step]
  stepChange: [from: number, to: number]
}>()

const activatingStep = ref(-1)
const completingStep = ref(-1)
const previousStep = ref(props.currentStep)

// 计算当前主题
const currentTheme = computed(() => {
  return props.theme || themeManager.getCurrentTheme()
})

// 主题样式类
const themeClass = computed(() => {
  return `theme-${currentTheme.value}`
})

// 方向样式类
const orientationClass = computed(() => {
  return `orientation-${props.orientation}`
})

// 步骤状态判断
const isActive = (index: number) => index === props.currentStep
const isCompleted = (index: number) => index < props.currentStep
const isDisabled = (index: number) => props.steps[index]?.disabled || false
const isActivating = (index: number) => index === activatingStep.value
const isCompleting = (index: number) => index === completingStep.value

// 获取步骤圆圈样式
const getStepCircleStyle = (index: number) => {
  const step = props.steps[index]
  const style: Record<string, string> = {}
  
  if (step?.disabled) {
    style.opacity = '0.5'
    style.cursor = 'not-allowed'
  } else if (props.clickable || props.allowStepClick) {
    style.cursor = 'pointer'
  }
  
  return style
}

// 获取连接线样式
const getConnectorStyle = (index: number) => {
  const style: Record<string, string> = {}
  
  if (props.orientation === 'vertical') {
    style.height = '40px'
    style.width = '2px'
  } else {
    style.width = '60px'
    style.height = '2px'
  }
  
  return style
}

// 获取进度样式
const getProgressStyle = (index: number) => {
  const isConnectorCompleted = isCompleted(index)
  const isCurrentConnection = index === props.currentStep - 1
  
  const style: Record<string, string> = {
    transition: `all ${props.animationDuration}ms ease`
  }
  
  if (isConnectorCompleted) {
    if (props.orientation === 'vertical') {
      style.height = '100%'
    } else {
      style.width = '100%'
    }
  } else if (isCurrentConnection) {
    // 当前连接的动画进度
    if (props.orientation === 'vertical') {
      style.height = '50%'
    } else {
      style.width = '50%'
    }
  } else {
    if (props.orientation === 'vertical') {
      style.height = '0%'
    } else {
      style.width = '0%'
    }
  }
  
  return style
}

// 处理步骤点击
const handleStepClick = (index: number, step: Step) => {
  if (step.disabled || (!props.clickable && !props.allowStepClick)) return
  
  emit('stepClick', index, step)
}

// 监听当前步骤变化
watch(() => props.currentStep, async (newStep, oldStep) => {
  if (newStep !== oldStep) {
    previousStep.value = oldStep
    
    // 触发激活动画
    if (newStep > oldStep) {
      activatingStep.value = newStep
      
      // 完成之前步骤的动画
      if (oldStep >= 0 && oldStep < newStep) {
        completingStep.value = oldStep
        
        await nextTick()
        setTimeout(() => {
          completingStep.value = -1
        }, props.animationDuration)
      }
      
      await nextTick()
      setTimeout(() => {
        activatingStep.value = -1
      }, props.animationDuration)
    }
    
    emit('stepChange', oldStep, newStep)
  }
})
</script>

<style scoped>
.animated-steps {
  display: flex;
  align-items: center;
  position: relative;
}

.orientation-horizontal {
  flex-direction: row;
  
  .step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
  }
  
  .step-content {
    margin-top: 12px;
    text-align: center;
    max-width: 120px;
  }
  
  .step-connector {
    position: absolute;
    top: 50%;
    left: 100%;
    transform: translateY(-50%);
    margin-left: 8px;
  }
}

.orientation-vertical {
  flex-direction: column;
  align-items: flex-start;
  
  .step-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    position: relative;
    width: 100%;
    min-height: 60px;
  }
  
  .step-content {
    margin-left: 16px;
    flex: 1;
  }
  
  .step-connector {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 8px;
  }
}

.step-circle {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: hsl(var(--muted));
  border: 2px solid hsl(var(--border));
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  z-index: 2;
  
  &:hover {
    transform: scale(1.05);
  }
}

.step-number {
  font-weight: 600;
  font-size: 14px;
  color: hsl(var(--muted-foreground));
}

.step-icon {
  width: 20px;
  height: 20px;
  color: hsl(var(--muted-foreground));
}

.step-check {
  width: 20px;
  height: 20px;
  color: white;
}

.check-icon {
  width: 100%;
  height: 100%;
  fill: currentColor;
}

.step-content {
  user-select: none;
}

.step-title {
  font-weight: 600;
  font-size: 14px;
  color: hsl(var(--foreground));
  margin-bottom: 4px;
}

.step-description {
  font-size: 12px;
  color: hsl(var(--muted-foreground));
  line-height: 1.4;
}

.step-connector {
  background: hsl(var(--border));
  position: relative;
  z-index: 1;
  
  &.is-completed {
    background: hsl(var(--primary));
  }
}

.connector-progress {
  background: hsl(var(--primary));
  position: absolute;
  top: 0;
  left: 0;
}

/* 步骤状态样式 */
.step-item {
  &.is-active {
    .step-circle {
      background: hsl(var(--primary));
      border-color: hsl(var(--primary));
      transform: scale(1.1);
      
      .step-number,
      .step-icon {
        color: white;
      }
    }
    
    .step-title {
      color: hsl(var(--primary));
      font-weight: 700;
    }
  }
  
  &.is-completed {
    .step-circle {
      background: hsl(var(--primary));
      border-color: hsl(var(--primary));
      
      .step-number,
      .step-icon {
        color: white;
      }
    }
    
    .step-title {
      color: hsl(var(--foreground));
    }
  }
  
  &.is-disabled {
    opacity: 0.5;
    pointer-events: none;
  }
}

/* 医美主题样式 */
.theme-medical-beauty {
  .step-circle {
    @apply medical-steps;
  }
  
  .step-item.is-active .step-circle,
  .step-item.is-completed .step-circle {
    background: linear-gradient(135deg, 
      hsl(var(--medical-gradient-start)), 
      hsl(var(--medical-gradient-end)));
    border-color: hsl(var(--primary));
    box-shadow: 0 4px 12px rgba(329, 100%, 65%, 0.3);
  }
  
  .connector-progress {
    background: linear-gradient(90deg, 
      hsl(var(--medical-gradient-start)), 
      hsl(var(--medical-gradient-end)));
  }
}

/* 地产主题样式 */
.theme-real-estate {
  .step-circle {
    @apply estate-steps;
    border-radius: 6px;
  }
  
  .step-item.is-active .step-circle,
  .step-item.is-completed .step-circle {
    background: hsl(var(--primary));
    border-color: hsl(var(--primary));
    box-shadow: 0 2px 8px rgba(210, 100%, 50%, 0.2);
  }
}

/* 动画效果 */
.step-activate {
  animation: step-activate 0.4s ease-out;
}

.step-complete {
  animation: step-complete 0.5s ease-out;
}

@keyframes step-activate {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1.1);
  }
}

@keyframes step-complete {
  0% {
    transform: scale(1.1);
  }
  50% {
    transform: scale(1.25);
  }
  100% {
    transform: scale(1);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .orientation-horizontal {
    .step-content {
      margin-top: 8px;
      max-width: 80px;
    }
    
    .step-title {
      font-size: 12px;
    }
    
    .step-description {
      font-size: 10px;
    }
  }
  
  .step-circle {
    width: 32px;
    height: 32px;
  }
  
  .step-number {
    font-size: 12px;
  }
  
  .step-icon,
  .step-check {
    width: 16px;
    height: 16px;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .step-circle {
    transition: none !important;
  }
  
  .connector-progress {
    transition: none !important;
  }
  
  .step-activate,
  .step-complete {
    animation: none !important;
  }
}
</style>