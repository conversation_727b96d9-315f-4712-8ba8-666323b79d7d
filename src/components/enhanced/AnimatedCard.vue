<template>
  <div
    :class="[
      'animated-card',
      themeClass,
      animationClass,
      {
        'is-hovering': isHovering,
        'is-loading': loading,
        'gpu-accelerated': useGpuAcceleration
      }
    ]"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    @click="handleClick"
    :style="cardStyle"
  >
    <div v-if="showRipple" class="ripple" :style="rippleStyle"></div>
    
    <div class="card-content">
      <slot />
    </div>
    
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue'
import { getCardAnimation } from '@/utils/animations/pageTransitions'
import { themeManager } from '@/utils/themeManager'

interface Props {
  theme?: string
  loading?: boolean
  disabled?: boolean
  rippleEffect?: boolean
  staggerIndex?: number
  parallaxSpeed?: number
  useGpuAcceleration?: boolean
  hoverScale?: number
  clickScale?: number
  animationDelay?: number
}

const props = withDefaults(defineProps<Props>(), {
  theme: '',
  loading: false,
  disabled: false,
  rippleEffect: true,
  staggerIndex: 0,
  parallaxSpeed: 0,
  useGpuAcceleration: true,
  hoverScale: 1.02,
  clickScale: 0.98,
  animationDelay: 0
})

const emit = defineEmits<{
  click: [event: MouseEvent]
  mouseenter: [event: MouseEvent]
  mouseleave: [event: MouseEvent]
}>()

const isHovering = ref(false)
const showRipple = ref(false)
const rippleStyle = ref<Record<string, string>>({})
const cardRef = ref<HTMLElement>()

// 计算当前主题
const currentTheme = computed(() => {
  return props.theme || themeManager.getCurrentTheme()
})

// 获取动画配置
const animationConfig = computed(() => {
  return getCardAnimation(currentTheme.value)
})

// 主题样式类
const themeClass = computed(() => {
  return `theme-${currentTheme.value}`
})

// 动画样式类
const animationClass = computed(() => {
  const config = animationConfig.value
  return [
    config.hover,
    config.click,
    config.enter,
    `stagger-${Math.min(props.staggerIndex + 1, 6)}`
  ].join(' ')
})

// 卡片样式
const cardStyle = computed(() => {
  const style: Record<string, string> = {}
  
  if (props.animationDelay > 0) {
    style.animationDelay = `${props.animationDelay}ms`
  }
  
  if (props.staggerIndex > 0) {
    style.animationDelay = `${props.staggerIndex * 100}ms`
  }
  
  return style
})

// 处理鼠标进入
const handleMouseEnter = (event: MouseEvent) => {
  if (props.disabled) return
  
  isHovering.value = true
  emit('mouseenter', event)
}

// 处理鼠标离开
const handleMouseLeave = (event: MouseEvent) => {
  if (props.disabled) return
  
  isHovering.value = false
  emit('mouseleave', event)
}

// 处理点击
const handleClick = (event: MouseEvent) => {
  if (props.disabled || props.loading) return
  
  // 创建波纹效果
  if (props.rippleEffect) {
    createRippleEffect(event)
  }
  
  emit('click', event)
}

// 创建波纹效果
const createRippleEffect = (event: MouseEvent) => {
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  const size = Math.max(rect.width, rect.height)
  const x = event.clientX - rect.left - size / 2
  const y = event.clientY - rect.top - size / 2
  
  rippleStyle.value = {
    width: `${size}px`,
    height: `${size}px`,
    left: `${x}px`,
    top: `${y}px`
  }
  
  showRipple.value = true
  
  setTimeout(() => {
    showRipple.value = false
  }, 600)
}

// 视差效果
let parallaxObserver: IntersectionObserver | null = null

onMounted(() => {
  if (props.parallaxSpeed > 0 && cardRef.value) {
    parallaxObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const handleScroll = () => {
            const scrolled = window.pageYOffset
            const rate = scrolled * props.parallaxSpeed
            if (cardRef.value) {
              cardRef.value.style.transform = `translateY(${rate}px)`
            }
          }
          
          window.addEventListener('scroll', handleScroll, { passive: true })
          
          // 清理函数
          return () => {
            window.removeEventListener('scroll', handleScroll)
          }
        }
      })
    })
    
    parallaxObserver.observe(cardRef.value)
  }
})

// 监听主题变化
watch(() => themeManager.getCurrentTheme(), (newTheme) => {
  // 主题切换时的过渡动画
  if (cardRef.value) {
    cardRef.value.style.transition = 'all 0.3s ease'
  }
})
</script>

<style scoped>
.animated-card {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &.is-loading {
    pointer-events: none;
    opacity: 0.7;
  }
  
  &:disabled,
  &[disabled] {
    pointer-events: none;
    opacity: 0.5;
  }
}

.card-content {
  position: relative;
  z-index: 1;
}

.ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation: ripple-effect 0.6s ease-out;
  pointer-events: none;
  z-index: 0;
}

@keyframes ripple-effect {
  to {
    transform: scale(1);
    opacity: 0;
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid hsl(var(--muted));
  border-top: 2px solid hsl(var(--primary));
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 医美主题特定样式 */
.theme-medical-beauty {
  @apply medical-card;
  
  &.is-hovering {
    @apply medical-card-hover;
  }
  
  .loading-spinner {
    border-top-color: hsl(var(--medical-gradient-start));
  }
}

/* 地产主题特定样式 */
.theme-real-estate {
  @apply estate-card;
  
  &.is-hovering {
    @apply estate-card-hover;
  }
  
  .loading-spinner {
    border-top-color: hsl(var(--estate-gradient-start));
  }
}

/* 默认主题样式 */
.theme-default {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  
  &:hover {
    border-color: hsl(var(--primary));
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .animated-card {
    border-radius: 6px;
  }
  
  .ripple {
    animation-duration: 0.4s;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .animated-card {
    transition: none !important;
    animation: none !important;
  }
  
  .ripple {
    display: none;
  }
}
</style>