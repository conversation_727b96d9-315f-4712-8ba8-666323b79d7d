<template>
  <button
    :class="[
      'animated-button',
      themeClass,
      animationClass,
      sizeClass,
      variantClass,
      {
        'is-loading': loading,
        'is-disabled': disabled,
        'gpu-accelerated': useGpuAcceleration
      }
    ]"
    :disabled="disabled || loading"
    @click="handleClick"
    :style="buttonStyle"
    ref="buttonRef"
  >
    <span v-if="showRipple" class="button-ripple" :style="rippleStyle"></span>
    
    <span class="button-content">
      <slot name="icon-before" />
      <span v-if="!loading || showTextWhileLoading" class="button-text">
        <slot />
      </span>
      <span v-if="loading" class="loading-content">
        <slot name="loading">
          <span class="loading-spinner"></span>
          <span v-if="loadingText" class="loading-text">{{ loadingText }}</span>
        </slot>
      </span>
      <slot name="icon-after" />
    </span>
  </button>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'
import { getButtonAnimation } from '@/utils/animations/pageTransitions'
import { themeManager } from '@/utils/themeManager'

interface Props {
  theme?: string
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'link'
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  loading?: boolean
  disabled?: boolean
  loadingText?: string
  showTextWhileLoading?: boolean
  rippleEffect?: boolean
  useGpuAcceleration?: boolean
  animationDelay?: number
  hoverEffect?: boolean
  clickEffect?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  theme: '',
  variant: 'primary',
  size: 'md',
  loading: false,
  disabled: false,
  loadingText: '',
  showTextWhileLoading: false,
  rippleEffect: true,
  useGpuAcceleration: true,
  animationDelay: 0,
  hoverEffect: true,
  clickEffect: true
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const buttonRef = ref<HTMLButtonElement>()
const showRipple = ref(false)
const rippleStyle = ref<Record<string, string>>({})

// 计算当前主题
const currentTheme = computed(() => {
  return props.theme || themeManager.getCurrentTheme()
})

// 获取动画配置
const animationConfig = computed(() => {
  return getButtonAnimation(currentTheme.value)
})

// 主题样式类
const themeClass = computed(() => {
  return `theme-${currentTheme.value}`
})

// 动画样式类
const animationClass = computed(() => {
  const config = animationConfig.value
  const classes = []
  
  if (props.hoverEffect) classes.push(config.hover)
  if (props.clickEffect) classes.push(config.click)
  if (props.loading) classes.push(config.loading)
  
  return classes.join(' ')
})

// 尺寸样式类
const sizeClass = computed(() => {
  return `btn-${props.size}`
})

// 变体样式类
const variantClass = computed(() => {
  return `btn-${props.variant}`
})

// 按钮样式
const buttonStyle = computed(() => {
  const style: Record<string, string> = {}
  
  if (props.animationDelay > 0) {
    style.animationDelay = `${props.animationDelay}ms`
  }
  
  return style
})

// 处理点击
const handleClick = (event: MouseEvent) => {
  if (props.disabled || props.loading) return
  
  // 创建波纹效果
  if (props.rippleEffect) {
    createRippleEffect(event)
  }
  
  emit('click', event)
}

// 创建波纹效果
const createRippleEffect = (event: MouseEvent) => {
  if (!buttonRef.value) return
  
  const rect = buttonRef.value.getBoundingClientRect()
  const size = Math.max(rect.width, rect.height)
  const x = event.clientX - rect.left - size / 2
  const y = event.clientY - rect.top - size / 2
  
  rippleStyle.value = {
    width: `${size}px`,
    height: `${size}px`,
    left: `${x}px`,
    top: `${y}px`
  }
  
  showRipple.value = true
  
  setTimeout(() => {
    showRipple.value = false
  }, 600)
}

onMounted(() => {
  // 按钮入场动画
  if (buttonRef.value && props.animationDelay === 0) {
    buttonRef.value.classList.add('btn-enter')
  }
})
</script>

<style scoped>
.animated-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
  overflow: hidden;
  white-space: nowrap;
  user-select: none;
  
  &:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }
  
  &.is-disabled {
    pointer-events: none;
    opacity: 0.5;
  }
  
  &.is-loading {
    pointer-events: none;
  }
}

.button-content {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  z-index: 1;
}

.button-text {
  transition: opacity 0.2s ease;
}

.loading-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 0.9em;
}

.button-ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation: button-ripple-effect 0.6s ease-out;
  pointer-events: none;
  z-index: 0;
}

@keyframes button-ripple-effect {
  to {
    transform: scale(1);
    opacity: 0;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes btn-enter {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.btn-enter {
  animation: btn-enter 0.4s ease-out;
}

/* 尺寸变体 */
.btn-xs {
  height: 28px;
  padding: 0 12px;
  font-size: 12px;
  border-radius: 4px;
}

.btn-sm {
  height: 32px;
  padding: 0 16px;
  font-size: 13px;
  border-radius: 5px;
}

.btn-md {
  height: 36px;
  padding: 0 20px;
  font-size: 14px;
  border-radius: 6px;
}

.btn-lg {
  height: 44px;
  padding: 0 28px;
  font-size: 16px;
  border-radius: 8px;
}

.btn-xl {
  height: 52px;
  padding: 0 36px;
  font-size: 18px;
  border-radius: 10px;
}

/* 颜色变体 */
.btn-primary {
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  
  &:hover:not(.is-disabled):not(.is-loading) {
    background: hsl(var(--primary) / 90%);
  }
}

.btn-secondary {
  background: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
  
  &:hover:not(.is-disabled):not(.is-loading) {
    background: hsl(var(--secondary) / 80%);
  }
}

.btn-outline {
  background: transparent;
  color: hsl(var(--primary));
  border: 1px solid hsl(var(--primary));
  
  &:hover:not(.is-disabled):not(.is-loading) {
    background: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
  }
}

.btn-ghost {
  background: transparent;
  color: hsl(var(--foreground));
  
  &:hover:not(.is-disabled):not(.is-loading) {
    background: hsl(var(--accent));
    color: hsl(var(--accent-foreground));
  }
}

.btn-link {
  background: transparent;
  color: hsl(var(--primary));
  text-decoration: underline;
  padding: 0;
  height: auto;
  
  &:hover:not(.is-disabled):not(.is-loading) {
    text-decoration: none;
  }
}

/* 医美主题特定样式 */
.theme-medical-beauty {
  &.btn-primary {
    @apply medical-button;
  }
  
  .loading-spinner {
    border-top-color: currentColor;
  }
}

/* 地产主题特定样式 */
.theme-real-estate {
  &.btn-primary {
    @apply estate-button;
  }
  
  .loading-spinner {
    border-top-color: currentColor;
  }
}

/* 默认主题样式增强 */
.theme-default {
  &.btn-primary:hover:not(.is-disabled):not(.is-loading) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .btn-lg {
    height: 40px;
    padding: 0 24px;
    font-size: 15px;
  }
  
  .btn-xl {
    height: 48px;
    padding: 0 32px;
    font-size: 17px;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .animated-button {
    transition: none !important;
    animation: none !important;
  }
  
  .button-ripple {
    display: none;
  }
  
  .loading-spinner {
    animation: none;
  }
}
</style>