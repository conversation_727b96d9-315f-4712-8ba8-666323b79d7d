<template>
  <el-dialog
    v-model="visible"
    title="处理发票申请"
    width="600px"
    :before-close="handleClose">

    <div class="process-content" v-loading="loading">
      <!-- 发票信息预览 -->
      <div v-if="invoice" class="invoice-preview">
        <h4>发票申请信息</h4>
        <div class="preview-grid">
          <div class="preview-item">
            <label>发票单号：</label>
            <span>{{ invoice.invoiceNo }}</span>
          </div>
          <div class="preview-item">
            <label>发票抬头：</label>
            <span>{{ invoice.invoiceTitle }}</span>
          </div>
          <div class="preview-item">
            <label>发票类型：</label>
            <el-tag :type="invoice.invoiceType === 'NORMAL' ? 'info' : 'success'">
              {{ invoice.invoiceTypeDescription }}
            </el-tag>
          </div>
          <div class="preview-item">
            <label>发票金额：</label>
            <span class="amount">¥{{ invoice.totalAmount.toFixed(2) }}</span>
          </div>
        </div>
      </div>

      <!-- 处理表单 -->
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="process-form">

        <!-- 文件上传 -->
        <el-form-item label="发票文件" prop="invoiceFile">
          <el-upload
            ref="uploadRef"
            :before-upload="beforeUpload"
            :on-change="handleFileChange"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :file-list="fileList"
            :limit="1"
            :auto-upload="false"
            accept=".pdf,.jpg,.jpeg,.png"
            action="#">

            <el-button type="primary">
              <el-icon><Upload /></el-icon>
              选择发票文件
            </el-button>

            <template #tip>
              <div class="el-upload__tip">
                支持 PDF、JPG、PNG 格式，文件大小不超过 10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <!-- 发票号码 -->
        <el-form-item label="发票号码" prop="invoiceNumber">
          <el-input
            v-model="form.invoiceNumber"
            placeholder="请输入发票号码（可选）"
            maxlength="50" />
        </el-form-item>

        <!-- 开票日期 -->
        <el-form-item label="开票日期" prop="invoiceDate">
          <el-date-picker
            v-model="form.invoiceDate"
            type="date"
            placeholder="请选择开票日期（可选）"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%;" />
        </el-form-item>

        <!-- 处理备注 -->
        <el-form-item label="处理备注" prop="processorRemarks">
          <el-input
            v-model="form.processorRemarks"
            type="textarea"
            :rows="4"
            placeholder="请输入处理备注（可选）"
            maxlength="500" />
        </el-form-item>
      </el-form>

      <!-- 上传进度 -->
      <div v-if="uploading" class="upload-progress">
        <el-progress :percentage="uploadProgress" />
        <p>正在上传发票文件...</p>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="submitting"
          @click="submitProcess">
          完成处理
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules, type UploadInstance, type UploadRawFile } from 'element-plus'
import { Upload } from '@element-plus/icons-vue'
import { invoiceApi } from '@/api/invoice'

// Props & Emits
const props = defineProps<{
  modelValue: boolean
  invoice?: any
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const submitting = ref(false)
const uploading = ref(false)
const uploadProgress = ref(0)

const formRef = ref<FormInstance>()
const uploadRef = ref<UploadInstance>()

const fileList = ref<any[]>([])
const uploadedFileInfo = ref<{
  url: string
  name: string
  size: number
} | null>(null)

// 表单数据
const form = reactive({
  invoiceNumber: '',
  invoiceDate: '',
  processorRemarks: ''
})

// 表单验证规则
const rules: FormRules = {
  // 发票文件是必需的，但通过自定义验证处理
}

// 文件上传处理（备选方案）
const beforeUpload = (file: UploadRawFile) => {
  //console.log('beforeUpload 被调用:', file.name, file.type, file.size)

  // 文件类型检查
  const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png']
  if (!allowedTypes.includes(file.type)) {
    //console.log('beforeUpload: 文件类型不支持:', file.type)
    ElMessage.error('只支持 PDF、JPG、PNG 格式的文件')
    return false
  }

  // 文件大小检查 (10MB)
  const maxSize = 10 * 1024 * 1024
  if (file.size > maxSize) {
    //console.log('beforeUpload: 文件大小超出限制:', file.size, '最大:', maxSize)
    ElMessage.error('文件大小不能超过 10MB')
    return false
  }

  //console.log('beforeUpload: 文件验证通过，开始上传')
  // 开始上传
  uploadFile(file)
  return false // 阻止默认上传
}

const uploadFile = async (file: File) => {
  try {
    uploading.value = true
    uploadProgress.value = 0

    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += 10
      }
    }, 200)

    //console.log('开始上传文件:', file.name, 'size:', file.size)
    const response = await invoiceApi.uploadInvoiceFile(file)
    //console.log('文件上传响应:', response)
    //console.log('response.data:', response.data)
    //console.log('response类型:', typeof response.data)

    clearInterval(progressInterval)
    uploadProgress.value = 100

    // 先详细检查响应结构
    //console.log('完整响应对象:', JSON.stringify(response, null, 2))
    //console.log('response.data:', response.data)
    //console.log('response.data类型:', typeof response.data)

    // 根据实际响应结构提取文件URL
    // 从调试发现，URL在message字段中
    let fileUrl = (response as any).message || response.data

    //console.log('最终提取的文件URL:', fileUrl)

    if (!fileUrl || typeof fileUrl !== 'string' || !fileUrl.startsWith('http')) {
      console.error('无法获取有效的文件URL，fileUrl:', fileUrl)
      console.error('原始response.data:', response.data)
      ElMessage.error('服务器返回的文件URL无效，请重新上传')
      return
    }

    uploadedFileInfo.value = {
      url: fileUrl,
      name: file.name,
      size: file.size
    }

    //console.log('设置uploadedFileInfo.value后:', uploadedFileInfo.value)
    //console.log('uploadedFileInfo.value.url:', uploadedFileInfo.value.url)

    // 使用nextTick确保响应式更新完成后再验证
    await nextTick()

    // 验证设置是否成功
    if (!uploadedFileInfo.value || !uploadedFileInfo.value.url) {
      console.error('设置uploadedFileInfo失败，当前值:', uploadedFileInfo.value)
      ElMessage.error('文件信息设置失败，请重新上传')
      return
    }

    fileList.value = [{
      name: file.name,
      status: 'success',
      url: fileUrl
    }] as any[]

    //console.log('文件上传成功，uploadedFileInfo:', uploadedFileInfo.value)

    ElMessage.success('文件上传成功')
  } catch (error) {
    console.error('文件上传失败:', error)
    ElMessage.error('文件上传失败')
    fileList.value = []
    uploadedFileInfo.value = null
  } finally {
    uploading.value = false
    uploadProgress.value = 0
  }
}

// 处理文件选择变化
const handleFileChange = (file: any, fileList: any[]) => {
  //console.log('文件选择变化:', file, fileList)
  //console.log('文件状态:', file.status)
  //console.log('文件raw:', file.raw)

  if (file.status === 'ready' && file.raw) {
    // 文件刚被选择，进行验证并上传
    const rawFile = file.raw

    //console.log('处理新选择的文件:', rawFile.name, rawFile.type, rawFile.size)

    // 文件类型检查
    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png']
    if (!allowedTypes.includes(rawFile.type)) {
      //console.log('文件类型不支持:', rawFile.type)
      ElMessage.error('只支持 PDF、JPG、PNG 格式的文件')
      // 清除文件列表
      fileList.splice(fileList.indexOf(file), 1)
      return
    }

    // 文件大小检查 (10MB)
    const maxSize = 10 * 1024 * 1024
    if (rawFile.size > maxSize) {
      //console.log('文件大小超出限制:', rawFile.size, '最大:', maxSize)
      ElMessage.error('文件大小不能超过 10MB')
      // 清除文件列表
      fileList.splice(fileList.indexOf(file), 1)
      return
    }

    //console.log('文件验证通过，开始上传')
    // 开始上传
    uploadFile(rawFile)
  } else {
    //console.log('文件状态不是ready或没有raw文件，跳过处理')
  }
}

const handleUploadSuccess = () => {
  // 已在 uploadFile 中处理
}

const handleUploadError = () => {
  ElMessage.error('文件上传失败')
  fileList.value = []
  uploadedFileInfo.value = null
}

// 提交处理
const submitProcess = async () => {
  //console.log('提交处理 - uploadedFileInfo:', uploadedFileInfo.value)
  //console.log('提交处理 - fileList:', fileList.value)

  if (!uploadedFileInfo.value) {
    ElMessage.warning('请先上传发票文件')
    //console.log('uploadedFileInfo.value 为空，无法提交')
    return
  }

  if (!uploadedFileInfo.value.url) {
    ElMessage.warning('文件URL缺失，请重新上传文件')
    //console.log('uploadedFileInfo.value.url 为空:', uploadedFileInfo.value)
    return
  }

  if (!props.invoice) {
    ElMessage.error('发票信息缺失')
    return
  }

  try {
    submitting.value = true

    const processData = {
      invoiceId: props.invoice.id,
      fileUrl: uploadedFileInfo.value.url,
      fileName: uploadedFileInfo.value.name,
      fileSize: Number(uploadedFileInfo.value.size),
      invoiceNumber: form.invoiceNumber || undefined,
      processorRemarks: form.processorRemarks || undefined
    }

    //console.log('提交处理数据:', processData)
    //console.log('解构后的数据 - invoiceId:', processData.invoiceId)
    //console.log('解构后的数据 - 其他参数:', {
      fileUrl: processData.fileUrl,
      fileName: processData.fileName,
      fileSize: processData.fileSize,
      invoiceNumber: processData.invoiceNumber,
      processorRemarks: processData.processorRemarks
    })

    await invoiceApi.processInvoice(processData)

    ElMessage.success('发票处理完成')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('处理发票失败:', error)
    ElMessage.error('处理发票失败')
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }

  Object.assign(form, {
    invoiceNumber: '',
    invoiceDate: '',
    processorRemarks: ''
  })

  fileList.value = []
  uploadedFileInfo.value = null
  uploading.value = false
  uploadProgress.value = 0
}

// 监听对话框显示
watch(visible, (newVisible) => {
  if (newVisible) {
    // 对话框打开时的初始化逻辑
  } else {
    // 对话框关闭时的清理逻辑
    resetForm()
  }
})
</script>

<style scoped lang="scss">
.process-content {
  .invoice-preview {
    padding: 16px;
    margin-bottom: 20px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;

    h4 {
      margin: 0 0 12px;
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
    }

    .preview-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;

      .preview-item {
        display: flex;
        align-items: center;

        label {
          min-width: 80px;
          font-weight: 500;
          color: #606266;
        }

        span {
          color: #2c3e50;
        }

        .amount {
          font-weight: 600;
          color: #e74c3c;
        }
      }
    }
  }

  .process-form {
    .el-upload {
      width: 100%;
    }

    :deep(.el-upload-list) {
      margin-top: 8px;
    }
  }

  .upload-progress {
    padding: 16px;
    margin-top: 16px;
    background-color: #f0f9ff;
    border: 1px solid #bfdbfe;
    border-radius: 8px;

    p {
      margin: 8px 0 0;
      font-size: 14px;
      color: #1e40af;
      text-align: center;
    }
  }
}

.dialog-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

@media (width <= 768px) {
  .process-content {
    .invoice-preview {
      .preview-grid {
        grid-template-columns: 1fr;

        .preview-item {
          flex-direction: column;
          align-items: flex-start;

          label {
            margin-bottom: 4px;
          }
        }
      }
    }
  }
}
</style>
