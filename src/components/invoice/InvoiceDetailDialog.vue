<template>
  <a-modal
    v-model:open="visible"
    title="发票详情"
    width="900px"
    :footer="null"
    :mask-closable="false"
    @cancel="handleClose">

    <div class="invoice-detail-content">
      <a-spin :spinning="loading">
        <div v-if="invoiceDetail" class="invoice-detail">
          <!-- 发票基本信息 -->
          <div class="detail-section">
            <h3 class="section-title">发票信息</h3>
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="发票抬头">
                <div class="invoice-title-highlight">
                  <div class="invoice-title-content">
                    {{ invoiceDetail.invoiceTitle }}
                  </div>
                  <div class="invoice-title-actions">
                    <CopyButton
                      :value="invoiceDetail.invoiceTitle"
                      label="发票抬头" />
                  </div>
                  <div class="invoice-title-badge">发票抬头</div>
                </div>
              </a-descriptions-item>
              <a-descriptions-item label="发票单号">
                {{ invoiceDetail.invoiceNo }}
              </a-descriptions-item>
              <a-descriptions-item label="发票类型">
                <a-tag :color="invoiceDetail.invoiceType === 'NORMAL' ? 'blue' : 'green'">
                  {{ invoiceDetail.invoiceType === 'NORMAL' ? '普通发票' : '专用发票' }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="发票金额">
                <span class="amount">¥{{ invoiceDetail.totalAmount.toFixed(2) }}</span>
              </a-descriptions-item>
              <a-descriptions-item label="发票状态">
                <a-tag :color="getStatusColor(invoiceDetail.status)">
                  {{ getStatusText(invoiceDetail.status) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="申请时间">
                {{ formatDateTime(invoiceDetail.applicationTime) }}
              </a-descriptions-item>
              <a-descriptions-item v-if="invoiceDetail.processedTime" label="处理时间">
                {{ formatDateTime(invoiceDetail.processedTime) }}
              </a-descriptions-item>
            </a-descriptions>
          </div>

          <!-- 专用发票企业信息 -->
          <div v-if="invoiceDetail.invoiceType === 'SPECIAL'" class="detail-section">
            <h3 class="section-title">企业信息</h3>
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="纳税人识别号">
                <CopyableField
                  :value="invoiceDetail.taxNumber"
                  label="纳税人识别号" />
              </a-descriptions-item>
              <a-descriptions-item label="企业注册电话">
                <CopyableField
                  :value="invoiceDetail.companyPhone"
                  label="企业注册电话" />
              </a-descriptions-item>
              <a-descriptions-item label="企业注册地址" :span="2">
                <CopyableField
                  :value="invoiceDetail.companyAddress"
                  label="企业注册地址" />
              </a-descriptions-item>
              <a-descriptions-item label="开户银行">
                <CopyableField
                  :value="invoiceDetail.bankName"
                  label="开户银行" />
              </a-descriptions-item>
              <a-descriptions-item label="银行账号">
                <CopyableField
                  :value="invoiceDetail.bankAccount"
                  label="银行账号" />
              </a-descriptions-item>
            </a-descriptions>
          </div>

          <!-- 收件人信息 -->
          <div class="detail-section">
            <h3 class="section-title">收件人信息</h3>
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="收件人姓名">
                <CopyableField
                  :value="invoiceDetail.recipientName"
                  label="收件人姓名" />
              </a-descriptions-item>
              <a-descriptions-item label="收件人手机">
                <CopyableField
                  :value="invoiceDetail.recipientPhone"
                  label="收件人手机" />
              </a-descriptions-item>
              <a-descriptions-item label="收件人邮箱">
                <CopyableField
                  :value="invoiceDetail.recipientEmail"
                  label="收件人邮箱" />
              </a-descriptions-item>
              <a-descriptions-item label="收件人地址">
                <CopyableField
                  :value="invoiceDetail.recipientAddress"
                  label="收件人地址" />
              </a-descriptions-item>
            </a-descriptions>
          </div>

          <!-- 发票文件信息 -->
          <div v-if="invoiceDetail.status === 'COMPLETED'" class="detail-section">
            <h3 class="section-title">发票文件</h3>
            <a-descriptions :column="2" bordered>
              <a-descriptions-item v-if="invoiceDetail.invoiceCode" label="发票代码">
                {{ invoiceDetail.invoiceCode }}
              </a-descriptions-item>
              <a-descriptions-item v-if="invoiceDetail.invoiceNumber" label="发票号码">
                {{ invoiceDetail.invoiceNumber }}
              </a-descriptions-item>
              <a-descriptions-item v-if="invoiceDetail.invoiceDate" label="开票日期">
                {{ invoiceDetail.invoiceDate }}
              </a-descriptions-item>
              <a-descriptions-item v-if="invoiceDetail.invoiceFileName" label="发票文件">
                <div class="invoice-file-actions">
                  <a-button
                    v-if="invoiceDetail.invoiceFileUrl"
                    type="primary"
                    size="small"
                    @click="handlePreview">
                    <template #icon>
                      <EyeOutlined />
                    </template>
                    预览发票
                  </a-button>
                  <a-button
                    type="default"
                    size="small"
                    @click="handleDownload">
                    <template #icon>
                      <DownloadOutlined />
                    </template>
                    下载 {{ invoiceDetail.invoiceFileName }}
                  </a-button>
                </div>
              </a-descriptions-item>
            </a-descriptions>
          </div>

          <!-- 关联交易 -->
          <div v-if="invoiceDetail.transactions?.length" class="detail-section">
            <h3 class="section-title">关联交易</h3>
            <a-table
              :columns="transactionColumns"
              :data-source="invoiceDetail.transactions"
              :pagination="false"
              size="small"
              row-key="id">

              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'transactionAmount'">
                  <span class="amount">¥{{ record.transactionAmount.toFixed(2) }}</span>
                </template>
                <template v-if="column.key === 'createdAt'">
                  <span>{{ formatDateTime(record.createdAt) }}</span>
                </template>
              </template>
            </a-table>
          </div>

          <!-- 备注信息 -->
          <div class="detail-section">
            <h3 class="section-title">备注信息</h3>
            <div class="remarks-container">
              <div v-if="invoiceDetail.applicantRemarks" class="remark-item">
                <div class="remark-label">申请备注：</div>
                <div class="remark-content">{{ invoiceDetail.applicantRemarks }}</div>
              </div>
              <div v-if="invoiceDetail.processorRemarks" class="remark-item">
                <div class="remark-label">处理备注：</div>
                <div class="remark-content">{{ invoiceDetail.processorRemarks }}</div>
              </div>
              <div v-if="!invoiceDetail.applicantRemarks && !invoiceDetail.processorRemarks" class="remark-item">
                <a-empty description="暂无备注信息" :image="Empty.PRESENTED_IMAGE_SIMPLE" />
              </div>
            </div>
          </div>
        </div>

        <div v-else class="no-data">
          <a-empty description="暂无发票详情数据" />
        </div>
      </a-spin>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <a-button @click="handleClose">关闭</a-button>
        <div v-if="invoiceDetail?.status === 'COMPLETED'" class="footer-actions">
          <a-button
            v-if="invoiceDetail.invoiceFileUrl"
            type="primary"
            @click="handlePreview">
            <template #icon>
              <EyeOutlined />
            </template>
            预览发票
          </a-button>
          <a-button
            type="default"
            @click="handleDownload">
            <template #icon>
              <DownloadOutlined />
            </template>
            下载发票
          </a-button>
        </div>
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { message, Empty } from 'ant-design-vue'
import { DownloadOutlined, EyeOutlined } from '@ant-design/icons-vue'
import { formatDateTime } from '@/utils/format'
import { invoiceApi, type InvoiceDetail } from '@/api/invoice'
import CopyButton from '@/components/common/CopyButton.vue'
import CopyableField from '@/components/common/CopyableField.vue'

// 组件属性
interface Props {
  modelValue: boolean
  invoiceId?: number
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'download', invoice: InvoiceDetail): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const invoiceDetail = ref<InvoiceDetail | null>(null)

// 交易记录表格列配置
const transactionColumns = [
  {
    title: '交易单号',
    dataIndex: 'transactionNo',
    key: 'transactionNo',
    width: 180,
    ellipsis: true
  },
  {
    title: '交易金额',
    dataIndex: 'transactionAmount',
    key: 'transactionAmount',
    width: 120,
    align: 'right'
  },
  {
    title: '交易描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true
  },
  {
    title: '交易时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 180
  }
]

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

// 监听对话框打开
watch(visible, (newValue) => {
  if (newValue && props.invoiceId) {
    getInvoiceDetail()
  }
})

// 监听发票ID变化
watch(() => props.invoiceId, (newInvoiceId) => {
  if (newInvoiceId && visible.value) {
    getInvoiceDetail()
  }
})

// 获取发票详情
const getInvoiceDetail = async () => {
  if (!props.invoiceId) {
    console.warn('发票ID不能为空')
    return
  }

  try {
    loading.value = true
    const response = await invoiceApi.getInvoiceDetail(props.invoiceId)
    invoiceDetail.value = response.data
  } catch (error) {
    console.error('获取发票详情失败:', error)
    message.error('获取发票详情失败')
    invoiceDetail.value = null
  } finally {
    loading.value = false
  }
}

// 状态相关方法
const getStatusColor = (status: string): string => {
  const statusMap: Record<string, string> = {
    'PENDING': 'orange',
    'COMPLETED': 'green',
    'CANCELLED': 'red'
  }
  return statusMap[status] || 'default'
}

const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    'PENDING': '开票中',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消'
  }
  return statusMap[status] || status
}

// 下载发票
const handleDownload = async () => {
  //console.log('🚀 handleDownload 开始执行')
  //console.log('🔍 invoiceDetail.value:', invoiceDetail.value)

  if (!invoiceDetail.value) {
    console.error('❌ 发票信息不存在')
    message.error('发票信息不存在')
    return
  }

  //console.log('📋 发票ID:', invoiceDetail.value.id)
  //console.log('📋 发票状态:', invoiceDetail.value.status)
  //console.log('📋 发票文件名:', invoiceDetail.value.invoiceFileName)

  try {
    //console.log('🌐 开始调用下载API...')
    const response = await invoiceApi.downloadInvoiceFile(invoiceDetail.value.id)
    //console.log('📥 API响应:', response)
    //console.log('📥 响应类型:', typeof response)
    //console.log('📥 响应success:', response?.success)
    //console.log('📥 响应data:', response?.data)
    //console.log('📥 响应message:', response?.message)

        // 兼容处理：下载链接可能在 data 或 message 字段中
    const downloadUrl = response.data || response.message

    if (response.success && downloadUrl) {
      //console.log('✅ 获取下载链接成功:', downloadUrl)
      // 使用获取的下载链接打开新窗口下载
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = invoiceDetail.value.invoiceFileName || `发票_${invoiceDetail.value.invoiceNo}.pdf`
      link.target = '_blank'
      //console.log('🔗 创建下载链接:', {
        href: link.href,
        download: link.download,
        target: link.target
      })

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      //console.log('✅ 下载链接点击成功')
      message.success('发票下载成功')
      // 仍然触发父组件的download事件，以便父组件可以做其他处理
      emit('download', invoiceDetail.value)
    } else {
      console.error('❌ API响应失败:', response)
      console.error('❌ 无有效下载链接 - data:', response?.data, 'message:', response?.message)
      message.error('获取下载链接失败')
    }
  } catch (error) {
    console.error('❌ 下载发票失败 - 详细错误信息:')
    console.error('错误对象:', error)
    console.error('错误类型:', typeof error)
    console.error('错误消息:', (error as any)?.message)
    console.error('错误响应:', (error as any)?.response)
    console.error('错误状态码:', (error as any)?.response?.status)
    console.error('错误数据:', (error as any)?.response?.data)
    console.error('错误堆栈:', (error as any)?.stack)
    message.error('下载发票失败')
  }
}

// 预览发票PDF
const handlePreview = async () => {
  //console.log('🚀 handlePreview 开始执行')
  //console.log('🔍 invoiceDetail.value:', invoiceDetail.value)

  if (!invoiceDetail.value) {
    console.error('❌ 发票信息不存在')
    message.error('发票信息不存在')
    return
  }

  //console.log('📋 发票ID:', invoiceDetail.value.id)

  try {
    //console.log('🌐 开始调用预览API...')
    const response = await invoiceApi.downloadInvoiceFile(invoiceDetail.value.id)
    //console.log('📥 预览API响应:', response)

    // 兼容处理：预览链接可能在 data 或 message 字段中
    const previewUrl = response.data || response.message

    if (response.success && previewUrl) {
      //console.log('✅ 获取预览链接成功:', previewUrl)
      // 在新窗口打开预览
      window.open(previewUrl, '_blank')
      //console.log('✅ 预览窗口已打开')
    } else {
      console.error('❌ 预览API响应失败:', response)
      console.error('❌ 无有效预览链接 - data:', response?.data, 'message:', response?.message)
      message.error('获取预览链接失败')
    }
  } catch (error) {
    console.error('❌ 预览发票失败 - 详细错误信息:')
    console.error('错误对象:', error)
    console.error('错误类型:', typeof error)
    console.error('错误消息:', (error as any)?.message)
    console.error('错误响应:', (error as any)?.response)
    console.error('错误状态码:', (error as any)?.response?.status)
    console.error('错误数据:', (error as any)?.response?.data)
    message.error('预览发票失败')
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  invoiceDetail.value = null
}
</script>

<style scoped lang="scss">
.invoice-detail-content {
  .invoice-detail {
    .detail-section {
      @apply mb-6;

      .section-title {
        @apply text-lg font-semibold mb-4 text-gray-800;
      }

      .amount {
        @apply font-semibold text-blue-600;
      }

      // 发票抬头特殊样式
      .invoice-title-highlight {
        @apply relative bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-200 rounded-lg p-4 shadow-sm;

        .invoice-title-content {
          @apply text-xl font-bold text-gray-800 mb-2;

          line-height: 1.4;
          letter-spacing: 0.5px;
        }

        .invoice-title-actions {
          @apply absolute top-2 right-10;
        }

        .invoice-title-badge {
          @apply absolute top-0 right-0 bg-blue-500 text-white text-xs px-2 py-1 rounded-bl-lg rounded-tr-lg;

          font-weight: 500;
        }

        // 添加左侧装饰线
        &::before {
          content: "";

          @apply absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-blue-500 to-indigo-500 rounded-l-lg;
        }

        // 鼠标悬停效果
        &:hover {
          @apply shadow-md;

          transform: translateY(-1px);
          transition: all 0.2s ease-in-out;
        }
      }

      .invoice-file-actions {
        @apply flex gap-2;
      }
    }

    .remarks-container {
      .remark-item {
        @apply mb-4;

        .remark-label {
          @apply font-medium text-gray-700 mb-2;
        }

        .remark-content {
          @apply text-gray-600 bg-gray-50 p-3 rounded border;

          white-space: pre-wrap;
        }
      }
    }
  }

  .no-data {
    @apply text-center py-10;
  }
}

.dialog-footer {
  @apply flex justify-between items-center;

  .footer-actions {
    @apply flex gap-2;
  }
}

// 响应式设计
@media (width <= 768px) {
  .ant-modal {
    width: 95% !important;
    max-width: none !important;
  }

  .invoice-detail-content {
    .invoice-detail {
      .detail-section {
        .section-title {
          @apply text-base;
        }

        // 移动端发票抬头样式调整
        .invoice-title-highlight {
          @apply p-3;

          .invoice-title-content {
            @apply text-lg;
          }

          .invoice-title-actions {
            @apply top-1 right-8;
          }

          .invoice-title-badge {
            @apply text-xs px-2 py-1;
          }
        }
      }
    }
  }
}
</style>
