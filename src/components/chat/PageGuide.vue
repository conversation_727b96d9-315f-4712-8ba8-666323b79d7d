<script setup lang="ts">
import { driver } from 'driver.js'
import { onMounted, ref } from 'vue'
import 'driver.js/dist/driver.css'

// 定义 props
const props = defineProps({
  // 是否自动开始引导
  autoStart: {
    type: Boolean,
    default: false,
  },
})

// 定义 emits
const emits = defineEmits(['guideStarted', 'guideCompleted', 'guideCancelled'])

// 引导实例
let driverInstance: any = null
const isGuideActive = ref(false)

// 创建引导实例
function createDriver() {
  driverInstance = driver({
    showProgress: true,
    showButtons: ['next', 'previous', 'close'],
    nextBtnText: '下一步',
    prevBtnText: '上一步',
    doneBtnText: '完成',
    progressText: '{{current}} / {{total}}',

    // 按钮点击事件处理
    onCloseClick: () => {
      //console.log('Close Button Clicked')
      driverInstance.destroy()
    },

    onNextClick: (element, step, { state }) => {
      //console.log('Next/Done Button Clicked')

      // 检查是否是最后一步
      const currentIndex = state.activeIndex || 0
      const totalSteps = driverInstance.getConfig().steps?.length || 0

      if (currentIndex >= totalSteps - 1) {
        // 最后一步，完成引导
        //console.log('完成引导')
        driverInstance.destroy()
      }
      else {
        // 不是最后一步，继续下一步
        //console.log('继续下一步')
        driverInstance.moveNext()
      }
    },

    // 引导步骤配置
    steps: [
      {
        element: '#chat-sender',
        popover: {
          title: '消息输入区域',
          description: '在这里输入您的问题或消息。支持文本输入和模板消息发送。您可以点击模板按钮选择预设的提示词模板。',
          side: 'top',
          showButtons: ['next', 'close'],
        },
      },
      {
        element: '#conversation-history',
        popover: {
          title: '历史会话',
          description: '查看和管理您的历史对话记录。点击任意会话可以继续之前的对话，会话按时间分组显示。',
          side: 'right',
          showButtons: ['next', 'previous', 'close'],
        },
      },
      {
        element: '#prompt-templates',
        popover: {
          title: '提示词模板',
          description: '浏览和选择预设的提示词模板。这些模板可以帮助您快速开始特定类型的对话，提高沟通效率。',
          side: 'left',
          showButtons: ['next', 'previous', 'close'],
        },
      },
      {
        element: '#chat-welcome',
        popover: {
          title: '建议示例',
          description: '在欢迎页面中，您可以看到一些建议的问题示例。点击这些示例可以快速开始对话，了解AI助手的能力。',
          side: 'top',
          showButtons: ['previous', 'close'],
        },
      },
    ],

    // 事件回调
    onHighlightStarted: () => {
      isGuideActive.value = true
      emits('guideStarted')
    },

    onDestroyStarted: () => {
      isGuideActive.value = false
      emits('guideCompleted')
    },

    onDestroyed: () => {
      isGuideActive.value = false
      emits('guideCancelled')
    },

    onPopoverRender: (popover: any, { config: _config }: any) => {
      // 自定义样式 - 设置更高的 z-index
      if (popover.wrapper) {
        popover.wrapper.style.zIndex = '10002'
      }

      // 设置遮罩层的 z-index
      const overlay = document.querySelector('.driver-overlay')
      if (overlay) {
        overlay.style.zIndex = '10001'
      }
    },
  })
}

// 开始引导
function startGuide() {
  if (!driverInstance) {
    createDriver()
  }

  // 检查必要的元素是否存在
  const requiredElements = ['#chat-sender', '#conversation-history', '#prompt-templates']
  const missingElements = requiredElements.filter(selector => !document.querySelector(selector))

  if (missingElements.length > 0) {
    console.warn('引导所需的元素不存在:', missingElements)
    // 可以选择跳过缺失的步骤或显示提示
  }

  driverInstance.drive()
}

// 停止引导
function stopGuide() {
  if (driverInstance) {
    driverInstance.destroy()
    isGuideActive.value = false
    emits('guideCancelled')
  }
}

// 重置引导
function resetGuide() {
  if (driverInstance) {
    driverInstance.destroy()
  }
  createDriver()
}

// 组件挂载时的处理
onMounted(() => {
  createDriver()

  // 如果设置了自动开始，延迟一段时间后开始引导
  if (props.autoStart) {
    setTimeout(() => {
      startGuide()
    }, 1000) // 延迟1秒，确保页面元素都已渲染
  }
})

// 暴露方法给父组件
defineExpose({
  startGuide,
  stopGuide,
  resetGuide,
  isGuideActive,
})
</script>

<template>
  <div class="page-guide">
    <!-- 引导功能组件，不再包含UI按钮 -->
  </div>
</template>

<style scoped>
/* 自定义 Driver.js 样式 */
:global(.driver-overlay) {
  z-index: 10001 !important;
}

:global(.driver-popover) {
  z-index: 10002 !important;
  max-width: 320px !important;
  color: #333 !important;
  background: white !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 20px rgb(0 0 0 / 15%) !important;
}

:global(.driver-popover-title) {
  margin-bottom: 8px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  color: #1f2937 !important;
}

:global(.driver-popover-description) {
  font-size: 14px !important;
  line-height: 1.5 !important;
  color: #6b7280 !important;
}

/* 按钮基础样式 - 与项目 FaButton 保持一致 */
:global(.driver-popover-next-btn),
:global(.driver-popover-prev-btn),
:global(.driver-popover-close-btn),
:global(.driver-popover-done-btn) {
  display: inline-flex !important;
  gap: 8px !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 8px 16px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  white-space: nowrap !important;
  cursor: pointer !important;
  outline: none !important;
  border: none !important;
  border-radius: calc(var(--radius) - 2px) !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  ring-offset-color: hsl(var(--background)) !important;
}

/* Focus 状态 */
:global(.driver-popover-next-btn:focus-visible),
:global(.driver-popover-prev-btn:focus-visible),
:global(.driver-popover-close-btn:focus-visible),
:global(.driver-popover-done-btn:focus-visible) {
  outline: 2px solid hsl(var(--ring)) !important;
  outline-offset: 2px !important;
}

/* Next 按钮 - Primary 样式 */
:global(.driver-popover-next-btn) {
  color: hsl(var(--primary-foreground)) !important;
  background: hsl(var(--primary)) !important;
}

:global(.driver-popover-next-btn:hover) {
  background: hsl(var(--primary) / 90%) !important;
}

/* Done 按钮 - Primary 样式 (与 Next 按钮相同) */
:global(.driver-popover-done-btn) {
  color: hsl(var(--primary-foreground)) !important;
  background: hsl(var(--primary)) !important;
}

:global(.driver-popover-done-btn:hover) {
  background: hsl(var(--primary) / 90%) !important;
}

/* Previous 按钮 - Secondary 样式 */
:global(.driver-popover-prev-btn) {
  margin-right: 8px !important;
  color: hsl(var(--secondary-foreground)) !important;
  background: hsl(var(--secondary)) !important;
}

:global(.driver-popover-prev-btn:hover) {
  background: hsl(var(--secondary) / 80%) !important;
}

/* Close 按钮 - Outline 样式 */
:global(.driver-popover-close-btn) {
  color: hsl(var(--foreground)) !important;
  background: hsl(var(--background)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

:global(.driver-popover-close-btn:hover) {
  color: hsl(var(--accent-foreground)) !important;
  background: hsl(var(--accent)) !important;
}

:global(.driver-popover-progress-text) {
  margin-bottom: 12px !important;
  font-size: 12px !important;
  color: #9ca3af !important;
}

/* 高亮效果自定义 */
:global(.driver-highlighted-element) {
  border-radius: 4px !important;
  box-shadow: 0 0 0 4px rgb(59 130 246 / 30%) !important;
}

/* 暗色模式适配 */
:global(.dark .driver-popover) {
  color: #f9fafb !important;
  background: #1f2937 !important;
}

:global(.dark .driver-popover-title) {
  color: #f9fafb !important;
}

:global(.dark .driver-popover-description) {
  color: #d1d5db !important;
}
</style>
