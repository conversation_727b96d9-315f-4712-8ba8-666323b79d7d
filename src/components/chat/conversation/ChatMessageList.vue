<script setup lang="ts">
// import type { BubbleListProps } from 'ant-design-x-vue'
import type { VNode } from 'vue'
import type { IntermediateStep } from './ChatThoughtChain.vue'
import { checkTokenUsageStatus, getRoundTokenUsage } from '@/api/tokenUsage'
import assistantAvatar from '@/assets/images/robot-4.gif'
import SubjectVisual from '@/components/visual/SubjectVisual.vue'
import { useBillingStore } from '@/stores/billing'
import {
  createCyclicLoadingMessage,
  createTokenStateManager,
  createTokenUsagePoller,
  extractRealConversationId,
  identifyRounds,
  isLastAssistantMessageInRound,
  isValidUUID,
  LOADING_MESSAGES,
  validateTokenQueryParams,
} from '@/utils/conversationUtils'
import { renderMarkdown } from '@/utils/markdownRenderer'
import {
  ExclamationCircleOutlined,
  FileOutlined,
  LineChartOutlined,
  LoadingOutlined,
  SmileOutlined,
} from '@ant-design/icons-vue'
import { Button, Flex, Space, Spin } from 'ant-design-vue'
import { Attachments, BubbleList, Prompts } from 'ant-design-x-vue'
import { computed, h, nextTick, onMounted, onUnmounted, readonly, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import ChatThoughtChain from './ChatThoughtChain.vue'

// 定义组件 props
const props = defineProps({
  chatHistory: {
    type: Array as () => ChatMessage[],
    required: true,
  },
  isHistoryLoading: {
    type: Boolean,
    default: false,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
  styles: {
    type: Object,
    default: () => ({}),
  },
})

// 定义组件的 emits
const emits = defineEmits([
  'suggestionClick', // suggestion 点击事件
])

// 获取stores
const billingStore = useBillingStore()

// 获取router实例
const router = useRouter()

// 修复类型定义，使其与测试数据兼容
interface ChatMessage {
  id?: string
  role: 'user' | 'assistant' | string
  content?: string
  createdAt?: string
  loading?: boolean
  error?: boolean
  intermediateSteps?: IntermediateStep[]

  // 新增轮次相关字段
  roundSequence?: number
  messageOrder?: number
  conversationId?: string

  // 修复：使用更宽松的类型定义以兼容测试数据
  list?: Array<{
    type: string
    content: any
  }>

  [key: string]: any
}

// 新增：Token 用量状态接口
interface TokenUsageState {
  status: 'idle' | 'loading' | 'success' | 'error' | 'not_found' | 'retrying'
  data: any | null
  error: string | null
  retryCount: number
  pollTimer: NodeJS.Timeout | null // 修复 timer 类型
  notFoundRetryCount: number // NOT_FOUND状态重试计数器
  notFoundRetryTimer: NodeJS.Timeout | null // NOT_FOUND重试定时器
}

// 新增：Token 缓存项接口
interface TokenCacheItem {
  status: 'idle' | 'loading' | 'success' | 'error' | 'not_found' | 'retrying'
  data: any | null
  error: string | null
  timestamp: number
  conversationId: string
  roundSequence: number
}

// 新增：轮次信息接口
interface RoundInfo {
  roundSequence: number
  conversationId: string
  messages: ChatMessage[]
  isLastRoundMessage: boolean
}

// Define a type for the items that BubbleList will consume.
interface CustomBubbleItem {
  key: string
  role: string
  content: any
  loading?: boolean
  loadingRender?: () => VNode
  header?: VNode
  footer?: (content: any) => VNode | string // 修复footer类型不返回null
  messageRender?: (content: any) => VNode | string
  placement?: 'start' | 'end'
  avatar?: any
  styles?: any
  typing?: any
  classNames?: any
}

// 可折叠内容的状态管理
const collapsedStates = ref<Record<string, Record<string, boolean>>>({})

// Suggestion extra描述的样式常量
const suggestionExtraStyle = {
  marginTop: '6px',
  padding: '8px 12px',
  backgroundColor: '#f8f9fa',
  borderRadius: '6px',
  fontSize: '13px',
  color: '#9D9D9D',
  fontStyle: 'italic',
  lineHeight: '1.4',
  border: '1px solid #e8e8e8',
}

// Token 管理器集合，用于组件卸载时清理
const tokenManagers = new Set<ReturnType<typeof createTokenUsageManager>>()

// 新增：Token 缓存存储
const tokenCache = ref<Map<string, TokenCacheItem>>(new Map())

// 新增：生成Token缓存键
function generateTokenCacheKey(conversationId: string, roundSequence: number): string {
  return `${conversationId}-${roundSequence}`
}

// 新增：从缓存获取Token状态
function getTokenFromCache(conversationId: string, roundSequence: number): TokenCacheItem | null {
  const key = generateTokenCacheKey(conversationId, roundSequence)
  return tokenCache.value.get(key) || null
}

// 新增：设置Token缓存
function setTokenCache(conversationId: string, roundSequence: number, item: TokenCacheItem): void {
  const key = generateTokenCacheKey(conversationId, roundSequence)
  tokenCache.value.set(key, {
    ...item,
    conversationId,
    roundSequence,
    timestamp: Date.now(),
  })
}

// 新增：检查消息Token状态
function getMessageTokenState(message: ChatMessage): {
  hasCache: boolean
  cacheItem: TokenCacheItem | null
  shouldQuery: boolean
} {
  if (!message.conversationId || !message.roundSequence) {
    return { hasCache: false, cacheItem: null, shouldQuery: false }
  }

  const cacheItem = getTokenFromCache(message.conversationId, message.roundSequence)
  const hasCache = !!cacheItem
  const shouldQuery = !hasCache && message.role === 'assistant'

  return { hasCache, cacheItem, shouldQuery }
}

// 检测消息是否为余额不足类型
function isInsufficientBalanceMessage(message: ChatMessage): boolean {
  // 方式1：检查agentType
  if (message.agentType === 'insufficient_balance') {
    return true
  }

  // 方式2：检查消息内容（备用方案）
  if (message.list && Array.isArray(message.list)) {
    return message.list.some(item =>
      item.type === 'text'
      && typeof item.content === 'string'
      && item.content.includes('账户余额不足，请先充值后再发起对话'),
    )
  }

  // 方式3：检查content字段（备用方案）
  if (typeof message.content === 'string'
    && message.content.includes('账户余额不足，请先充值后再发起对话')) {
    return true
  }

  return false
}

// 创建充值链接
function createRechargeLink() {
  return h('a', {
    onClick: (e: Event) => {
      e.preventDefault()
      router.push('/billing/recharge')
    },
    style: {
      color: '#1890ff',
      textDecoration: 'underline',
      cursor: 'pointer',
      marginLeft: '8px',
    },
  }, '立即充值')
}

// 切换折叠状态的函数
function toggleCollapsed(messageKey: string, itemIndex: number, detailIndex: number) {
  // 创建组合键以避免不同 collapsible 分组间的状态冲突
  const stateKey = `${itemIndex}-${detailIndex}`

  // 确保响应式追踪：使用 Vue 3 推荐的方式更新嵌套对象
  if (!collapsedStates.value[messageKey]) {
    // 使用 Vue.set 等价的方式确保响应式追踪
    collapsedStates.value = {
      ...collapsedStates.value,
      [messageKey]: {},
    }
  }

  // 确保嵌套对象的响应式更新
  const currentState = collapsedStates.value[messageKey][stateKey] ?? true
  collapsedStates.value = {
    ...collapsedStates.value,
    [messageKey]: {
      ...collapsedStates.value[messageKey],
      [stateKey]: !currentState,
    },
  }
}

// 获取折叠状态的函数
function isCollapsed(messageKey: string, itemIndex: number, detailIndex: number) {
  // 使用相同的组合键获取状态
  const stateKey = `${itemIndex}-${detailIndex}`
  const result = collapsedStates.value[messageKey]?.[stateKey] ?? true // 默认折叠
  return result
}

// 处理 suggestion 点击事件
function handleSuggestionClick(suggestionItem: any) {
  // 发射事件给父组件处理
  emits('suggestionClick', suggestionItem)
}

// 使用新的循环加载消息功能
const loadingMessage = ref(LOADING_MESSAGES[0]) // 初始化为第一条消息
const messagesRef = ref<HTMLElement | null>(null)

// 创建循环加载消息控制器
const cyclicLoader = createCyclicLoadingMessage((message: string) => {
  loadingMessage.value = message
})

// 监听加载状态变化，控制循环显示
watch(() => props.isLoading || props.isHistoryLoading, (newLoading: boolean, oldLoading: boolean) => {
  if (newLoading && !oldLoading) {
    // 开始加载时启动循环显示
    cyclicLoader.start()
  }
  else if (!newLoading && oldLoading) {
    // 停止加载时停止循环显示
    cyclicLoader.stop()
  }
})

// 监听聊天历史变化，当有新的加载消息时控制循环显示
watch(() => props.chatHistory, (newHistory: ChatMessage[], oldHistory: ChatMessage[]) => {
  // console.log('📨 [Watch监听] 聊天历史发生变化:', {
  //   newHistoryLength: newHistory ? newHistory.length : 0,
  //   oldHistoryLength: oldHistory ? oldHistory.length : 0,
  //   changeType: !oldHistory || oldHistory.length === 0
  //     ? '首次加载'
  //     : newHistory.length > oldHistory.length
  //       ? '新增消息'
  //       : newHistory.length === oldHistory.length ? '内容变化' : '消息减少',
  // })

  // 检查是否有新的加载消息
  const hasLoadingMessage = newHistory.some((msg: ChatMessage) => msg.loading)
  if (hasLoadingMessage && !cyclicLoader.isActive()) {
    //console.log('🔄 [Watch监听] 发现加载消息，启动循环显示')
    cyclicLoader.start()
  }
  else if (!hasLoadingMessage && cyclicLoader.isActive()) {
    //console.log('⏹️ [Watch监听] 加载完成，停止循环显示')
    cyclicLoader.stop()
  }

  //console.log('oldHistory', oldHistory)
  //console.log('newHistory', newHistory)

  // 检查消息变化：可能是新增消息或现有消息内容变化
  if (oldHistory && oldHistory.length > 0) {
    // 情况1：长度增加（新增消息）
    if (newHistory.length > oldHistory.length) {
      //console.log('📈 [Watch监听] 情况1：长度增加（新增消息）')
      const newMessages = newHistory.slice(oldHistory.length)
      // console.log('📝 [Watch监听] 新增的消息列表:', {
      //   count: newMessages.length,
      //   messages: newMessages.map(msg => ({
      //     id: msg.id,
      //     role: msg.role,
      //     conversationId: msg.conversationId,
      //     roundSequence: msg.roundSequence,
      //     hasContent: !!(msg.content || msg.list),
      //   })),
      // })

      newMessages.forEach((newMsg, index) => {
        // console.log(`🔍 [Watch监听] 处理新增消息 ${index + 1}/${newMessages.length}:`, {
        //   messageId: newMsg.id,
        //   role: newMsg.role,
        //   conversationId: newMsg.conversationId,
        //   roundSequence: newMsg.roundSequence,
        // })

        if (newMsg.role === 'assistant') {
          // console.log('🤖 [Watch监听] 发现新增的助手消息:', {
          //   messageId: newMsg.id,
          //   conversationId: newMsg.conversationId,
          //   roundSequence: newMsg.roundSequence,
          //   loading: newMsg.loading,
          //   isComplete: isMessageComplete(newMsg),
          // })

          // 如果消息已完成，立即触发Token查询
          if (isMessageComplete(newMsg)) {
            //console.log('✅ [Watch监听] 新增消息已完成，立即查询')
            handleAssistantMessageCompleted(newMsg)
          }
          else {
            //console.log('⏳ [Watch监听] 新增消息未完成，等待后续状态变化')
          }
        }
        else {
          //console.log('👤 [Watch监听] 跳过用户消息')
        }
      })
    }
    // 情况2：长度相同，检查消息内容变化
    else if (newHistory.length === oldHistory.length) {
      //console.log('🔄 [Watch监听] 情况2：长度相同，检查消息内容变化')
      newHistory.forEach((newMsg, index) => {
        const oldMsg = oldHistory[index]

        // console.log(`🔍 [Watch监听] 对比消息 ${index + 1}/${newHistory.length}:`, {
        //   messageId: newMsg.id,
        //   oldMessageId: oldMsg?.id,
        //   role: newMsg.role,
        //   conversationId: newMsg.conversationId,
        //   oldConversationId: oldMsg?.conversationId,
        //   roundSequence: newMsg.roundSequence,
        //   oldRoundSequence: oldMsg?.roundSequence,
        // })

        // 检查助手消息状态变化
        if (newMsg.role === 'assistant') {
          // 使用新的状态检测逻辑
          const messageCompleted = detectMessageCompletion(oldMsg, newMsg)

          if (messageCompleted) {
            // console.log('🎯 [Watch监听] 检测到助手消息完成状态变化:', {
            //   oldId: oldMsg?.id,
            //   newId: newMsg.id,
            //   conversationId: newMsg.conversationId,
            //   roundSequence: newMsg.roundSequence,
            //   loading: newMsg.loading,
            //   isComplete: isMessageComplete(newMsg),
            //   completionType: {
            //     loadingChanged: oldMsg?.loading === true && newMsg.loading === false,
            //     contentAdded: (!oldMsg?.content && !oldMsg?.list) && (newMsg.content || newMsg.list),
            //     messageReplaced: oldMsg?.id !== newMsg.id && (newMsg.content || newMsg.list) && !newMsg.loading,
            //   },
            // })

            handleAssistantMessageCompleted(newMsg)
          }
          else {
            // console.log('📝 [Watch监听] 助手消息状态无关键变化，跳过Token查询:', {
            //   messageId: newMsg.id,
            //   oldLoading: oldMsg?.loading,
            //   newLoading: newMsg.loading,
            //   oldHasContent: !!(oldMsg?.content || oldMsg?.list),
            //   newHasContent: !!(newMsg.content || newMsg.list),
            //   sameId: oldMsg?.id === newMsg.id,
            // })
          }
        }
      })
    }
  }
  // 情况3：第一次加载历史记录
  else if (!oldHistory || oldHistory.length === 0) {
    // console.log('🆕 [Watch监听] 情况3：第一次加载历史记录，检查现有助手消息')
    // console.log('📋 [Watch监听] 首次加载历史记录详情:', {
    //   totalMessages: newHistory.length,
    //   assistantMessages: newHistory.filter(msg => msg.role === 'assistant').length,
    //   userMessages: newHistory.filter(msg => msg.role === 'user').length,
    //   messagesWithConversationId: newHistory.filter(msg => msg.conversationId).length,
    //   messagesWithRoundSequence: newHistory.filter(msg => msg.roundSequence !== undefined).length,
    // })

    newHistory.forEach((newMsg, index) => {
      // console.log(`🔍 [Watch监听] 检查历史消息 ${index + 1}/${newHistory.length}:`, {
      //   messageId: newMsg.id,
      //   role: newMsg.role,
      //   conversationId: newMsg.conversationId,
      //   roundSequence: newMsg.roundSequence,
      //   hasValidConversationId: isValidConversationId(newMsg.conversationId),
      // })

      if (newMsg.role === 'assistant') {
        // console.log('🤖 [Watch监听] 发现现有助手消息，触发Token查询检查:', {
        //   messageId: newMsg.id,
        //   conversationId: newMsg.conversationId,
        //   roundSequence: newMsg.roundSequence,
        //   loading: newMsg.loading,
        //   isComplete: isMessageComplete(newMsg),
        // })

        // 如果消息已完成，立即触发Token查询
        if (isMessageComplete(newMsg)) {
          //console.log('✅ [Watch监听] 现有消息已完成，立即查询')
          handleAssistantMessageCompleted(newMsg)
        }
        else {
          //console.log('⏳ [Watch监听] 现有消息未完成，等待后续状态变化')
        }
      }
    })
  }

  nextTick(() => scrollToBottom())
}, { deep: true })

// Define roles for BubbleList. These provide default styling/avatar for 'user' and 'assistant' roles.
const rolesConfig = {
  user: {
    placement: 'end',
    avatar: { icon: '👤', style: { background: '#ffffff' } },
  },
  assistant: {
    placement: 'start',
    avatar: {
      src: assistantAvatar,
      style: {
        background: '#ffffff',
        overflow: 'hidden',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      },
      className: 'assistant-avatar',
    },
    typing: { step: 20, interval: 3 }, // Default typing for assistant messages
  },
  // 新增：文本消息类型
  text: {
    placement: 'start',
    avatar: {
      src: assistantAvatar,
      style: {
        background: '#ffffff',
        overflow: 'hidden',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      },
      className: 'assistant-avatar',
    },
    typing: { step: 20, interval: 3 },
  },
  // 新增：建议消息类型
  suggestion: {
    placement: 'start',
    avatar: { style: { visibility: 'hidden' } },
    variant: 'borderless',
    messageRender: (content: any) => {
      const suggestions = Array.isArray(content) ? content : []

      // 为每个suggestion创建包含item和extra描述的组件
      const suggestionItems = suggestions.map((item: any) => {
        const itemKey = item.value || item.label || item

        // 创建单个suggestion item
        const singlePrompt = h(Prompts, {
          vertical: true,
          items: [{
            key: itemKey,
            icon: h(SmileOutlined, { style: { color: '#FAAD14' } }),
            description: item.label || item.value || item,
          }],
          onItemClick: (_info: any) => {
            // console.log('Suggestion clicked:', _info)
            if (item.linkParams) {
              // console.log('Emitting suggestion click with linkParams:', item.linkParams)
              if ((window as any).__suggestionClickHandler) {
                (window as any).__suggestionClickHandler(item.linkParams)
              }
            }
          },
        })

        // 如果有extra内容，创建包含item和extra的容器
        if (item.extra && item.extra.trim()) {
          return h('div', {
            key: `suggestion-container-${itemKey}`,
            style: { marginBottom: '12px' },
          }, [
            singlePrompt,
            h('div', {
              style: suggestionExtraStyle,
            }, item.extra),
          ])
        }

        // 如果没有extra内容，直接返回item
        return h('div', {
          key: `suggestion-container-${itemKey}`,
          style: { marginBottom: '12px' },
        }, [singlePrompt])
      })

      return h('div', {
        style: { width: '100%' },
      }, suggestionItems)
    },
  },
  // 新增：文件消息类型
  file: {
    placement: 'start',
    avatar: { style: { visibility: 'hidden' } },
    variant: 'borderless',
    messageRender: (content: any) => {
      const files = Array.isArray(content) ? content : []
      return h(Flex, { vertical: true, gap: 'middle' }, {
        default: () => files.map((file: any) =>
          h(Attachments.FileCard, {
            key: file.uid || file.name,
            item: {
              uid: file.uid || file.name,
              name: file.name,
              size: file.size,
              status: file.status || 'done',
              percent: file.percent,
              description: file.description,
            },
          }),
        ),
      })
    },
  },
  // 新增：可折叠消息类型
  collapsible: {
    placement: 'start',
    avatar: { style: { visibility: 'hidden' } },
    variant: 'borderless',
  },
} as const

// 修复轮次识别逻辑中的类型转换
const identifiedRounds = computed(() => {
  let chatHistory = props.chatHistory

  // 处理后端返回的包装数据结构
  if (chatHistory && typeof chatHistory === 'object' && 'data' in chatHistory) {
    chatHistory = (chatHistory as any).data
  }

  if (!chatHistory || chatHistory.length === 0) {
    return new Map<number, RoundInfo>()
  }

  // 类型转换以确保兼容性，使用类型断言
  const messages = chatHistory.map((msg: any) => ({
    ...msg,
    list: msg.list?.map((item: any) => ({
      type: item.type as any, // 使用类型断言绕过严格检查
      content: item.content,
    })),
  })) as any[]

  return identifyRounds(messages)
})

// 修复类似的类型转换问题
function isLastAssistantInRound(message: ChatMessage): boolean {
  let chatHistory = props.chatHistory

  // 处理后端返回的包装数据结构
  if (chatHistory && typeof chatHistory === 'object' && 'data' in chatHistory) {
    chatHistory = (chatHistory as any).data
  }

  if (!chatHistory || chatHistory.length === 0) {
    // console.log('isLastAssistantInRound: 聊天历史为空')
    return false
  }

  // 类型转换以确保兼容性
  const messages = chatHistory.map((msg: any) => ({
    ...msg,
    list: msg.list?.map((item: any) => ({
      type: item.type as any,
      content: item.content,
    })),
  })) as any[]

  const result = isLastAssistantMessageInRound(message as any, messages)

  // console.log('isLastAssistantInRound检查结果:', {
  //   messageId: message.id,
  //   messageRole: message.role,
  //   result,
  //   totalMessages: messages.length,
  //   identifiedRounds: identifyRounds(messages).size,
  // })

  return result
}

// 获取消息的轮次信息
function _getMessageRoundInfo(message: ChatMessage): RoundInfo | null {
  const rounds = identifiedRounds.value
  return rounds.get(message.roundSequence || 1) || null
}

// 本地 Token 用量格式化函数（避免命名冲突）
function formatTokenUsageDisplay(data: any): string {
  if (!data) {
    return ''
  }

  // 确保Token数量为整数（不显示小数位）
  const inputTokens = Math.round(Number(data.inputTokens || 0))
  const outputTokens = Math.round(Number(data.outputTokens || 0))

  // 确保费用为有效数字
  let totalCost = data.totalCost || 0
  if (typeof totalCost === 'string') {
    totalCost = Number.parseFloat(totalCost)
  }

  // 检查费用是否为有效数字
  if (Number.isNaN(totalCost)) {
    totalCost = 0
  }

  // 格式化费用为2位小数
  const formattedCost = totalCost.toFixed(2)

  return `输入: ${inputTokens.toLocaleString()} · 输出: ${outputTokens.toLocaleString()} · 费用: ¥${formattedCost}`
}

// 检查是否为有效的会话ID（包括临时会话ID）
function isValidConversationId(conversationId: string | undefined): boolean {
  if (!conversationId || typeof conversationId !== 'string') {
    return false
  }

  // 接受UUID格式或临时会话ID
  return isValidUUID(conversationId) || conversationId === 'temp-conversation'
}

// 创建重新查询链接
function createRetryLink(onRetry: () => void) {
  return h('span', {
    style: {
      color: '#1890ff',
      cursor: 'pointer',
      fontSize: '12px',
      marginLeft: '8px',
      textDecoration: 'underline',
    },
    onClick: () => {
      //console.log('🔄 [重新查询] 点击事件触发')
      //console.log('🔄 [重新查询] onRetry 类型:', typeof onRetry)
      //console.log('🔄 [重新查询] onRetry 函数:', onRetry)
      try {
        const result = onRetry()
        //console.log('🔄 [重新查询] onRetry 返回值:', result)
        //console.log('🔄 [重新查询] onRetry 执行完成')
      }
      catch (error) {
        console.error('🔄 [重新查询] onRetry 执行错误:', error)
      }
    },
    onMouseenter: (e: MouseEvent) => {
      (e.target as HTMLElement).style.color = '#40a9ff'
    },
    onMouseleave: (e: MouseEvent) => {
      (e.target as HTMLElement).style.color = '#1890ff'
    },
  }, '重新查询')
}

// 独立的重新查询函数，用于缓存数据场景
function createIndependentRetryQuery(message: ChatMessage) {
  return async () => {
    // console.log('🔄 [独立重查] createIndependentRetryQuery 被调用:', {
    //   conversationId: message.conversationId,
    //   roundSequence: message.roundSequence,
    //   messageId: message.id,
    // })

    try {
      // 清除缓存
      if (message.conversationId && message.roundSequence) {
        const cacheKey = generateTokenCacheKey(message.conversationId, message.roundSequence)
        //console.log('🔄 [独立重查] 清除缓存键:', cacheKey)
        tokenCache.value.delete(cacheKey)

        // 重新创建管理器并开始查询
        //console.log('🔄 [独立重查] 创建新的 tokenUsageManager')
        const tokenUsageManager = createTokenUsageManager(message)
        tokenManagers.add(tokenUsageManager)

        //console.log('🔄 [独立重查] 开始查询')
        await tokenUsageManager.startQuery()

        //console.log('🔄 [独立重查] 查询完成')
      }
      else {
        console.error('🔄 [独立重查] 缺少必要参数:', {
          conversationId: message.conversationId,
          roundSequence: message.roundSequence,
        })
      }
    }
    catch (error) {
      console.error('🔄 [独立重查] 执行异常:', error)
    }
  }
}

// 创建 Token 用量 Footer
function createTokenUsageFooter(message: ChatMessage, tokenState: TokenUsageState, tokenUsageManager?: any) {
  return (_content: any) => {
    const { status, data, error } = tokenState

    // 根据状态显示不同内容
    if (status === 'loading') {
      return h(Space, { size: 'small', style: { fontSize: '12px', color: '#666' } }, {
        default: () => [
          h(LoadingOutlined, { spin: true }),
          h('span', '正在计算Token用量...'),
        ],
      })
    }

    if (status === 'retrying') {
      return h(Space, { size: 'small', style: { fontSize: '12px', color: '#faad14' } }, {
        default: () => [
          h('span', '🔄'),
          h('span', 'Token用量统计中...'),
        ],
      })
    }

    if (status === 'error') {
      // 检测是否为余额不足消息
      if (isInsufficientBalanceMessage(message)) {
        const rechargeElements = [
          h('span', { style: { color: '#faad14' } }, '💳'),
          h('span', '账户余额不足，'),
          createRechargeLink(),
        ]

        return h(Space, { size: 'small', style: { fontSize: '12px', color: '#faad14' } }, {
          default: () => rechargeElements,
        })
      }

      // console.log('🔄 [Footer] 错误状态，检查 tokenUsageManager:', {
      //   hasTokenUsageManager: !!tokenUsageManager,
      //   hasRetryQuery: !!(tokenUsageManager && tokenUsageManager.retryQuery),
      //   error,
      //   status,
      // })

      // 为NOT_FOUND状态提供专门的显示
      if (error === 'Token用量暂未生成') {
        return h(Space, { size: 'small', style: { fontSize: '12px', color: '#faad14' } }, {
          default: () => [
            h('span', '💭'),
            h('span', 'Token用量统计中...'),
          ],
        })
      }

      const errorElements = [
        h(ExclamationCircleOutlined),
        h('span', error || 'Token用量获取失败'),
      ]

      // 如果有tokenUsageManager，添加重新查询链接
      if (tokenUsageManager && tokenUsageManager.retryQuery) {
        //console.log('🔄 [Footer] 添加重新查询链接')
        errorElements.push(createRetryLink(() => {
          //console.log('🔄 [Footer] 调用 tokenUsageManager.retryQuery')
          tokenUsageManager.retryQuery()
        }))
      }
      else {
        //console.log('🔄 [Footer] 未添加重新查询链接 - tokenUsageManager 或 retryQuery 不存在')
      }

      return h(Space, { size: 'small', style: { fontSize: '12px', color: '#ff4d4f' } }, {
        default: () => errorElements,
      })
    }

    if (status === 'success' && data) {
      return h(Space, { size: 'small', style: { fontSize: '12px', color: '#666' } }, {
        default: () => [
          h(LineChartOutlined, { style: { color: '#52c41a' } }),
          h('span', formatTokenUsageDisplay(data)),
        ],
      })
    }

    // 默认返回空字符串而不是null
    return ''
  }
}

// Token 状态管理器（不使用 Vue 生命周期钩子）
function createTokenUsageManager(message: ChatMessage) {
  const MAX_NOT_FOUND_RETRIES = 3
  const NOT_FOUND_RETRY_INTERVAL = 3000 // 3秒

  const state = ref<TokenUsageState>({
    status: 'idle',
    data: null,
    error: null,
    retryCount: 0,
    pollTimer: null,
    notFoundRetryCount: 0,
    notFoundRetryTimer: null,
  })

  // 停止轮询
  const stopPolling = () => {
    if (state.value.pollTimer) {
      clearInterval(state.value.pollTimer)
      state.value.pollTimer = null
    }
  }

  // 停止NOT_FOUND重试
  const stopNotFoundRetry = () => {
    if (state.value.notFoundRetryTimer) {
      clearInterval(state.value.notFoundRetryTimer)
      state.value.notFoundRetryTimer = null
    }
  }

  // 开始NOT_FOUND重试
  const startNotFoundRetry = () => {
    if (state.value.notFoundRetryTimer) {
      clearInterval(state.value.notFoundRetryTimer)
    }

    state.value.status = 'retrying'
    state.value.notFoundRetryCount = 0

    console.log('🔄 [Token管理器] 开始NOT_FOUND重试机制:', {
      conversationId: message.conversationId,
      roundSequence: message.roundSequence,
      maxRetries: MAX_NOT_FOUND_RETRIES,
      retryInterval: NOT_FOUND_RETRY_INTERVAL,
    })

    state.value.notFoundRetryTimer = setInterval(async () => {
      try {
        state.value.notFoundRetryCount++

        // console.log(`🔄 [Token管理器] NOT_FOUND重试 ${state.value.notFoundRetryCount}/${MAX_NOT_FOUND_RETRIES}:`, {
        //   conversationId: message.conversationId,
        //   roundSequence: message.roundSequence,
        // })

        const tokenUsage = await getRoundTokenUsage(
          message.conversationId!,
          message.roundSequence!,
        )

        // console.log('📨 [Token管理器] 重试API调用返回结果:', {
        //   tokenUsage,
        //   status: tokenUsage?.status,
        //   retryCount: state.value.notFoundRetryCount,
        // })

        if ((tokenUsage as any).status === 'SUCCESS') {
          // console.log('✅ [Token管理器] 重试成功，Token计算完成:', {
          //   tokenUsage,
          //   retryCount: state.value.notFoundRetryCount,
          //   inputTokens: tokenUsage.inputTokens,
          //   outputTokens: tokenUsage.outputTokens,
          //   totalCost: tokenUsage.totalCost,
          // })

          stopNotFoundRetry()
          state.value.status = 'success'
          state.value.data = tokenUsage

          // 更新缓存
          setTokenCache(message.conversationId!, message.roundSequence!, {
            status: 'success',
            data: tokenUsage,
            error: null,
            timestamp: Date.now(),
            conversationId: message.conversationId!,
            roundSequence: message.roundSequence!,
          })

          // Token计算完成后刷新余额
          billingStore.refreshBalanceAfterTokenUsage()
        }
        else if (state.value.notFoundRetryCount >= MAX_NOT_FOUND_RETRIES) {
          // console.log('❌ [Token管理器] NOT_FOUND重试超时:', {
          //   maxRetries: MAX_NOT_FOUND_RETRIES,
          //   finalStatus: (tokenUsage as any).status,
          // })

          stopNotFoundRetry()
          state.value.status = 'error'
          state.value.error = 'Token用量统计超时，请稍后刷新'

          // 更新缓存
          setTokenCache(message.conversationId!, message.roundSequence!, {
            status: 'error',
            data: null,
            error: 'Token用量统计超时，请稍后刷新',
            timestamp: Date.now(),
            conversationId: message.conversationId!,
            roundSequence: message.roundSequence!,
          })
        }
        else {
          // console.log(`⏳ [Token管理器] 重试 ${state.value.notFoundRetryCount}/${MAX_NOT_FOUND_RETRIES} 仍为NOT_FOUND，继续等待...`)
        }
      }
      catch (error: any) {
        console.error('❌ [Token管理器] NOT_FOUND重试失败:', error)
        state.value.notFoundRetryCount++

        if (state.value.notFoundRetryCount >= MAX_NOT_FOUND_RETRIES) {
          stopNotFoundRetry()
          state.value.status = 'error'
          state.value.error = '重试失败，请稍后刷新'

          // 更新缓存
          setTokenCache(message.conversationId!, message.roundSequence!, {
            status: 'error',
            data: null,
            error: '重试失败，请稍后刷新',
            timestamp: Date.now(),
            conversationId: message.conversationId!,
            roundSequence: message.roundSequence!,
          })
        }
      }
    }, NOT_FOUND_RETRY_INTERVAL) as NodeJS.Timeout
  }

  // 开始轮询
  const startPolling = () => {
    if (state.value.pollTimer) {
      clearInterval(state.value.pollTimer)
    }

    state.value.pollTimer = setInterval(async () => {
      try {
        const result = await checkTokenUsageStatus(
          message.conversationId!,
          message.roundSequence!,
        )

        if (result.isCompleted) {
          stopPolling()

          if (result.status === 'SUCCESS' && result.data) {
            state.value.status = 'success'
            state.value.data = result.data

            // 新增：更新缓存
            setTokenCache(message.conversationId!, message.roundSequence!, {
              status: 'success',
              data: result.data,
              error: null,
              timestamp: Date.now(),
              conversationId: message.conversationId!,
              roundSequence: message.roundSequence!,
            })

            // Token计算完成后刷新余额
            billingStore.refreshBalanceAfterTokenUsage()
          }
          else if ((result as any).status === 'NOT_FOUND') {
            state.value.status = 'error'
            state.value.error = 'Token用量暂未生成'

            // 新增：更新缓存
            setTokenCache(message.conversationId!, message.roundSequence!, {
              status: 'error',
              data: null,
              error: 'Token用量暂未生成',
              timestamp: Date.now(),
              conversationId: message.conversationId!,
              roundSequence: message.roundSequence!,
            })
          }
          else {
            state.value.status = 'error'
            state.value.error = '计算失败'

            // 新增：更新缓存
            setTokenCache(message.conversationId!, message.roundSequence!, {
              status: 'error',
              data: null,
              error: '计算失败',
              timestamp: Date.now(),
              conversationId: message.conversationId!,
              roundSequence: message.roundSequence!,
            })
          }
        }
      }
      catch (error: any) {
        console.error('轮询Token状态失败:', error)
        state.value.retryCount++

        // 重试次数过多时停止轮询
        if (state.value.retryCount >= 10) {
          stopPolling()
          state.value.status = 'error'
          state.value.error = '查询超时'
        }
      }
    }, 5000) as NodeJS.Timeout
  }

  // 开始查询Token用量
  const startQuery = async () => {
    // console.log('🚀 [Token管理器] 开始查询Token用量:', {
    //   conversationId: message.conversationId,
    //   roundSequence: message.roundSequence,
    //   messageId: message.id,
    // })

    // console.log('🔍 [Token管理器] 检查参数完整性:', {
    //   conversationId: message.conversationId,
    //   roundSequence: message.roundSequence,
    //   conversationIdType: typeof message.conversationId,
    //   roundSequenceType: typeof message.roundSequence,
    //   conversationIdTruthy: !!message.conversationId,
    //   roundSequenceTruthy: !!message.roundSequence,
    //   willReturn: !message.conversationId || !message.roundSequence,
    // })

    if (!message.conversationId || !message.roundSequence) {
      // console.warn('❌ [Token管理器] 缺少必要的参数进行Token用量查询:', {
      //   conversationId: message.conversationId,
      //   roundSequence: message.roundSequence,
      //   messageId: message.id,
      //   conversationIdMissing: !message.conversationId,
      //   roundSequenceMissing: !message.roundSequence,
      // })
      return
    }

    // 新增：检查缓存，如果已有数据则直接使用
    const existingCache = getTokenFromCache(message.conversationId, message.roundSequence)
    if (existingCache) {
      //console.log('💾 [Token管理器] 发现缓存数据，直接使用:', existingCache)
      state.value.status = existingCache.status
      state.value.data = existingCache.data
      state.value.error = existingCache.error
      return
    }

    state.value.status = 'loading'
    state.value.error = null

    // 新增：设置loading状态到缓存
    setTokenCache(message.conversationId, message.roundSequence, {
      status: 'loading',
      data: null,
      error: null,
      timestamp: Date.now(),
      conversationId: message.conversationId,
      roundSequence: message.roundSequence,
    })

    //console.log('📡 [Token管理器] 设置状态为loading，准备调用API')

    try {
      // console.log('🌐 [Token管理器] 开始调用getRoundTokenUsage API:', {
      //   conversationId: message.conversationId,
      //   roundSequence: message.roundSequence,
      //   apiPath: `/api/conversations/${message.conversationId}/rounds/${message.roundSequence}/tokens`,
      // })

      const tokenUsage = await getRoundTokenUsage(
        message.conversationId,
        message.roundSequence,
      )

      // console.log('📨 [Token管理器] API调用返回结果:', {
      //   tokenUsage,
      //   status: tokenUsage?.status,
      //   hasData: !!tokenUsage,
      // })

      if (tokenUsage.status === 'PENDING') {
        //console.log('⏳ [Token管理器] Token计算中，开始轮询')
        startPolling()
      }
      else if (tokenUsage.status === 'SUCCESS') {
        // console.log('✅ [Token管理器] Token计算完成:', {
        //   tokenUsage,
        //   inputTokens: tokenUsage.inputTokens,
        //   outputTokens: tokenUsage.outputTokens,
        //   totalCost: tokenUsage.totalCost,
        // })
        state.value.status = 'success'
        state.value.data = tokenUsage

        // 新增：更新缓存
        setTokenCache(message.conversationId, message.roundSequence, {
          status: 'success',
          data: tokenUsage,
          error: null,
          timestamp: Date.now(),
          conversationId: message.conversationId,
          roundSequence: message.roundSequence,
        })

        // Token计算完成后刷新余额
        billingStore.refreshBalanceAfterTokenUsage()
      }
      else if ((tokenUsage as any).status === 'NOT_FOUND') {
        // console.log('❓ [Token管理器] Token用量未找到，启动重试机制:', {
        //   tokenUsage,
        //   conversationId: message.conversationId,
        //   roundSequence: message.roundSequence,
        // })

        // 启动重试机制而不是直接设置错误状态
        startNotFoundRetry()

        // 设置重试状态到缓存
        setTokenCache(message.conversationId!, message.roundSequence!, {
          status: 'retrying',
          data: null,
          error: null,
          timestamp: Date.now(),
          conversationId: message.conversationId!,
          roundSequence: message.roundSequence!,
        })
      }
      else {
        // console.log('🔄 [Token管理器] Token状态未知:', {
        //   tokenUsage,
        //   status: tokenUsage.status,
        //   conversationId: message.conversationId,
        //   roundSequence: message.roundSequence,
        // })
        state.value.status = 'error'
        state.value.error = `未知状态: ${tokenUsage.status}`

        // 新增：更新缓存
        setTokenCache(message.conversationId, message.roundSequence, {
          status: 'error',
          data: null,
          error: `未知状态: ${tokenUsage.status}`,
          timestamp: Date.now(),
          conversationId: message.conversationId,
          roundSequence: message.roundSequence,
        })
      }
    }
    catch (error: any) {
      console.error('❌ [Token管理器] Token用量查询失败:', {
        error,
        errorMessage: error.message,
        errorName: error.name,
        conversationId: message.conversationId,
        roundSequence: message.roundSequence,
        apiUrl: `/api/conversations/${message.conversationId}/rounds/${message.roundSequence}/tokens`,
      })
      state.value.status = 'error'
      state.value.error = error.message

      // 新增：更新缓存
      setTokenCache(message.conversationId, message.roundSequence, {
        status: 'error',
        data: null,
        error: error.message,
        timestamp: Date.now(),
        conversationId: message.conversationId,
        roundSequence: message.roundSequence,
      })
    }
  }

  // 重新查询方法
  const retryQuery = async () => {
    // console.log('🔄 [重新查询] retryQuery 方法被调用:', {
    //   conversationId: message.conversationId,
    //   roundSequence: message.roundSequence,
    //   messageId: message.id,
    // })

    try {
      // 清除缓存
      const cacheKey = generateTokenCacheKey(message.conversationId!, message.roundSequence!)
      //console.log('🔄 [重新查询] 清除缓存键:', cacheKey)
      tokenCache.value.delete(cacheKey)

      // 停止所有正在进行的查询
      //console.log('🔄 [重新查询] 停止现有查询')
      stopPolling()
      stopNotFoundRetry()

      // 重置状态
      //console.log('🔄 [重新查询] 重置状态')
      state.value.status = 'idle'
      state.value.data = null
      state.value.error = null
      state.value.retryCount = 0
      state.value.notFoundRetryCount = 0

      // 重新开始查询
      //console.log('🔄 [重新查询] 开始新查询')
      await startQuery()

      //console.log('🔄 [重新查询] retryQuery 完成')
    }
    catch (error) {
      console.error('🔄 [重新查询] retryQuery 执行异常:', error)
    }
  }

  // 手动清理资源
  const cleanup = () => {
    stopPolling()
    stopNotFoundRetry()
  }

  return {
    state: readonly(state),
    startQuery,
    stopPolling,
    cleanup,
    retryQuery,
  }
}

// 新增：检查消息是否完成（不在loading状态）
function isMessageComplete(message: ChatMessage): boolean {
  return !message.loading && !!(message.content || message.list)
}

// 新增：检测消息完成状态变化
function detectMessageCompletion(oldMsg: ChatMessage | null, newMsg: ChatMessage): boolean {
  // 如果不是assistant消息，不需要检测
  if (newMsg.role !== 'assistant') {
    return false
  }

  // 情况1：消息从loading状态变为非loading状态
  const loadingStatusChanged = oldMsg?.loading === true && newMsg.loading === false

  // 情况2：消息内容从空变为有内容（流式响应完成）
  const oldHasContent = !!(oldMsg?.content || oldMsg?.list)
  const newHasContent = !!(newMsg.content || newMsg.list)
  const contentAdded = !oldHasContent && newHasContent

  // 情况3：消息ID发生变化且新消息有内容（消息替换完成）
  const messageReplaced = oldMsg?.id !== newMsg.id && newHasContent && !newMsg.loading

  return loadingStatusChanged || contentAdded || messageReplaced
}

// 新增：处理assistant消息完成事件
function handleAssistantMessageCompleted(message: ChatMessage) {
  // console.log('✅ [消息完成] 检测到assistant消息完成:', {
  //   messageId: message.id,
  //   conversationId: message.conversationId,
  //   roundSequence: message.roundSequence,
  //   hasContent: !!(message.content || message.list),
  //   loading: message.loading,
  // })

  // 确保消息确实完成
  if (isMessageComplete(message)) {
    triggerTokenQueryIfNeeded(message)
  }
  else {
    //console.log('⚠️ [消息完成] 消息检测完成但实际未完成，跳过Token查询')
  }
}

// 新增：检查并触发Token查询的函数
function triggerTokenQueryIfNeeded(message: ChatMessage) {
  // console.log('🔍 [Token查询] 开始检查消息:', {
  //   messageId: message.id,
  //   messageRole: message.role,
  //   conversationId: message.conversationId,
  //   roundSequence: message.roundSequence,
  //   loading: message.loading,
  //   isComplete: isMessageComplete(message),
  // })

  const isUser = message.role === 'user'

  // 只处理助手消息
  if (isUser) {
    //console.log('❌ [Token查询] 跳过用户消息')
    return
  }

  // 检查消息是否完成（不在loading状态）
  if (message.loading) {
    // console.log('⏳ [Token查询] 消息还在加载中，跳过查询:', {
    //   messageId: message.id,
    //   loading: message.loading,
    // })
    return
  }

  // 检查消息内容是否完整
  if (!isMessageComplete(message)) {
    // console.log('📝 [Token查询] 消息内容不完整，跳过查询:', {
    //   messageId: message.id,
    //   hasContent: !!message.content,
    //   hasList: !!message.list,
    // })
    return
  }

  // console.log('✅ [Token查询] 确认为助手消息，继续检查条件')

  // 新增：检查缓存状态
  const tokenState = getMessageTokenState(message)
  // console.log('💾 [Token查询] 缓存状态检查:', {
  //   messageId: message.id,
  //   hasCache: tokenState.hasCache,
  //   cacheStatus: tokenState.cacheItem?.status,
  //   shouldQuery: tokenState.shouldQuery,
  // })

  // 如果缓存中有数据，直接返回
  if (tokenState.hasCache && tokenState.cacheItem) {
    if (tokenState.cacheItem.status === 'loading') {
      // console.log('⏳ [Token查询] 缓存命中 - 正在查询中，跳过重复查询:', {
      //   messageId: message.id,
      //   cacheKey: generateTokenCacheKey(message.conversationId!, message.roundSequence!),
      //   cacheTimestamp: tokenState.cacheItem.timestamp,
      // })
      return
    }
    else if (tokenState.cacheItem.status === 'success') {
      // console.log('✅ [Token查询] 缓存命中 - 已有成功数据，跳过查询:', {
      //   messageId: message.id,
      //   cacheKey: generateTokenCacheKey(message.conversationId!, message.roundSequence!),
      //   tokenData: tokenState.cacheItem.data,
      //   cacheAge: Date.now() - tokenState.cacheItem.timestamp,
      // })
      return
    }
    else if (tokenState.cacheItem.status === 'error') {
      // console.log('❌ [Token查询] 缓存命中 - 有错误状态，跳过查询:', {
      //   messageId: message.id,
      //   cacheKey: generateTokenCacheKey(message.conversationId!, message.roundSequence!),
      //   error: tokenState.cacheItem.error,
      //   cacheAge: Date.now() - tokenState.cacheItem.timestamp,
      // })
      return
    }
    else if (tokenState.cacheItem.status === 'not_found') {
      // console.log('❓ [Token查询] 缓存命中 - NOT_FOUND状态，跳过查询:', {
      //   messageId: message.id,
      //   cacheKey: generateTokenCacheKey(message.conversationId!, message.roundSequence!),
      //   error: tokenState.cacheItem.error,
      //   cacheAge: Date.now() - tokenState.cacheItem.timestamp,
      // })
      return
    }
    else if (tokenState.cacheItem.status === 'retrying') {
      // console.log('🔄 [Token查询] 缓存命中 - 正在重试中，跳过查询:', {
      //   messageId: message.id,
      //   cacheKey: generateTokenCacheKey(message.conversationId!, message.roundSequence!),
      //   cacheAge: Date.now() - tokenState.cacheItem.timestamp,
      // })
      return
    }
  }

  // 检查是否为轮次中的最后一条助手消息
  const isLastAssistant = isLastAssistantInRound(message)
  // console.log('🎯 [Token查询] 检查是否为轮次最后一条助手消息:', {
  //   messageId: message.id,
  //   isLastAssistant,
  //   roundSequence: message.roundSequence,
  // })

  // 检查会话ID有效性
  const hasValidConversationId = isValidConversationId(message.conversationId)
  // console.log('🆔 [Token查询] 检查会话ID有效性:', {
  //   conversationId: message.conversationId,
  //   hasValidConversationId,
  //   isUUID: message.conversationId ? isValidUUID(message.conversationId) : false,
  //   isTempConversation: message.conversationId === 'temp-conversation',
  // })

  // 检查轮次序号
  const hasRoundSequence = message.roundSequence !== undefined
  // console.log('🔢 [Token查询] 检查轮次序号:', {
  //   roundSequence: message.roundSequence,
  //   hasRoundSequence,
  //   roundSequenceType: typeof message.roundSequence,
  // })

  // 检查是否为轮次中的最后一条助手消息，并且消息包含必要参数
  const shouldShowTokenUsage = isLastAssistant
    && hasValidConversationId
    && hasRoundSequence

  // console.log('📊 [Token查询] 初步条件检查结果:', {
  //   isLastAssistant,
  //   hasValidConversationId,
  //   hasRoundSequence,
  //   shouldShowTokenUsage,
  //   messageId: message.id,
  // })

  if (!shouldShowTokenUsage) {
    // console.log('❌ [Token查询] 不满足基本条件，跳过Token查询:', {
    //   messageId: message.id,
    //   失败原因: {
    //     不是轮次最后助手消息: !isLastAssistant,
    //     会话ID无效: !hasValidConversationId,
    //     缺少轮次序号: !hasRoundSequence,
    //   },
    // })
    return
  }

  // console.log('✅ [Token查询] 通过基本条件检查，开始提取真实会话ID')

  // 使用新的会话ID提取逻辑
  let chatHistory = props.chatHistory
  if (chatHistory && typeof chatHistory === 'object' && 'data' in chatHistory) {
    chatHistory = (chatHistory as any).data
  }

  // console.log('📋 [Token查询] 聊天历史信息:', {
  //   originalHistoryType: typeof props.chatHistory,
  //   hasDataProperty: props.chatHistory && typeof props.chatHistory === 'object' && 'data' in props.chatHistory,
  //   finalHistoryLength: chatHistory ? chatHistory.length : 0,
  //   currentConversationId: message.conversationId,
  // })

  const realConversationId = extractRealConversationId(message.conversationId, chatHistory as any[])
  // console.log('🔍 [Token查询] 会话ID提取结果:', {
  //   originalConversationId: message.conversationId,
  //   realConversationId,
  //   提取成功: !!realConversationId,
  // })

  let effectiveRoundSequence = message.roundSequence

  // 如果消息没有后端的roundSequence，使用前端识别的轮次信息
  if (!effectiveRoundSequence) {
    //console.log('🔄 [Token查询] 缺少后端轮次信息，尝试前端识别')
    const rounds = identifiedRounds.value
    let foundRoundSequence = 1

    //console.log('🗺️ [Token查询] 前端识别的轮次信息:', {
    //   totalRounds: rounds.size,
    //   roundsMap: Array.from(rounds.entries() as any).map((entry: any) => {
    //     const [seq, info] = entry
    //     return {
    //       sequence: seq,
    //       messageCount: info.messages.length,
    //       messageIds: info.messages.map((m: any) => m.id),
    //     }
    //   }),
    // })

    for (const [roundSeq, roundInfo] of rounds.entries() as any) {
      if (roundInfo.messages.some((m: any) => m.id === message.id)) {
        foundRoundSequence = roundSeq
        // console.log('🎯 [Token查询] 找到消息所属轮次:', {
        //   messageId: message.id,
        //   foundRoundSequence,
        //   roundInfo: {
        //     sequence: roundInfo.roundSequence,
        //     messageCount: roundInfo.messages.length,
        //   },
        // })
        break
      }
    }

    effectiveRoundSequence = foundRoundSequence
    //console.log('📍 [Token查询] 使用前端识别的轮次序号:', effectiveRoundSequence)
  }
  else {
    // console.log('✅ [Token查询] 使用后端提供的轮次序号:', effectiveRoundSequence)
  }

  // 验证Token查询所需的参数
  // console.log('🔒 [Token查询] 开始参数验证:', {
  //   realConversationId,
  //   effectiveRoundSequence,
  // })

  const tokenValidation = validateTokenQueryParams(realConversationId || undefined, effectiveRoundSequence)
  // console.log('✅ [Token查询] 参数验证结果:', {
  //   isValid: tokenValidation.isValid,
  //   error: tokenValidation.error,
  //   conversationId: realConversationId,
  //   roundSequence: effectiveRoundSequence,
  // })

  if (tokenValidation.isValid && realConversationId) {
    // 参数有效，创建Token管理器
    const messageWithRoundInfo = {
      ...message,
      conversationId: realConversationId,
      roundSequence: effectiveRoundSequence!,
    }

    // console.log('🚀 [Token查询] 创建Token管理器并开始查询:', {
    //   messageId: message.id,
    //   conversationId: realConversationId,
    //   roundSequence: effectiveRoundSequence,
    //   managersCount: tokenManagers.size,
    // })

    const tokenUsageManager = createTokenUsageManager(messageWithRoundInfo)
    tokenManagers.add(tokenUsageManager)

    // 立即开始Token查询
    tokenUsageManager.startQuery()

    // console.log('✅ [Token查询] Token查询已触发成功')
  }
  else {
    // console.warn('❌ [Token查询] 参数验证失败，无法进行Token查询:', {
    //   validationError: tokenValidation.error,
    //   conversationId: realConversationId,
    //   roundSequence: effectiveRoundSequence,
    //   messageId: message.id,
    //   详细原因: {
    //     无真实会话ID: !realConversationId,
    //     参数验证失败: !tokenValidation.isValid,
    //     验证错误信息: tokenValidation.error,
    //   },
    // })
  }
}

// 使用计算属性确保 loadingMessage 变化时重新计算
const formattedHistory = computed(() => {
  // 直接使用真实数据源
  let chatHistory = props.chatHistory

  // 处理后端返回的包装数据结构
  if (chatHistory && typeof chatHistory === 'object' && 'data' in chatHistory) {
    chatHistory = (chatHistory as any).data
  }

  if (!chatHistory || chatHistory.length === 0) {
    return []
  }

  // 使用 flatMap 处理消息，包括加载状态和多类型消息
  const bubbleListItems = chatHistory.flatMap((message: any, index: number, arr: any[]) => {
    const messageKey = message.id || `msg-${Date.now()}-${Math.random()}`
    const roleStr = typeof message.role === 'string' ? message.role.toLowerCase() : 'assistant'
    const isUser = roleStr === 'user' || roleStr.includes('user')
    const currentRole: 'user' | 'assistant' = isUser ? 'user' : 'assistant'

    // 检查是否为轮次中的最后一条助手消息，并且消息包含必要参数
    const shouldShowTokenUsage = !isUser
      && isLastAssistantInRound(message)
      && isValidConversationId(message.conversationId)
      && message.roundSequence !== undefined

    // 临时解决方案：如果没有后端轮次信息，基于前端识别逻辑显示Token
    const shouldShowTokenUsageFallback = !isUser
      && isLastAssistantInRound(message)
      && isValidConversationId(message.conversationId)

    // 简化方案：暂时为所有助手消息显示Token信息（用于调试和测试）
    const shouldShowTokenUsageSimple = !isUser && isValidConversationId(message.conversationId)

    const finalShouldShowTokenUsage = shouldShowTokenUsage || shouldShowTokenUsageFallback || shouldShowTokenUsageSimple

    // 调试信息：记录Token显示决策过程
    if (!isUser) {
      // console.log('Token显示检查:', {
      //   messageId: message.id,
      //   messageRole: message.role,
      //   isUser,
      //   conversationId: message.conversationId,
      //   roundSequence: message.roundSequence,
      //   isLastAssistant: isLastAssistantInRound(message),
      //   shouldShowTokenUsage,
      //   shouldShowTokenUsageFallback,
      //   shouldShowTokenUsageSimple,
      //   finalShouldShowTokenUsage,
      //   message,
      // })
    }

    // 为助手消息创建Token状态管理
    let tokenUsageManager: ReturnType<typeof createTokenUsageManager> | null = null
    let tokenValidation: { isValid: boolean, error?: string } | null = null
    let cachedTokenData: TokenCacheItem | null = null

    if (finalShouldShowTokenUsage) {
      // 使用新的会话ID提取逻辑，避免使用temp-conversation
      let chatHistory = props.chatHistory
      if (chatHistory && typeof chatHistory === 'object' && 'data' in chatHistory) {
        chatHistory = (chatHistory as any).data
      }

      const realConversationId = extractRealConversationId(message.conversationId, chatHistory as any[])
      let effectiveRoundSequence = message.roundSequence

      // 如果消息没有后端的roundSequence，使用前端识别的轮次信息
      if (!effectiveRoundSequence) {
        // 基于消息在数组中的位置和前端轮次识别逻辑来确定轮次
        const rounds = identifiedRounds.value
        let foundRoundSequence = 1

        // 遍历所有轮次，找到包含当前消息的轮次
        for (const [roundSeq, roundInfo] of rounds.entries() as any) {
          if (roundInfo.messages.some((m: any) => m.id === message.id)) {
            foundRoundSequence = roundSeq
            break
          }
        }

        effectiveRoundSequence = foundRoundSequence
      }

      // 验证Token查询所需的参数
      tokenValidation = validateTokenQueryParams(realConversationId || undefined, effectiveRoundSequence)

      // console.log('Token参数验证结果:', {
      //   messageId: message.id,
      //   validation: tokenValidation,
      //   realConversationId,
      //   effectiveRoundSequence,
      //   originalRoundSequence: message.roundSequence,
      // })

      if (tokenValidation.isValid && realConversationId) {
        // 新增：检查缓存，如果有缓存则使用缓存数据
        cachedTokenData = getTokenFromCache(realConversationId, effectiveRoundSequence!)

        if (cachedTokenData) {
          // console.log('💾 [formattedHistory] 使用缓存的Token数据:', {
          //   messageId: message.id,
          //   cacheStatus: cachedTokenData.status,
          //   hasData: !!cachedTokenData.data,
          // })
        }
        else {
          // 没有缓存时才创建Token管理器
          const messageWithRoundInfo = {
            ...message,
            conversationId: realConversationId,
            roundSequence: effectiveRoundSequence!,
          }

          // console.log('📦 [formattedHistory] 准备创建Token管理器:', {
          //   messageId: message.id,
          //   originalConversationId: message.conversationId,
          //   realConversationId,
          //   effectiveRoundSequence,
          //   messageWithRoundInfo: {
          //     id: messageWithRoundInfo.id,
          //     conversationId: messageWithRoundInfo.conversationId,
          //     roundSequence: messageWithRoundInfo.roundSequence,
          //   },
          // })

          tokenUsageManager = createTokenUsageManager(messageWithRoundInfo)
          tokenManagers.add(tokenUsageManager) // 添加到管理器集合

          // console.log('✅ [formattedHistory] 创建Token管理器成功，立即触发查询:', {
          //   messageId: message.id,
          //   conversationId: realConversationId,
          //   roundSequence: effectiveRoundSequence,
          // })

          // 立即触发Token查询
          tokenUsageManager.startQuery()
        }
      }
      else {
        // 参数无效，记录警告但不阻止消息显示
        // console.warn(`Token查询参数无效，跳过Token用量查询: ${tokenValidation.error}`, {
        //   conversationId: realConversationId,
        //   roundSequence: effectiveRoundSequence,
        //   messageId: message.id,
        // })
      }
    }

    // 处理加载状态的气泡
    if (message.loading) {
      return {
        role: 'assistant',
        key: `${messageKey}-loading`,
        placement: 'start',
        avatar: rolesConfig.assistant.avatar,
        loading: true,
        loadingRender: () => h(Space, {}, {
          default: () => [
            h(Spin, { size: 'small' }),
            loadingMessage.value, // 这里会响应 loadingMessage 的变化
          ],
        }),
        classNames: { content: 'loading-message' },
      } as CustomBubbleItem
    }

    // 检查是否有多类型内容列表
    if (message.list && message.list.length > 0) {
      // 分离不同类型的内容
      const textAndCollapsibleItems = message.list.filter((item: any) =>
        (item.type === 'text' || item.type === 'collapsible') && !item.independent,
      )
      const independentItems = message.list.filter((item: any) =>
        item.type === 'suggestion' || item.type === 'file' || item.independent,
      )

      const bubbles: CustomBubbleItem[] = []
      const isSameRoleAsPrevious = index > 0 && arr[index - 1].role === message.role
      const placement = isUser ? 'end' : 'start'

      // 1. 处理主气泡（text + collapsible 内容）
      if (textAndCollapsibleItems.length > 0) {
        const mainAvatarConfig = !isSameRoleAsPrevious
          ? (isUser ? rolesConfig.user.avatar : rolesConfig.assistant.avatar)
          : { style: { visibility: 'hidden' } }

        const mainBubble: CustomBubbleItem = {
          key: `${messageKey}-main`,
          role: isUser ? 'user' : 'assistant',
          content: textAndCollapsibleItems,
          placement,
          avatar: mainAvatarConfig,
          styles: message.agentType === 'visual' || message.list[0].content.includes('layout')
            ? { content: { width: '90%', height: '720px' } }
            : { content: { maxWidth: isUser ? '70%' : '80%' } },
        }

        // 为主气泡添加自定义渲染逻辑
        if (isUser) {
          // 用户消息的自定义渲染逻辑
          mainBubble.messageRender = (contentToRender) => {
            const items = Array.isArray(contentToRender) ? contentToRender : []

            return h('div', { class: 'user-message-container' }, [
              // 渲染所有 text 内容
              ...items.map((item: any, itemIndex: number) => {
                if (item.type === 'text') {
                  return h('div', {
                    key: `text-${itemIndex}`,
                    innerHTML: renderMarkdown(String(item.content)),
                    class: 'markdown-content',
                    style: { marginBottom: itemIndex < items.length - 1 ? '12px' : '0' },
                  })
                }
                return null
              }).filter(Boolean),
            ])
          }
        }
        else if (!isUser) {
          mainBubble.messageRender = (contentToRender) => {
            const items = Array.isArray(contentToRender) ? contentToRender : []

            return h('div', { class: 'assistant-message-container' }, [
              // 如果有思维链，先渲染思维链
              message.intermediateSteps && message.intermediateSteps.length > 0
                ? h(ChatThoughtChain, {
                    steps: message.intermediateSteps || [],
                    messageKey,
                  })
                : null,

              // 渲染所有 text 和 collapsible 内容
              ...items.map((item: any, itemIndex: number) => {
                if (item.type === 'text') {
                  return message.agentType === 'visual' || item.content.includes('layout')
                    ? h(SubjectVisual, {
                        visualData: item.content,
                      })
                    : h('div', {
                        key: `text-${itemIndex}`,
                        innerHTML: renderMarkdown(String(item.content)),
                        class: 'markdown-content',
                        style: { marginBottom: itemIndex < items.length - 1 ? '12px' : '0' },
                      })
                }
                else if (item.type === 'collapsible') {
                  const { summary, details } = item.content

                  return h('div', {
                    key: `collapsible-${itemIndex}`,
                    class: 'collapsible-content',
                    style: { marginTop: '6px' },
                  }, [
                    // 主要内容（summary）- 始终显示
                    summary
                      ? h('div', {
                          class: 'collapsible-summary',
                          style: {
                            fontWeight: '500',
                            marginBottom: '6px',
                            fontSize: '15px',
                          },
                        }, summary)
                      : null,

                    // 可折叠的详细内容
                    ...(details || []).map((detail: any, detailIndex: number) => {
                      const isExpandable = detail.expandable !== false
                      const collapsed = isCollapsed(messageKey, itemIndex, detailIndex)

                      return h('div', {
                        key: `detail-${detailIndex}`,
                        class: 'collapsible-detail',
                        style: { marginBottom: '8px' },
                      }, [
                        // 根据expandable配置决定渲染方式
                        isExpandable
                          ? h(Button, {
                              type: 'text',
                              size: 'small',
                              onClick: () => toggleCollapsed(messageKey, itemIndex, detailIndex),
                              style: {
                                padding: '4px 8px',
                                height: 'auto',
                                marginBottom: '4px',
                                color: '#6B46C1',
                                fontWeight: '0',
                              },
                            }, {
                              default: () => [
                                h('span', { style: { marginRight: '4px' } }, collapsed ? '▶' : '▼'),
                                h('span', {
                                  innerHTML: renderMarkdown(detail.title),
                                }),
                              ],
                            })
                          : h('div', {
                              style: {
                                padding: '4px 8px',
                                marginBottom: '4px',
                                fontWeight: '0',
                                color: '#000000',
                                fontSize: '14px',
                              },
                              innerHTML: renderMarkdown(detail.title),
                            }),

                        // 可折叠的内容
                        isExpandable && !collapsed
                          ? h('div', {
                              class: 'collapsible-detail-content',
                              style: {
                                paddingLeft: '16px',
                                borderLeft: '3px solid #f0f0f0',
                                marginLeft: '8px',
                              },
                              innerHTML: renderMarkdown(detail.content),
                            })
                          : null,
                      ])
                    }),
                  ])
                }
                return null
              }).filter(Boolean),
            ])
          }

          // 为助手主气泡添加打字效果
          mainBubble.typing = rolesConfig.assistant?.typing
        }

        // 为主气泡添加footer（仅限助手消息的轮次最后一条）
        if (finalShouldShowTokenUsage) {
          if (cachedTokenData) {
            // 使用缓存数据创建footer
            mainBubble.footer = () => {
              const { status, data, error } = cachedTokenData!

              if (status === 'loading') {
                return h(Space, { size: 'small', style: { fontSize: '12px', color: '#666' } }, {
                  default: () => [
                    h(LoadingOutlined, { spin: true }),
                    h('span', '正在计算Token用量...'),
                  ],
                })
              }

              if (status === 'retrying') {
                return h(Space, { size: 'small', style: { fontSize: '12px', color: '#faad14' } }, {
                  default: () => [
                    h('span', '🔄'),
                    h('span', 'Token用量统计中...'),
                  ],
                })
              }

              if (status === 'error') {
                // 检测是否为余额不足消息
                if (isInsufficientBalanceMessage(message)) {
                  const rechargeElements = [
                    h('span', { style: { color: '#faad14' } }, '💳'),
                    h('span', '账户余额不足，'),
                    createRechargeLink(),
                  ]

                  return h(Space, { size: 'small', style: { fontSize: '12px', color: '#faad14' } }, {
                    default: () => rechargeElements,
                  })
                }

                // 为NOT_FOUND状态提供专门的显示
                if (error === 'Token用量暂未生成') {
                  return h(Space, { size: 'small', style: { fontSize: '12px', color: '#faad14' } }, {
                    default: () => [
                      h('span', '💭'),
                      h('span', 'Token用量统计中...'),
                    ],
                  })
                }

                const errorElements = [
                  h(ExclamationCircleOutlined),
                  h('span', error || 'Token用量获取失败'),
                ]

                // 添加重新查询链接
                errorElements.push(createRetryLink(() => {
                  // 使用缓存中的信息创建消息对象
                  const messageWithRoundInfo = {
                    ...message,
                    conversationId: cachedTokenData!.conversationId,
                    roundSequence: cachedTokenData!.roundSequence,
                  }
                  const retryFn = createIndependentRetryQuery(messageWithRoundInfo)
                  retryFn()
                }))

                return h(Space, { size: 'small', style: { fontSize: '12px', color: '#ff4d4f' } }, {
                  default: () => errorElements,
                })
              }

              if (status === 'success' && data) {
                return h(Space, { size: 'small', style: { fontSize: '12px', color: '#666' } }, {
                  default: () => [
                    h(LineChartOutlined, { style: { color: '#52c41a' } }),
                    h('span', formatTokenUsageDisplay(data)),
                  ],
                })
              }

              return ''
            }
          }
          else if (tokenUsageManager) {
            mainBubble.footer = createTokenUsageFooter(message, tokenUsageManager.state.value, tokenUsageManager)
          }
          else {
            // 当Token查询参数无效时，显示提示信息
            const validation = tokenValidation || validateTokenQueryParams(message.conversationId, message.roundSequence)
            mainBubble.footer = () => h(Space, {
              size: 'small',
              style: { fontSize: '12px', color: '#999' },
            }, {
              default: () => [
                h('span', `Token用量暂不可用: ${validation.error || '数据不完整'}`),
              ],
            })
          }
        }

        bubbles.push(mainBubble)
      }

      // 2. 处理独立气泡（suggestion 和 file）
      independentItems.forEach((listItem: any, listIndex: number) => {
        const isFirstIndependent = listIndex === 0 && textAndCollapsibleItems.length === 0

        const avatarConfig = isFirstIndependent && !isSameRoleAsPrevious
          ? rolesConfig[listItem.type as keyof typeof rolesConfig]?.avatar || rolesConfig.assistant.avatar
          : { style: { visibility: 'hidden' } }

        const bubbleItem: CustomBubbleItem = {
          key: `${messageKey}-${listItem.type}-${listIndex}`,
          role: isUser ? 'user' : listItem.type,
          content: listItem.content,
          placement,
          avatar: avatarConfig,
          styles: { content: { maxWidth: isUser ? '70%' : '80%' } },
        }

        // 为独立的text类型添加渲染逻辑和头像配置
        if (listItem.type === 'text' && listItem.independent) {
          // 为独立的text气泡显示助手头像
          bubbleItem.avatar = rolesConfig.assistant.avatar

          bubbleItem.messageRender = (content) => {
            return h('div', {
              innerHTML: renderMarkdown(String(content)),
              class: 'markdown-content',
            })
          }
        }

        bubbles.push(bubbleItem)
      })

      return bubbles
    }

    // 处理常规消息（向后兼容）
    const isSameRoleAsPrevious = index > 0 && arr[index - 1].role === message.role
    const avatarConfig = isSameRoleAsPrevious
      ? { styles: { avatar: { visibility: 'hidden' } } }
      : rolesConfig[currentRole]?.avatar

    const formattedMessage: CustomBubbleItem = {
      key: messageKey,
      role: currentRole,
      content: message.content || '',
      placement: rolesConfig[currentRole]?.placement,
      avatar: avatarConfig,
      styles: { content: { maxWidth: currentRole === 'assistant' ? '80%' : '70%' } },
    }

    if (currentRole === 'assistant') {
      formattedMessage.typing = rolesConfig.assistant?.typing

      // 修改渲染方式，将思维链和消息内容作为整体渲染
      formattedMessage.messageRender = (contentToRender) => {
        const hasThoughtChain = message.intermediateSteps && message.intermediateSteps.length > 0

        return h('div', { class: 'assistant-message-container' }, [
          // 如果有思维链数据，先渲染思维链组件
          hasThoughtChain
            ? h(ChatThoughtChain, {
                steps: message.intermediateSteps || [],
                messageKey,
              })
            : null,

          // 渲染实际消息内容
          h('div', {
            innerHTML: renderMarkdown(String(contentToRender)),
            class: 'markdown-content',
          }),
        ])
      }

      // 为助手消息添加footer（仅限轮次最后一条）
      if (finalShouldShowTokenUsage) {
        if (cachedTokenData) {
          // 使用缓存数据创建footer
          formattedMessage.footer = () => {
            const { status, data, error } = cachedTokenData!

            if (status === 'loading') {
              return h(Space, { size: 'small', style: { fontSize: '12px', color: '#666' } }, {
                default: () => [
                  h(LoadingOutlined, { spin: true }),
                  h('span', '正在计算Token用量...'),
                ],
              })
            }

            if (status === 'retrying') {
              return h(Space, { size: 'small', style: { fontSize: '12px', color: '#faad14' } }, {
                default: () => [
                  h('span', '🔄'),
                  h('span', 'Token用量统计中...'),
                ],
              })
            }

            if (status === 'error') {
              // 检测是否为余额不足消息
              if (isInsufficientBalanceMessage(message)) {
                const rechargeElements = [
                  h('span', { style: { color: '#faad14' } }, '💳'),
                  h('span', '账户余额不足，'),
                  createRechargeLink(),
                ]

                return h(Space, { size: 'small', style: { fontSize: '12px', color: '#faad14' } }, {
                  default: () => rechargeElements,
                })
              }

              // 为NOT_FOUND状态提供专门的显示
              if (error === 'Token用量暂未生成') {
                return h(Space, { size: 'small', style: { fontSize: '12px', color: '#faad14' } }, {
                  default: () => [
                    h('span', '💭'),
                    h('span', 'Token用量统计中...'),
                  ],
                })
              }

              const errorElements = [
                h(ExclamationCircleOutlined),
                h('span', error || 'Token用量获取失败'),
              ]

              // 添加重新查询链接
              errorElements.push(createRetryLink(() => {
                // 使用缓存中的信息创建消息对象
                const messageWithRoundInfo = {
                  ...message,
                  conversationId: cachedTokenData!.conversationId,
                  roundSequence: cachedTokenData!.roundSequence,
                }
                const retryFn = createIndependentRetryQuery(messageWithRoundInfo)
                retryFn()
              }))

              return h(Space, { size: 'small', style: { fontSize: '12px', color: '#ff4d4f' } }, {
                default: () => errorElements,
              })
            }

            if (status === 'success' && data) {
              return h(Space, { size: 'small', style: { fontSize: '12px', color: '#666' } }, {
                default: () => [
                  h(LineChartOutlined, { style: { color: '#52c41a' } }),
                  h('span', formatTokenUsageDisplay(data)),
                ],
              })
            }

            return ''
          }
        }
        else if (tokenUsageManager) {
          formattedMessage.footer = createTokenUsageFooter(message, tokenUsageManager.state.value, tokenUsageManager)
        }
        else {
          // 当Token查询参数无效时，显示提示信息
          const validation = tokenValidation || validateTokenQueryParams(message.conversationId, message.roundSequence)
          formattedMessage.footer = () => h(Space, {
            size: 'small',
            style: { fontSize: '12px', color: '#999' },
          }, {
            default: () => [
              h('span', `Token用量暂不可用: ${validation.error || '数据不完整'}`),
            ],
          })
        }
      }
    }

    return formattedMessage
  })

  // 如果当前正在加载且没有加载消息在历史中，添加一个加载气泡
  if ((props.isLoading || props.isHistoryLoading) && !chatHistory.some((msg: any) => msg.loading)) {
    bubbleListItems.push({
      role: 'assistant',
      key: 'assistant-loading',
      placement: 'start',
      avatar: rolesConfig.assistant.avatar,
      loading: true,
      loadingRender: () => h(Space, {}, {
        default: () => [
          h(Spin, { size: 'small' }),
          loadingMessage.value,
        ],
      }),
      classNames: { content: 'loading-message' },
    } as CustomBubbleItem)
  }

  return bubbleListItems
})

onMounted(() => {
  // 设置全局事件处理器，用于在 messageRender 中调用
  ;(window as any).__suggestionClickHandler = handleSuggestionClick
  scrollToBottom()
})

// 组件卸载时清理定时器和全局事件处理器
onUnmounted(() => {
  cyclicLoader.cleanup()

  // 清理所有 Token 管理器
  tokenManagers.forEach((manager) => {
    manager.cleanup()
  })
  tokenManagers.clear()

  // 新增：清理Token缓存
  tokenCache.value.clear()
  console.log('🧹 [组件卸载] Token缓存已清理')

  // 清理全局事件处理器
  delete (window as any).__suggestionClickHandler
})

function scrollToBottom() {
  if (messagesRef.value) {
    const container = messagesRef.value
    container.scrollTop = container.scrollHeight
  }
}
</script>

<template>
  <div ref="messagesRef" class="chat-messages" :class="styles.messagesContainer">
    <BubbleList
      :items="formattedHistory"
      :roles="rolesConfig"
    />

    <!-- 滚动到底部按钮可以在这里添加 -->
  </div>
</template>

<style scoped>
.chat-messages {
  height: 100%;
  padding: 12px 16px;
  overflow-y: auto;
}

/* 可以添加更多自定义样式 */
</style>

<style>
/* 新增样式，确保思维链和消息内容的布局正确 */
.assistant-message-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.user-message-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ant-bubble .ant-bubble-content-filled {
  background-color: #fff;
  border: 1px solid rgb(220 220 220);
  box-shadow: 4px 4px 8px rgb(0 0 0 / 10%);
}

/* 增加气泡之间的间距 */
.ant-bubble-list .ant-bubble {
  margin-bottom: 16px;
}

/* 确保最后一个气泡也有足够的底部间距 */
.ant-bubble-list .ant-bubble:last-child {
  margin-bottom: 24px;
}

/* 助手头像样式 */
.assistant-avatar img,
.ant-avatar.assistant-avatar img,
[class*="assistant-avatar"] img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  transform: scale(1.8) !important;
}

/* 更通用的助手头像样式选择器 */
.ant-bubble-list .ant-bubble[data-role="assistant"] .ant-avatar img,
.ant-bubble-list .ant-bubble .ant-avatar img[src*="robot"] {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  transform: scale(1.8) !important;
}

/* Markdown内容样式 */
.markdown-content {
  font-size: 14px;
  line-height: 1.6;
  word-break: break-word;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-content h1 {
  font-size: 1.5em;
}

.markdown-content h2 {
  font-size: 1.35em;
}

.markdown-content h3 {
  font-size: 1.25em;
}

.markdown-content p {
  min-height: 1.2em; /* 确保空段落也有最小高度 */
  margin-top: 0;
  margin-bottom: 8px;
}

/* 空段落样式 - 确保包含 &nbsp; 的段落显示为空行 */
.markdown-content p:empty {
  min-height: 1.2em;
  margin: 8px 0;
}

.markdown-content blockquote {
  padding: 0 8px;
  margin: 8px 0;
  color: #6a737d;
  border-left: 4px solid #e8e8e8;
}

.markdown-content pre {
  padding: 12px;
  margin: 8px 0;
  overflow-x: auto;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.markdown-content code {
  padding: 2px 4px;
  font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, Courier, monospace;
  font-size: 90%;
  background-color: rgb(0 0 0 / 5%);
  border-radius: 3px;
}

.markdown-content pre code {
  padding: 0;
  font-size: 100%;
  background-color: transparent;
  border-radius: 0;
}

.markdown-content a {
  color: #1677ff;
  text-decoration: none;
}

.markdown-content a:hover {
  text-decoration: underline;
}

.markdown-content table {
  width: 100%;
  margin: 8px 0;
  border-collapse: collapse;
}

.markdown-content table th,
.markdown-content table td {
  padding: 8px;
  text-align: left;
  border: 1px solid #e8e8e8;
}

.markdown-content table th {
  font-weight: 500;
  background-color: #fafafa;
}

.markdown-content img {
  max-width: 100%;
  height: auto;
  margin: 8px 0;
}

.markdown-content ul,
.markdown-content ol {
  padding-left: 20px;
  margin: 8px 0;
}

.markdown-content li {
  margin-bottom: 4px;
}

.markdown-content hr {
  height: 1px;
  padding: 0;
  margin: 16px 0;
  background-color: #e8e8e8;
  border: 0;
}

/* 加载消息样式 */
.loading-message {
  font-style: italic;
  color: #666;
}

/* 可折叠内容样式 */
.collapsible-content {
  width: 100%;
}

.collapsible-summary {
  line-height: 1.5;
  color: #6b46c1;
}

.collapsible-detail {
  border-radius: 6px;
  transition: all 0.2s ease;
}

.collapsible-detail:hover {
  /* background-color: #fafafa; */
}

.collapsible-detail-content {
  padding: 8px;
  margin-top: 4px;
  font-size: 14px;
  line-height: 1.6;

  /* background-color: #fafafa; */
  border-radius: 4px;
}

.collapsible-detail-content h1,
.collapsible-detail-content h2,
.collapsible-detail-content h3,
.collapsible-detail-content h4,
.collapsible-detail-content h5,
.collapsible-detail-content h6 {
  margin-top: 8px;
  margin-bottom: 4px;
  font-weight: 600;
}

.collapsible-detail-content p {
  margin-bottom: 4px;
}

.collapsible-detail-content pre {
  padding: 8px;
  margin: 4px 0;
  overflow-x: auto;
  background-color: #f0f0f0;
  border-radius: 4px;
}

.collapsible-detail-content code {
  padding: 2px 4px;
  font-family: "Courier New", monospace;
  background-color: #f0f0f0;
  border-radius: 3px;
}

.collapsible-detail-content pre code {
  padding: 0;
  background-color: transparent;
}
</style>
