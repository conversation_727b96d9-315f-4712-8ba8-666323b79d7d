<script setup lang="tsx">
import type { PropType, VNode } from 'vue'

import { Space } from 'ant-design-vue'
import { Sender } from 'ant-design-x-vue'
import { computed, ref } from 'vue'

// 定义组件 props
const props = defineProps({
  // 样式对象
  styles: {
    type: Object,
    required: true,
  },
  // 输入框的值
  inputValue: {
    type: String,
    required: true,
  },
  // 输入值变化的回调函数
  onInputChange: {
    type: Function as PropType<(value: string) => void>,
    required: true,
  },
  // 是否正在加载
  isLoading: {
    type: Boolean,
    required: true,
  },
  // 是否正在加载历史记录
  isHistoryLoading: {
    type: Boolean,
    required: true,
  },
  // 提交表单的回调函数
  onSubmit: {
    type: Function as PropType<(message: string) => void>,
    required: true,
  },
  // 输入框的占位符
  placeholder: {
    type: String,
    default: '输入消息...',
  },
  // 是否禁用提交按钮
  isSubmitDisabled: {
    type: Boolean,
    default: false,
  },
  // 自定义操作区域
  actions: {
    type: [Function, Boolean] as PropType<((...args: any[]) => VNode) | false>,
    default: undefined,
  },
  // 自定义类名
  className: {
    type: String,
    default: '',
  },
  // 通知父组件强制重新渲染的回调函数
  onForceRerender: {
    type: Function as PropType<() => void>,
    default: undefined,
  },

})

// 计算是否禁用
const isDisabled = computed(() => {
  return props.isHistoryLoading || props.isLoading
})

// 包装onSubmit函数以添加清空逻辑
function wrappedOnSubmit(messageContent: string) {
  // 防重复提交：如果正在加载中，直接返回
  if (props.isLoading) {
    return
  }

  // 调用原始onSubmit函数
  props.onSubmit(messageContent)

  // 按照 ant-design-x-vue 标准模式，在 onSubmit 中立即清空输入框
  props.onInputChange('')

  // 通知父组件强制重新渲染
  if (props.onForceRerender) {
    props.onForceRerender()
  }
}

// 默认操作区域
function defaultActions(_: any, info: any) {
  const { SendButton, LoadingButton, ClearButton } = info.components

  return (
    <Space size="small">
      <ClearButton onClick={() => {
        //console.log('BaseSender - 清除按钮点击')
        props.onInputChange('')
      }}
      />
      {props.isLoading
        ? (
            <LoadingButton type="default" disabled />
          )
        : (
            <SendButton
              type="primary"
              disabled={isDisabled.value || props.isSubmitDisabled}
              onClick={() => console.log('BaseSender - 发送按钮点击')}
            />
          )}
    </Space>
  )
}

// 计算使用的操作区域
const actionsToUse = computed(() => {
  return props.actions !== undefined ? props.actions : defaultActions
})

// 组合类名
const senderClass = computed(() => {
  return `${props.styles.sender || ''} ${props.className || ''}`
})
</script>

<template>
  <Sender
    :placeholder="placeholder"
    :value="inputValue"
    :on-change="onInputChange"
    :loading="isLoading"
    :disabled="isDisabled"
    :class="senderClass"
    :on-submit="wrappedOnSubmit"
    :actions="actionsToUse"
  >
    <!-- 传递插槽给 Sender 组件 -->
    <template #header>
      <slot name="header" />
    </template>

    <template #prefix>
      <slot name="prefix" />
    </template>

    <template #footer>
      <slot name="footer" />
    </template>

    <!-- 默认插槽 -->
    <slot />
  </Sender>
</template>

<style scoped>
/* 修复 Sender 组件按钮中图标垂直居中问题 */
:deep(.ant-btn) {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 确保按钮内的图标正确对齐 */
:deep(.ant-btn .anticon) {
  display: inline-flex !important;
  align-items: center !important;
  vertical-align: baseline !important;
}

/* 修复 ant-design-x-vue Sender 组件内按钮的图标对齐 */
:deep(.ant-sender .ant-btn) {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:deep(.ant-sender .ant-btn .anticon) {
  display: inline-flex !important;
  align-items: center !important;
  vertical-align: baseline !important;
}
</style>
