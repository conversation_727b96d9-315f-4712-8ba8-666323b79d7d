<template>
  <div class="copyable-field">
    <span class="field-content">{{ displayValue }}</span>
    <CopyButton
      :value="value"
      :label="label"
      :before-copy="beforeCopy"
      @copy="handleCopy"
      @success="handleSuccess"
      @error="handleError" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import CopyButton from './CopyButton.vue'

// 组件属性
interface Props {
  /** 字段值 */
  value: string | number | null | undefined
  /** 字段标签（用于复制提示） */
  label?: string
  /** 空值时显示的文本 */
  placeholder?: string
  /** 自定义复制前的处理函数 */
  beforeCopy?: (value: string) => string
}

const props = withDefaults(defineProps<Props>(), {
  label: '内容',
  placeholder: '-',
  beforeCopy: (value: string) => value
})

// 组件事件
interface Emits {
  (e: 'copy', value: string): void
  (e: 'success', value: string): void
  (e: 'error', error: Error): void
}

const emit = defineEmits<Emits>()

// 计算属性
const displayValue = computed(() => {
  const value = props.value
  if (value === null || value === undefined || value === '') {
    return props.placeholder
  }
  return String(value)
})

// 事件处理
const handleCopy = (value: string) => {
  emit('copy', value)
}

const handleSuccess = (value: string) => {
  emit('success', value)
}

const handleError = (error: Error) => {
  emit('error', error)
}
</script>

<style scoped lang="scss">
.copyable-field {
  @apply flex items-center gap-2;

  .field-content {
    @apply text-gray-800 font-medium flex-1;
  }
}

// 移动端适配
@media (width <= 768px) {
  .copyable-field {
    @apply flex-col items-start gap-1;

    .field-content {
      @apply text-sm;
    }
  }
}
</style>
