<template>
  <div class="invoice-list">
    <a-card>
      <template #title>
        <div class="card-header">
          <span>发票记录</span>
          <a-button :loading="loading" @click="refreshList">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新
          </a-button>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filters-section">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-select
              v-model:value="filters.status"
              placeholder="选择状态"
              allow-clear
              @change="onFilterChange"
            >
              <a-select-option value="">全部</a-select-option>
              <a-select-option value="PENDING">待开票</a-select-option>
              <a-select-option value="PROCESSING">开票中</a-select-option>
              <a-select-option value="SUCCESS">开票成功</a-select-option>
              <a-select-option value="FAILED">开票失败</a-select-option>
              <a-select-option value="CANCELLED">已取消</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="8">
            <a-range-picker
              v-model:value="filters.dateRange"
              class="date-range"
              @change="onFilterChange"
            />
          </a-col>
          <a-col :span="4">
            <a-button @click="resetFilters">
              重置
            </a-button>
          </a-col>
        </a-row>
      </div>

      <!-- 发票列表表格 -->
      <a-table
        :columns="columns"
        :data-source="invoiceList"
        :loading="loading"
        :pagination="pagination"
        row-key="invoiceNo"
        class="invoice-table"
        @change="onTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'amount'">
            <span class="amount-cell">
              {{ record.formattedAmount }}
            </span>
          </template>

          <template v-if="column.key === 'invoiceType'">
            <a-tag :color="record.invoiceType === 'NORMAL' ? 'blue' : 'green'">
              {{ record.invoiceTypeDisplayName }}
            </a-tag>
          </template>

          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ record.statusDisplayName }}
            </a-tag>
          </template>

          <template v-if="column.key === 'createdAt'">
            <span>{{ formatDateTime(record.createdAt) }}</span>
          </template>

          <template v-if="column.key === 'actions'">
            <a-space>
              <a-button
                type="link"
                size="small"
                @click="viewInvoiceDetail(record)"
              >
                查看详情
              </a-button>
              <a-button
                v-if="record.canDownload"
                type="link"
                size="small"
                @click="downloadInvoice(record)"
              >
                下载发票
              </a-button>
              <a-button
                v-if="record.canReissue"
                type="link"
                size="small"
                @click="reissueInvoice(record)"
              >
                重新开票
              </a-button>
              <a-button
                v-if="record.status === 'PENDING'"
                type="link"
                size="small"
                danger
                @click="cancelInvoice(record)"
              >
                取消申请
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 发票详情抽屉 -->
    <a-drawer
      v-model:open="detailDrawerVisible"
      title="发票详情"
      placement="right"
      :width="500"
      @close="closeDetailDrawer"
    >
      <div v-if="selectedInvoice" class="invoice-detail">
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="发票申请单号">
            {{ selectedInvoice.invoiceNo }}
          </a-descriptions-item>
          <a-descriptions-item label="商户订单号">
            {{ selectedInvoice.outTradeNo }}
          </a-descriptions-item>
          <a-descriptions-item v-if="selectedInvoice.transactionId" label="微信支付单号">
            {{ selectedInvoice.transactionId }}
          </a-descriptions-item>
          <a-descriptions-item label="发票金额">
            <span class="amount-value">
              {{ selectedInvoice.formattedAmount }}
            </span>
          </a-descriptions-item>
          <a-descriptions-item label="发票类型">
            <a-tag :color="selectedInvoice.invoiceType === 'NORMAL' ? 'blue' : 'green'">
              {{ selectedInvoice.invoiceTypeDisplayName }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="发票抬头">
            {{ selectedInvoice.invoiceTitle }}
          </a-descriptions-item>
          <a-descriptions-item label="发票状态">
            <a-tag :color="getStatusColor(selectedInvoice.status)">
              {{ selectedInvoice.statusDisplayName }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item v-if="selectedInvoice.invoiceCode" label="发票代码">
            {{ selectedInvoice.invoiceCode }}
          </a-descriptions-item>
          <a-descriptions-item v-if="selectedInvoice.invoiceNumber" label="发票号码">
            {{ selectedInvoice.invoiceNumber }}
          </a-descriptions-item>
          <a-descriptions-item v-if="selectedInvoice.invoiceDate" label="开票日期">
            {{ formatDateTime(selectedInvoice.invoiceDate) }}
          </a-descriptions-item>
          <a-descriptions-item v-if="selectedInvoice.checkCode" label="校验码">
            {{ selectedInvoice.checkCode }}
          </a-descriptions-item>
          <a-descriptions-item label="申请时间">
            {{ formatDateTime(selectedInvoice.createdAt) }}
          </a-descriptions-item>
          <a-descriptions-item v-if="selectedInvoice.failureReason" label="失败原因">
            <span class="failure-reason">{{ selectedInvoice.failureReason }}</span>
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-drawer>
  </div>
</template>

<script setup lang="ts">
import type { InvoiceResponse, InvoiceQueryParams } from '@/types/billing'
import type { TableColumnsType } from 'ant-design-vue'
import { InvoiceAPI } from '@/api/billing'
import { ReloadOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { onMounted, ref, reactive } from 'vue'

// 响应式数据
const loading = ref(false)
const invoiceList = ref<InvoiceResponse[]>([])
const detailDrawerVisible = ref(false)
const selectedInvoice = ref<InvoiceResponse | null>(null)

// 筛选条件
const filters = reactive({
  status: '',
  dateRange: null as any,
})

// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
})

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '发票申请单号',
    dataIndex: 'invoiceNo',
    key: 'invoiceNo',
    width: 150,
    ellipsis: true,
  },
  {
    title: '发票金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 120,
    align: 'right',
  },
  {
    title: '发票类型',
    dataIndex: 'invoiceType',
    key: 'invoiceType',
    width: 100,
  },
  {
    title: '发票抬头',
    dataIndex: 'invoiceTitle',
    key: 'invoiceTitle',
    width: 200,
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '申请时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 160,
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
  },
]

// 方法
async function loadInvoiceList() {
  loading.value = true
  try {
    const params: InvoiceQueryParams = {
      page: pagination.value.current - 1,
      size: pagination.value.pageSize,
      status: (filters.status as 'PENDING' | 'PROCESSING' | 'SUCCESS' | 'FAILED' | 'CANCELLED') || undefined,
      startDate: filters.dateRange?.[0]?.format('YYYY-MM-DD'),
      endDate: filters.dateRange?.[1]?.format('YYYY-MM-DD'),
    }

    const response = await InvoiceAPI.getUserInvoices(params)

    if (response.success) {
      invoiceList.value = response.data.content
      pagination.value.total = response.data.totalElements
    } else {
      message.error(response.message || '获取发票列表失败')
    }
  } catch (error: any) {
    console.error('获取发票列表失败:', error)
    message.error('获取发票列表失败')
  } finally {
    loading.value = false
  }
}

function refreshList() {
  loadInvoiceList()
}

function onFilterChange() {
  pagination.value.current = 1
  loadInvoiceList()
}

function resetFilters() {
  filters.status = ''
  filters.dateRange = null
  pagination.value.current = 1
  loadInvoiceList()
}

function onTableChange({ current, pageSize }: any) {
  pagination.value.current = current
  pagination.value.pageSize = pageSize
  loadInvoiceList()
}

function viewInvoiceDetail(record: InvoiceResponse) {
  selectedInvoice.value = record
  detailDrawerVisible.value = true
}

function closeDetailDrawer() {
  detailDrawerVisible.value = false
  selectedInvoice.value = null
}

async function downloadInvoice(record: InvoiceResponse) {
  try {
    const response = await InvoiceAPI.downloadInvoice(record.invoiceNo)
    if (response.success) {
      // 打开下载链接
      window.open(response.data, '_blank')
      message.success('发票下载成功')
    } else {
      message.error(response.message || '下载发票失败')
    }
  } catch (error: any) {
    console.error('下载发票失败:', error)
    message.error('下载发票失败')
  }
}

async function reissueInvoice(record: InvoiceResponse) {
  // TODO: 实现重新开票功能
  message.info('重新开票功能开发中...')
}

async function cancelInvoice(record: InvoiceResponse) {
  try {
    const response = await InvoiceAPI.cancelInvoice(record.invoiceNo)
    if (response.success) {
      message.success('发票申请已取消')
      loadInvoiceList()
    } else {
      message.error(response.message || '取消发票申请失败')
    }
  } catch (error: any) {
    console.error('取消发票申请失败:', error)
    message.error('取消发票申请失败')
  }
}

function getStatusColor(status: string) {
  const colorMap: Record<string, string> = {
    PENDING: 'orange',
    PROCESSING: 'blue',
    SUCCESS: 'green',
    FAILED: 'red',
    CANCELLED: 'default',
  }
  return colorMap[status] || 'default'
}

function formatDateTime(dateTime: string) {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadInvoiceList()
})
</script>

<style scoped lang="scss">
.invoice-list {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  .filters-section {
    margin-bottom: 24px;

    .date-range {
      width: 100%;
    }
  }

  .invoice-table {
    .amount-cell {
      font-weight: 600;
      color: var(--primary-color, #1890ff);
    }
  }

  .invoice-detail {
    .amount-value {
      font-size: 16px;
      font-weight: 600;
      color: var(--primary-color, #1890ff);
    }

    .failure-reason {
      color: var(--error-color, #ff4d4f);
    }
  }
}

// 响应式设计
@media (width <= 768px) {
  .invoice-list {
    .filters-section {
      .ant-row {
        flex-direction: column;
        gap: 16px;
      }
    }
  }
}
</style>
