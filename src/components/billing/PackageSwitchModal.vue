<script setup lang="ts">
import type { BillingPackage, PackageSwitchPreview, UserSubscription } from '@/types/packages'
import { PackageAPI } from '@/api/packages'
import { formatCurrency } from '@/utils/format'
import {
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  SwapOutlined,
  WarningOutlined,
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { computed, onMounted, ref, watch } from 'vue'

// Props
interface Props {
  visible: boolean
  targetPackage: BillingPackage
  currentSubscription: UserSubscription
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  confirmed: []
  cancelled: []
  close: []
}>()

// 响应式状态
const isLoading = ref(false)
const isSwitching = ref(false)
const switchPreview = ref<PackageSwitchPreview | null>(null)

// 计算属性
const currentPackage = computed(() => switchPreview.value?.comparison.currentPackage)

const isVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => {
    if (!value) {
      emit('close')
    }
  },
})

const canConfirm = computed(() => {
  return !isSwitching.value && switchPreview.value
})

const priceComparison = computed(() => {
  if (!switchPreview.value) {
    return null
  }

  const comparison = switchPreview.value.comparison
  const current = comparison.currentPackage
  const target = comparison.targetPackage

  return {
    input: {
      current: current.inputTokenPrice,
      target: target.inputTokenPrice,
      change: target.inputTokenPrice - current.inputTokenPrice,
      percentage: ((target.inputTokenPrice - current.inputTokenPrice) / current.inputTokenPrice * 100),
    },
    output: {
      current: current.outputTokenPrice,
      target: target.outputTokenPrice,
      change: target.outputTokenPrice - current.outputTokenPrice,
      percentage: ((target.outputTokenPrice - current.outputTokenPrice) / current.outputTokenPrice * 100),
    },
  }
})

const isUpgrade = computed(() => {
  if (!currentPackage.value || !props.targetPackage) {
    return false
  }
  return props.targetPackage.sortOrder > currentPackage.value.sortOrder
})

const modalTitle = computed(() => {
  const action = isUpgrade.value ? '升级' : '切换'
  return `${action}到 ${props.targetPackage?.name}`
})

const warningLevel = computed(() => {
  if (!switchPreview.value) {
    return 'info'
  }

  const warnings = switchPreview.value.warnings
  if (warnings.some(w => w.includes('费用增加') || w.includes('最低消费提高'))) {
    return 'warning'
  }
  if (warnings.some(w => w.includes('功能限制') || w.includes('额度降低'))) {
    return 'error'
  }
  return 'info'
})

// 方法
async function fetchSwitchPreview() {
  if (!props.targetPackage) {
    return
  }

  try {
    isLoading.value = true
    const response = await PackageAPI.previewPackageSwitch(props.targetPackage.id)

    if (response.success) {
      switchPreview.value = response.data
    }
    else {
      message.error(response.message || '获取切换预览失败')
    }
  }
  catch (error: any) {
    message.error(error.message || '获取切换预览失败')
  }
  finally {
    isLoading.value = false
  }
}

async function handleConfirm() {
  if (!canConfirm.value || !props.targetPackage) {
    return
  }

  try {
    isSwitching.value = true

    const response = await PackageAPI.switchPackage({
      targetPackageId: props.targetPackage.id,
      confirmed: true,
    })

    if (response.success) {
      message.success('套餐切换成功')
      emit('confirmed')
    }
    else {
      message.error(response.message || '套餐切换失败')
    }
  }
  catch (error: any) {
    message.error(error.message || '套餐切换失败')
  }
  finally {
    isSwitching.value = false
  }
}

function handleCancel() {
  emit('cancelled')
}

function resetForm() {
  switchPreview.value = null
}

// 监听器
watch(() => props.visible, (visible) => {
  if (visible) {
    fetchSwitchPreview()
  }
  else {
    resetForm()
  }
})

// 生命周期
onMounted(() => {
  if (props.visible) {
    fetchSwitchPreview()
  }
})
</script>

<template>
  <a-modal
    v-model:open="isVisible"
    :title="modalTitle"
    :width="600"
    :confirm-loading="isSwitching"
    :ok-text="isUpgrade ? '确认升级' : '确认切换'"
    cancel-text="取消"
    :ok-button-props="{ disabled: !canConfirm }"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <div v-if="isLoading" class="loading-container">
      <a-spin size="large" />
    </div>

    <div v-else-if="switchPreview" class="switch-content">
      <!-- 套餐对比 -->
      <div class="comparison-section">
        <h4 class="section-title">
          <SwapOutlined class="title-icon" />
          套餐对比
        </h4>

        <div class="comparison-table">
          <a-table
            :columns="[
              { title: '项目', dataIndex: 'item', key: 'item' },
              { title: '当前套餐', dataIndex: 'current', key: 'current' },
              { title: '目标套餐', dataIndex: 'target', key: 'target' },
              { title: '变化', dataIndex: 'change', key: 'change' },
            ]"
            :data-source="[
              {
                key: 'name',
                item: '套餐名称',
                current: currentPackage?.name,
                target: targetPackage?.name,
                change: isUpgrade ? '升级' : '切换',
              },
              {
                key: 'inputPrice',
                item: '输入Token价格',
                current: `¥${((currentPackage?.inputTokenPrice || 0) * 1000).toFixed(2)}/K`,
                target: `¥${((targetPackage?.inputTokenPrice || 0) * 1000).toFixed(2)}/K`,
                change: priceComparison ? (
                  priceComparison.input.change > 0
                    ? `+¥${(priceComparison.input.change * 1000).toFixed(2)}/K`
                    : `¥${(priceComparison.input.change * 1000).toFixed(2)}/K`
                ) : '-',
              },
              {
                key: 'outputPrice',
                item: '输出Token价格',
                current: `¥${((currentPackage?.outputTokenPrice || 0) * 1000).toFixed(2)}/K`,
                target: `¥${((targetPackage?.outputTokenPrice || 0) * 1000).toFixed(2)}/K`,
                change: priceComparison ? (
                  priceComparison.output.change > 0
                    ? `+¥${(priceComparison.output.change * 1000).toFixed(2)}/K`
                    : `¥${(priceComparison.output.change * 1000).toFixed(2)}/K`
                ) : '-',
              },
              {
                key: 'freeTokens',
                item: '免费额度',
                current: `${currentPackage?.freeTokens.toLocaleString()} tokens`,
                target: `${targetPackage?.freeTokens.toLocaleString()} tokens`,
                change: targetPackage && currentPackage ? (
                  targetPackage.freeTokens > currentPackage.freeTokens
                    ? `+${(targetPackage.freeTokens - currentPackage.freeTokens).toLocaleString()}`
                    : targetPackage.freeTokens < currentPackage.freeTokens
                      ? `-${(currentPackage.freeTokens - targetPackage.freeTokens).toLocaleString()}`
                      : '无变化'
                ) : '-',
              },
            ]"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'change'">
                <span
                  :class="{
                    'change-positive': record.change && (record.change.includes('+') || record.change === '升级'),
                    'change-negative': record.change && record.change.includes('-'),
                    'change-neutral': record.change && (record.change === '切换' || record.change === '无变化' || record.change === '-'),
                  }"
                >
                  {{ record.change }}
                </span>
              </template>
            </template>
          </a-table>
        </div>
      </div>

      <!-- 影响分析 -->
      <div class="impact-section">
        <h4 class="section-title">
          <InfoCircleOutlined class="title-icon" />
          影响分析
        </h4>

        <div class="impact-content">
          <div class="impact-item">
            <strong>生效时间：</strong>{{ switchPreview.effectiveDate }}
          </div>
          <div class="impact-item">
            <strong>本月影响：</strong>{{ switchPreview.impactAnalysis.currentMonthImpact }}
          </div>
          <div class="impact-item">
            <strong>下月影响：</strong>{{ switchPreview.impactAnalysis.nextMonthImpact }}
          </div>
          <div class="impact-item">
            <strong>长期影响：</strong>{{ switchPreview.impactAnalysis.longTermImpact }}
          </div>
        </div>
      </div>

      <!-- 警告信息 -->
      <div v-if="switchPreview.warnings.length > 0" class="warnings-section">
        <h4 class="section-title">
          <WarningOutlined class="title-icon" />
          注意事项
        </h4>

        <a-alert
          :type="warningLevel"
          show-icon
          class="warnings-alert"
        >
          <template #message>
            <ul class="warnings-list">
              <li v-for="warning in switchPreview.warnings" :key="warning">
                {{ warning }}
              </li>
            </ul>
          </template>
        </a-alert>
      </div>

      <!-- 建议信息 -->
      <div v-if="switchPreview.recommendations.length > 0" class="recommendations-section">
        <h4 class="section-title">
          <InfoCircleOutlined class="title-icon" />
          建议
        </h4>

        <a-alert
          type="info"
          show-icon
          class="recommendations-alert"
        >
          <template #message>
            <ul class="recommendations-list">
              <li v-for="recommendation in switchPreview.recommendations" :key="recommendation">
                {{ recommendation }}
              </li>
            </ul>
          </template>
        </a-alert>
      </div>
    </div>

    <div v-else class="error-state">
      <a-result
        status="error"
        title="无法加载切换预览"
        sub-title="请稍后重试或联系客服"
      >
        <template #extra>
          <a-button @click="fetchSwitchPreview">
            重新加载
          </a-button>
        </template>
      </a-result>
    </div>
  </a-modal>
</template>

<style scoped lang="scss">
.loading-container {
  @apply flex justify-center items-center py-12;
}

.switch-content {
  @apply space-y-6;
}

.section-title {
  @apply flex items-center gap-2 text-gray-800 font-medium mb-3;

  .title-icon {
    @apply text-blue-500;
  }
}

.comparison-section {
  .comparison-table {
    :deep(.ant-table) {
      @apply border border-gray-200;
    }

    :deep(.ant-table-thead > tr > th) {
      @apply bg-gray-50 font-medium;
    }

    :deep(.ant-table-tbody > tr > td) {
      @apply py-2;
    }

    .change-positive {
      @apply text-green-600 font-medium;
    }

    .change-negative {
      @apply text-green-600 font-medium;
    }

    .change-neutral {
      @apply text-gray-600;
    }
  }
}

.impact-section {
  .impact-content {
    @apply space-y-2;
  }

  .impact-item {
    @apply text-sm text-gray-700 py-1;

    strong {
      @apply text-gray-800;
    }
  }
}

.warnings-section,
.recommendations-section {
  .warnings-alert,
  .recommendations-alert {
    :deep(.ant-alert-message) {
      @apply m-0;
    }

    .warnings-list,
    .recommendations-list {
      @apply m-0 pl-4 space-y-1;

      li {
        @apply text-sm;
      }
    }
  }
}

.error-state {
  @apply py-6;
}

// 响应式设计
@media (width <= 768px) {
  .switch-content {
    .comparison-table {
      :deep(.ant-table) {
        @apply text-xs;
      }
    }

    .impact-item,
    .warnings-list li,
    .recommendations-list li {
      @apply text-xs;
    }
  }
}
</style>
