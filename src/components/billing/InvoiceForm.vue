<template>
  <div class="invoice-form">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
      @finish="handleSubmit"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <h3 class="section-title">基本信息</h3>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="发票类型" name="invoiceType">
              <a-radio-group v-model:value="formData.invoiceType" @change="onInvoiceTypeChange">
                <a-radio value="NORMAL">普通发票</a-radio>
                <a-radio value="SPECIAL">专用发票</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="发票金额" name="amount">
              <a-input-number
                v-model:value="formData.amount"
                :min="0"
                :precision="2"
                :disabled="true"
                style="width: 100%;"
              >
                <template #addonBefore>¥</template>
              </a-input-number>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="发票抬头" name="invoiceTitle">
          <a-input
            v-model:value="formData.invoiceTitle"
            placeholder="请输入发票抬头"
            :maxlength="100"
            show-count
          />
        </a-form-item>
      </div>

      <!-- 专用发票信息 -->
      <div v-if="formData.invoiceType === 'SPECIAL'" class="form-section">
        <h3 class="section-title">专用发票信息</h3>

        <a-form-item label="纳税人识别号" name="taxNumber">
          <a-input
            v-model:value="formData.taxNumber"
            placeholder="请输入纳税人识别号"
            :maxlength="30"
          />
        </a-form-item>

        <a-form-item label="注册地址" name="registeredAddress">
          <a-input
            v-model:value="formData.registeredAddress"
            placeholder="请输入注册地址"
            :maxlength="200"
          />
        </a-form-item>

        <a-form-item label="注册电话" name="registeredPhone">
          <a-input
            v-model:value="formData.registeredPhone"
            placeholder="请输入注册电话"
            :maxlength="20"
          />
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="开户银行" name="bankName">
              <a-input
                v-model:value="formData.bankName"
                placeholder="请输入开户银行"
                :maxlength="100"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="银行账号" name="bankAccount">
              <a-input
                v-model:value="formData.bankAccount"
                placeholder="请输入银行账号"
                :maxlength="30"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- 收件人信息 -->
      <div class="form-section">
        <h3 class="section-title">收件人信息（可选）</h3>

        <a-form-item label="收件人姓名" name="recipientName">
          <a-input
            v-model:value="formData.recipientName"
            placeholder="请输入收件人姓名"
            :maxlength="50"
          />
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="收件人手机号" name="recipientPhone">
              <a-input
                v-model:value="formData.recipientPhone"
                placeholder="请输入收件人手机号"
                :maxlength="11"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="收件人邮箱" name="recipientEmail">
              <a-input
                v-model:value="formData.recipientEmail"
                placeholder="请输入收件人邮箱"
                :maxlength="100"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- 提交按钮 -->
      <div class="form-actions">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" html-type="submit" :loading="loading">
            申请发票
          </a-button>
        </a-space>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import type { InvoiceRequest } from '@/types/billing'
import type { FormInstance, Rule } from 'ant-design-vue/es/form'
import { computed, reactive, ref } from 'vue'

// Props
interface Props {
  outTradeNo: string
  amount: number
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
})

// Emits
interface Emits {
  (e: 'submit', data: InvoiceRequest): void
  (e: 'cancel'): void
}

const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive<InvoiceRequest>({
  outTradeNo: props.outTradeNo,
  amount: props.amount,
  invoiceType: 'NORMAL',
  invoiceTitle: '',
  taxNumber: '',
  registeredAddress: '',
  registeredPhone: '',
  bankName: '',
  bankAccount: '',
  recipientName: '',
  recipientPhone: '',
  recipientEmail: '',
})

// 表单验证规则
const formRules = computed<Record<string, Rule[]>>(() => {
  const baseRules: Record<string, Rule[]> = {
    invoiceType: [
      { required: true, message: '请选择发票类型', trigger: 'change' },
    ],
    amount: [
      { required: true, message: '发票金额不能为空', trigger: 'blur' },
      { type: 'number', min: 0, message: '发票金额不能为负数', trigger: 'blur' },
    ],
    invoiceTitle: [
      { required: true, message: '请输入发票抬头', trigger: 'blur' },
      { max: 100, message: '发票抬头不能超过100个字符', trigger: 'blur' },
    ],
    recipientPhone: [
      {
        pattern: /^$|^1[3-9]\d{9}$/,
        message: '手机号格式不正确',
        trigger: 'blur'
      },
    ],
    recipientEmail: [
      {
        type: 'email',
        message: '邮箱格式不正确',
        trigger: 'blur'
      },
    ],
  }

  // 专用发票需要额外验证
  if (formData.invoiceType === 'SPECIAL') {
    baseRules.taxNumber = [
      { required: true, message: '请输入纳税人识别号', trigger: 'blur' },
      { max: 30, message: '纳税人识别号不能超过30个字符', trigger: 'blur' },
    ]
    baseRules.registeredAddress = [
      { required: true, message: '请输入注册地址', trigger: 'blur' },
      { max: 200, message: '注册地址不能超过200个字符', trigger: 'blur' },
    ]
    baseRules.registeredPhone = [
      { required: true, message: '请输入注册电话', trigger: 'blur' },
      { max: 20, message: '注册电话不能超过20个字符', trigger: 'blur' },
    ]
    baseRules.bankName = [
      { required: true, message: '请输入开户银行', trigger: 'blur' },
      { max: 100, message: '开户银行不能超过100个字符', trigger: 'blur' },
    ]
    baseRules.bankAccount = [
      { required: true, message: '请输入银行账号', trigger: 'blur' },
      { max: 30, message: '银行账号不能超过30个字符', trigger: 'blur' },
    ]
  }

  return baseRules
})

// 发票类型变化处理
function onInvoiceTypeChange() {
  // 清空专用发票字段
  if (formData.invoiceType === 'NORMAL') {
    formData.taxNumber = ''
    formData.registeredAddress = ''
    formData.registeredPhone = ''
    formData.bankName = ''
    formData.bankAccount = ''
  }
}

// 提交表单
async function handleSubmit() {
  try {
    await formRef.value?.validate()
    emit('submit', { ...formData })
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 取消操作
function handleCancel() {
  emit('cancel')
}
</script>

<style scoped lang="scss">
.invoice-form {
  .form-section {
    margin-bottom: 32px;

    .section-title {
      padding-bottom: 8px;
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 600;
      color: var(--text-color, #262626);
      border-bottom: 1px solid var(--border-color, #d9d9d9);
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    padding-top: 24px;
    border-top: 1px solid var(--border-color, #f0f0f0);
  }
}

// 响应式设计
@media (width <= 768px) {
  .invoice-form {
    .form-section {
      margin-bottom: 24px;
    }

    .ant-col {
      margin-bottom: 16px;
    }
  }
}
</style>
