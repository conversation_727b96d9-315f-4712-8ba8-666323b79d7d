<script setup lang="ts">
import CurrentPackageInfo from '@/components/billing/CurrentPackageInfo.vue'
import { useBillingStore } from '@/stores/billing'
import { formatCurrency, formatNumber } from '@/utils/format'
import {
  ExclamationCircleOutlined,
  PlusOutlined,
  ReloadOutlined,
  WarningOutlined,
} from '@ant-design/icons-vue'
import { computed, onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'

// Props
interface Props {
  size?: 'small' | 'default' | 'large'
  showActions?: boolean
  showStatus?: boolean
  showPackageInfo?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'default',
  showActions: true,
  showStatus: true,
  showPackageInfo: true,
})

// Emits
const emit = defineEmits<{
  rechargeClick: []
  balanceChange: [oldBalance: number, newBalance: number]
}>()

// 路由
const router = useRouter()

// Store
const billingStore = useBillingStore()

// 响应式状态
const isRefreshing = ref(false)
const isAnimating = ref(false)
const showBalanceChangeAlert = ref(false)
const balanceChangeMessage = ref('')
const previousBalance = ref(0)
const error = ref<string | null>(null)
const retryCount = ref(0)
const maxRetries = 3

// 计算属性
const balance = computed(() => billingStore.balance)
const currency = computed(() => billingStore.currency)
const balanceStatus = computed(() => billingStore.balanceStatus)
const accountStatus = computed(() => billingStore.accountStatus)

const formattedBalance = computed(() => {
  return formatCurrency(balance.value, currency.value)
})

const giftBalance = computed(() => billingStore.giftBalance)
const rechargedBalance = computed(() => billingStore.rechargedBalance)

const formattedGiftBalance = computed(() => {
  return formatCurrency(giftBalance.value, currency.value)
})

const freeTokens = computed(() => billingStore.freeTokens)

const formattedFreeTokens = computed(() => {
  return formatNumber(freeTokens.value)
})

const balanceStatusClass = computed(() => {
  return {
    'balance-warning': balanceStatus.value === 'warning',
    'balance-critical': balanceStatus.value === 'critical',
    [`balance-${props.size}`]: true,
  }
})

const badgeStatus = computed(() => {
  const statusMap = {
    normal: 'success' as const,
    warning: 'warning' as const,
    critical: 'error' as const,
  }
  return statusMap[balanceStatus.value]
})

const statusText = computed(() => {
  const textMap = {
    normal: '余额充足',
    warning: '余额不足',
    critical: '余额严重不足',
  }
  return textMap[balanceStatus.value]
})

const warningText = computed(() => {
  if (balanceStatus.value === 'warning') {
    return '建议及时充值以确保服务正常使用'
  }
  if (balanceStatus.value === 'critical') {
    return '余额过低，请立即充值避免服务中断'
  }
  return ''
})

const glowHue = computed(() => {
  const hueMap = {
    normal: 210,
    warning: 35,
    critical: 0,
  }
  return hueMap[balanceStatus.value]
})

const glowSaturation = computed(() => {
  const saturationMap = {
    normal: 80,
    warning: 90,
    critical: 85,
  }
  return saturationMap[balanceStatus.value]
})

const glowLightness = computed(() => {
  const lightnessMap = {
    normal: 60,
    warning: 55,
    critical: 50,
  }
  return lightnessMap[balanceStatus.value]
})

const glowSize = computed(() => {
  const sizeMap = {
    small: 150,
    default: 200,
    large: 250,
  }
  return sizeMap[props.size]
})

const balanceAmountColor = computed(() => {
  switch (balanceStatus.value) {
    case 'critical':
      return '#ff4d4f' // 红色 - 小于等于0
    case 'warning':
      return '#faad14' // 橙色 - 大于0小于等于10
    case 'normal':
    default:
      return '#1890ff' // 蓝色 - 大于10
  }
})

// 错误处理方法
function clearError() {
  error.value = null
  retryCount.value = 0
}

async function handleWithRetry<T>(
  operation: () => Promise<T>,
  operationName: string,
): Promise<T | null> {
  try {
    clearError()
    const result = await operation()
    return result
  }
  catch (err: any) {
    console.error(`${operationName}失败:`, err)

    if (retryCount.value < maxRetries) {
      retryCount.value++
      error.value = `${operationName}失败，正在重试... (${retryCount.value}/${maxRetries})`

      // 延迟重试
      await new Promise(resolve => setTimeout(resolve, 1000 * retryCount.value))
      return handleWithRetry(operation, operationName)
    }
    else {
      error.value = `${operationName}失败: ${err.message || '网络错误，请稍后重试'}`
      return null
    }
  }
}

// 方法
async function handleRefresh() {
  if (isRefreshing.value) {
    return
  }

  isRefreshing.value = true
  try {
    await handleWithRetry(
      () => billingStore.manualRefreshBalance(),
      '刷新余额',
    )
  }
  finally {
    isRefreshing.value = false
  }
}

function handleQuickRecharge() {
  emit('rechargeClick')
  router.push('/billing/recharge')
}

function handleViewHistory() {
  router.push('/billing/usage-history')
}

function triggerBalanceAnimation() {
  isAnimating.value = true
  setTimeout(() => {
    isAnimating.value = false
  }, 600)
}

function showBalanceChange(oldBalance: number, newBalance: number) {
  const change = newBalance - oldBalance
  if (Math.abs(change) > 0.01) {
    const changeText = change > 0 ? `+${formatCurrency(change, currency.value)}` : formatCurrency(change, currency.value)
    balanceChangeMessage.value = `余额变更：${changeText}`
    showBalanceChangeAlert.value = true

    setTimeout(() => {
      hideBalanceChangeAlert()
    }, 5000)
  }
}

function hideBalanceChangeAlert() {
  showBalanceChangeAlert.value = false
  balanceChangeMessage.value = ''
}

// 监听余额变化
watch(balance, (newBalance, oldBalance) => {
  if (oldBalance !== undefined && oldBalance !== newBalance) {
    triggerBalanceAnimation()
    showBalanceChange(oldBalance, newBalance)
    emit('balanceChange', oldBalance, newBalance)
  }
  previousBalance.value = newBalance
})

// 生命周期
onMounted(async () => {
  previousBalance.value = balance.value

  // 如果还没有账户数据，则初始化获取
  if (!billingStore.account) {
    try {
      await billingStore.fetchAccount()
    }
    catch (error) {
      console.warn('BalanceDisplay 初始化获取账户信息失败:', error)
    }
  }
})
</script>

<template>
  <FaGlowyCardWrapper
    :hue="glowHue"
    :saturation="glowSaturation"
    :lightness="glowLightness"
    :size="glowSize"
    :border="2"
    :radius="8"
    class="balance-display-wrapper"
  >
    <FaGlowyCard :class="balanceStatusClass">
      <div class="balance-container">
        <!-- 余额标题 -->
        <div class="balance-header">
          <span class="balance-label">账户余额</span>
          <a-button
            type="text"
            size="small"
            :loading="isRefreshing"
            class="refresh-btn"
            @click="handleRefresh"
          >
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新
          </a-button>
        </div>

        <!-- 余额内容包装器 -->
        <div class="balance-content-wrapper">
          <!-- 余额显示 -->
          <div class="balance-amount">
            <span class="currency">{{ currency }}</span>
            <span
              class="amount"
              :class="{ 'amount-animated': isAnimating }"
              :style="{ color: balanceAmountColor }"
            >
              {{ formattedBalance }}
            </span>
          </div>

          <!-- 余额分项显示 -->
          <div class="balance-breakdown">
            <!-- <div class="balance-item">
              <span class="balance-item-label">充值余额</span>
              <span class="balance-item-value">{{ formattedRechargedBalance }}</span>
            </div> -->
            <!-- <div class="balance-item">
              <span class="balance-item-label">免费Token</span>
              <span class="balance-item-value free-tokens">{{ formattedFreeTokens }}</span>
            </div>
            <div class="balance-item">
              <span class="balance-item-label">赠送余额</span>
              <span class="balance-item-value gift-balance">{{ formattedGiftBalance }}</span>
            </div> -->
          </div>
        </div>

        <!-- 状态指示器 -->
        <div class="balance-status">
          <a-badge
            :status="badgeStatus"
            :text="statusText"
            class="status-badge"
          />
          <div v-if="balanceStatus !== 'normal'" class="status-warning">
            <ExclamationCircleOutlined v-if="balanceStatus === 'warning'" />
            <WarningOutlined v-if="balanceStatus === 'critical'" />
            <span class="warning-text">{{ warningText }}</span>
          </div>
        </div>

        <!-- 快速操作按钮 -->
        <div class="balance-actions">
          <a-button
            type="primary"
            size="small"
            :disabled="accountStatus !== 'ACTIVE'"
            @click="handleQuickRecharge"
          >
            <template #icon>
              <PlusOutlined />
            </template>
            充值
          </a-button>

          <!-- <a-button
            type="link"
            size="small"
            @click="handleViewHistory"
          >
            查看详情
          </a-button> -->
        </div>
      </div>

      <!-- 错误提示 -->
      <a-alert
        v-if="error"
        :message="error"
        type="error"
        show-icon
        closable
        class="error-alert"
        @close="clearError"
      />

      <!-- 余额变动提示 -->
      <a-alert
        v-if="showBalanceChangeAlert"
        :message="balanceChangeMessage"
        type="success"
        show-icon
        closable
        class="balance-alert"
        @close="hideBalanceChangeAlert"
      />

      <!-- 套餐信息 -->
      <CurrentPackageInfo
        v-if="props.showPackageInfo"
        :size="props.size === 'large' ? 'default' : 'small'"
        class="package-info-section"
      />
    </FaGlowyCard>
  </FaGlowyCardWrapper>
</template>

<style scoped lang="scss">
.balance-display-wrapper {
  width: 100%;
  height: auto;
}

.balance-display {
  &.balance-small .balance-container {
    padding: 12px 16px;

    .balance-amount .amount {
      font-size: 20px;
    }
  }

  &.balance-large .balance-container {
    padding: 24px 32px;

    .balance-amount .amount {
      font-size: 32px;
    }
  }
}

.balance-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 20px 24px;
}

.package-info-section {
  padding-top: 16px;
  margin-top: 16px;
  border-top: 1px solid var(--border-color, #e8e8e8);
}

.balance-header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .balance-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color-secondary, #666);
  }

  .refresh-btn {
    height: 24px;
    padding: 0 8px;
    font-size: 12px;
  }
}

.balance-content-wrapper {
  display: flex;
  flex-direction: row;
  gap: 32px;
  align-items: center;
  justify-content: space-between;
}

.balance-amount {
  display: flex;
  gap: 4px;
  align-items: baseline;

  .currency {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color-secondary, #666);
  }

  .amount {
    font-size: 24px;
    font-weight: 600;
    transition: all 0.3s ease;

    &.amount-animated {
      color: #52c41a !important;
      transform: scale(1.05);
    }
  }
}

.balance-breakdown {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 240px;

  .balance-item {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .balance-item-label {
      font-size: 12px;
      color: var(--text-color-secondary, #8c8c8c);
    }

    .balance-item-value {
      font-size: 14px;
      font-weight: 500;
      color: var(--text-color, #262626);

      &.gift-balance {
        color: var(--success-color, #52c41a);
      }

      &.free-tokens {
        color: var(--info-color, #1890ff);
      }
    }
  }
}

.balance-status {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .status-badge {
    :deep(.ant-badge-status-text) {
      font-size: 12px;
    }
  }

  .status-warning {
    display: flex;
    gap: 4px;
    align-items: center;
    padding: 6px 8px;
    background: #fffbe6;
    border: 1px solid #ffe58f;
    border-radius: 4px;

    .anticon {
      font-size: 12px;
      color: #faad14;
    }

    .warning-text {
      font-size: 12px;
      color: #faad14;
    }
  }
}

.balance-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-start;

  .ant-btn {
    height: 28px;
    font-size: 12px;
    border-radius: 4px;
  }
}

.balance-alert {
  margin-top: 12px;
  border-radius: 4px;

  :deep(.ant-alert-message) {
    font-size: 12px;
  }
}

// 响应式设计
@media (width <= 768px) {
  .balance-container {
    padding: 12px 16px;
  }

  .balance-display.balance-large .balance-container {
    padding: 16px 20px;
  }

  .balance-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .balance-content-wrapper {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .balance-amount {
    justify-content: center;
  }

  .balance-breakdown {
    max-width: none;
    padding-top: 12px;
    margin-top: 12px;
    border-top: 1px solid var(--border-color, #e8e8e8);
  }

  .balance-actions {
    justify-content: stretch;

    .ant-btn {
      flex: 1;
    }
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  .balance-display {
    --text-color-secondary: #a6a6a6;
    --primary-color: #1890ff;
  }
}
</style>
