<script setup lang="ts">
import type { RechargeRequest } from '@/types/billing'
import { formatCurrency, getCurrencySymbol } from '@/utils/format'
import {
  AlipayOutlined,
  CloseOutlined,
  CreditCardOutlined,
  ExpandOutlined,
  WechatOutlined,
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { computed, ref, watch } from 'vue'
import WechatPaymentModal from './WechatPaymentModal.vue'

// Props
interface Props {
  visible: boolean
  mode: 'quick' | 'full'
  currency: string
  paymentMethods: Array<{ code: string; name: string; enabled: boolean }>
  presetAmounts: Array<{ value: number }>
  minRechargeAmount: number
  maxRechargeAmount: number
  isProcessing: boolean
  initialAmount?: number
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  mode: 'quick',
  currency: 'CNY',
  paymentMethods: () => [],
  presetAmounts: () => [],
  minRechargeAmount: 1,
  maxRechargeAmount: 10000,
  isProcessing: false,
  initialAmount: 0,
})

// Emits
interface Emits {
  'update:visible': [value: boolean]
  'update:mode': [value: 'quick' | 'full']
  'submit-recharge': [data: RechargeRequest]
  'payment-success': [outTradeNo: string]
}

const emit = defineEmits<Emits>()

// 响应式状态
const selectedAmount = ref<number>(0)
const customAmount = ref<number | null>(null)
const selectedPaymentMethod = ref<string>('')

// 微信支付相关状态
const wechatPaymentVisible = ref<boolean>(false)
const currentRechargeData = ref<RechargeRequest | null>(null)

// 计算属性
const quickPresetAmounts = computed(() => {
  return props.presetAmounts.slice(0, 4) // 快捷模式只显示前4个
})

const finalAmount = computed(() => {
  return selectedAmount.value > 0 ? selectedAmount.value : (customAmount.value || 0)
})

const canSubmitRecharge = computed(() => {
  return finalAmount.value >= props.minRechargeAmount
    && selectedPaymentMethod.value
    && !props.isProcessing
})

// 监听器
watch(() => props.visible, (newVisible) => {
  if (!newVisible) {
    // 面板关闭时重置状态
    resetForm()
    wechatPaymentVisible.value = false
  } else if (newVisible && props.initialAmount && props.initialAmount > 0) {
    // 面板打开时设置初始金额
    setInitialAmount(props.initialAmount)
  }
})

watch(() => props.initialAmount, (newAmount) => {
  if (props.visible && newAmount && newAmount > 0) {
    setInitialAmount(newAmount)
  }
})

// 方法
function closePanel() {
  emit('update:visible', false)
}

function switchToFullMode() {
  emit('update:mode', 'full')
}

function switchToQuickMode() {
  emit('update:mode', 'quick')
}

function selectAmount(amount: number) {
  selectedAmount.value = amount
  customAmount.value = null

  // 自动选择默认支付方式
  if (!selectedPaymentMethod.value && props.paymentMethods.length > 0) {
    const enabledMethod = props.paymentMethods.find(m => m.enabled)
    if (enabledMethod) {
      selectedPaymentMethod.value = enabledMethod.code
    }
  }
}

function onCustomAmountChange(value: number | null) {
  if (value && value > 0) {
    selectedAmount.value = 0

    // 自动选择默认支付方式
    if (!selectedPaymentMethod.value && props.paymentMethods.length > 0) {
      const enabledMethod = props.paymentMethods.find(m => m.enabled)
      if (enabledMethod) {
        selectedPaymentMethod.value = enabledMethod.code
      }
    }
  }
}

function submitRecharge() {
  if (!canSubmitRecharge.value) {
    return
  }

  const rechargeData: RechargeRequest = {
    amount: finalAmount.value,
    paymentMethod: selectedPaymentMethod.value as 'ALIPAY' | 'WECHAT' | 'BANK_CARD' | 'OTHER',
    description: `充值 ${formatCurrency(finalAmount.value)}`,
  }

  // 如果选择微信支付，使用新的微信支付组件
  if (selectedPaymentMethod.value === 'WECHAT' || selectedPaymentMethod.value === 'wechat') {
    currentRechargeData.value = rechargeData
    wechatPaymentVisible.value = true
  } else {
    // 其他支付方式保持原有逻辑
    emit('submit-recharge', rechargeData)
  }
}

function setInitialAmount(amount: number) {
  // 检查是否匹配预设金额
  const matchedPreset = props.presetAmounts.find(preset => preset.value === amount)

  if (matchedPreset) {
    // 如果匹配预设金额，选择对应的预设金额
    selectAmount(amount)
  } else {
    // 如果不匹配预设金额，设置为自定义金额
    selectedAmount.value = 0
    customAmount.value = amount

    // 自动选择默认支付方式
    if (!selectedPaymentMethod.value && props.paymentMethods.length > 0) {
      const enabledMethod = props.paymentMethods.find(m => m.enabled)
      if (enabledMethod) {
        selectedPaymentMethod.value = enabledMethod.code
      }
    }
  }
}

function resetForm() {
  selectedAmount.value = 0
  customAmount.value = null
  selectedPaymentMethod.value = ''
}

function getPaymentIcon(code: string) {
  const iconMap: Record<string, any> = {
    alipay: AlipayOutlined,
    wechat: WechatOutlined,
  }
  return iconMap[code] || CreditCardOutlined
}

// 微信支付相关方法
function handleWechatPaymentSuccess(outTradeNo: string) {
  wechatPaymentVisible.value = false
  emit('update:visible', false)
  emit('payment-success', outTradeNo)
}

function handleWechatPaymentFailed(error: string) {
  console.error('微信支付失败:', error)
  // 保持面板打开，用户可以重试或选择其他支付方式
}

function handleWechatPaymentCancelled() {
  // 支付取消，保持面板打开
}
</script>

<template>
  <a-modal
    :open="visible"
    :footer="null"
    :closable="false"
    :width="mode === 'quick' ? 400 : 600"
    :centered="true"
    :destroy-on-close="true"
    class="quick-recharge-modal"
    @cancel="closePanel"
  >
    <template #title>
      <div class="panel-header">
        <span class="panel-title">{{ mode === 'quick' ? '快速充值' : '账户充值' }}</span>
        <div class="panel-actions">
          <a-button
            v-if="mode === 'quick'"
            type="text"
            size="small"
            @click="switchToFullMode"
          >
            <template #icon>
              <ExpandOutlined />
            </template>
            更多选项
          </a-button>
          <a-button
            v-else
            type="text"
            size="small"
            @click="switchToQuickMode"
          >
            简化模式
          </a-button>
          <a-button
            type="text"
            size="small"
            @click="closePanel"
          >
            <template #icon>
              <CloseOutlined />
            </template>
          </a-button>
        </div>
      </div>
    </template>

    <div class="panel-content">
      <!-- 快捷模式 -->
      <div v-if="mode === 'quick'" class="quick-mode">
        <div class="quick-amounts">
          <h4 class="section-title">选择充值金额</h4>
          <div class="amount-grid">
            <a-button
              v-for="amount in quickPresetAmounts"
              :key="amount.value"
              :type="selectedAmount === amount.value ? 'primary' : 'default'"
              class="amount-btn"
              size="large"
              @click="selectAmount(amount.value)"
            >
              {{ formatCurrency(amount.value, currency) }}
            </a-button>
          </div>
        </div>

        <!-- 支付方式（快捷模式下如果有金额才显示） -->
        <div v-if="selectedAmount > 0 && paymentMethods.length > 0" class="quick-payment">
          <h4 class="section-title">支付方式</h4>
          <a-radio-group v-model:value="selectedPaymentMethod" class="payment-options">
            <a-radio
              v-for="method in paymentMethods.slice(0, 3)"
              :key="method.code"
              :value="method.code"
              :disabled="!method.enabled"
              class="payment-option"
            >
              <div class="payment-content">
                <component :is="getPaymentIcon(method.code)" class="payment-icon" />
                <span class="payment-name">{{ method.name }}</span>
              </div>
            </a-radio>
          </a-radio-group>
        </div>

        <!-- 充值按钮 -->
        <div v-if="selectedAmount > 0" class="quick-actions">
          <a-button
            type="primary"
            size="large"
            block
            :loading="isProcessing"
            :disabled="!canSubmitRecharge"
            @click="submitRecharge"
          >
            <template #icon>
              <CreditCardOutlined />
            </template>
            立即充值 {{ formatCurrency(selectedAmount, currency) }}
          </a-button>
        </div>
      </div>

      <!-- 完整模式 -->
      <div v-else class="full-mode">
        <div class="amount-selection">
          <!-- 预设金额 -->
          <div class="preset-amounts">
            <h4 class="section-title">推荐金额</h4>
            <div class="amount-buttons">
              <a-button
                v-for="amount in presetAmounts"
                :key="amount.value"
                :type="selectedAmount === amount.value ? 'primary' : 'default'"
                class="amount-btn"
                @click="selectAmount(amount.value)"
              >
                {{ formatCurrency(amount.value, currency) }}
              </a-button>
            </div>
          </div>

          <!-- 自定义金额 -->
          <div class="custom-amount">
            <h4 class="section-title">自定义金额</h4>
            <a-input-number
              v-model:value="customAmount"
              :min="minRechargeAmount"
              :max="maxRechargeAmount"
              :precision="2"
              :step="1"
              placeholder="请输入充值金额"
              size="large"
              class="amount-input"
              @change="onCustomAmountChange"
            >
              <template #addonBefore>
                {{ getCurrencySymbol(currency) }}
              </template>
            </a-input-number>
            <div class="amount-tips">
              <span class="tip-text">
                最低充值 {{ formatCurrency(minRechargeAmount, currency) }}，
                最高充值 {{ formatCurrency(maxRechargeAmount, currency) }}
              </span>
            </div>
          </div>
        </div>

        <!-- 充值信息确认 -->
        <div v-if="finalAmount > 0" class="recharge-summary">
          <a-divider />
          <div class="summary-content">
            <div class="summary-item">
              <span class="label">充值金额：</span>
              <span class="value primary">{{ formatCurrency(finalAmount, currency) }}</span>
            </div>
            <div class="summary-item total">
              <span class="label">到账金额：</span>
              <span class="value">{{ formatCurrency(finalAmount, currency) }}</span>
            </div>
          </div>
        </div>

        <!-- 支付方式选择 -->
        <div v-if="finalAmount > 0" class="payment-section">
          <a-divider />
          <h4 class="section-title">选择支付方式</h4>
          <a-radio-group v-model:value="selectedPaymentMethod" class="payment-methods">
            <a-radio
              v-for="method in paymentMethods"
              :key="method.code"
              :value="method.code"
              :disabled="!method.enabled"
              class="payment-method"
            >
              <div class="payment-content">
                <div class="payment-icon">
                  <component :is="getPaymentIcon(method.code)" />
                </div>
                <span class="payment-name">{{ method.name }}</span>
                <span v-if="!method.enabled" class="payment-disabled">(暂不可用)</span>
              </div>
            </a-radio>
          </a-radio-group>
        </div>

        <!-- 充值按钮 -->
        <div v-if="finalAmount > 0" class="recharge-actions">
          <a-button
            type="primary"
            size="large"
            :loading="isProcessing"
            :disabled="!canSubmitRecharge"
            class="recharge-btn"
            @click="submitRecharge"
          >
            <template #icon>
              <CreditCardOutlined />
            </template>
            确认充值 {{ formatCurrency(finalAmount, currency) }}
          </a-button>
        </div>
      </div>
    </div>
  </a-modal>

  <!-- 微信支付二维码对话框 -->
  <WechatPaymentModal
    v-model:visible="wechatPaymentVisible"
    :recharge-data="currentRechargeData"
    @payment-success="handleWechatPaymentSuccess"
    @payment-failed="handleWechatPaymentFailed"
    @payment-cancelled="handleWechatPaymentCancelled"
  />
</template>

<style scoped lang="scss">
.quick-recharge-modal {
  .panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .panel-title {
      font-size: 16px;
      font-weight: 600;
    }

    .panel-actions {
      display: flex;
      gap: 8px;
      align-items: center;
    }
  }
}

.panel-content {
  padding: 8px 0;

  .section-title {
    margin-bottom: 12px;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color, #262626);
  }
}

// 快捷模式样式
.quick-mode {
  .quick-amounts {
    margin-bottom: 24px;

    .amount-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
    }

    .amount-btn {
      height: 48px;
      font-size: 16px;
      font-weight: 600;
    }
  }

  .quick-payment {
    margin-bottom: 24px;

    .payment-options {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .payment-option {
      .payment-content {
        display: flex;
        gap: 8px;
        align-items: center;

        .payment-icon {
          font-size: 16px;
          color: var(--primary-color, #1890ff);
        }

        .payment-name {
          font-size: 14px;
        }
      }
    }
  }

  .quick-actions {
    .ant-btn {
      height: 48px;
      font-size: 16px;
      font-weight: 600;
    }
  }
}

// 完整模式样式
.full-mode {
  .amount-selection {
    .preset-amounts {
      margin-bottom: 24px;

      .amount-buttons {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 8px;
      }

      .amount-btn {
        height: 40px;
        font-size: 14px;
        font-weight: 500;
      }
    }

    .custom-amount {
      margin-bottom: 16px;

      .amount-input {
        width: 100%;
        margin-bottom: 8px;
      }

      .amount-tips {
        .tip-text {
          font-size: 12px;
          color: var(--text-color-secondary, #8c8c8c);
        }
      }
    }
  }

  .recharge-summary {
    .summary-content {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .summary-item {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .label {
        font-size: 14px;
        color: var(--text-color-secondary, #8c8c8c);
      }

      .value {
        font-size: 14px;
        font-weight: 500;

        &.primary {
          color: var(--primary-color, #1890ff);
        }
      }

      &.total {
        padding-top: 4px;
        border-top: 1px solid var(--border-color, #e8e8e8);

        .value {
          font-size: 16px;
          font-weight: 600;
          color: var(--primary-color, #1890ff);
        }
      }
    }
  }

  .payment-section {
    margin-bottom: 24px;

    .payment-methods {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .payment-method {
      .payment-content {
        display: flex;
        gap: 8px;
        align-items: center;

        .payment-icon {
          font-size: 16px;
          color: var(--primary-color, #1890ff);
        }

        .payment-name {
          font-size: 14px;
          font-weight: 500;
        }

        .payment-disabled {
          font-size: 12px;
          color: var(--text-color-secondary, #8c8c8c);
        }
      }
    }
  }

  .recharge-actions {
    text-align: center;

    .recharge-btn {
      height: 48px;
      padding: 0 32px;
      font-size: 16px;
      font-weight: 600;
    }
  }
}

// 响应式设计
@media (width <= 768px) {
  .quick-mode {
    .amount-grid {
      grid-template-columns: 1fr !important;
    }
  }

  .full-mode {
    .amount-buttons {
      grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)) !important;
    }
  }
}

</style>
